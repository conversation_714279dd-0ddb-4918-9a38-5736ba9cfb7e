"""
ETD 解析器簡化測試
基於 VBA ANF 邏輯，遵循 TDD 開發

VBA 邏輯參考：
- 識別條件：主旨包含 "anf"
- 主旨格式：用 "/" 分隔，product=parts[1], mo_number=parts[6][:-1], lot_number=parts[4]
- 從內文提取數量和良率
"""

import pytest
from datetime import datetime

from backend.email.models.email_models import EmailData
from backend.email.parsers.etd_parser import ETDParser
from backend.email.parsers.base_parser import ParsingContext


class TestETDParserSimple:
    """ETD 解析器簡化測試"""

    def setup_method(self):
        """每個測試方法前的設置"""
        self.parser = ETDParser()

    def test_etd_parser_initialization(self):
        """測試 ETD 解析器初始化"""
        assert self.parser.vendor_name == "ETD"
        assert self.parser.vendor_code == "ETD"
        assert self.parser.get_confidence_threshold() == 0.8
        assert "anf" in self.parser.supported_patterns

    def test_identify_vendor_anf_format(self):
        """測試識別包含 'anf' 的郵件"""
        # 基於 VBA 邏輯：If InStr(1, LCase(subject), "anf", vbTextCompare) > 0
        email = EmailData(
            message_id="test-etd-001",
            subject="ETD/PRODUCTX/TYPE/DATA/LOT123/STATUS/MO456789X/ANF",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "anf" in result.matching_patterns
        assert result.vendor_code == "ETD"

    def test_identify_vendor_case_insensitive(self):
        """測試大小寫不敏感的 ANF 識別"""
        # 基於 VBA vbTextCompare 邏輯
        email = EmailData(
            message_id="test-etd-002",
            subject="ETD/PRODUCTX/TYPE/DATA/LOT123/STATUS/MO456789X/AnF",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8

    def test_cannot_identify_non_etd_email(self):
        """測試不能識別非 ETD 格式的郵件"""
        email = EmailData(
            message_id="test-non-etd-001",
            subject="GTK FT HOLD MO:F123456 LOT:ABC123",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is False
        assert result.confidence_score < 0.8

    def test_parse_anf_subject_format(self):
        """測試 ANF 主旨格式解析"""
        # 基於 VBA Split(subject, "/") 邏輯
        subject = "ETD/CHIP_A100/TYPE2/DATA/LOT_ABC123/STATUS/MO_G456789X"
        
        result = self.parser.parse_anf_subject(subject)
        
        assert result["product"] == "CHIP_A100"  # parts[1]
        assert result["lot_number"] == "LOT_ABC123"  # parts[4]
        assert result["mo_number"] == "MO_G456789"  # parts[6][:-1] - 去[EXCEPT_CHAR]最後字元
        assert result["raw_parts"] == subject.split("/")

    def test_parse_anf_subject_insufficient_parts(self):
        """測試 ANF 主旨部分不足的錯誤處理"""
        # 不足 7 個部分的情況
        subject = "ETD/CHIP_A100/TYPE2"
        
        result = self.parser.parse_anf_subject(subject)
        
        assert result["product"] == "CHIP_A100"  # parts[1] 可取得
        assert result["lot_number"] == "?"  # parts[4] 不存在
        assert result["mo_number"] == "?"  # parts[6] 不存在

    def test_extract_qty_from_body(self):
        """測試從郵件內文提取數量"""
        # 基於 VBA GetQtyFromMailBody 函數邏輯
        email_body = """
        Processing Report:
        Input Quantity: 1500 units
        Total processed: 1450 units
        Failed: 50 units
        """
        
        qty = self.parser.extract_qty_from_body(email_body)
        assert qty is not None
        assert "1500" in str(qty)  # 應該找到輸入數量

    def test_extract_yield_from_body(self):
        """測試從郵件內文提取良率"""
        # 基於 VBA GetYieldFromMail 函數邏輯
        email_body = """
        Test Results:
        Yield: 96.7%
        Pass rate: 1450/1500
        Quality status: PASS
        """
        
        yield_value = self.parser.extract_yield_from_body(email_body)
        assert yield_value is not None
        assert "96.7%" in yield_value

    def test_find_line_containing_issue(self):
        """測試找尋包含異常資訊的行"""
        # 基於 VBA FindLineContainingString(body, "異常")
        email_body = """
        Processing Status:
        Normal operation
        異常: 溫度超出範圍 (Temperature out of range)
        Action required: Check cooling system
        """
        
        issue_line = self.parser.find_line_containing(email_body, "異常")
        assert issue_line is not None
        assert "異常: 溫度超出範圍" in issue_line

    def test_parse_email_complete(self):
        """測試完整的 ETD 郵件解析"""
        email = EmailData(
            message_id="test-etd-complete",
            subject="ETD/CHIP_B200/TYPE1/DATA/LOT_XYZ789/STATUS/MO_H123456Y",
            sender="<EMAIL>",
            body="""
            ETD Processing Report
            Input Quantity: 2000 units
            Processed: 1950 units
            Yield: 97.5%
            異常: 無 (No issues)
            Status: COMPLETED
            """,
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="ETD"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "ETD"
        assert result.is_success is True
        assert result.extracted_data["product"] == "CHIP_B200"
        assert result.extracted_data["lot_number"] == "LOT_XYZ789"
        assert result.mo_number == "H123456"  # 清理後的 MO 編號
        assert result.extracted_data["raw_mo_number"] == "MO_H123456"  # 原始 MO 編號
        assert "2000" in str(result.extracted_data["in_qty"])
        assert "97.5%" in result.extracted_data["yield_value"]

    def test_handle_etrendtech_sender(self):
        """測試處理 etrendtech 寄件者"""
        # 基於 VBA InStr(senderAddress, "etrendtech") 邏輯
        email = EmailData(
            message_id="test-etd-sender",
            subject="ETD/PRODUCT/TYPE/DATA/LOT123/STATUS/MO456X/ANF",
            sender="<EMAIL>",
            body="Test body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        # etrendtech 寄件者應該提高信心分數
        assert result.confidence_score >= 0.9

    def test_parse_malformed_anf_subject(self):
        """測試處理格式錯誤的 ANF 主旨"""
        email = EmailData(
            message_id="test-etd-malformed",
            subject="ANF malformed subject without proper format",
            sender="<EMAIL>",
            body="Test body",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="ETD")
        result = self.parser.parse_email(context)
        
        # 應該優雅處理，不拋出異常
        assert result.vendor_code == "ETD"
        # 部分資料可能無法提取，但解析不應該完全失敗
        assert result.is_success in [True, False]  # 根據實作決定

    def test_performance_with_long_body(self):
        """測試長郵件內容的效能"""
        long_body = "Processing data\n" * 5000 + "Yield: 95.0%\nInput Quantity: 1000\n" + "More data\n" * 5000
        
        email = EmailData(
            message_id="test-etd-performance",
            subject="ETD/CHIP/TYPE/DATA/LOT123/STATUS/MO456X",
            sender="<EMAIL>",
            body=long_body,
            received_time=datetime.now()
        )
        
        import time
        start_time = time.time()
        
        context = ParsingContext(email_data=email, vendor_code="ETD")
        result = self.parser.parse_email(context)
        
        end_time = time.time()
        
        # 解析應該在 1 秒內完成
        assert end_time - start_time < 1.0
        assert result.vendor_code == "ETD"

    def test_vendor_confidence_scoring(self):
        """測試 ETD 解析器的信心分數計算"""
        # 完全匹配的情況
        perfect_email = EmailData(
            message_id="test-perfect",
            subject="ETD/CHIP_X/TYPE/DATA/LOT123/STATUS/MO456X/ANF",
            sender="<EMAIL>",
            body="Standard ETD processing",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(perfect_email)
        assert result.confidence_score >= 0.9
        
        # 部分匹配的情況
        partial_email = EmailData(
            message_id="test-partial",
            subject="Some subject with anf keyword",
            sender="<EMAIL>",
            body="Generic body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(partial_email)
        assert 0.5 <= result.confidence_score < 0.9