"""Step 1.2.1: 統一錯誤處理測試
測試統一錯誤處理機制的各種場景和行為

這個文件定義了期望的錯誤處理行為，用於指導實現統一的錯誤處理機制。
"""

import pytest
import sys
import os
from datetime import datetime
from unittest.mock import Mock, patch
from typing import Dict, Any

# 設置導入路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from fastapi import HTTPException


class TestUnifiedErrorResponse:
    """測試統一錯誤響應格式"""
    
    def test_standard_error_response_structure(self):
        """測試：標準錯誤響應應該包含所有必需字段"""
        print("🧪 測試標準錯誤響應結構...")
        
        # 期望的錯誤響應結構
        expected_fields = [
            "success",      # 布林值，表示操作是否成功
            "error",        # 錯誤詳情對象
            "status_code"   # HTTP 狀態碼
        ]
        
        expected_error_fields = [
            "code",         # 錯誤代碼
            "message",      # 用戶友好的錯誤訊息
            "details",      # 詳細錯誤信息（可選）
            "timestamp",    # 錯誤發生時間
            "trace_id",     # 追蹤 ID
            "path"          # 請求路徑
        ]
        
        # 這個測試定義了我們期望的錯誤響應格式
        # 實際實現應該產生符合這個結構的響應
        
        # 模擬創建統一錯誤響應
        from frontend.api.error_handling import create_unified_error_response
        
        error_response = create_unified_error_response(
            error_code="STAGING_SERVICE_UNAVAILABLE",  # 修正為統一的錯誤代碼
            message="檔案暫存服務不可用",
            details="連接資料庫失敗",
            status_code=503,
            path="/api/staging/create",
            trace_id="req_12345"
        )
        
        # 驗證頂層結構
        for field in expected_fields:
            assert field in error_response, f"錯誤響應缺少必需字段: {field}"
        
        # 驗證錯誤對象結構
        error_obj = error_response["error"]
        for field in expected_error_fields:
            assert field in error_obj, f"錯誤對象缺少必需字段: {field}"
        
        # 驗證字段類型和值
        assert error_response["success"] is False
        assert isinstance(error_response["status_code"], int)
        assert error_response["status_code"] == 503
        
        assert error_obj["code"] == "STAGING_SERVICE_UNAVAILABLE"  # 修正為統一的錯誤代碼
        assert error_obj["message"] == "檔案暫存服務不可用"
        assert error_obj["details"] == "連接資料庫失敗"
        assert error_obj["path"] == "/api/staging/create"
        assert error_obj["trace_id"] == "req_12345"
        
        # 驗證時間戳格式
        timestamp = error_obj["timestamp"]
        assert isinstance(timestamp, str)
        # 應該是 ISO 格式
        datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        print("✅ 標準錯誤響應結構測試通過")
    
    def test_error_response_without_optional_fields(self):
        """測試：錯誤響應在沒有可選字段時的行為"""
        print("🧪 測試沒有可選字段的錯誤響應...")
        
        from frontend.api.error_handling import create_unified_error_response
        
        # 只提供必需字段
        error_response = create_unified_error_response(
            error_code="INTERNAL_ERROR",
            message="內部服務器錯誤",
            status_code=500
        )
        
        # 驗證必需字段存在
        assert error_response["success"] is False
        assert error_response["status_code"] == 500
        assert error_response["error"]["code"] == "INTERNAL_ERROR"
        assert error_response["error"]["message"] == "內部服務器錯誤"
        
        # 驗證可選字段的默認值
        error_obj = error_response["error"]
        assert error_obj["details"] is None or error_obj["details"] == ""
        assert error_obj["path"] is None or error_obj["path"] == ""
        assert "trace_id" in error_obj  # 應該自動生成
        assert "timestamp" in error_obj  # 應該自動生成
        
        print("✅ 可選字段測試通過")


class TestErrorCategories:
    """測試錯誤分類和狀態碼"""
    
    def test_service_unavailable_error(self):
        """測試：服務不可用錯誤應該返回 503 狀態碼"""
        print("🧪 測試服務不可用錯誤...")
        
        from frontend.api.error_handling.errors import StagingServiceUnavailableError
        
        error = StagingServiceUnavailableError(
            details="連接資料庫失敗"
        )
        
        # 驗證錯誤屬性
        assert error.status_code == 503
        assert error.error_code == "STAGING_SERVICE_UNAVAILABLE"  # 修正為統一的錯誤代碼
        assert "檔案暫存服務" in error.message
        assert error.details == "連接資料庫失敗"
        
        # 驗證轉換為 HTTPException
        http_exc = error.to_http_exception()
        assert isinstance(http_exc, HTTPException)
        assert http_exc.status_code == 503
        
        print("✅ 服務不可用錯誤測試通過")
    
    def test_validation_error(self):
        """測試：驗證錯誤應該返回 422 狀態碼"""
        print("🧪 測試驗證錯誤...")
        
        from frontend.api.error_handling.errors import RequiredFieldMissingError

        error = RequiredFieldMissingError(
            field_name="product_name"
        )
        
        assert error.status_code == 422
        assert error.error_code == "REQUIRED_FIELD_MISSING"
        assert "product_name" in error.message
        assert "必需" in error.message
        
        print("✅ 驗證錯誤測試通過")
    
    def test_timeout_error(self):
        """測試：超時錯誤應該返回 408 狀態碼"""
        print("🧪 測試超時錯誤...")
        
        from frontend.api.error_handling.errors import OperationTimeoutError
        
        error = OperationTimeoutError(
            operation="檔案處理",
            timeout_seconds=30
        )
        
        assert error.status_code == 408
        assert error.error_code == "OPERATION_TIMEOUT"
        assert "檔案處理" in error.message
        assert "30" in str(error.timeout_seconds)
        
        print("✅ 超時錯誤測試通過")
    
    def test_resource_not_found_error(self):
        """測試：資源不存在錯誤應該返回 404 狀態碼"""
        print("🧪 測試資源不存在錯誤...")
        
        from frontend.api.error_handling.errors import TaskNotFoundError
        
        error = TaskNotFoundError(
            task_id="task-12345"
        )
        
        assert error.status_code == 404
        assert error.error_code == "TASK_NOT_FOUND"  # 修正錯誤代碼
        assert "任務" in error.message
        assert "task-12345" in error.message
        
        print("✅ 資源不存在錯誤測試通過")


class TestErrorRecovery:
    """測試錯誤恢復機制"""
    
    def test_retry_mechanism(self):
        """測試：自動重試機制"""
        print("🧪 測試自動重試機制...")
        
        from frontend.api.error_handling import with_retry
        
        # 模擬會失敗然後成功的函數
        call_count = 0
        def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("暫時失敗")
            return "成功"
        
        # 測試重試機制
        result = with_retry(
            func=failing_function,
            max_retries=3,
            backoff_factor=0.1  # 快速重試用於測試
        )
        
        assert result == "成功"
        assert call_count == 3  # 失敗2次，第3次成功
        
        print("✅ 自動重試機制測試通過")
    
    def test_circuit_breaker(self):
        """測試：熔斷器模式"""
        print("🧪 測試熔斷器模式...")
        
        from frontend.api.error_handling import CircuitBreaker
        
        circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=1.0
        )
        
        # 模擬連續失敗
        def always_fail():
            raise Exception("服務失敗")
        
        # 前3次應該嘗試調用
        for i in range(3):
            with pytest.raises(Exception):
                circuit_breaker.call(always_fail)
        
        # 第4次應該直接失敗（熔斷器開啟）
        with pytest.raises(Exception) as exc_info:
            circuit_breaker.call(always_fail)
        
        assert "熔斷器" in str(exc_info.value) or "circuit breaker" in str(exc_info.value).lower()
        
        print("✅ 熔斷器模式測試通過")


class TestErrorLogging:
    """測試錯誤日誌記錄"""
    
    def test_error_logging_format(self):
        """測試：錯誤日誌格式"""
        print("🧪 測試錯誤日誌格式...")
        
        from frontend.api.error_handling import ErrorLogger
        
        logger = ErrorLogger()
        
        # 模擬錯誤
        error = Exception("測試錯誤")
        
        # 測試日誌記錄
        with patch('src.presentation.api.dependencies.logger') as mock_logger:
            logger.log_error(
                error=error,
                context={
                    "user_id": "user123",
                    "request_path": "/api/test",
                    "request_method": "POST"
                }
            )
            
            # 驗證日誌被調用
            mock_logger.error.assert_called_once()
            
            # 驗證日誌內容
            log_call = mock_logger.error.call_args
            log_message = log_call[0][0]
            
            assert "測試錯誤" in log_message
            assert "user123" in str(log_call)
            assert "/api/test" in str(log_call)
        
        print("✅ 錯誤日誌格式測試通過")
    
    def test_sensitive_data_filtering(self):
        """測試：敏感數據過濾"""
        print("🧪 測試敏感數據過濾...")
        
        from frontend.api.error_handling import ErrorLogger
        
        logger = ErrorLogger()
        
        # 包含敏感數據的上下文
        sensitive_context = {
            "password": "secret123",
            "api_key": "sk-1234567890",
            "credit_card": "4111-1111-1111-1111",
            "normal_data": "這是正常數據"
        }
        
        # 測試敏感數據過濾
        filtered_context = logger.filter_sensitive_data(sensitive_context)
        
        # 驗證敏感數據被過濾
        assert filtered_context["password"] == "***"
        assert filtered_context["api_key"] == "***"
        assert filtered_context["credit_card"] == "***"
        assert filtered_context["normal_data"] == "這是正常數據"
        
        print("✅ 敏感數據過濾測試通過")


def run_all_error_handling_tests():
    """運行所有錯誤處理測試"""
    print("🎯 Step 1.2.1: 統一錯誤處理測試")
    print("=" * 60)
    
    test_classes = [
        TestUnifiedErrorResponse,
        TestErrorCategories,
        TestErrorRecovery,
        TestErrorLogging
    ]
    
    passed = 0
    failed = 0
    
    for test_class in test_classes:
        print(f"\n📋 運行 {test_class.__name__} 測試...")
        
        instance = test_class()
        methods = [method for method in dir(instance) if method.startswith('test_')]
        
        for method_name in methods:
            try:
                method = getattr(instance, method_name)
                method()
                passed += 1
            except Exception as e:
                print(f"❌ {method_name} 失敗: {e}")
                failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 測試結果: {passed} 通過, {failed} 失敗")
    
    if failed == 0:
        print("🎉 所有錯誤處理測試都通過了！")
        print("✅ Step 1.2.1 完成 - 可以開始實現統一錯誤處理機制")
    else:
        print("⚠️ 有測試失敗，這是正常的（TDD Red 階段）")
        print("🔄 下一步：實現統一錯誤處理機制讓測試通過")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_error_handling_tests()
    exit(0 if success else 1)