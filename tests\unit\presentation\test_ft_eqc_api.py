"""
FT-EQC 分組 API 端點測試
測試驅動開發 - 先寫測試，再實作功能
"""

import pytest
import os
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from typing import Dict, Any


class TestFTEQCGroupingAPI:
    """FT-EQC 分組 API 端點測試類別"""
    
    @pytest.fixture
    def test_client(self):
        """建立測試用的 FastAPI 客戶端 - 這會失敗直到我們實作 API"""
        # 這會在實作 API 前失敗，符合 TDD 紅-綠-重構循環
        from frontend.api.ft_eqc_api import app
        return TestClient(app)
    
    @pytest.fixture
    def temp_csv_folder(self):
        """建立臨時測試資料夾和測試 CSV 檔案"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 建立測試 FT 檔案
            ft_file_content = "Test,Header\n(FT),Data\nTest,Data"
            ft_file_path = Path(temp_dir) / "test_ft_20240101.csv"
            ft_file_path.write_text(ft_file_content, encoding='utf-8')
            
            # 建立測試 EQC 檔案
            eqc_file_content = "Test,Header\n(QC),Data\nTest,Data"
            eqc_file_path = Path(temp_dir) / "test_eqc_20240101.csv"
            eqc_file_path.write_text(eqc_file_content, encoding='utf-8')
            
            yield temp_dir
    
    def test_api_endpoint_exists(self, test_client: TestClient):
        """測試 API 端點是否存在"""
        # 這個測試應該會失敗，直到我們實作端點
        response = test_client.get("/api/health")
        assert response.status_code in [200, 404]  # 404 表示還未實作
    
    def test_process_ft_eqc_grouping_success(self, test_client: TestClient, temp_csv_folder):
        """測試成功的 FT-EQC 分組處理請求"""
        # 測試資料
        request_data = {
            "folder_path": temp_csv_folder
        }
        
        # 執行 API 請求
        response = test_client.post("/api/process_ft_eqc_grouping", json=request_data)
        
        # 驗證回應
        assert response.status_code == 200
        
        response_data = response.json()
        assert "status" in response_data
        assert response_data["status"] == "success"
        assert "data" in response_data
        
        # 驗證分組結果資料結構
        data = response_data["data"]
        assert "matched_pairs" in data
        assert "unmatched_eqc" in data
        assert "statistics" in data
        
        # 驗證統計資料
        stats = data["statistics"]
        assert "total_csv_files" in stats
        assert "ft_files_count" in stats
        assert "eqc_files_count" in stats
        assert "successful_matches" in stats
        assert "eqc_rt_count" in stats
        assert "matching_rate" in stats
        assert "processing_timestamp" in stats
    
    def test_process_ft_eqc_grouping_invalid_folder(self, test_client: TestClient):
        """測試無效資料夾路徑的錯誤處理"""
        request_data = {
            "folder_path": "/不存在的路徑/測試"
        }
        
        response = test_client.post("/api/process_ft_eqc_grouping", json=request_data)
        
        assert response.status_code == 400
        response_data = response.json()
        assert "status" in response_data
        assert response_data["status"] == "error"
        assert "message" in response_data
        assert "資料夾不存在" in response_data["message"]
    
    def test_process_ft_eqc_grouping_missing_folder_path(self, test_client: TestClient):
        """測試缺少 folder_path 參數的請求"""
        request_data = {}  # 缺少 folder_path
        
        response = test_client.post("/api/process_ft_eqc_grouping", json=request_data)
        
        assert response.status_code == 422  # Pydantic 驗證錯誤
    
    def test_process_ft_eqc_grouping_invalid_json(self, test_client: TestClient):
        """測試無效 JSON 格式的請求"""
        response = test_client.post(
            "/api/process_ft_eqc_grouping",
            data="無效的JSON",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422
    
    def test_process_ft_eqc_grouping_empty_folder(self, test_client: TestClient):
        """測試空資料夾的處理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            request_data = {
                "folder_path": temp_dir
            }
            
            response = test_client.post("/api/process_ft_eqc_grouping", json=request_data)
            
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "success"
            
            # 空資料夾應該回傳空的結果
            data = response_data["data"]
            assert data["statistics"]["total_csv_files"] == 0
            assert data["statistics"]["ft_files_count"] == 0
            assert data["statistics"]["eqc_files_count"] == 0
    
    @patch('src.presentation.api.ft_eqc_api.FTEQCGroupingProcessor')
    def test_process_ft_eqc_grouping_internal_error(self, mock_processor, test_client: TestClient, temp_csv_folder):
        """測試內部處理錯誤的處理"""
        # 模擬處理器拋出異常
        mock_instance = Mock()
        mock_instance.process_folder.side_effect = Exception("內部處理錯誤")
        mock_processor.return_value = mock_instance
        
        request_data = {
            "folder_path": temp_csv_folder
        }
        
        response = test_client.post("/api/process_ft_eqc_grouping", json=request_data)
        
        assert response.status_code == 500
        response_data = response.json()
        assert response_data["status"] == "error"
        assert "內部處理錯誤" in response_data["message"]
    
    def test_cors_headers(self, test_client: TestClient):
        """測試 CORS 標頭設定"""
        response = test_client.options("/api/process_ft_eqc_grouping")
        # CORS 標頭測試會在實作時確認
        assert response.status_code in [200, 405, 404]


class TestFTEQCAPIDataModels:
    """測試 API 資料模型"""
    
    def test_grouping_request_model(self):
        """測試分組請求資料模型"""
        # 這會失敗直到我們定義模型
        from frontend.api.models import FTEQCGroupingRequest
        
        # 有效請求
        valid_request = FTEQCGroupingRequest(folder_path="/test/path")
        assert valid_request.folder_path == "/test/path"
        
        # 無效請求應該拋出驗證錯誤
        with pytest.raises(Exception):
            FTEQCGroupingRequest(folder_path="")  # 空字串應該無效
    
    def test_grouping_response_model(self):
        """測試分組回應資料模型"""
        from frontend.api.models import FTEQCGroupingResponse, GroupingData, StatisticsData
        
        # 建立測試回應
        stats = StatisticsData(
            total_csv_files=10,
            ft_files_count=5,
            eqc_files_count=5,
            successful_matches=4,
            eqc_rt_count=1,
            matching_rate=0.8,
            processing_timestamp="2024-01-01T00:00:00"
        )
        
        data = GroupingData(
            matched_pairs=[("ft1.csv", "eqc1.csv")],
            unmatched_eqc=["eqc2.csv"],
            statistics=stats
        )
        
        response = FTEQCGroupingResponse(
            status="success",
            message="處理完成",
            data=data
        )
        
        assert response.status == "success"
        assert response.data.statistics.matching_rate == 0.8


if __name__ == "__main__":
    # 執行測試應該會失敗，因為我們還沒實作 API
    pytest.main([__file__, "-v"])