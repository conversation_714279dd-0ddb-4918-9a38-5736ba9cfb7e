"""
統一監控儀表板告警服務單元測試

測試告警服務的核心功能：
- 告警規則評估
- 多管道通知系統
- 告警合併和去重
- 告警歷史記錄
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from backend.monitoring.core.dashboard_alert_service import (
    DashboardAlertService, get_dashboard_alert_service
)
from backend.monitoring.models.dashboard_alert_models import (
    DashboardAlert, AlertLevel, AlertStatus, AlertType, NotificationChannel
)
from backend.monitoring.config.dashboard_monitoring_rules import (
    MonitoringRule, RuleCategory, AlertCondition, ConditionType
)


class TestDashboardAlertService:
    """告警服務測試類"""
    
    @pytest.fixture
    def alert_service(self):
        """創建告警服務實例"""
        with patch('src.dashboard_monitoring.core.dashboard_alert_service.get_dashboard_config'), \
             patch('src.dashboard_monitoring.core.dashboard_alert_service.get_monitoring_rules_manager'):
            service = DashboardAlertService()
            return service
    
    @pytest.fixture
    def sample_metrics_data(self):
        """樣本指標資料"""
        return {
            'email': {
                'pending_count': 15,
                'processing_count': 3,
                'avg_processing_time': 120
            },
            'celery': {
                'total_pending': 25,
                'total_active': 5,
                'failure_rate_percent': 12
            },
            'system': {
                'cpu_percent': 85,
                'memory_percent': 78,
                'disk_percent': 92
            }
        }
    
    @pytest.fixture
    def sample_monitoring_rule(self):
        """樣本監控規則"""
        return MonitoringRule(
            rule_id="test_rule_001",
            rule_name="郵件佇列警告測試",
            category=RuleCategory.EMAIL,
            alert_level=AlertLevel.WARNING,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=10,
                    comparison_field="email.pending_count",
                    description="郵件待處理數量超過閾值"
                )
            ],
            alert_title="郵件佇列警告",
            alert_message="郵件待處理數量過多",
            notification_channels=["system"]
        )
    
    def test_init_alert_service(self, alert_service):
        """測試告警服務初始化"""
        assert alert_service is not None
        assert isinstance(alert_service.active_alerts, dict)
        assert isinstance(alert_service.alert_history, list)
        assert isinstance(alert_service.notification_channels, dict)
        assert len(alert_service.notification_channels) >= 1  # 至少有系統通知
    
    def test_init_notification_channels(self, alert_service):
        """測試通知管道初始化"""
        # 檢查系統通知管道是否存在
        assert 'system' in alert_service.notification_channels
        system_config = alert_service.notification_channels['system']
        assert system_config.channel == NotificationChannel.SYSTEM_NOTIFICATION
        assert system_config.enabled is True
    
    @pytest.mark.asyncio
    async def test_evaluate_alerts_no_rules(self, alert_service, sample_metrics_data):
        """測試無規則時的告警評估"""
        with patch.object(alert_service.rules_manager, 'get_enabled_rules', return_value=[]):
            alerts = await alert_service.evaluate_alerts(sample_metrics_data)
            assert alerts == []
    
    @pytest.mark.asyncio
    async def test_evaluate_alerts_with_triggered_rule(self, alert_service, sample_metrics_data, sample_monitoring_rule):
        """測試觸發規則的告警評估"""
        # 模擬規則管理器返回觸發的規則
        with patch.object(alert_service.rules_manager, 'get_enabled_rules', return_value=[sample_monitoring_rule]), \
             patch.object(alert_service.rules_manager, 'evaluate_all_rules', return_value=[sample_monitoring_rule]):
            
            alerts = await alert_service.evaluate_alerts(sample_metrics_data)
            
            assert len(alerts) == 1
            alert = alerts[0]
            assert alert.level == AlertLevel.WARNING
            assert alert.title == "郵件佇列警告"
            assert alert.source == "email_monitor"
            assert alert.rule_id == "test_rule_001"
            assert alert.current_value == 15  # 來自 sample_metrics_data
    
    @pytest.mark.asyncio
    async def test_create_alert_from_rule(self, alert_service, sample_metrics_data, sample_monitoring_rule):
        """測試從規則創建告警"""
        alert = await alert_service._create_alert_from_rule(sample_monitoring_rule, sample_metrics_data)
        
        assert alert is not None
        assert alert.level == AlertLevel.WARNING
        assert alert.title == "郵件佇列警告"
        assert alert.message == "郵件待處理數量過多"
        assert alert.source == "email_monitor"
        assert alert.rule_id == "test_rule_001"
        assert alert.current_value == 15
        assert alert.threshold_value == 10
        assert alert.status == AlertStatus.ACTIVE
    
    def test_determine_alert_type(self, alert_service):
        """測試告警類型判斷"""
        # 測試佇列溢出
        alert_type = alert_service._determine_alert_type("email", "郵件佇列警告")
        assert alert_type == AlertType.QUEUE_OVERFLOW
        
        # 測試任務失敗
        alert_type = alert_service._determine_alert_type("celery", "任務失敗率警告")
        assert alert_type == AlertType.TASK_FAILURE
        
        # 測試資源過高
        alert_type = alert_service._determine_alert_type("system", "CPU使用率警告")
        assert alert_type == AlertType.RESOURCE_HIGH
        
        # 測試預設類型
        alert_type = alert_service._determine_alert_type("unknown", "未知告警")
        assert alert_type == AlertType.SYSTEM_ERROR
    
    def test_get_nested_value(self, alert_service, sample_metrics_data):
        """測試嵌套值獲取"""
        # 測試正常路徑
        value = alert_service._get_nested_value(sample_metrics_data, "email.pending_count")
        assert value == 15
        
        # 測試深層嵌套
        value = alert_service._get_nested_value(sample_metrics_data, "system.cpu_percent")
        assert value == 85
        
        # 測試不存在的路徑
        value = alert_service._get_nested_value(sample_metrics_data, "nonexistent.field")
        assert value is None
    
    def test_merge_and_deduplicate_alerts(self, alert_service):
        """測試告警合併和去重"""
        # 創建重複告警
        alert1 = DashboardAlert(
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="測試告警",
            message="測試訊息",
            source="test_monitor"
        )
        
        alert2 = DashboardAlert(
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="測試告警",
            message="測試訊息",
            source="test_monitor"
        )
        
        alerts = [alert1, alert2]
        merged_alerts = alert_service._merge_and_deduplicate_alerts(alerts)
        
        # 應該合併為一個告警
        assert len(merged_alerts) <= len(alerts)
    
    def test_find_similar_active_alert(self, alert_service):
        """測試查找相似活躍告警"""
        # 添加一個活躍告警
        existing_alert = DashboardAlert(
            id="existing_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="現有告警",
            message="現有訊息",
            source="test_monitor",
            rule_id="rule_001"
        )
        alert_service.active_alerts[existing_alert.id] = existing_alert
        
        # 創建相似告警
        similar_alert = DashboardAlert(
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="相似告警",
            message="相似訊息",
            source="test_monitor",
            rule_id="rule_001"
        )
        
        found_alert = alert_service._find_similar_active_alert(similar_alert)
        assert found_alert is not None
        assert found_alert.id == existing_alert.id
    
    @pytest.mark.asyncio
    async def test_should_send_notification(self, alert_service):
        """測試通知發送條件檢查"""
        from backend.monitoring.models.dashboard_alert_models import NotificationConfig
        
        # 創建通知配置
        config = NotificationConfig(
            channel=NotificationChannel.SYSTEM_NOTIFICATION,
            enabled=True,
            min_alert_level=AlertLevel.WARNING,
            cooldown_minutes=5
        )
        
        # 創建告警
        alert = DashboardAlert(
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="測試告警",
            message="測試訊息",
            source="test_monitor"
        )
        
        # 測試應該發送
        should_send = await alert_service._should_send_notification(config, alert)
        assert should_send is True
        
        # 測試等級不足
        alert.level = AlertLevel.INFO
        should_send = await alert_service._should_send_notification(config, alert)
        assert should_send is False
        
        # 測試管道停用
        config.enabled = False
        alert.level = AlertLevel.WARNING
        should_send = await alert_service._should_send_notification(config, alert)
        assert should_send is False
    
    @pytest.mark.asyncio
    async def test_send_system_notification(self, alert_service):
        """測試系統通知發送"""
        alert = DashboardAlert(
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="測試告警",
            message="測試訊息",
            source="test_monitor"
        )
        
        with patch.object(alert_service.logger, 'log') as mock_log:
            success = await alert_service._send_system_notification(alert)
            assert success is True
            mock_log.assert_called_once()
    
    def test_create_email_body(self, alert_service):
        """測試郵件正文創建"""
        alert = DashboardAlert(
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="測試告警",
            message="測試訊息",
            source="test_monitor",
            current_value=15,
            threshold_value=10,
            occurrence_count=2
        )
        
        body = alert_service._create_email_body(alert)
        
        assert "測試告警" in body
        assert "測試訊息" in body
        assert "test_monitor" in body
        assert "15" in body  # current_value
        assert "10" in body  # threshold_value
        assert "2" in body   # occurrence_count
        assert "html" in body.lower()
    
    def test_create_line_message(self, alert_service):
        """測試 LINE 訊息創建"""
        alert = DashboardAlert(
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.CRITICAL,
            title="緊急告警",
            message="系統異常",
            source="system_monitor",
            current_value=95,
            threshold_value=90
        )
        
        message = alert_service._create_line_message(alert)
        
        assert "🚨" in message  # CRITICAL 等級的表情符號
        assert "緊急告警" in message
        assert "系統異常" in message
        assert "system_monitor" in message
        assert "95" in message
        assert "90" in message
    
    @pytest.mark.asyncio
    async def test_acknowledge_alert(self, alert_service):
        """測試告警確認"""
        # 創建並添加告警
        alert = DashboardAlert(
            id="test_alert_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="測試告警",
            message="測試訊息",
            source="test_monitor"
        )
        alert_service.active_alerts[alert.id] = alert
        
        # 確認告警
        success = await alert_service.acknowledge_alert(alert.id, "test_user")
        
        assert success is True
        assert alert.status == AlertStatus.ACKNOWLEDGED
        assert alert.acknowledged_at is not None
        assert alert.metadata.get("acknowledged_by") == "test_user"
    
    @pytest.mark.asyncio
    async def test_resolve_alert(self, alert_service):
        """測試告警解決"""
        # 創建並添加告警
        alert = DashboardAlert(
            id="test_alert_002",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="測試告警",
            message="測試訊息",
            source="test_monitor"
        )
        alert_service.active_alerts[alert.id] = alert
        
        # 解決告警
        success = await alert_service.resolve_alert(alert.id, "test_user")
        
        assert success is True
        assert alert.status == AlertStatus.RESOLVED
        assert alert.resolved_at is not None
        assert alert.metadata.get("resolved_by") == "test_user"
    
    def test_get_active_alerts(self, alert_service):
        """測試獲取活躍告警"""
        # 添加不同狀態的告警
        active_alert = DashboardAlert(
            id="active_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="活躍告警",
            message="測試訊息",
            source="test_monitor",
            status=AlertStatus.ACTIVE
        )
        
        resolved_alert = DashboardAlert(
            id="resolved_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="已解決告警",
            message="測試訊息",
            source="test_monitor",
            status=AlertStatus.RESOLVED
        )
        
        alert_service.active_alerts[active_alert.id] = active_alert
        alert_service.active_alerts[resolved_alert.id] = resolved_alert
        
        active_alerts = alert_service.get_active_alerts()
        
        assert len(active_alerts) == 1
        assert active_alerts[0].id == active_alert.id
    
    def test_get_alert_summary(self, alert_service):
        """測試獲取告警摘要"""
        # 添加不同等級的告警
        critical_alert = DashboardAlert(
            id="critical_001",
            alert_type=AlertType.RESOURCE_HIGH,
            level=AlertLevel.CRITICAL,
            title="緊急告警",
            message="系統資源不足",
            source="system_monitor",
            status=AlertStatus.ACTIVE
        )
        
        warning_alert = DashboardAlert(
            id="warning_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="警告告警",
            message="佇列過多",
            source="email_monitor",
            status=AlertStatus.ACTIVE
        )
        
        alert_service.active_alerts[critical_alert.id] = critical_alert
        alert_service.active_alerts[warning_alert.id] = warning_alert
        alert_service.alert_history.extend([critical_alert, warning_alert])
        
        summary = alert_service.get_alert_summary()
        
        assert summary.total_active == 2
        assert summary.critical_count == 1
        assert summary.warning_count == 1
        assert summary.info_count == 0
        assert summary.error_count == 0
        assert "system_monitor" in summary.top_alert_sources
        assert "email_monitor" in summary.top_alert_sources
    
    def test_get_alert_history(self, alert_service):
        """測試獲取告警歷史"""
        # 添加不同時間的告警
        recent_alert = DashboardAlert(
            id="recent_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="最近告警",
            message="測試訊息",
            source="test_monitor",
            triggered_at=datetime.now() - timedelta(hours=1)
        )
        
        old_alert = DashboardAlert(
            id="old_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="舊告警",
            message="測試訊息",
            source="test_monitor",
            triggered_at=datetime.now() - timedelta(days=2)
        )
        
        alert_service.alert_history.extend([recent_alert, old_alert])
        
        # 獲取最近24小時的告警
        history = alert_service.get_alert_history(hours=24)
        
        assert len(history) == 1
        assert history[0].id == recent_alert.id
    
    def test_cleanup_old_alerts(self, alert_service):
        """測試清理舊告警"""
        # 添加舊告警到歷史
        old_alert = DashboardAlert(
            id="old_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="舊告警",
            message="測試訊息",
            source="test_monitor",
            triggered_at=datetime.now() - timedelta(days=10)
        )
        
        recent_alert = DashboardAlert(
            id="recent_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="最近告警",
            message="測試訊息",
            source="test_monitor",
            triggered_at=datetime.now() - timedelta(hours=1)
        )
        
        alert_service.alert_history.extend([old_alert, recent_alert])
        
        # 清理7天前的告警
        cleaned_count = alert_service.cleanup_old_alerts(days=7)
        
        assert cleaned_count >= 1
        assert len(alert_service.alert_history) == 1
        assert alert_service.alert_history[0].id == recent_alert.id
    
    def test_get_service_statistics(self, alert_service):
        """測試獲取服務統計"""
        # 添加一些告警
        alert = DashboardAlert(
            id="stats_001",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="統計測試告警",
            message="測試訊息",
            source="test_monitor"
        )
        
        alert_service.active_alerts[alert.id] = alert
        alert_service.alert_history.append(alert)
        
        stats = alert_service.get_service_statistics()
        
        assert 'active_alerts_count' in stats
        assert 'total_alerts_in_history' in stats
        assert 'notification_channels' in stats
        assert 'alert_stats' in stats
        assert 'enabled_channels' in stats
        
        assert stats['active_alerts_count'] >= 1
        assert stats['total_alerts_in_history'] >= 1


def test_get_dashboard_alert_service_singleton():
    """測試告警服務單例模式"""
    with patch('src.dashboard_monitoring.core.dashboard_alert_service.get_dashboard_config'), \
         patch('src.dashboard_monitoring.core.dashboard_alert_service.get_monitoring_rules_manager'):
        
        service1 = get_dashboard_alert_service()
        service2 = get_dashboard_alert_service()
        
        assert service1 is service2  # 應該是同一個實例


if __name__ == "__main__":
    pytest.main([__file__])