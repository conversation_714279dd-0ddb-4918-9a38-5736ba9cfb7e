"""
統一監控儀表板快取服務單元測試
"""

import asyncio
import pytest
import time
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from backend.monitoring.core.dashboard_cache_service import (
    DashboardCacheService, CacheEntry, CacheStatistics, get_cache_service
)
from backend.monitoring.config.dashboard_cache_config import CacheConfig


class TestCacheEntry:
    """快取項目測試"""
    
    def test_cache_entry_creation(self):
        """測試快取項目創建"""
        entry = CacheEntry(
            key="test_key",
            value="test_value",
            created_at=time.time(),
            expires_at=time.time() + 300,
            size_bytes=100
        )
        
        assert entry.key == "test_key"
        assert entry.value == "test_value"
        assert entry.access_count == 0
        assert entry.size_bytes == 100
    
    def test_cache_entry_expiration(self):
        """測試快取項目過期檢查"""
        # 未過期項目
        entry = CacheEntry(
            key="test_key",
            value="test_value",
            created_at=time.time(),
            expires_at=time.time() + 300,
            size_bytes=100
        )
        assert not entry.is_expired()
        
        # 已過期項目
        expired_entry = CacheEntry(
            key="expired_key",
            value="expired_value",
            created_at=time.time() - 600,
            expires_at=time.time() - 300,
            size_bytes=100
        )
        assert expired_entry.is_expired()
        
        # 無過期時間項目
        no_expiry_entry = CacheEntry(
            key="no_expiry_key",
            value="no_expiry_value",
            created_at=time.time(),
            expires_at=None,
            size_bytes=100
        )
        assert not no_expiry_entry.is_expired()
    
    def test_cache_entry_touch(self):
        """測試快取項目存取更新"""
        entry = CacheEntry(
            key="test_key",
            value="test_value",
            created_at=time.time(),
            expires_at=time.time() + 300,
            size_bytes=100
        )
        
        initial_access_count = entry.access_count
        initial_last_accessed = entry.last_accessed
        
        time.sleep(0.01)  # 確保時間差異
        entry.touch()
        
        assert entry.access_count == initial_access_count + 1
        assert entry.last_accessed > initial_last_accessed


class TestCacheStatistics:
    """快取統計測試"""
    
    def test_statistics_creation(self):
        """測試統計資料創建"""
        stats = CacheStatistics()
        
        assert stats.total_entries == 0
        assert stats.hit_count == 0
        assert stats.miss_count == 0
        assert stats.hit_rate == 0.0
    
    def test_hit_rate_calculation(self):
        """測試命中率計算"""
        stats = CacheStatistics()
        stats.hit_count = 80
        stats.miss_count = 20
        
        stats.calculate_hit_rate()
        assert stats.hit_rate == 0.8
        
        # 測試零除法
        zero_stats = CacheStatistics()
        zero_stats.calculate_hit_rate()
        assert zero_stats.hit_rate == 0.0


class TestDashboardCacheService:
    """快取服務測試"""
    
    @pytest.fixture
    def cache_service(self):
        """快取服務測試夾具"""
        return DashboardCacheService(
            max_size=100,
            default_ttl=300,
            cleanup_interval=60,
            max_memory_mb=10
        )
    
    def test_cache_service_initialization(self, cache_service):
        """測試快取服務初始化"""
        assert cache_service.max_size == 100
        assert cache_service.default_ttl == 300
        assert cache_service.cleanup_interval == 60
        assert cache_service.max_memory_bytes == 10 * 1024 * 1024
        assert not cache_service._running
    
    @pytest.mark.asyncio
    async def test_cache_service_lifecycle(self, cache_service):
        """測試快取服務生命週期"""
        # 啟動服務
        await cache_service.start()
        assert cache_service._running
        assert cache_service._cleanup_task is not None
        
        # 停止服務
        await cache_service.stop()
        assert not cache_service._running
        assert len(cache_service._cache) == 0
    
    def test_cache_set_and_get(self, cache_service):
        """測試快取設定和獲取"""
        # 設定快取
        success = cache_service.set("test_key", "test_value", 300)
        assert success
        
        # 獲取快取
        value = cache_service.get("test_key")
        assert value == "test_value"
        
        # 獲取不存在的鍵
        missing_value = cache_service.get("missing_key", "default")
        assert missing_value == "default"
    
    def test_cache_expiration(self, cache_service):
        """測試快取過期"""
        # 設定短期快取
        cache_service.set("short_key", "short_value", 0.1)  # 0.1秒
        
        # 立即獲取應該成功
        value = cache_service.get("short_key")
        assert value == "short_value"
        
        # 等待過期
        time.sleep(0.2)
        
        # 獲取過期項目應該返回預設值
        expired_value = cache_service.get("short_key", "expired")
        assert expired_value == "expired"
    
    def test_cache_delete(self, cache_service):
        """測試快取刪除"""
        # 設定快取
        cache_service.set("delete_key", "delete_value")
        assert cache_service.exists("delete_key")
        
        # 刪除快取
        success = cache_service.delete("delete_key")
        assert success
        assert not cache_service.exists("delete_key")
        
        # 刪除不存在的鍵
        not_found = cache_service.delete("not_found_key")
        assert not not_found
    
    def test_cache_clear(self, cache_service):
        """測試快取清空"""
        # 設定多個快取項目
        cache_service.set("key1", "value1")
        cache_service.set("key2", "value2")
        cache_service.set("key3", "value3")
        
        assert len(cache_service._cache) == 3
        
        # 清空快取
        cache_service.clear()
        assert len(cache_service._cache) == 0
    
    def test_cache_exists(self, cache_service):
        """測試快取存在檢查"""
        # 不存在的鍵
        assert not cache_service.exists("not_exist")
        
        # 設定快取
        cache_service.set("exist_key", "exist_value")
        assert cache_service.exists("exist_key")
        
        # 過期的鍵
        cache_service.set("expire_key", "expire_value", 0.1)
        time.sleep(0.2)
        assert not cache_service.exists("expire_key")
    
    def test_cache_get_keys(self, cache_service):
        """測試獲取快取鍵"""
        # 設定多個快取項目
        cache_service.set("user:1", "user1")
        cache_service.set("user:2", "user2")
        cache_service.set("admin:1", "admin1")
        
        # 獲取所有鍵
        all_keys = cache_service.get_keys()
        assert len(all_keys) == 3
        assert "user:1" in all_keys
        assert "user:2" in all_keys
        assert "admin:1" in all_keys
        
        # 使用模式過濾
        user_keys = cache_service.get_keys("user:*")
        assert len(user_keys) == 2
        assert "user:1" in user_keys
        assert "user:2" in user_keys
        assert "admin:1" not in user_keys
    
    def test_cache_statistics(self, cache_service):
        """測試快取統計"""
        # 初始統計
        stats = cache_service.get_statistics()
        assert stats.total_entries == 0
        assert stats.hit_count == 0
        assert stats.miss_count == 0
        
        # 設定快取並測試命中
        cache_service.set("stats_key", "stats_value")
        value = cache_service.get("stats_key")
        assert value == "stats_value"
        
        # 測試未命中
        missing = cache_service.get("missing_stats_key")
        assert missing is None
        
        # 檢查統計更新
        updated_stats = cache_service.get_statistics()
        assert updated_stats.total_entries == 1
        assert updated_stats.hit_count == 1
        assert updated_stats.miss_count == 1
    
    def test_cache_info(self, cache_service):
        """測試快取詳細資訊"""
        # 設定一些快取項目
        cache_service.set("info_key1", "value1")
        cache_service.set("info_key2", "value2")
        
        # 獲取快取資訊
        info = cache_service.get_cache_info()
        
        assert 'statistics' in info
        assert 'configuration' in info
        assert 'recent_entries' in info
        assert 'largest_entries' in info
        assert 'memory_pressure' in info
        assert 'running' in info
        
        assert info['statistics']['total_entries'] == 2
        assert info['configuration']['max_size'] == 100
    
    def test_lru_eviction(self, cache_service):
        """測試 LRU 淘汰機制"""
        # 設定小容量快取服務
        small_cache = DashboardCacheService(max_size=3)
        
        # 填滿快取
        small_cache.set("key1", "value1")
        small_cache.set("key2", "value2")
        small_cache.set("key3", "value3")
        
        assert len(small_cache._cache) == 3
        
        # 存取 key1 使其成為最近使用
        small_cache.get("key1")
        
        # 添加新項目，應該淘汰 key2（最少使用）
        small_cache.set("key4", "value4")
        
        assert len(small_cache._cache) == 3
        assert small_cache.exists("key1")  # 最近使用，應該保留
        assert not small_cache.exists("key2")  # 應該被淘汰
        assert small_cache.exists("key3")
        assert small_cache.exists("key4")
    
    @pytest.mark.asyncio
    async def test_cleanup_expired(self, cache_service):
        """測試過期項目清理"""
        # 設定短期快取項目
        cache_service.set("expire1", "value1", 0.1)
        cache_service.set("expire2", "value2", 0.1)
        cache_service.set("keep", "keep_value", 300)
        
        assert len(cache_service._cache) == 3
        
        # 等待過期
        time.sleep(0.2)
        
        # 手動觸發清理
        cache_service._cleanup_expired()
        
        # 檢查清理結果
        assert len(cache_service._cache) == 1
        assert cache_service.exists("keep")
        assert not cache_service.exists("expire1")
        assert not cache_service.exists("expire2")
    
    def test_memory_pressure_handling(self, cache_service):
        """測試記憶體壓力處理"""
        # 模擬大型物件
        large_data = "x" * 1000000  # 1MB 資料
        
        # 設定多個大型快取項目
        for i in range(5):
            cache_service.set(f"large_key_{i}", large_data)
        
        # 檢查記憶體壓力
        assert cache_service._memory_pressure
        
        # 記憶體壓力應該觸發清理
        cache_service._handle_memory_pressure()
        
        # 快取項目應該被減少
        assert len(cache_service._cache) < 5
    
    @pytest.mark.asyncio
    async def test_cache_with_callback(self, cache_service):
        """測試使用回調的快取操作"""
        # 模擬資料獲取函數
        call_count = 0
        
        async def get_data():
            nonlocal call_count
            call_count += 1
            return f"data_{call_count}"
        
        # 首次調用應該執行回調
        result1 = await cache_service.cache_with_callback("callback_key", get_data, 300)
        assert result1 == "data_1"
        assert call_count == 1
        
        # 第二次調用應該使用快取
        result2 = await cache_service.cache_with_callback("callback_key", get_data, 300)
        assert result2 == "data_1"  # 應該是快取的值
        assert call_count == 1  # 回調不應該再次執行
        
        # 強制重新整理應該執行回調
        result3 = await cache_service.cache_with_callback("callback_key", get_data, 300, force_refresh=True)
        assert result3 == "data_2"
        assert call_count == 2


class TestCacheServiceSingleton:
    """快取服務單例測試"""
    
    def test_singleton_instance(self):
        """測試單例模式"""
        # 獲取兩個實例
        service1 = get_cache_service()
        service2 = get_cache_service()
        
        # 應該是同一個實例
        assert service1 is service2
    
    def test_singleton_with_different_params(self):
        """測試不同參數的單例"""
        # 第一次獲取
        service1 = get_cache_service(max_size=100)
        
        # 第二次獲取（不同參數）
        service2 = get_cache_service(max_size=200)
        
        # 應該是同一個實例，參數不會改變
        assert service1 is service2
        assert service1.max_size == 100  # 保持第一次的參數


@pytest.mark.asyncio
async def test_cache_service_integration():
    """快取服務整合測試"""
    # 初始化快取服務
    from backend.monitoring.core.dashboard_cache_service import initialize_cache_service
    
    cache_service = await initialize_cache_service()
    
    try:
        # 測試基本操作
        cache_service.set("integration_key", "integration_value")
        value = cache_service.get("integration_key")
        assert value == "integration_value"
        
        # 測試統計
        stats = cache_service.get_statistics()
        assert stats.total_entries >= 1
        
    finally:
        # 清理
        from backend.monitoring.core.dashboard_cache_service import shutdown_cache_service
        await shutdown_cache_service()


if __name__ == "__main__":
    pytest.main([__file__])