"""企業級網路檔案上傳服務"""

import os
import sys
import shutil
import hashlib
import time
import threading
import subprocess
import socket
import platform
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
import uuid

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from backend.shared.infrastructure.logging.logger_manager import LoggerManager


class UploadStatus(Enum):
    PENDING = "pending"
    INITIALIZING = "initializing"
    NETWORK_CHECK = "network_check"
    DIRECTORY_PREP = "directory_prep"
    UPLOADING = "uploading"
    VERIFYING = "verifying"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NetworkConnectivityError(Exception):
    pass

class FileIntegrityError(Exception):
    pass

class InsufficientSpaceError(Exception):
    pass


@dataclass
class UploadProgress:
    upload_id: str
    status: UploadStatus
    current_file: Optional[str] = None
    files_completed: int = 0
    files_total: int = 0
    bytes_transferred: int = 0
    bytes_total: int = 0
    transfer_rate_mbps: float = 0.0
    estimated_remaining_seconds: int = 0
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    @property
    def progress_percentage(self) -> float:
        return min(100.0, (self.files_completed / self.files_total) * 100.0) if self.files_total > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['status'] = self.status.value
        data['progress_percentage'] = self.progress_percentage
        return data


@dataclass
class UploadResult:
    success: bool
    upload_id: str
    files_uploaded: int
    total_size_bytes: int
    upload_duration_seconds: float
    target_path: str
    verification_passed: bool
    error_message: Optional[str] = None
    retry_count: int = 0
    network_path_accessible: bool = True
    md5_verification_results: Dict[str, bool] = None
    
    @property
    def total_size_mb(self) -> float:
        return self.total_size_bytes / 1024 / 1024
    
    @property
    def average_speed_mbps(self) -> float:
        return self.total_size_mb / self.upload_duration_seconds if self.upload_duration_seconds > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['total_size_mb'] = self.total_size_mb
        data['average_speed_mbps'] = self.average_speed_mbps
        return data


class NetworkFileUploadService:
    
    def __init__(self, network_base_path: Optional[str] = None, max_concurrent_uploads: int = 3,
                 chunk_size_mb: int = 10, max_retries: int = 3, retry_delay_seconds: int = 5,
                 connection_timeout_seconds: int = 30, enable_md5_verification: bool = True,
                 enable_progress_callback: bool = True, logger: Optional = None):
        self.logger = logger or LoggerManager().get_logger("NetworkFileUploadService")
        
        self.network_base_path = network_base_path or os.getenv('HYPERLINK_NETWORK_PATH', r'\\192.168.1.60\temp_7days')
        self.upload_enabled = os.getenv('NETWORK_UPLOAD_ENABLED', 'true').lower() == 'true'
        self.max_concurrent_uploads = max_concurrent_uploads
        self.chunk_size_bytes = chunk_size_mb * 1024 * 1024
        self.max_retries = max_retries
        self.retry_delay_seconds = retry_delay_seconds
        self.connection_timeout_seconds = connection_timeout_seconds
        self.enable_md5_verification = enable_md5_verification
        self.enable_progress_callback = enable_progress_callback
        self._upload_progress: Dict[str, UploadProgress] = {}
        self._progress_lock = threading.RLock()
        self._progress_callbacks: Dict[str, List[Callable]] = {}
        self._executor = ThreadPoolExecutor(max_workers=max_concurrent_uploads, thread_name_prefix="NetworkUpload")
        self._cancelled_uploads: set = set()
        
        self.logger.info(f"[INIT] 網路上傳服務已初始化 - 目標路徑: {self.network_base_path}")
    
    def upload_directory(self, source_path: Union[str, Path], pd: str, mo: str,
                        progress_callback: Optional[Callable[[UploadProgress], None]] = None) -> UploadResult:
        upload_id = str(uuid.uuid4())
        source_path = Path(source_path)
        target_path = Path(self.network_base_path) / pd / mo.upper()
        
        # 初始化進度追蹤
        progress = UploadProgress(
            upload_id=upload_id,
            status=UploadStatus.PENDING,
            started_at=datetime.now()
        )
        
        with self._progress_lock:
            self._upload_progress[upload_id] = progress
            if progress_callback:
                self._progress_callbacks[upload_id] = [progress_callback]
        
        try:
            if not self.upload_enabled:
                return UploadResult(success=True, upload_id=upload_id, files_uploaded=0, total_size_bytes=0,
                                  upload_duration_seconds=0, target_path=str(target_path), verification_passed=True,
                                  error_message="網路上傳已停用")
            return self._perform_upload_with_retry(upload_id, source_path, target_path)
        except Exception as e:
            self.logger.error(f"[ERROR] 上傳異常: {upload_id} - {e}")
            self._update_progress(upload_id, status=UploadStatus.FAILED, error_message=str(e))
            return UploadResult(success=False, upload_id=upload_id, files_uploaded=0, total_size_bytes=0,
                              upload_duration_seconds=0, target_path=str(target_path), verification_passed=False,
                              error_message=str(e))
        finally:
            self._cleanup_progress(upload_id)
    
    def _perform_upload_with_retry(self, upload_id: str, source_path: Path, target_path: Path) -> UploadResult:
        """執行帶重試的上傳"""
        last_error = None
        
        for retry_count in range(self.max_retries + 1):
            try:
                if upload_id in self._cancelled_uploads:
                    self._update_progress(upload_id, status=UploadStatus.CANCELLED)
                    return UploadResult(
                        success=False,
                        upload_id=upload_id,
                        files_uploaded=0,
                        total_size_bytes=0,
                        upload_duration_seconds=0,
                        target_path=str(target_path),
                        verification_passed=False,
                        error_message="上傳已取消",
                        retry_count=retry_count
                    )
                
                # 執行實際上傳
                result = self._perform_upload(upload_id, source_path, target_path)
                result.retry_count = retry_count
                return result
                
            except (NetworkConnectivityError, InsufficientSpaceError) as e:
                # 這些錯誤不重試
                raise e
            except Exception as e:
                last_error = e
                self.logger.warning(f"[RETRY] 上傳失敗，準備重試: {upload_id} - 第{retry_count + 1}次 - {e}")
                
                if retry_count < self.max_retries:
                    # 指數退避延遲
                    delay = self.retry_delay_seconds * (2 ** retry_count)
                    time.sleep(delay)
                else:
                    # 最後一次重試失敗
                    raise last_error
    
    def _perform_upload(self, upload_id: str, source_path: Path, target_path: Path) -> UploadResult:
        """執行實際上傳"""
        start_time = datetime.now()
        
        # 步驟1: 初始化檢查
        self._update_progress(upload_id, status=UploadStatus.INITIALIZING)
        self._validate_source_path(source_path)
        
        # 步驟2: 網路連通性檢查
        self._update_progress(upload_id, status=UploadStatus.NETWORK_CHECK)
        self._check_network_connectivity(target_path)
        
        # 步驟3: 目錄準備
        self._update_progress(upload_id, status=UploadStatus.DIRECTORY_PREP)
        self._prepare_target_directory(target_path)
        
        # 步驟4: 分析源檔案
        file_list = self._analyze_source_files(source_path)
        total_size = sum(f['size'] for f in file_list)
        
        self._update_progress(
            upload_id,
            files_total=len(file_list),
            bytes_total=total_size
        )
        
        # 步驟5: 執行上傳
        self._update_progress(upload_id, status=UploadStatus.UPLOADING)
        uploaded_files = self._upload_files(upload_id, source_path, target_path, file_list)
        
        # 步驟6: 檔案完整性驗證
        if self.enable_md5_verification and uploaded_files > 0:
            self._update_progress(upload_id, status=UploadStatus.VERIFYING)
            verification_results = self._verify_file_integrity(source_path, target_path, file_list)
            verification_passed = all(verification_results.values())
        else:
            verification_results = {}
            verification_passed = True
        
        # 完成
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self._update_progress(
            upload_id,
            status=UploadStatus.COMPLETED,
            completed_at=end_time
        )
        
        self.logger.info(f"[OK] 上傳完成: {upload_id} - {uploaded_files}個檔案, {total_size / 1024 / 1024:.2f}MB")
        
        return UploadResult(
            success=True,
            upload_id=upload_id,
            files_uploaded=uploaded_files,
            total_size_bytes=total_size,
            upload_duration_seconds=duration,
            target_path=str(target_path),
            verification_passed=verification_passed,
            md5_verification_results=verification_results,
            network_path_accessible=True
        )
    
    def _validate_source_path(self, source_path: Path):
        """驗證源路徑"""
        if not source_path.exists():
            raise FileNotFoundError(f"源路徑不存在: {source_path}")
        
        if not source_path.is_dir():
            raise ValueError(f"源路徑不是目錄: {source_path}")
        
        # 檢查是否有檔案可上傳
        files = list(source_path.rglob('*'))
        if not any(f.is_file() for f in files):
            raise ValueError(f"源目錄中沒有檔案: {source_path}")
    
    def _check_network_connectivity(self, target_path: Path):
        """檢查網路連通性"""
        try:
            # 嘗試解析 UNC 路徑的主機名
            if str(target_path).startswith('\\\\'):
                parts = str(target_path).split('\\')
                if len(parts) >= 3:
                    hostname = parts[2]
                    self._ping_host(hostname)
            
            # 嘗試訪問父目錄
            parent_path = target_path.parent
            if not parent_path.exists():
                parent_path.mkdir(parents=True, exist_ok=True)
            
            # 測試寫入權限
            test_file = target_path / f"_upload_test_{uuid.uuid4().hex[:8]}.tmp"
            test_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(test_file, 'w') as f:
                f.write("upload test")
            
            if test_file.exists():
                test_file.unlink()
                self.logger.info(f"[OK] 網路路徑可寫入: {target_path}")
            else:
                raise NetworkConnectivityError(f"無法寫入測試檔案: {target_path}")
                
        except Exception as e:
            raise NetworkConnectivityError(f"網路連通性檢查失敗: {e}")
    
    def _ping_host(self, hostname: str):
        """Ping 主機檢查連通性"""
        try:
            cmd = ["ping", "-n", "1", "-w", "3000", hostname] if platform.system().lower() == "windows" else ["ping", "-c", "1", "-W", "3", hostname]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.connection_timeout_seconds)
            if result.returncode != 0:
                raise NetworkConnectivityError(f"無法ping通主機: {hostname}")
            self.logger.debug(f"[OK] 主機連通性正常: {hostname}")
        except subprocess.TimeoutExpired:
            raise NetworkConnectivityError(f"Ping主機超時: {hostname}")
        except Exception as e:
            raise NetworkConnectivityError(f"Ping主機失敗: {hostname} - {e}")
    
    def _prepare_target_directory(self, target_path: Path):
        """準備目標目錄"""
        try:
            target_path.mkdir(parents=True, exist_ok=True)
            # 檢查磁碟空間
            try:
                if hasattr(shutil, 'disk_usage'):
                    total, used, free = shutil.disk_usage(target_path)
                    free_gb = free / (1024 ** 3)
                    if free_gb < 1:
                        self.logger.warning(f"[WARNING] 目標磁碟可用空間不足: {free_gb:.2f}GB")
                        if free_gb < 0.1:
                            raise InsufficientSpaceError(f"磁碟空間不足: {free_gb:.2f}GB")
            except Exception as e:
                self.logger.debug(f"[DEBUG] 無法檢查磁碟空間: {e}")
            self.logger.info(f"[OK] 目標目錄已準備: {target_path}")
        except Exception as e:
            raise RuntimeError(f"準備目標目錄失敗: {e}")
    
    def _analyze_source_files(self, source_path: Path) -> List[Dict[str, Any]]:
        """分析源檔案"""
        file_list = []
        for file_path in source_path.rglob('*'):
            if file_path.is_file():
                file_list.append({
                    'source_path': file_path,
                    'relative_path': file_path.relative_to(source_path),
                    'size': file_path.stat().st_size,
                    'md5': None
                })
        self.logger.info(f"[ANALYZE] 發現 {len(file_list)} 個檔案需要上傳")
        return file_list
    
    def _upload_files(self, upload_id: str, source_path: Path, target_path: Path, file_list: List[Dict]) -> int:
        """上傳檔案"""
        uploaded_count = 0
        start_time = time.time()
        
        for i, file_info in enumerate(file_list):
            if upload_id in self._cancelled_uploads:
                break
            
            source_file = file_info['source_path']
            relative_path = file_info['relative_path']
            target_file = target_path / relative_path
            
            try:
                self._update_progress(upload_id, current_file=str(relative_path), files_completed=i)
                target_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source_file, target_file)
                uploaded_count += 1
                
                bytes_transferred = sum(f['size'] for f in file_list[:i + 1])
                elapsed_time = time.time() - start_time
                avg_rate = (bytes_transferred / 1024 / 1024) / elapsed_time if elapsed_time > 0 else 0
                
                self._update_progress(upload_id, files_completed=i + 1, bytes_transferred=bytes_transferred,
                                    transfer_rate_mbps=avg_rate)
                
            except Exception as e:
                self.logger.error(f"[ERROR] 上傳檔案失敗: {relative_path} - {e}")
                continue
        
        return uploaded_count
    
    def _verify_file_integrity(self, source_path: Path, target_path: Path, file_list: List[Dict]) -> Dict[str, bool]:
        """驗證檔案完整性"""
        verification_results = {}
        
        for file_info in file_list:
            source_file = file_info['source_path']
            relative_path = file_info['relative_path']
            target_file = target_path / relative_path
            
            try:
                if not target_file.exists():
                    verification_results[str(relative_path)] = False
                    continue
                
                source_size = source_file.stat().st_size
                target_size = target_file.stat().st_size
                
                if source_size != target_size:
                    verification_results[str(relative_path)] = False
                    continue
                
                source_md5 = self._calculate_md5(source_file)
                target_md5 = self._calculate_md5(target_file)
                verification_results[str(relative_path)] = source_md5 == target_md5
                
            except Exception as e:
                verification_results[str(relative_path)] = False
                self.logger.error(f"[VERIFY] 檔案驗證異常: {relative_path} - {e}")
        
        passed_count = sum(verification_results.values())
        self.logger.info(f"[VERIFY] 檔案完整性驗證完成: {passed_count}/{len(verification_results)} 通過")
        return verification_results
    
    def _calculate_md5(self, file_path: Path) -> str:
        """計算檔案MD5"""
        hash_md5 = hashlib.md5()
        
        with open(file_path, "rb") as f:
            while chunk := f.read(self.chunk_size_bytes):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
    
    def _update_progress(self, upload_id: str, **kwargs):
        """更新上傳進度"""
        with self._progress_lock:
            if upload_id not in self._upload_progress:
                return
            
            progress = self._upload_progress[upload_id]
            
            # 更新進度資訊
            for key, value in kwargs.items():
                if hasattr(progress, key):
                    setattr(progress, key, value)
            
            # 調用進度回調
            if self.enable_progress_callback and upload_id in self._progress_callbacks:
                for callback in self._progress_callbacks[upload_id]:
                    try:
                        callback(progress)
                    except Exception as e:
                        self.logger.error(f"[ERROR] 進度回調異常: {e}")
    
    def _cleanup_progress(self, upload_id: str):
        """清理進度記錄"""
        with self._progress_lock:
            self._upload_progress.pop(upload_id, None)
            self._progress_callbacks.pop(upload_id, None)
            self._cancelled_uploads.discard(upload_id)
    
    def verify_upload(self, source_path: Union[str, Path], target_path: Union[str, Path]) -> bool:
        """驗證上傳結果"""
        try:
            source_path = Path(source_path)
            target_path = Path(target_path)
            if not source_path.exists() or not target_path.exists():
                return False
            source_files = self._analyze_source_files(source_path)
            verification_results = self._verify_file_integrity(source_path, target_path, source_files)
            return all(verification_results.values())
        except Exception as e:
            self.logger.error(f"[ERROR] 驗證上傳失敗: {e}")
            return False
    
    def get_upload_status(self, upload_id: str) -> Optional[UploadProgress]:
        """獲取上傳狀態"""
        with self._progress_lock:
            return self._upload_progress.get(upload_id)
    
    def shutdown(self, wait: bool = True):
        """關閉上傳服務"""
        self.logger.info("[SHUTDOWN] 正在關閉網路上傳服務...")
        self._executor.shutdown(wait=wait)
        self.logger.info("[OK] 網路上傳服務已關閉")


_upload_service_instance = None
_upload_service_lock = threading.Lock()

def get_network_upload_service(**kwargs) -> NetworkFileUploadService:
    global _upload_service_instance
    
    if _upload_service_instance is None:
        with _upload_service_lock:
            if _upload_service_instance is None:
                _upload_service_instance = NetworkFileUploadService(**kwargs)
    
    return _upload_service_instance