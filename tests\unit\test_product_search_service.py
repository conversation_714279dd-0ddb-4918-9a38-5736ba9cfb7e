"""產品搜尋服務單元測試"""

import pytest
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from backend.shared.infrastructure.adapters.product_search_service import ProductSearchService
from backend.shared.domain.entities.file_search import SearchFilters, TimeRange, SearchStatus
from backend.shared.models.search_models import TimeRangeType


class TestProductSearchService:
    """產品搜尋服務測試類"""
    
    @pytest.fixture
    def search_service(self):
        """建立搜尋服務實例"""
        return ProductSearchService(max_workers=2, search_timeout=60)
    
    @pytest.fixture
    def sample_time_range(self):
        """建立範例時間範圍"""
        return TimeRange.last_months(6)
    
    @pytest.fixture
    def sample_filters(self, sample_time_range):
        """建立範例搜尋篩選條件"""
        return SearchFilters(
            time_range=sample_time_range,
            file_types=['.csv', '.xlsx'],
            include_directories=True
        )
    
    def test_create_time_range_last_months(self, search_service):
        """測試建立最近N個月的時間範圍"""
        time_range = search_service.create_time_range(TimeRangeType.LAST_6_MONTHS)
        
        assert isinstance(time_range, TimeRange)
        assert time_range.end_date > time_range.start_date
        
        # 檢查時間差大約是 6 個月
        diff_days = (time_range.end_date - time_range.start_date).days
        assert 150 <= diff_days <= 200  # 大約 6 個月
    
    def test_create_time_range_current_quarter(self, search_service):
        """測試建立當前季度的時間範圍"""
        time_range = search_service.create_time_range(TimeRangeType.CURRENT_QUARTER)
        
        assert isinstance(time_range, TimeRange)
        assert time_range.end_date > time_range.start_date
        
        # 檢查是否在當前年份
        now = datetime.now()
        assert time_range.start_date.year == now.year
        assert time_range.end_date.year in [now.year, now.year + 1]
    
    def test_create_time_range_custom(self, search_service):
        """測試建立自訂時間範圍"""
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
        
        time_range = search_service.create_time_range(
            TimeRangeType.CUSTOM,
            custom_start=start_date,
            custom_end=end_date
        )
        
        assert time_range.start_date == start_date
        assert time_range.end_date == end_date
    
    def test_get_file_type(self, search_service):
        """測試檔案類型判斷"""
        test_cases = [
            (Path("test.csv"), "文字檔案"),
            (Path("test.xlsx"), "Excel 檔案"),
            (Path("test.zip"), "壓縮檔案"),
            (Path("test.pdf"), "PDF 檔案"),
            (Path("test.unknown"), "其他檔案"),
        ]
        
        for file_path, expected_type in test_cases:
            result = search_service._get_file_type(file_path)
            assert result == expected_type
    
    def test_convert_to_accessible_path(self, search_service):
        """測試路徑轉換"""
        # UNC 路徑應該保持不變
        unc_path = Path("\\\\server\\share\\folder")
        result = search_service._convert_to_accessible_path(unc_path)
        assert str(result) == "\\\\server\\share\\folder"
        
        # 普通路徑應該保持不變
        normal_path = Path("C:\\temp")
        result = search_service._convert_to_accessible_path(normal_path)
        assert result == normal_path
    
    @pytest.mark.asyncio
    async def test_create_search_task(self, search_service, sample_filters):
        """測試建立搜尋任務"""
        product_name = "test_product"
        base_path = Path("C:\\temp")
        
        task_id = await search_service.create_search_task(
            product_name, base_path, sample_filters
        )
        
        assert isinstance(task_id, str)
        assert len(task_id) > 0
        
        # 檢查任務是否被正確儲存
        task = search_service.get_task_status(task_id)
        assert task is not None
        assert task.product_name == product_name
        assert task.base_path == base_path
        assert task.status == SearchStatus.PENDING
    
    def test_cleanup_completed_tasks(self, search_service):
        """測試清理已完成任務"""
        # 建立一些模擬任務
        from backend.shared.domain.entities.file_search import SearchTask
        
        # 建立舊的已完成任務
        old_task = SearchTask(
            task_id="old_task",
            product_name="old_product",
            base_path=Path("C:\\temp"),
            filters=SearchFilters(),
            created_at=datetime.now() - timedelta(hours=25),  # 25 小時前
            status=SearchStatus.COMPLETED
        )
        
        # 建立新的已完成任務
        new_task = SearchTask(
            task_id="new_task",
            product_name="new_product",
            base_path=Path("C:\\temp"),
            filters=SearchFilters(),
            created_at=datetime.now() - timedelta(hours=1),  # 1 小時前
            status=SearchStatus.COMPLETED
        )
        
        # 建立進行中的任務
        active_task = SearchTask(
            task_id="active_task",
            product_name="active_product",
            base_path=Path("C:\\temp"),
            filters=SearchFilters(),
            created_at=datetime.now() - timedelta(hours=25),  # 25 小時前
            status=SearchStatus.IN_PROGRESS
        )
        
        # 添加到服務中
        search_service.active_tasks["old_task"] = old_task
        search_service.active_tasks["new_task"] = new_task
        search_service.active_tasks["active_task"] = active_task
        
        # 執行清理（保留 24 小時內的任務）
        search_service.cleanup_completed_tasks(max_age_hours=24)
        
        # 檢查結果
        assert "old_task" not in search_service.active_tasks  # 應該被清理
        assert "new_task" in search_service.active_tasks      # 應該保留
        assert "active_task" in search_service.active_tasks   # 應該保留（進行中）


if __name__ == "__main__":
    pytest.main([__file__, "-v"])