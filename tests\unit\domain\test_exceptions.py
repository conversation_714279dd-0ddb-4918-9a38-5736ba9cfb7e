"""
領域例外處理測試
遵循 TDD 原則 - 先寫失敗的測試
"""

from backend.shared.domain.exceptions.base import (
    OutlookSummaryException,
    DomainException,
    ValidationException,
    ConfigurationException,
    EmailProcessingException,
    FileProcessingException,
    ParsingException,
)


class TestDomainExceptions:
    """測試領域例外處理類別"""

    def test_outlook_summary_exception_base(self):
        """測試基礎例外"""
        message = "測試例外訊息"
        exc = OutlookSummaryException(message)
        
        assert str(exc) == message
        assert exc.message == message
        assert exc.error_code is None
        assert exc.details is None

    def test_outlook_summary_exception_with_details(self):
        """測試包含詳細資訊的例外"""
        message = "測試例外"
        error_code = "ERR_001"
        details = {"field": "value", "timestamp": "2024-01-01"}
        
        exc = OutlookSummaryException(
            message=message,
            error_code=error_code,
            details=details
        )
        
        assert str(exc) == f"[{error_code}] {message}"
        assert exc.message == message
        assert exc.error_code == error_code
        assert exc.details == details

    def test_domain_exception(self):
        """測試領域例外"""
        message = "領域邏輯錯誤"
        exc = DomainException(message)
        
        assert isinstance(exc, OutlookSummaryException)
        assert str(exc) == message
        assert exc.error_code is None

    def test_domain_exception_with_code(self):
        """測試帶錯誤代碼的領域例外"""
        message = "無效的業務規則"
        error_code = "DOMAIN_001"
        
        exc = DomainException(message, error_code=error_code)
        
        assert str(exc) == f"[{error_code}] {message}"
        assert exc.error_code == error_code

    def test_validation_exception(self):
        """測試驗證例外"""
        message = "驗證失敗"
        field_name = "email"
        field_value = "invalid-email"
        
        exc = ValidationException(
            message=message,
            field_name=field_name,
            field_value=field_value
        )
        
        assert isinstance(exc, DomainException)
        assert str(exc) == f"[VALIDATION_ERROR] {message}"
        assert exc.field_name == field_name
        assert exc.field_value == field_value
        assert exc.error_code == "VALIDATION_ERROR"

    def test_configuration_exception(self):
        """測試配置例外"""
        message = "配置錯誤"
        config_key = "database.host"
        config_value = "invalid-host"
        
        exc = ConfigurationException(
            message=message,
            config_key=config_key,
            config_value=config_value
        )
        
        assert isinstance(exc, OutlookSummaryException)
        assert str(exc) == f"[CONFIG_ERROR] {message}"
        assert exc.config_key == config_key
        assert exc.config_value == config_value
        assert exc.error_code == "CONFIG_ERROR"

    def test_email_processing_exception(self):
        """測試郵件處理例外"""
        message = "郵件處理失敗"
        email_id = "email_123"
        vendor = "GTK"
        step = "parsing"
        
        exc = EmailProcessingException(
            message=message,
            email_id=email_id,
            vendor=vendor,
            processing_step=step
        )
        
        assert isinstance(exc, DomainException)
        assert str(exc) == f"[EMAIL_PROCESSING_ERROR] {message}"
        assert exc.email_id == email_id
        assert exc.vendor == vendor
        assert exc.processing_step == step
        assert exc.error_code == "EMAIL_PROCESSING_ERROR"

    def test_file_processing_exception(self):
        """測試檔案處理例外"""
        message = "檔案處理失敗"
        file_path = "/path/to/file.csv"
        operation = "read"
        
        exc = FileProcessingException(
            message=message,
            file_path=file_path,
            operation=operation
        )
        
        assert isinstance(exc, DomainException)
        assert str(exc) == f"[FILE_PROCESSING_ERROR] {message}"
        assert exc.file_path == file_path
        assert exc.operation == operation
        assert exc.error_code == "FILE_PROCESSING_ERROR"

    def test_parsing_exception(self):
        """測試解析例外"""
        message = "解析失敗"
        content = "無效的郵件內容"
        parser_type = "GTKParser"
        line_number = 42
        
        exc = ParsingException(
            message=message,
            content=content,
            parser_type=parser_type,
            line_number=line_number
        )
        
        assert isinstance(exc, DomainException)
        assert str(exc) == f"[PARSING_ERROR] {message}"
        assert exc.content == content
        assert exc.parser_type == parser_type
        assert exc.line_number == line_number
        assert exc.error_code == "PARSING_ERROR"

    def test_exception_chaining(self):
        """測試例外鏈接"""
        original_error = ValueError("原始錯誤")
        
        exc = EmailProcessingException(
            message="處理郵件時發生錯誤",
            email_id="test_email",
            vendor="GTK"
        )
        
        # 測試例外可以被正確 raise
        try:
            raise exc from original_error
        except EmailProcessingException as e:
            assert e.__cause__ == original_error
            assert str(e) == "[EMAIL_PROCESSING_ERROR] 處理郵件時發生錯誤"

    def test_exception_to_dict(self):
        """測試例外轉換為字典"""
        exc = ValidationException(
            message="驗證失敗",
            field_name="email_address",
            field_value="invalid@",
            details={"rule": "email_format"}
        )
        
        result = exc.to_dict()
        
        expected = {
            "error_code": "VALIDATION_ERROR",
            "message": "驗證失敗",
            "field_name": "email_address",
            "field_value": "invalid@",
            "details": {"rule": "email_format"}
        }
        
        assert result == expected

    def test_exception_repr(self):
        """測試例外的字串表示"""
        exc = EmailProcessingException(
            message="處理失敗",
            email_id="email_123",
            vendor="GTK"
        )
        
        repr_str = repr(exc)
        assert "EmailProcessingException" in repr_str
        assert "EMAIL_PROCESSING_ERROR" in repr_str
        assert "處理失敗" in repr_str