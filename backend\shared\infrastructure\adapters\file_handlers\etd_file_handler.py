"""
ETD 廠商檔案處理器
對應 VBA 的 CopyFilesETD 和 CopyFilesETD2 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class ETDFileHandler(BaseFileHandler):
    """
    ETD 廠商檔案處理器
    
    VBA 邏輯：
    - CopyFilesETD: 從 \ETD\FT\{model}\{lot}\ 複製檔案
    - CopyFilesETD2: 從 \Etrend\FT\{pd}\{lot} 複製整個資料夾
    """
    
    def __init__(self, source_base_path: str):
        """初始化 ETD 檔案處理器"""
        super().__init__(source_base_path, "ETD")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        ETD 有兩個可能的來源路徑

        🔧 修復後的路徑結構:
        1. \\server\ETD\FT\{pd}\{lot}\ (主要路徑，包含LOT層級)
        2. \\server\Etrend\FT\{pd}\{lot}\ (備用路徑)
        3. \\server\ETD\FT\{pd}\ (備用路徑，不含LOT層級，用於向後兼容)
        """
        paths = []

        if pd != "default":
            # 🔧 修復：主要路徑包含LOT層級
            if lot != "default":
                # 主要路徑：ETD\FT\{pd}\{lot}\ (包含LOT層級)
                paths.append(self.source_base_path / "ETD" / "FT" / pd / lot)

                # 備用路徑：Etrend\FT\{pd}\{lot}\ (原有備用路徑)
                paths.append(self.source_base_path / "Etrend" / "FT" / pd / lot)

            # 🔧 向後兼容：不含LOT層級的路徑 (作為最後備用)
            paths.append(self.source_base_path / "ETD" / "FT" / pd)

        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        ETD 的檔案搜尋模式
        主要使用資料夾複製，較少用檔案搜尋
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")
            
        if lot:
            patterns.append(f"*{lot}*")
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """ETD 支援資料夾複製（對應 CopyFilesETD2）"""
        return True
        
    def copy_files(self, file_name: str, file_temp: str,
                  pd: str = "default", lot: str = "default") -> bool:
        """
        ETD 特殊邏輯：優先嘗試資料夾複製
        
        Returns:
            bool: 處理成功返回 True，失敗返回 False（絕不返回 None）
        """
        try:
            self.logger.info(f"ETD 檔案處理開始: MO={file_name}, LOT={lot}, PD={pd}")
            self.logger.info(f"ETD 基本來源路徑: {self.source_base_path}")
            self.logger.info(f"ETD 目標路徑: {file_temp}")

            # file_temp 已經是最終的目標目錄，直接使用
            destination_path = Path(file_temp)
            destination_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"建立目標資料夾: {destination_path}")
                
            # ETD 需要 PD 和 LOT 才能運作
            if pd == "default" or lot == "default":
                self.logger.error(f"ETD 處理失敗: 需要有效的 PD 和 LOT 資訊，當前 PD={pd}, LOT={lot}")
                return False
                
            # 取得來源路徑
            source_paths = self.get_source_paths(pd, lot, file_name)
            if not source_paths:
                self.logger.error(f"ETD 處理失敗: 無法取得來源路徑，PD={pd}, LOT={lot}")
                return False
            
            # 記錄所有待嘗試的路徑
            self.logger.info(f"ETD 將嘗試以下來源路徑:")
            for i, path in enumerate(source_paths, 1):
                self.logger.info(f"  路徑 {i}: {path}")
            
            # 嘗試每個來源路徑
            for idx, source_path in enumerate(source_paths, 1):
                self.logger.info(f"ETD 嘗試路徑 {idx}/{len(source_paths)}: {source_path}")
                
                # 檢查路徑是否存在
                if not self._safe_path_exists(source_path):
                    self.logger.warning(f"ETD 路徑不存在: {source_path}")
                    continue
                    
                # 檢查是否為目錄
                if not source_path.is_dir():
                    self.logger.warning(f"ETD 路徑不是資料夾: {source_path}")
                    continue
                
                # 🔧 改進的ETD檔案複製邏輯
                try:
                    import shutil
                    import os

                    dest_folder = destination_path / pd / lot
                    dest_folder.mkdir(parents=True, exist_ok=True)

                    copied_count = 0

                    # 遍歷來源路徑中的所有項目
                    if source_path.exists() and source_path.is_dir():
                        self.logger.info(f"ETD 掃描來源路徑: {source_path}")

                        # 🔧 修復：只搜尋包含MO的檔案，不複製整個目錄
                        search_patterns = []
                        if file_name:
                            search_patterns.extend([
                                file_name.upper(),           # 完全匹配MO
                                file_name.upper()[:8],       # 前8位匹配 (常見MO格式)
                                file_name.upper()[:6],       # 前6位匹配
                            ])

                        items_found = list(source_path.iterdir())
                        self.logger.info(f"ETD 掃描路徑 {source_path}，找到 {len(items_found)} 個項目")

                        for item in items_found:
                            item_name_upper = item.name.upper()
                            should_copy = False
                            match_reason = ""

                            # 🔧 關鍵修復：只處理包含MO的檔案/資料夾
                            for pattern in search_patterns:
                                if pattern in item_name_upper:
                                    should_copy = True
                                    match_reason = f"匹配MO模式: {pattern}"
                                    break

                            # 🔧 額外檢查：如果是資料夾，確保資料夾名稱包含MO
                            if should_copy and item.is_dir():
                                # 對於資料夾，更嚴格的匹配
                                mo_found_in_folder = any(pattern in item_name_upper for pattern in search_patterns)
                                if not mo_found_in_folder:
                                    should_copy = False
                                    self.logger.debug(f"ETD 跳過資料夾 (不包含MO): {item.name}")
                                    continue

                            if should_copy:
                                dest_item = dest_folder / item.name

                                try:
                                    if item.is_dir():
                                        # 複製資料夾
                                        if dest_item.exists():
                                            shutil.rmtree(dest_item)
                                        shutil.copytree(item, dest_item)
                                        self.logger.info(f"ETD 複製資料夾: {item.name} -> {dest_item} ({match_reason})")
                                    else:
                                        # 複製檔案
                                        shutil.copy2(item, dest_item)
                                        self.logger.info(f"ETD 複製檔案: {item.name} -> {dest_item} ({match_reason})")

                                    copied_count += 1

                                except Exception as copy_error:
                                    self.logger.warning(f"ETD 複製項目失敗 {item.name}: {copy_error}")
                                    continue
                            else:
                                self.logger.debug(f"ETD 跳過項目 (不匹配任何模式): {item.name}")

                    if copied_count > 0:
                        self.logger.info(f"ETD 檔案處理成功: 從 {source_path} 複製了 {copied_count} 個匹配項目到 {dest_folder}")
                        return True
                    else:
                        self.logger.warning(f"ETD 在 {source_path} 中未找到匹配 '{file_name}' 的檔案或資料夾")
                        
                except Exception as e:
                    self.logger.error(f"ETD 檔案複製失敗: {e}")
                    self.logger.error(f"ETD 檔案複製失敗詳情: 來源={source_path}, 目標={dest_folder}, MO={file_name}")
                    import traceback
                    self.logger.error(f"ETD 檔案複製錯誤堆疊: {traceback.format_exc()}")
                    # 繼續嘗試下一個路徑，不要直接返回 False
                    continue
                    
            # 所有路徑都嘗試失敗後的詳細記錄
            self._log_detailed_failure_info(source_paths, destination_path, pd, lot, file_name)
            return False
            
        except Exception as e:
            import traceback
            self.logger.error(f"ETD 檔案處理過程中發生異常: {e}")
            self.logger.error(f"ETD 異常詳情: {traceback.format_exc()}")
            return False  # 確保即使發生異常也返回 False 而不是 None
    
    def _log_detailed_failure_info(self, source_paths: List[Path], destination_path: Path, 
                                   pd: str, lot: str, file_name: str):
        """記錄詳細的失敗資訊"""
        attempted_paths = [str(path) for path in source_paths]
        target_path = str(destination_path)
        
        self.logger.error(f"ETD 檔案處理失敗: 找不到包含 MO '{file_name}' 的檔案或資料夾")
        self.logger.error(f"ETD 目標路徑: {target_path}")
        self.logger.error(f"ETD 基本來源路徑: {self.source_base_path}")
        self.logger.error(f"ETD 完整路徑模式說明:")
        self.logger.error(f"  模式 1: {self.source_base_path}\\ETD\\FT\\{pd}\\{lot}")
        self.logger.error(f"  模式 2: {self.source_base_path}\\Etrend\\FT\\{pd}\\{lot}")
        self.logger.error(f"ETD 嘗試的來源路徑詳情:")
        for i, path in enumerate(attempted_paths, 1):
            path_obj = Path(path)
            exists_status = "[存在]" if self._safe_path_exists(path_obj) else "[不存在]"
            is_dir_status = "[資料夾]" if self._safe_path_exists(path_obj) and path_obj.is_dir() else "[非資料夾]" if self._safe_path_exists(path_obj) else "[未檢查]"
            self.logger.error(f"  {i}. {exists_status} {is_dir_status} {path}")
        
        self.logger.error(f"ETD 處理參數總結: PD={pd}, LOT={lot}, MO={file_name}")
        self.logger.error(f"ETD 故障排除建議: 請檢查網路連線和路徑權限，確認 {pd}/{lot} 資料夾中是否存在包含 '{file_name}' 的檔案或資料夾")