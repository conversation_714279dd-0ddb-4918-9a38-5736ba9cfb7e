"""測試唯一產品名稱功能"""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import getpass

from backend.shared.infrastructure.adapters.file_staging_service import FileStagingService


@pytest.fixture
def temp_staging_dir():
    """建立臨時暫存目錄"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def temp_source_files():
    """建立臨時來源檔案"""
    temp_dir = tempfile.mkdtemp()
    
    # 建立測試檔案
    test_files = []
    for i in range(2):
        file_path = Path(temp_dir) / f"test_file_{i}.txt"
        file_path.write_text(f"Test content {i}" * 50)
        test_files.append(file_path)
    
    yield test_files
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def staging_service(temp_staging_dir):
    """建立檔案暫存服務實例"""
    return FileStagingService(
        base_staging_path=temp_staging_dir,
        max_workers=2,
        verify_integrity=True
    )


class TestUniqueProductName:
    """測試唯一產品名稱功能"""
    
    def test_generate_unique_product_name(self, staging_service):
        """測試生成唯一產品名稱"""
        product_name = "TEST_PRODUCT"
        
        # 生成唯一名稱
        unique_name = staging_service._generate_unique_product_name(product_name)
        
        # 驗證格式
        assert unique_name != product_name  # 應該不同於原始名稱
        assert product_name in unique_name  # 應該包含原始名稱
        assert getpass.getuser() in unique_name  # 應該包含用戶名
        
        # 驗證時間戳格式
        parts = unique_name.split('_')
        assert len(parts) >= 3  # 至少有產品名稱、用戶名、時間戳
        
        # 檢查時間戳部分
        timestamp_part = parts[-1]  # 最後一部分應該是時間戳
        assert len(timestamp_part) == 6  # HHMMSS 格式
        assert timestamp_part.isdigit()  # 應該是數字
        
        print(f"原始產品名稱: {product_name}")
        print(f"唯一產品名稱: {unique_name}")
    
    def test_generate_unique_product_name_with_special_chars(self, staging_service):
        """測試包含特殊字符的產品名稱"""
        product_name = "TEST@PRODUCT#123!"
        
        # 生成唯一名稱
        unique_name = staging_service._generate_unique_product_name(product_name)
        
        # 驗證特殊字符被清理
        assert "@" not in unique_name
        assert "#" not in unique_name
        assert "!" not in unique_name
        
        # 但應該保留字母數字和允許的字符
        assert "TEST" in unique_name
        assert "PRODUCT" in unique_name
        assert "123" in unique_name
        
        print(f"原始產品名稱: {product_name}")
        print(f"清理後唯一名稱: {unique_name}")
    
    def test_generate_unique_product_name_empty(self, staging_service):
        """測試空產品名稱"""
        product_name = ""
        
        # 生成唯一名稱
        unique_name = staging_service._generate_unique_product_name(product_name)
        
        # 應該使用預設名稱
        assert "PRODUCT" in unique_name
        assert getpass.getuser() in unique_name
        
        print(f"空產品名稱生成結果: {unique_name}")
    
    def test_create_staging_task_with_unique_name(self, staging_service, temp_source_files):
        """測試使用唯一名稱建立暫存任務"""
        product_name = "TEST_PRODUCT"
        
        # 建立任務（使用唯一名稱）
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:1],
            preserve_structure=True,
            use_unique_name=True
        )
        
        # 檢查任務
        task = staging_service.get_task_status(task_id)
        assert task is not None
        
        # 檢查暫存目錄路徑
        staging_dir_name = task.staging_directory.name
        assert staging_dir_name != product_name  # 應該不同於原始名稱
        assert product_name in staging_dir_name  # 應該包含原始名稱
        assert getpass.getuser() in staging_dir_name  # 應該包含用戶名
        
        print(f"原始產品名稱: {product_name}")
        print(f"暫存目錄名稱: {staging_dir_name}")
        print(f"完整暫存路徑: {task.staging_directory}")
    
    def test_create_staging_task_without_unique_name(self, staging_service, temp_source_files):
        """測試不使用唯一名稱建立暫存任務"""
        product_name = "TEST_PRODUCT"
        
        # 建立任務（不使用唯一名稱）
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:1],
            preserve_structure=True,
            use_unique_name=False
        )
        
        # 檢查任務
        task = staging_service.get_task_status(task_id)
        assert task is not None
        
        # 檢查暫存目錄路徑
        staging_dir_name = task.staging_directory.name
        assert staging_dir_name == product_name  # 應該與原始名稱相同
        
        print(f"原始產品名稱: {product_name}")
        print(f"暫存目錄名稱: {staging_dir_name}")
    
    @pytest.mark.asyncio
    async def test_multiple_users_same_product_name(self, staging_service, temp_source_files):
        """測試多個用戶使用相同產品名稱的情況"""
        product_name = "SHARED_PROJECT"
        
        # 建立多個任務（模擬不同時間點）
        task_ids = []
        for i in range(3):
            task_id = staging_service.create_staging_task(
                product_name=product_name,
                source_files=temp_source_files[:1],
                preserve_structure=True,
                use_unique_name=True
            )
            task_ids.append(task_id)
            
            # 稍微延遲以確保時間戳不同
            import time
            time.sleep(1)
        
        # 檢查所有任務都有不同的暫存目錄
        staging_dirs = []
        for task_id in task_ids:
            task = staging_service.get_task_status(task_id)
            staging_dirs.append(task.staging_directory.name)
        
        # 所有暫存目錄名稱應該都不同
        assert len(set(staging_dirs)) == len(staging_dirs)
        
        # 但都應該包含原始產品名稱
        for staging_dir in staging_dirs:
            assert product_name in staging_dir
            assert getpass.getuser() in staging_dir
        
        print(f"原始產品名稱: {product_name}")
        for i, staging_dir in enumerate(staging_dirs):
            print(f"任務 {i+1} 暫存目錄: {staging_dir}")
    
    def test_unique_name_format_validation(self, staging_service):
        """測試唯一名稱格式驗證"""
        test_cases = [
            "PROJECT_A",
            "test-project",
            "My_Project_123",
            "SIMPLE",
        ]
        
        for product_name in test_cases:
            unique_name = staging_service._generate_unique_product_name(product_name)
            
            # 驗證格式：[產品名稱]_[用戶名]_[日期]_[時間]
            parts = unique_name.split('_')
            assert len(parts) >= 3, f"格式錯誤: {unique_name}"
            
            # 檢查是否包含預期的組件
            assert any(part in product_name.replace('-', '_') for part in parts), f"未包含產品名稱: {unique_name}"
            
            # 檢查時間戳格式（最後兩部分應該是日期和時間）
            if len(parts) >= 2:
                date_part = parts[-2]  # 倒數第二部分應該是日期
                time_part = parts[-1]  # 最後一部分應該是時間
                
                assert len(date_part) == 8, f"日期格式錯誤: {date_part}"
                assert date_part.isdigit(), f"日期應該是數字: {date_part}"
                assert len(time_part) == 6, f"時間格式錯誤: {time_part}"
                assert time_part.isdigit(), f"時間應該是數字: {time_part}"
            
            print(f"{product_name} -> {unique_name}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])