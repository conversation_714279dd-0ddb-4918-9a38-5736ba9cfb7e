#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量CSV to Excel處理器
遵循CLAUDE.md規範：檔案≤500行，功能替換原則，反假測試

功能：對指定資料夾中所有CSV檔案執行csv_to_excel_converter，輸出xlsx到相同位置
基於：CsvToExcelConverter
"""

import os
import glob
import time
from typing import List, Dict, Any
from pathlib import Path
from dataclasses import dataclass
from dotenv import load_dotenv

# 修復 Windows cp950 編碼問題
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

# 絕對路徑import
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 重新配置標準輸出編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')

from backend.shared.infrastructure.adapters.excel.csv_to_excel_converter import CsvToExcelConverter
from backend.shared.infrastructure.adapters.excel.ft_summary_generator import FTSummaryGenerator
from backend.shared.infrastructure.adapters.excel.eqc.utils.timestamp_extractor import TimestampExtractor
from backend.shared.infrastructure.adapters.excel.ft_eqc_grouping_processor import CSVFileDiscovery, OnlineEQCFailProcessor
from backend.shared.infrastructure.adapters.excel.cta.cta_integrated_processor import process_directory_with_cta
import hashlib

# 載入環境變數
load_dotenv()


@dataclass
class BatchProcessResult:
    """批量處理結果"""
    success: bool
    total_files: int
    processed_files: int
    skipped_files: int
    failed_files: int
    processing_time: float
    results: List[Dict[str, Any]]
    error_message: str = ""
    # FT Summary相關欄位
    summary_files: int = 0
    ft_summary_file: str = ""
    # EQC Summary相關欄位（互斥邏輯）
    eqc_summary_files: int = 0
    eqc_summary_file: str = ""
    eqc_all_pass_file: str = ""
    # 檔案清單（用於前端顯示）
    ft_file_list: List[str] = None
    eqc_file_list: List[str] = None
    
    def __post_init__(self):
        """初始化列表欄位"""
        if self.ft_file_list is None:
            self.ft_file_list = []
        if self.eqc_file_list is None:
            self.eqc_file_list = []


class BatchCsvToExcelProcessor:
    """
    批量CSV to Excel處理器
    
    遵循CLAUDE.md功能替換原則：整合現有CsvToExcelConverter，不重複開發
    """
    
    def __init__(self, enable_logging: bool = True):
        """初始化批量處理器"""
        self.enable_logging = enable_logging
        self.csv_converter = CsvToExcelConverter()
        self.ft_generator = FTSummaryGenerator(enable_logging=enable_logging)
        
        # 初始化EQC檔案發現器和失敗處理器
        self.eqc_discovery = CSVFileDiscovery()
        self.eqc_fail_processor = OnlineEQCFailProcessor()
        
        # 從.env獲取排[EXCEPT_CHAR]關鍵字
        excluded_keywords_str = os.getenv('EXCLUDED_FILE_KEYWORDS', 'eqctotaldata,eqcfaildata,summary,correlation')
        self.excluded_keywords = [kw.strip().lower() for kw in excluded_keywords_str.split(',') if kw.strip()]
        
        # 從.env獲取FT/QC處理關鍵字
        ft_keywords_str = os.getenv('FT_PROCESSING_KEYWORDS', 'auto_qc,ft,final_test')
        self.ft_keywords = [kw.strip().lower() for kw in ft_keywords_str.split(',') if kw.strip()]
        
        qc_keywords_str = os.getenv('QC_PROCESSING_KEYWORDS', 'qc,quality_control,eqc')
        self.qc_keywords = [kw.strip().lower() for kw in qc_keywords_str.split(',') if kw.strip()]
        
        if self.enable_logging:
            print("[ROCKET] 初始化批量CSV to Excel處理器")
            print(f"[BOARD] 排[EXCEPT_CHAR]關鍵字：{self.excluded_keywords}")
            print(f"[TARGET] FT處理關鍵字：{self.ft_keywords}")
            print(f"[SEARCH] QC處理關鍵字：{self.qc_keywords}")
    
    def _get_file_md5(self, file_path: str) -> str:
        """計算檔案 MD5"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def _sort_files_by_timestamp(self, csv_files: List[str]) -> List[str]:
        """使用 TimestampExtractor 按時間排序檔案"""
        def get_timestamp(file_path):
            timestamp = TimestampExtractor.extract_internal_timestamp(file_path)
            return timestamp if timestamp else 0
        return sorted(csv_files, key=get_timestamp)
    
    def _deduplicate_files_by_md5(self, csv_files: List[str]) -> List[str]:
        """MD5去重，保留第一個出現的檔案"""
        seen_md5s = set()
        unique_files = []
        duplicates_count = 0
        
        for file_path in csv_files:
            md5_hash = self._get_file_md5(file_path)
            if md5_hash and md5_hash not in seen_md5s:
                seen_md5s.add(md5_hash)
                unique_files.append(file_path)
            elif md5_hash:  # 重複檔案
                duplicates_count += 1
                if self.enable_logging:
                    print(f"[REFRESH] 排[EXCEPT_CHAR]重複檔案：{os.path.basename(file_path)}")
        
        if self.enable_logging and duplicates_count > 0:
            print(f"[CHART] MD5去重：移[EXCEPT_CHAR] {duplicates_count} 個重複檔案")
        
        return unique_files
    
    def scan_csv_files(self, folder_path: str) -> List[str]:
        """
        掃描資料夾內所有CSV檔案（含子資料夾）
        使用.env EXCLUDED_FILE_KEYWORDS排[EXCEPT_CHAR]檔案和資料夾
        """
        if self.enable_logging:
            print(f"[SEARCH] 掃描資料夾：{folder_path}")
        
        csv_files = []
        
        # 遞迴掃描所有CSV檔案
        for root, dirs, files in os.walk(folder_path):
            # 檢查資料夾是否包含排[EXCEPT_CHAR]關鍵字
            folder_name = os.path.basename(root).lower()
            folder_excluded = any(keyword in folder_name for keyword in self.excluded_keywords)
            
            if folder_excluded:
                if self.enable_logging:
                    print(f"[FILE_FOLDER] 排[EXCEPT_CHAR]資料夾：{os.path.basename(root)}")
                continue
            
            for file in files:
                if file.lower().endswith('.csv'):
                    # 檢查檔案名稱是否包含排[EXCEPT_CHAR]關鍵字
                    file_name_lower = file.lower()
                    file_excluded = any(keyword in file_name_lower for keyword in self.excluded_keywords)
                    
                    if not file_excluded:
                        full_path = os.path.join(root, file)
                        csv_files.append(full_path)
                    elif self.enable_logging:
                        print(f"[BOARD] 排[EXCEPT_CHAR]檔案：{file}")
        
        # MD5去重和時間排序
        original_count = len(csv_files)
        csv_files = self._deduplicate_files_by_md5(csv_files)
        csv_files = self._sort_files_by_timestamp(csv_files)
        
        if self.enable_logging:
            print(f"[OK] 找到 {original_count} 個CSV檔案，去重後 {len(csv_files)} 個")
            for i, file in enumerate(csv_files[:10], 1):  # 顯示前10個
                timestamp = TimestampExtractor.extract_internal_timestamp(file)
                time_str = TimestampExtractor.format_timestamp_readable(timestamp) if timestamp else "無時間戳"
                print(f"  {i}. {os.path.basename(file)} ({time_str})")
            if len(csv_files) > 10:
                print(f"  ... 另有 {len(csv_files) - 10} 個檔案")
        
        return csv_files
    
    def check_existing_excel(self, csv_file_path: str) -> bool:
        """檢查對應的Excel檔案是否已存在"""
        excel_path = csv_file_path.replace('.csv', '.xlsx').replace('.CSV', '.xlsx')
        return os.path.exists(excel_path)
    
    def check_csv_processing_type(self, csv_file_path: str) -> str:
        """
        檢查CSV檔案B2欄位決定處理類型
        返回: 'ft', 'qc', 'none'
        """
        try:
            import pandas as pd
            # 讀取CSV前3行來檢查B2欄位
            df = pd.read_csv(csv_file_path, nrows=3, header=None)
            
            # 檢查B2欄位(第1行第1列，因為pandas是0-based)
            if len(df) >= 2 and len(df.columns) >= 2:
                b2_content = str(df.iloc[1, 1]).lower()
                
                # 檢查是否符合FT處理條件
                if any(keyword in b2_content for keyword in self.ft_keywords):
                    return 'ft'
                
                # 檢查是否符合QC處理條件
                if any(keyword in b2_content for keyword in self.qc_keywords):
                    return 'qc'
            
            return 'none'
            
        except Exception as e:
            if self.enable_logging:
                print(f"[WARNING] 檢查CSV類型失敗：{os.path.basename(csv_file_path)} - {str(e)}")
            return 'none'
    
    def check_eqc_file_status(self, csv_file_path: str) -> dict:
        """
        檢查是否為EQC檔案並檢測失敗狀態
        使用現有的CSVFileDiscovery和OnlineEQCFailProcessor
        
        Returns:
            dict: {'is_eqc': bool, 'has_fail': bool, 'fail_count': int}
        """
        try:
            # 使用CSVFileDiscovery檢查是否為EQC檔案
            is_eqc = self.eqc_discovery._is_eqc_file(csv_file_path)
            
            if not is_eqc:
                return {'is_eqc': False, 'has_fail': False, 'fail_count': 0}
            
            # 使用OnlineEQCFailProcessor檢查失敗設備
            first_fail_row, fail_count = self.eqc_fail_processor.find_first_non_one_row_with_count(csv_file_path)
            has_fail = fail_count > 0
            
            return {
                'is_eqc': True,
                'has_fail': has_fail,
                'fail_count': fail_count
            }
            
        except Exception as e:
            if self.enable_logging:
                print(f"[WARNING] EQC檔案檢查失敗：{os.path.basename(csv_file_path)} - {str(e)}")
            return {'is_eqc': False, 'has_fail': False, 'fail_count': 0}
    
    def process_single_csv(self, csv_file_path: str, force_overwrite: bool = False) -> Dict[str, Any]:
        """處理單一CSV檔案"""
        if self.enable_logging:
            print(f"\n[REFRESH] 處理：{os.path.basename(csv_file_path)}")
        
        try:
            # 生成輸出路徑（同目錄）
            output_path = csv_file_path.replace('.csv', '.xlsx').replace('.CSV', '.xlsx')
            
            # 檢查檔案是否已存在
            if os.path.exists(output_path) and not force_overwrite:
                if self.enable_logging:
                    print(f"[FAST_FORWARD] 跳過：檔案已存在 - {os.path.basename(output_path)}")
                return {
                    'csv_file': csv_file_path,
                    'excel_file': output_path,
                    'status': 'skipped',
                    'reason': '檔案已存在',
                    'processing_time': 0.0
                }
            
            # 執行轉換
            start_time = time.perf_counter()
            result = self.csv_converter.convert_csv_to_excel(csv_file_path, output_path)
            processing_time = time.perf_counter() - start_time
            
            if result.success:
                if self.enable_logging:
                    print(f"[OK] 成功：{os.path.basename(output_path)} ({processing_time:.2f}秒)")
                return {
                    'csv_file': csv_file_path,
                    'excel_file': output_path,
                    'status': 'success',
                    'processing_time': processing_time,
                    'total_rows': result.total_rows,
                    'total_columns': result.total_columns
                }
            else:
                if self.enable_logging:
                    print(f"[ERROR] 失敗：{result.error_message}")
                return {
                    'csv_file': csv_file_path,
                    'excel_file': output_path,
                    'status': 'failed',
                    'error': result.error_message,
                    'processing_time': processing_time
                }
                
        except Exception as e:
            if self.enable_logging:
                print(f"[ERROR] 異常：{str(e)}")
            return {
                'csv_file': csv_file_path,
                'excel_file': '',
                'status': 'failed',
                'error': str(e),
                'processing_time': 0.0
            }
    
    def process_with_ft_summary(self, csv_file_path: str, force_overwrite: bool = False) -> Dict[str, Any]:
        """處理CSV檔案並生成FT Summary"""
        if self.enable_logging:
            print(f"\n[REFRESH] FT處理：{os.path.basename(csv_file_path)}")
        
        try:
            # 先執行標準Excel轉換
            excel_result = self.process_single_csv(csv_file_path, force_overwrite)
            
            # 如果Excel轉換成功，再生成FT Summary
            if excel_result['status'] == 'success':
                ft_result = self.ft_generator.generate_ft_summary(csv_file_path)
                
                if ft_result.success:
                    if self.enable_logging:
                        print(f"[OK] FT Summary成功：{os.path.basename(ft_result.output_file)}")
                    
                    excel_result.update({
                        'ft_summary_file': ft_result.output_file,
                        'ft_summary_status': 'success',
                        'ft_processing_time': ft_result.processing_time
                    })
                else:
                    if self.enable_logging:
                        print(f"[WARNING] FT Summary失敗：{ft_result.error_message}")
                    
                    excel_result.update({
                        'ft_summary_file': '',
                        'ft_summary_status': 'failed',
                        'ft_summary_error': ft_result.error_message
                    })
            
            return excel_result
            
        except Exception as e:
            if self.enable_logging:
                print(f"[ERROR] FT處理異常：{str(e)}")
            return {
                'csv_file': csv_file_path,
                'excel_file': '',
                'status': 'failed',
                'error': f"FT處理失敗：{str(e)}",
                'processing_time': 0.0
            }
    
    def generate_eqc_all_pass_file(self, all_pass_files: List[str], output_folder: str) -> str:
        """為全通過的EQC檔案生成 EQC_ALLPASS.txt"""
        if not all_pass_files:
            return ""
        
        try:
            output_file = os.path.join(output_folder, "EQC_ALLPASS.txt")
            
            if self.enable_logging:
                print(f"\n[OK] 生成EQC全通過檔案：{len(all_pass_files)}個檔案")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("EQC All Pass Files:\n")
                f.write("=" * 50 + "\n")
                f.write(f"Total Files: {len(all_pass_files)}\n")
                f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")
                
                for i, file_path in enumerate(all_pass_files, 1):
                    f.write(f"{i:2d}. {os.path.basename(file_path)}\n")
            
            if self.enable_logging:
                print(f"[FILE_FOLDER] EQC全通過檔案：{os.path.basename(output_file)}")
            
            return output_file
            
        except Exception as e:
            if self.enable_logging:
                print(f"[ERROR] EQC全通過檔案生成失敗：{str(e)}")
            return ""
    
    def consolidate_summaries(self, summary_files: List[str], output_folder: str) -> str:
        """整併多個Summary檔案生成FT_SUMMARY.csv"""
        if not summary_files:
            return ""
        
        try:
            import pandas as pd
            
            # 讀取所有Summary檔案並合併統計
            total_counts = {'Total': 0, 'Pass': 0, 'Fail': 0}
            all_bin_data = {}
            
            if self.enable_logging:
                print(f"\n[CHART] 整併{len(summary_files)}個Summary檔案")
            
            for summary_file in summary_files:
                if not os.path.exists(summary_file):
                    continue
                    
                try:
                    # 先嘗試 UTF-8，失敗則嘗試其他編碼
                    try:
                        df = pd.read_csv(summary_file, header=None, encoding='utf-8')
                    except UnicodeDecodeError:
                        try:
                            df = pd.read_csv(summary_file, header=None, encoding='big5')
                        except UnicodeDecodeError:
                            df = pd.read_csv(summary_file, header=None, encoding='cp950')
                    
                    # 讀取前4行統計數據
                    if len(df) >= 4:
                        total_counts['Total'] += int(df.iloc[0, 1])
                        total_counts['Pass'] += int(df.iloc[1, 1])
                        total_counts['Fail'] += int(df.iloc[2, 1])
                    
                    # 讀取BIN數據(從第7行開始，跳過標題行)
                    if len(df) >= 7:
                        for i in range(6, len(df)):  # 從索引6開始（第7行）
                            if pd.notna(df.iloc[i, 0]) and str(df.iloc[i, 0]).strip():
                                bin_name = str(df.iloc[i, 0])
                                # 跳過標題行（Bin欄位）
                                if bin_name.lower() == 'bin':
                                    continue
                                bin_count = int(df.iloc[i, 1]) if pd.notna(df.iloc[i, 1]) else 0
                                
                                if bin_name in all_bin_data:
                                    all_bin_data[bin_name] += bin_count
                                else:
                                    all_bin_data[bin_name] = bin_count
                    
                    if self.enable_logging:
                        print(f"  [OK] {os.path.basename(summary_file)}")
                        
                except Exception as e:
                    if self.enable_logging:
                        print(f"  [WARNING] 讀取失敗：{os.path.basename(summary_file)} - {str(e)}")
            
            # 計算總計Yield
            yield_rate = f"{(total_counts['Pass'] / total_counts['Total'] * 100):.3f}%" if total_counts['Total'] > 0 else "0.000%"
            
            # 生成整併結果
            output_file = os.path.join(output_folder, "FT_SUMMARY.csv")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                # 寫入前4行統計
                f.write(f"Total,{total_counts['Total']},Date:,Multiple Files\n")
                f.write(f"Pass,{total_counts['Pass']},Computer:,Batch Process\n")
                f.write(f"Fail,{total_counts['Fail']},Lot ID:,FT_SUMMARY.csv\n")
                f.write(f"Yield,{yield_rate}\n")
                f.write("\n")
                
                # 寫入BIN標題行
                f.write("Bin,Count,%,Definition,Note\n")
                
                # 寫入BIN數據(按BIN號碼排序)
                sorted_bins = sorted(all_bin_data.items(), key=lambda x: (str(x[0]).isdigit(), int(x[0]) if str(x[0]).isdigit() else float('inf'), x[0]))
                
                for bin_name, count in sorted_bins:
                    percentage = f"{(count / total_counts['Total'] * 100):.3f}" if total_counts['Total'] > 0 else "0.000"
                    f.write(f"{bin_name},{count},{percentage}%\n")
            
            if self.enable_logging:
                print(f"[OK] 整併完成：{os.path.basename(output_file)}")
                print(f"[CHART] 總計：Total={total_counts['Total']}, Pass={total_counts['Pass']}, Fail={total_counts['Fail']}, Yield={yield_rate}")
            
            return output_file
            
        except Exception as e:
            if self.enable_logging:
                print(f"[ERROR] 整併失敗：{str(e)}")
            return ""
    
    def process_folder(self, input_folder: str, force_overwrite: bool = False, processing_mode: str = "full") -> BatchProcessResult:
        """
        批量處理資料夾中的CSV檔案並生成FT Summary整併結果
        
        Args:
            input_folder: 輸入資料夾路徑
            force_overwrite: 是否強制覆寫已存在檔案
            processing_mode: 處理模式 ("full": Excel+Summary, "summary_only": 僅Summary)
        """
        start_time = time.perf_counter()
        
        if self.enable_logging:
            print("=== 反假測試檢查：記錄執行前狀態 ===")
            print(f"[FILE_FOLDER] 輸入資料夾：{input_folder}")
            print(f"[TARGET] 處理模式：{'完整模式(Excel+Summary)' if processing_mode == 'full' else '快速模式(僅Summary)'}")
            print(f"[ALARM_CLOCK] 開始時間：{time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # CTA All-in-One 前置處理
            if self.enable_logging:
                print("[TOOL] 執行 CTA All-in-One 前置處理...")
            try:
                cta_result = process_directory_with_cta(input_folder)
                if cta_result.get('success') and self.enable_logging:
                    print(f"   [OK] CTA處理完成: {len(cta_result.get('output_csv_files', []))} 個Data11檔案")
            except Exception as e:
                if self.enable_logging:
                    print(f"   [WARNING] CTA處理跳過: {e}")
            
            # 掃描CSV檔案
            csv_files = self.scan_csv_files(input_folder)
            if not csv_files:
                return BatchProcessResult(
                    False, 0, 0, 0, 0, 0, [],
                    "未找到任何CSV檔案", 0, "", 0, "", ""
                )
            
            # 處理所有檔案並記錄Summary檔案
            results = []
            processed_count = 0
            skipped_count = 0
            failed_count = 0
            ft_summary_files = []
            eqc_summary_files = []
            eqc_all_pass_files = []
            # 檔案清單收集（用於前端顯示）
            ft_file_list = []
            eqc_file_list = []
            
            for i, csv_file in enumerate(csv_files, 1):
                if self.enable_logging:
                    print(f"\n[CHART] 進度：{i}/{len(csv_files)}")
                
                # 檢查EQC檔案狀態並記錄
                eqc_status = self.check_eqc_file_status(csv_file)
                if eqc_status['is_eqc']:
                    if self.enable_logging:
                        print(f"[SEARCH] EQC檔案：{os.path.basename(csv_file)}")
                        if eqc_status['has_fail']:
                            print(f"[CHART] EQC檔案有失敗設備：{os.path.basename(csv_file)} (失敗數量: {eqc_status['fail_count']}個)")
                        else:
                            print(f"[OK] EQC檔案全通過：{os.path.basename(csv_file)}")
                
                # 檢查CSV檔案類型
                csv_type = self.check_csv_processing_type(csv_file)
                
                if csv_type == 'ft':
                    # 收集FT檔案名稱
                    ft_file_list.append(os.path.basename(csv_file))
                    
                    if processing_mode == 'summary_only':
                        # 快速模式：僅生成Summary，跳過Excel轉換
                        if self.enable_logging:
                            print(f"[FAST] 快速FT處理：{os.path.basename(csv_file)}")
                        ft_result = self.ft_generator.generate_ft_summary(csv_file)
                        if ft_result.success:
                            ft_summary_files.append(ft_result.output_file)
                            processed_count += 1
                            result = {
                                'csv_file': csv_file,
                                'excel_file': '',
                                'status': 'success',
                                'ft_summary_file': ft_result.output_file,
                                'ft_summary_status': 'success',
                                'processing_time': ft_result.processing_time
                            }
                        else:
                            failed_count += 1
                            result = {
                                'csv_file': csv_file,
                                'excel_file': '',
                                'status': 'failed',
                                'error': f"FT Summary生成失敗：{ft_result.error_message}",
                                'processing_time': 0.0
                            }
                    else:
                        # 完整模式：執行Excel轉換 + FT Summary生成
                        if self.enable_logging:
                            print(f"[REFRESH] 完整FT處理：{os.path.basename(csv_file)}")
                        result = self.process_with_ft_summary(csv_file, force_overwrite)
                        
                        # 記錄FT Summary檔案
                        if result.get('ft_summary_status') == 'success':
                            ft_summary_files.append(result['ft_summary_file'])
                elif eqc_status['is_eqc']:
                    # 收集EQC檔案名稱
                    eqc_file_list.append(os.path.basename(csv_file))
                    
                    # EQC檔案處理（互斥邏輯）
                    if eqc_status['has_fail']:
                        # EQC有失敗：使用FT Summary處理邏輯
                        if processing_mode == 'summary_only':
                            # 快速模式：僅生成Summary
                            if self.enable_logging:
                                print(f"[FAST] 快速EQC處理（有失敗）：{os.path.basename(csv_file)}")
                            ft_result = self.ft_generator.generate_ft_summary(csv_file)
                            if ft_result.success:
                                eqc_summary_files.append(ft_result.output_file)
                                processed_count += 1
                                result = {
                                    'csv_file': csv_file,
                                    'excel_file': '',
                                    'status': 'success',
                                    'eqc_summary_file': ft_result.output_file,
                                    'eqc_summary_status': 'success',
                                    'processing_time': ft_result.processing_time
                                }
                            else:
                                failed_count += 1
                                result = {
                                    'csv_file': csv_file,
                                    'excel_file': '',
                                    'status': 'failed',
                                    'error': f"EQC Summary生成失敗：{ft_result.error_message}",
                                    'processing_time': 0.0
                                }
                        else:
                            # 完整模式：執行Excel轉換 + EQC Summary生成
                            if self.enable_logging:
                                print(f"[REFRESH] 完整EQC處理（有失敗）：{os.path.basename(csv_file)}")
                            result = self.process_with_ft_summary(csv_file, force_overwrite)
                            
                            # 記錄EQC Summary檔案
                            if result.get('ft_summary_status') == 'success':
                                eqc_summary_files.append(result['ft_summary_file'])
                                # 更新結果標記為EQC
                                result['eqc_summary_file'] = result.pop('ft_summary_file', '')
                                result['eqc_summary_status'] = result.pop('ft_summary_status', 'failed')
                    else:
                        # EQC全通過：記錄到all_pass列表
                        if self.enable_logging:
                            print(f"[OK] EQC全通過記錄：{os.path.basename(csv_file)}")
                        eqc_all_pass_files.append(csv_file)
                        
                        if processing_mode == 'summary_only':
                            # 快速模式：僅記錄，不做其他處理
                            result = {
                                'csv_file': csv_file,
                                'excel_file': '',
                                'status': 'eqc_all_pass',
                                'reason': 'EQC全通過',
                                'processing_time': 0.0
                            }
                            skipped_count += 1
                        else:
                            # 完整模式：仍執行Excel轉換
                            result = self.process_single_csv(csv_file, force_overwrite)
                else:
                    if processing_mode == 'summary_only':
                        # 快速模式：跳過其他檔案
                        if self.enable_logging:
                            print(f"[FAST_FORWARD] 快速模式跳過標準：{os.path.basename(csv_file)}")
                        result = {
                            'csv_file': csv_file,
                            'excel_file': '',
                            'status': 'skipped',
                            'reason': '快速模式跳過標準檔案',
                            'processing_time': 0.0
                        }
                        skipped_count += 1
                    else:
                        # 其他類型：只執行Excel轉換
                        if self.enable_logging:
                            print(f"[NOTES] 標準處理：{os.path.basename(csv_file)}")
                        result = self.process_single_csv(csv_file, force_overwrite)
                
                results.append(result)
                
                # 統計已在上面處理，只需要處理完整模式的統計
                if processing_mode == 'full':
                    if result['status'] == 'success':
                        processed_count += 1
                    elif result['status'] == 'skipped':
                        skipped_count += 1
                    else:
                        failed_count += 1
            
            # 橫向整併FT Summary檔案（恢復6/18 PM12:27原始設計）
            ft_summary_file = ""
            if ft_summary_files:
                try:
                    from backend.shared.infrastructure.adapters.excel.ft_summary_generator import FTSummaryGenerator
                    generator = FTSummaryGenerator(enable_logging=self.enable_logging)
                    output_path = os.path.join(input_folder, "FT_SUMMARY.csv")
                    excel_output_path = os.path.join(input_folder, "FT_SUMMARY.xlsx")
                    
                    # 強制覆蓋：如果檔案已存在，先刪除
                    if self.enable_logging:
                        print(f"[DEBUG] FT_SUMMARY force_overwrite={force_overwrite}, output_path={output_path}, excel_output_path={excel_output_path}")
                        print(f"[DEBUG] FT_SUMMARY CSV存在: {os.path.exists(output_path)}, Excel存在: {os.path.exists(excel_output_path)}")
                    
                    if force_overwrite:
                        if os.path.exists(output_path):
                            os.remove(output_path)
                            if self.enable_logging:
                                print(f"[DELETE] 刪除舊檔案: {os.path.basename(output_path)}")
                        if os.path.exists(excel_output_path):
                            os.remove(excel_output_path)
                            if self.enable_logging:
                                print(f"[DELETE] 刪除舊檔案: {os.path.basename(excel_output_path)}")
                    
                    result = generator.generate_horizontal_summary(ft_summary_files, output_path)
                    ft_summary_file = result.output_file if result.success else ""
                    if self.enable_logging:
                        print(f"[TARGET] FT橫向整併完成: {os.path.basename(ft_summary_file) if ft_summary_file else '失敗'}")
                except Exception as e:
                    if self.enable_logging:
                        print(f"[ERROR] FT橫向整併失敗: {str(e)}")
                    ft_summary_file = ""
            
            # EQC Summary處理（互斥邏輯）
            eqc_summary_file = ""
            eqc_all_pass_file = ""
            
            if eqc_summary_files:
                # 有EQC失敗：生成EQC Summary
                try:
                    from backend.shared.infrastructure.adapters.excel.ft_summary_generator import FTSummaryGenerator
                    generator = FTSummaryGenerator(enable_logging=self.enable_logging)
                    output_path = os.path.join(input_folder, "EQC_SUMMARY.csv")
                    excel_output_path = os.path.join(input_folder, "EQC_SUMMARY.xlsx")
                    
                    # 強制覆蓋：如果檔案已存在，先刪除
                    if self.enable_logging:
                        print(f"[DEBUG] EQC_SUMMARY force_overwrite={force_overwrite}, output_path={output_path}, excel_output_path={excel_output_path}")
                        print(f"[DEBUG] EQC_SUMMARY CSV存在: {os.path.exists(output_path)}, Excel存在: {os.path.exists(excel_output_path)}")
                    
                    if force_overwrite:
                        if os.path.exists(output_path):
                            os.remove(output_path)
                            if self.enable_logging:
                                print(f"[DELETE] 刪除舊檔案: {os.path.basename(output_path)}")
                        if os.path.exists(excel_output_path):
                            os.remove(excel_output_path)
                            if self.enable_logging:
                                print(f"[DELETE] 刪除舊檔案: {os.path.basename(excel_output_path)}")
                    
                    result = generator.generate_horizontal_summary(eqc_summary_files, output_path)
                    eqc_summary_file = result.output_file if result.success else ""
                    if self.enable_logging:
                        print(f"[TARGET] EQC橫向整併完成: {os.path.basename(eqc_summary_file) if eqc_summary_file else '失敗'}")
                except Exception as e:
                    if self.enable_logging:
                        print(f"[ERROR] EQC橫向整併失敗: {str(e)}")
                    eqc_summary_file = ""
                    
            elif eqc_all_pass_files:
                # 全部EQC通過：生成AllPass檔案
                eqc_all_pass_file = self.generate_eqc_all_pass_file(eqc_all_pass_files, input_folder)
            
            # Excel轉換已在FTSummaryGenerator中處理，無需重複轉換
            if self.enable_logging:
                if ft_summary_file:
                    excel_file = ft_summary_file.replace('.csv', '.xlsx')
                    if os.path.exists(excel_file):
                        print(f"[PAGE_FACING_UP] FT Excel檔案已生成: {os.path.basename(excel_file)}")
                
                if eqc_summary_file:
                    excel_file = eqc_summary_file.replace('.csv', '.xlsx')
                    if os.path.exists(excel_file):
                        print(f"[PAGE_FACING_UP] EQC Excel檔案已生成: {os.path.basename(excel_file)}")
            
            total_time = time.perf_counter() - start_time
            
            # 反假測試：確認實際檔案生成
            if self.enable_logging:
                print("\n=== 反假測試檢查：記錄執行後狀態 ===")
                print(f"[ALARM_CLOCK] 結束時間：{time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"[CHART] 處理結果：成功{processed_count}個，跳過{skipped_count}個，失敗{failed_count}個")
                print(f"[TARGET] FT Summary檔案：{len(ft_summary_files)}個")
                print(f"[SEARCH] EQC Summary檔案：{len(eqc_summary_files)}個")
                print(f"[OK] EQC全通過檔案：{len(eqc_all_pass_files)}個")
                print(f"[TIME] 總處理時間：{total_time:.2f}秒")
                
                if ft_summary_file:
                    print(f"[CHART] FT最終整併：{os.path.basename(ft_summary_file)} + .xlsx")
                
                if eqc_summary_file:
                    print(f"[CHART] EQC最終整併：{os.path.basename(eqc_summary_file)} + .xlsx")
                elif eqc_all_pass_file:
                    print(f"[OK] EQC全通過檔案：{os.path.basename(eqc_all_pass_file)}")
            
            result = BatchProcessResult(
                True, len(csv_files), processed_count, skipped_count, failed_count,
                total_time, results, "", len(ft_summary_files), ft_summary_file,
                len(eqc_summary_files), eqc_summary_file, eqc_all_pass_file
            )
            # 設定檔案清單
            result.ft_file_list = ft_file_list
            result.eqc_file_list = eqc_file_list
            return result
            
        except Exception as e:
            total_time = time.perf_counter() - start_time
            error_result = BatchProcessResult(
                False, 0, 0, 0, 0, total_time, [],
                f"批量處理失敗：{str(e)}", 0, "", 0, "", ""
            )
            # 設定空的檔案清單
            error_result.ft_file_list = []
            error_result.eqc_file_list = []
            return error_result


def main():
    """主函數：測試FT Summary整合批量處理"""
    processor = BatchCsvToExcelProcessor(enable_logging=True)
    
    # 測試資料夾
    test_folder = "doc/20250523"
    
    print("[TARGET] 開始FT Summary整合批量處理")
    print("=" * 80)
    
    # 執行批量處理（force模式以測試FT Summary生成）
    result = processor.process_folder(test_folder, force_overwrite=True)
    
    print("\n" + "=" * 80)
    print("[PARTY] FT Summary整合處理完成")
    print("=" * 80)
    
    if result.success:
        print(f"[OK] 成功處理：{result.processed_files}/{result.total_files} 個檔案")
        print(f"[FAST_FORWARD] 跳過檔案：{result.skipped_files} 個（已存在）")
        print(f"[ERROR] 失敗檔案：{result.failed_files} 個")
        print(f"[TARGET] FT Summary檔案：{result.summary_files} 個")
        print(f"[SEARCH] EQC Summary檔案：{result.eqc_summary_files} 個")
        
        if result.ft_summary_file:
            print(f"[CHART] FT最終整併檔案：{os.path.basename(result.ft_summary_file)} + .xlsx")
        
        if result.eqc_summary_file:
            print(f"[CHART] EQC最終整併檔案：{os.path.basename(result.eqc_summary_file)} + .xlsx")
        elif result.eqc_all_pass_file:
            print(f"[OK] EQC全通過檔案：{os.path.basename(result.eqc_all_pass_file)}")
            
        print(f"[TIME] 總處理時間：{result.processing_time:.2f} 秒")
    else:
        print(f"[ERROR] 處理失敗：{result.error_message}")


if __name__ == "__main__":
    main()