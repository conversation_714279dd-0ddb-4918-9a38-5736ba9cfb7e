#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EQC Step 5 整合測試
驗證 Step 5 與 Step 1-4 的完整整合，以及 API 端點功能
"""

import os
import tempfile
import pytest
from unittest.mock import Mock, patch
from datetime import datetime

# 導入待測試模組
from backend.shared.infrastructure.adapters.excel.eqc.eqc_step5_testflow_processor import EQCStep5TestFlowProcessor


@pytest.mark.integration
class TestStep5Integration:
    """Step 5 與主系統整合測試"""
    
    def setup_method(self):
        """測試前設置"""
        self.processor = EQCStep5TestFlowProcessor()
        
        # 建立完整的測試 CSV 資料（模擬真實 EQCTOTALDATA.csv）
        self.full_csv_content = self._create_full_test_csv()
        
        # 建立真實的 Step4 DEBUG LOG 資料
        self.real_debug_log_content = self._create_real_debug_log()
    
    def _create_full_test_csv(self):
        """建立完整的測試 CSV 資料"""
        header_lines = [
            "# EQC TOTAL DATA CSV - 完整測試資料\n",
            "# 包含標準的 FT, OnlineEQC, RT 區域\n",
            "WAFER_ID,BIN1,BIN2,BIN3,BIN4,BIN5,BIN6,BIN7,BIN8,BIN9,BIN10,BIN11,BIN12,BIN13,BIN14,BIN15,BIN16,BIN17,BIN18,BIN19,BIN20\n",
            "DIE_X,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20\n",
            "DIE_Y,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20\n",
            "PART_ID,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17,P18,P19,P20\n",
            "SOFT_BIN,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20\n",
            "RESULT,PASS,FAIL,PASS,PASS,FAIL,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS,PASS\n",
            "X,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119\n",
            "Y,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219\n",
            "SITE_NUM,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20\n",
            "OnlineEQC_Fail,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0\n",
            "TEST_TIME,10.1,10.2,10.3,10.4,10.5,10.6,10.7,10.8,10.9,11.0,11.1,11.2,11.3,11.4,11.5,11.6,11.7,11.8,11.9,12.0\n",
            "FREQUENCY,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019\n",
        ]
        
        # OnlineEQC 區域資料行（第14-33行）
        online_eqc_lines = []
        for i in range(20):
            row_num = i + 14
            # 模擬一些 FAIL 行
            fail_marker = "FAIL" if row_num in [15, 17, 19] else "PASS"
            line = f"OnlineEQC_{i+1:02d},{fail_marker},{i+1},{i+2},{i+3},{i+4},{i+5},{i+6},{i+7},{i+8},{i+9},{i+10},{i+11},{i+12},{i+13},{i+14},{i+15},{i+16},{i+17},{i+18},{i+19}\n"
            online_eqc_lines.append(line)
        
        # RT 區域資料行（第34行之後）
        rt_lines = []
        for i in range(30):  # RT 區域有30行
            row_num = i + 34
            # 模擬對應的 RT 資料
            rt_marker = "RT_MATCH" if row_num in [37, 39, 41] else "RT_NORMAL"
            line = f"RT_{i+1:02d},{rt_marker},{row_num},{i+100},{i+200},{i+300},{i+400},{i+500},{i+600},{i+700},{i+800},{i+900},{i+1000},{i+1100},{i+1200},{i+1300},{i+1400},{i+1500},{i+1600},{i+1700},{i+1800}\n"
            rt_lines.append(line)
        
        return header_lines + online_eqc_lines + rt_lines
    
    def _create_real_debug_log(self):
        """建立真實的 Step4 DEBUG LOG 資料"""
        return [
            "EQC Step 4 - CODE區間匹配搜尋 DEBUG LOG",
            "=" * 60,
            "開始時間: 2025-06-11 15:30:00",
            "",
            "FAIL 行檢測結果:",
            "總共檢測到 3 個 FAIL 行:",
            "",
            "FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)",
            "  - OnlineEQC第15行 vs RT第37行",
            "  - 主要區間匹配: 38/38 欄位匹配 (100%)",
            "  - 備用區間匹配: 36/36 欄位匹配",
            "",
            "FAIL #2: 17(OnlineEQC) [LEFT_RIGHT_ARROW] 39(RT)",
            "  - OnlineEQC第17行 vs RT第39行", 
            "  - 主要區間匹配: 38/38 欄位匹配 (100%)",
            "  - 備用區間匹配: 36/36 欄位匹配",
            "",
            "FAIL #3: 19(OnlineEQC) [LEFT_RIGHT_ARROW] 41(RT)",
            "  - OnlineEQC第19行 vs RT第41行",
            "  - 主要區間匹配: 38/38 欄位匹配 (100%)",
            "  - 備用區間匹配: 36/36 欄位匹配",
            "",
            "CODE 匹配搜尋結果:",
            "總計：3 個匹配",
            "平均每 FAIL：38.0 個匹配",
            "",
            "處理完成時間: 2025-06-11 15:30:05"
        ]
    
    def test_step5_with_step1234_output(self):
        """測試 Step 5 與 Step 1-2-3-4 完整流程整合"""
        # 建立臨時檔案模擬完整處理流程的輸出
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_csv:
            temp_csv.writelines(self.full_csv_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as temp_log:
            temp_log.writelines('\n'.join(self.real_debug_log_content))
            temp_log_path = temp_log.name
        
        try:
            # 執行 Step 5 處理
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            # 驗證整合處理結果
            assert result['status'] == 'success'
            assert result['fail_mappings_count'] == 3  # 3個 FAIL 對應關係
            assert result['total_rows'] == len(self.full_csv_content)
            assert os.path.exists(result['output_file'])
            
            # 驗證 Step 5 輸出檔案格式
            with open(result['output_file'], 'r', encoding='utf-8') as f:
                output_lines = f.readlines()
            
            # 驗證完整性：前13行標頭完全保留
            for i in range(13):
                assert output_lines[i] == self.full_csv_content[i], f"標頭第{i+1}行整合後格式錯誤"
            
            # 驗證資料完整性：總行數不變
            assert len(output_lines) == len(self.full_csv_content), "整合處理後資料行數變化"
            
            # 驗證線性測試流程：應該能找到重新排列的 FAIL 對應行
            output_content = ''.join(output_lines)
            assert "OnlineEQC_02,FAIL" in output_content, "FAIL行15未正確包含在輸出中"
            assert "RT_04,RT_MATCH" in output_content, "RT行37未正確包含在輸出中"
            
        finally:
            # 清理檔案
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])
    
    def test_step5_format_consistency_with_standards(self):
        """測試 Step 5 輸出格式與標準處理器的一致性"""
        # 這個測試確保 Step 5 的輸出完全符合標準格式要求
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_csv:
            temp_csv.writelines(self.full_csv_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as temp_log:
            temp_log.writelines('\n'.join(self.real_debug_log_content))
            temp_log_path = temp_log.name
        
        try:
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            # 讀取原始和輸出檔案進行詳細比較
            with open(temp_csv_path, 'r', encoding='utf-8') as f:
                original_lines = f.readlines()
            
            with open(result['output_file'], 'r', encoding='utf-8') as f:
                output_lines = f.readlines()
            
            # 格式一致性檢查
            # 1. 字符編碼一致
            for i, (orig, out) in enumerate(zip(original_lines[:13], output_lines[:13])):
                assert orig.encode('utf-8') == out.encode('utf-8'), f"第{i+1}行編碼不一致"
            
            # 2. 欄位分隔符一致
            for i, (orig, out) in enumerate(zip(original_lines, output_lines)):
                orig_commas = orig.count(',')
                out_commas = out.count(',')
                assert orig_commas == out_commas, f"第{i+1}行欄位數量不一致: {orig_commas} vs {out_commas}"
            
            # 3. 行結束符一致
            for i, (orig, out) in enumerate(zip(original_lines, output_lines)):
                assert orig.endswith('\n') == out.endswith('\n'), f"第{i+1}行結束符不一致"
            
            # 4. 特殊字符保留
            special_chars_found = False
            for line in output_lines[:13]:
                if any(char in line for char in ['#', '中', '文', 'PASS', 'FAIL']):
                    special_chars_found = True
                    break
            assert special_chars_found, "特殊字符未正確保留"
            
        finally:
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])
    
    def test_step5_performance_with_large_dataset(self):
        """測試 Step 5 處理大型資料集的性能"""
        # 建立較大的測試資料集（模擬真實場景）
        large_csv_content = self.full_csv_content[:13]  # 保留標頭
        
        # 生成更多資料行（500行）
        for i in range(500):
            row_num = i + 14
            if i < 100:  # OnlineEQC 區域
                fail_marker = "FAIL" if i % 10 == 0 else "PASS"
                line = f"OnlineEQC_{i+1:03d},{fail_marker}," + ",".join([str(j) for j in range(19)]) + "\n"
            else:  # RT 區域
                rt_marker = "RT_MATCH" if (i-100) % 10 == 0 else "RT_NORMAL" 
                line = f"RT_{i-99:03d},{rt_marker}," + ",".join([str(j+row_num) for j in range(19)]) + "\n"
            large_csv_content.append(line)
        
        # 生成對應的大型 DEBUG LOG
        large_debug_log = self.real_debug_log_content[:6]  # 保留標頭
        
        # 添加多個 FAIL 對應（每10行一個）
        for i in range(10):
            online_row = 14 + i * 10
            rt_row = 114 + i * 10  # RT 區域對應行
            large_debug_log.extend([
                f"FAIL #{i+1}: {online_row}(OnlineEQC) [LEFT_RIGHT_ARROW] {rt_row}(RT)",
                f"  - OnlineEQC第{online_row}行 vs RT第{rt_row}行",
                f"  - 主要區間匹配: 38/38 欄位匹配 (100%)",
                ""
            ])
        
        large_debug_log.extend([
            "CODE 匹配搜尋結果:",
            "總計：10 個匹配",
            "處理完成"
        ])
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_csv:
            temp_csv.writelines(large_csv_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as temp_log:
            temp_log.writelines('\n'.join(large_debug_log))
            temp_log_path = temp_log.name
        
        try:
            # 測量處理時間
            import time
            start_time = time.time()
            
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 驗證性能要求（大型資料集應在合理時間內完成）
            assert processing_time < 30.0, f"大型資料集處理時間過長: {processing_time:.2f}秒"
            
            # 驗證處理結果正確性
            assert result['status'] == 'success'
            assert result['fail_mappings_count'] == 10
            assert result['total_rows'] == len(large_csv_content)
            
            # 驗證記憶體使用效率（檔案大小應該與輸入相當）
            input_size = os.path.getsize(temp_csv_path)
            output_size = os.path.getsize(result['output_file'])
            size_ratio = abs(output_size - input_size) / input_size
            assert size_ratio < 0.1, f"輸出檔案大小變化過大: {size_ratio:.2%}"
            
        finally:
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])
    
    @pytest.mark.skip(reason="API 端點整合尚未實作，預留給後續開發")
    def test_step5_api_integration(self):
        """測試 Step 5 API 端點整合"""
        # 此測試預留給 API 整合階段
        # 當 API 端點實作完成後，移[EXCEPT_CHAR] @pytest.mark.skip
        
        from frontend.api.ft_eqc_api import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # 測試 Step 5 API 端點
        response = client.post("/api/eqc/generate_test_flow", json={
            "doc_directory": "doc/20250523",
            "eqc_file": "EQCTOTALDATA.csv",
            "debug_log_file": "EQCTOTALDATA_Step4_DEBUG.log"
        })
        
        assert response.status_code == 200
        result = response.json()
        assert result['status'] == 'success'
        assert 'output_file' in result
        assert 'total_rows' in result
        assert 'fail_mappings_count' in result


@pytest.mark.integration  
class TestStep5ErrorScenarios:
    """Step 5 錯誤場景整合測試"""
    
    def setup_method(self):
        self.processor = EQCStep5TestFlowProcessor()
    
    def test_step5_with_corrupted_csv(self):
        """測試 Step 5 處理損壞的 CSV 檔案"""
        # 建立損壞的 CSV（欄位數量不一致）
        corrupted_csv = [
            "HEADER1,HEADER2,HEADER3\n",
            "A,B\n",  # 缺少欄位
            "C,D,E,F\n",  # 多出欄位
            "正常行,資料1,資料2\n"
        ]
        
        valid_debug_log = [
            "EQC Step 4 DEBUG LOG",
            "FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)"
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as temp_csv:
            temp_csv.writelines(corrupted_csv)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(valid_debug_log))
            temp_log_path = temp_log.name
        
        try:
            # 應該能處理但可能產生警告
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            # 應該有狀態回應（成功或錯誤都可接受，但要有適當處理）
            assert 'status' in result
            
            if result['status'] == 'success':
                # 如果成功處理，驗證基本輸出
                assert os.path.exists(result['output_file'])
            else:
                # 如果錯誤，應該有中文錯誤訊息
                assert 'error_message' in result
                assert any(char in result['error_message'] for char in ['錯誤', '失敗', '問題'])
                
        finally:
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and result.get('status') == 'success' and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])
    
    def test_step5_with_malformed_debug_log(self):
        """測試 Step 5 處理格式錯誤的 DEBUG LOG"""
        valid_csv = [
            "HEADER1,HEADER2,HEADER3\n",
            "A,B,C\n" * 20  # 20行資料
        ]
        
        # 格式完全錯誤的 DEBUG LOG
        malformed_debug_log = [
            "這不是標準的DEBUG LOG格式",
            "包含中文但沒有FAIL資訊",
            "random text without any pattern",
            "FAIL 但是沒有正確格式",
            "15 OnlineEQC 37 RT 但是沒有箭頭符號"
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as temp_csv:
            temp_csv.writelines(valid_csv)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(malformed_debug_log))
            temp_log_path = temp_log.name
        
        try:
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            # 應該能處理（保持原始順序）或給出適當錯誤
            assert 'status' in result
            
            if result['status'] == 'success':
                # 無法解析 FAIL 對應時，應該保持原始順序
                assert result['fail_mappings_count'] == 0
                
                # 輸出檔案應該與輸入相同（只是重新寫入）
                with open(result['output_file'], 'r', encoding='utf-8') as f:
                    output_content = f.readlines()
                assert len(output_content) == len(valid_csv)
                
        finally:
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and result.get('status') == 'success' and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])