"""
企業級 LINE 通知服務
提供高級通知功能：批量通知、通知範本、統計管理等
與現有 LineNotificationService 整合，提供企業級功能擴展
"""

import time
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from threading import RLock
import queue
import threading

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from ..line_notification_service import LineNotificationService

# 可選導入 ConfigManager 和 ConcurrentTaskManager
try:
    from backend.shared.infrastructure.config.config_manager import ConfigManager
except ImportError:
    ConfigManager = None

try:
    from backend.services.concurrent_task_manager import ConcurrentTaskManager
except ImportError:
    ConcurrentTaskManager = None
from ..models.notification_models import (
    NotificationRequest, NotificationResult, NotificationStatus,
    NotificationPriority, NotificationType, NotificationChannel,
    NotificationStatistics, NotificationHistory, NotificationConfig, NotificationQueue,
    NotificationRecipient, NotificationTemplate, NotificationRule,
    create_parsing_failure_notification, create_parsing_success_notification
)


class EnterpriseLineService:
    """
    企業級 LINE 通知服務
    整合現有 LineNotificationService，提供企業級功能擴展
    """
    
    def __init__(
        self,
        config_manager = None,
        logger_manager: Optional[LoggerManager] = None,
        base_service: Optional[LineNotificationService] = None
    ):
        """
        初始化企業級 LINE 通知服務
        
        Args:
            config_manager: 配置管理器
            logger_manager: 日誌管理器  
            base_service: 基礎 LINE 通知服務
        """
        # 修復配置管理器初始化邏輯
        if config_manager:
            self.config_manager = config_manager
        elif ConfigManager:
            try:
                self.config_manager = ConfigManager()
            except Exception as e:
                self.logger.warning(f"無法初始化 ConfigManager: {e}")
                self.config_manager = None
        else:
            self.config_manager = None
        self.logger_manager = logger_manager or LoggerManager()
        self.logger = self.logger_manager.get_logger("EnterpriseLineService")
        
        # 基礎服務整合
        self.base_service = base_service or LineNotificationService()
        
        # 企業級配置
        self.config = self._load_notification_config()
        
        # 核心資料結構
        self._recipients: Dict[str, NotificationRecipient] = {}
        self._templates: Dict[str, NotificationTemplate] = {}
        self._rules: Dict[str, NotificationRule] = {}
        self._statistics = NotificationStatistics()
        self._history: deque = deque(maxlen=self.config.max_history_records)
        
        # 通知佇列和處理
        self._notification_queue: queue.PriorityQueue = queue.PriorityQueue()
        self._processing_lock = RLock()
        self._rate_limiter = deque(maxlen=self.config.rate_limit_per_minute)
        
        # 執行緒池
        self._executor = ThreadPoolExecutor(
            max_workers=self.config.max_concurrent_notifications,
            thread_name_prefix="EnterpriseNotification"
        )
        
        # 佇列處理器
        self._queue_processor_thread = threading.Thread(
            target=self._process_notification_queue,
            daemon=True,
            name="NotificationQueueProcessor"
        )
        self._running = True
        self._queue_processor_thread.start()
        
        # 載入預設資料
        self._load_default_data()
        
        self.logger.info("企業級 LINE 通知服務已初始化")
    
    def _load_notification_config(self) -> NotificationConfig:
        """載入通知配置"""
        try:
            # 從配置管理器載入，如果沒有則使用預設值
            config_dict = {}
            if (self.config_manager and 
                hasattr(self.config_manager, 'config') and 
                hasattr(self.config_manager.config, 'notification')):
                config_dict = self.config_manager.config.notification.model_dump()
            
            return NotificationConfig(**config_dict)
        except Exception as e:
            self.logger.warning(f"載入通知配置失敗，使用預設配置: {e}")
            return NotificationConfig()
    
    def _load_default_data(self):
        """載入預設資料"""
        try:
            self._load_default_recipients()
            self._load_default_templates()
            self.logger.info("預設資料載入完成")
        except Exception as e:
            self.logger.error(f"載入預設資料失敗: {e}")
    
    def _load_default_recipients(self):
        """載入預設接收者"""
        default_user_id = self.base_service.user_id
        if default_user_id:
            recipient = NotificationRecipient(
                id="default_admin",
                name="系統管理員",
                channel=NotificationChannel.LINE,
                address=default_user_id,
                preferences={"all_notifications": True}
            )
            self._recipients["default_admin"] = recipient
    
    def _load_default_templates(self):
        """載入預設通知範本"""
        # 基本範本，實際範本可從配置載入
        templates = {
            "parsing_failure_default": NotificationTemplate(
                id="parsing_failure_default",
                name="解析失敗通知範本",
                type=NotificationType.PARSING_FAILURE,
                channel=NotificationChannel.LINE,
                subject_template="🚨 郵件解析失敗通知",
                body_template="🚨 解析失敗: {error_message}",
                variables=["error_message"]
            ),
            "parsing_success_default": NotificationTemplate(
                id="parsing_success_default",
                name="解析成功通知範本",
                type=NotificationType.PARSING_SUCCESS,
                channel=NotificationChannel.LINE,
                subject_template="✅ 郵件解析成功通知",
                body_template="✅ 解析成功: {vendor_code}",
                variables=["vendor_code"]
            )
        }
        self._templates.update(templates)
    
    def _process_notification_queue(self):
        """處理通知佇列的背景執行緒"""
        while self._running:
            try:
                try:
                    priority_item = self._notification_queue.get(timeout=1.0)
                    _, queue_item = priority_item
                except queue.Empty:
                    continue
                
                if not queue_item.can_process_now() or not self._check_rate_limit():
                    queue_item.scheduled_at = datetime.now() + timedelta(seconds=10)
                    self._schedule_notification(queue_item)
                    continue
                
                self._process_single_notification(queue_item)
                
            except Exception as e:
                self.logger.error(f"佇列處理器錯誤: {e}")
                time.sleep(1)
    
    def _check_rate_limit(self) -> bool:
        """檢查速率限制"""
        now = datetime.now()
        while self._rate_limiter and (now - self._rate_limiter[0]).seconds >= 60:
            self._rate_limiter.popleft()
        
        if len(self._rate_limiter) >= self.config.rate_limit_per_minute:
            return False
        
        self._rate_limiter.append(now)
        return True
    
    def _schedule_notification(self, queue_item: NotificationQueue):
        """排程通知到佇列"""
        priority_value = {
            NotificationPriority.LOW: 4, NotificationPriority.NORMAL: 3,
            NotificationPriority.HIGH: 2, NotificationPriority.URGENT: 1
        }.get(queue_item.priority, 3)
        self._notification_queue.put((priority_value, queue_item))
    
    def _process_single_notification(self, queue_item: NotificationQueue):
        """處理單一通知"""
        try:
            start_time = time.time()
            request = queue_item.request
            
            recipients = [self._recipients[rid] for rid in request.recipients 
                         if rid in self._recipients]
            
            if not recipients:
                self.logger.error(f"通知 {request.id} 沒有有效的接收者")
                return
            
            message = self._render_message(request)
            results = []
            
            for recipient in recipients:
                if recipient.channel == NotificationChannel.LINE:
                    result = self._send_line_notification(request, recipient, message)
                    results.append(result)
            
            processing_time = (time.time() - start_time) * 1000
            self._update_statistics(request, results, processing_time)
            
            if self.config.enable_notification_history:
                self._record_history(request, results, processing_time)
                
        except Exception as e:
            self.logger.error(f"處理通知失敗 {queue_item.request.id}: {e}")
            queue_item.retry_count += 1
            
            if queue_item.should_retry(self.config.max_retry_attempts):
                retry_delay = self.config.retry_delay_seconds * (queue_item.retry_count ** 2)
                queue_item.scheduled_at = datetime.now() + timedelta(seconds=retry_delay)
                queue_item.last_retry_at = datetime.now()
                self._schedule_notification(queue_item)
    
    def _render_message(self, request: NotificationRequest) -> str:
        """渲染通知訊息"""
        if request.template_id and request.template_id in self._templates:
            template = self._templates[request.template_id]
            return self._render_template(template, request.variables)
        else:
            return request.message
    
    def _render_template(self, template: NotificationTemplate, variables: Dict[str, Any]) -> str:
        """渲染範本"""
        try:
            return template.body_template.format(**variables)
        except KeyError as e:
            self.logger.warning(f"範本變數缺失: {e}")
            return template.body_template
        except Exception as e:
            self.logger.error(f"範本渲染失敗: {e}")
            return template.body_template
    
    def _send_line_notification(
        self, request: NotificationRequest, recipient: NotificationRecipient, message: str
    ) -> NotificationResult:
        """發送 LINE 通知"""
        try:
            start_time = time.time()
            
            # 相容現有服務的郵件資料格式
            email_data = request.metadata.get('email_data', {})
            email_data.update({
                'id': email_data.get('id', request.id),
                'subject': request.subject or 'N/A'
            })
            
            # 使用基礎服務發送
            if request.type == NotificationType.PARSING_FAILURE:
                success = self.base_service.send_parsing_failure_notification(email_data)
            elif request.type == NotificationType.PARSING_SUCCESS:
                success = self.base_service.send_parsing_success_notification(email_data)
            else:
                success = self.base_service._send_message(message)
            
            return NotificationResult(
                request_id=request.id,
                recipient_id=recipient.id,
                channel=NotificationChannel.LINE,
                status=NotificationStatus.SENT if success else NotificationStatus.FAILED,
                sent_at=datetime.now() if success else None,
                error_message=None if success else "發送失敗",
                processing_time_ms=(time.time() - start_time) * 1000
            )
            
        except Exception as e:
            return NotificationResult(
                request_id=request.id, recipient_id=recipient.id,
                channel=NotificationChannel.LINE, status=NotificationStatus.FAILED,
                error_message=str(e), processing_time_ms=None
            )
    
    def _update_statistics(self, request: NotificationRequest, results: List[NotificationResult], processing_time: float):
        """更新統計資料"""
        with self._processing_lock:
            success_count = sum(1 for r in results if r.status == NotificationStatus.SENT)
            failure_count = len(results) - success_count
            
            self._statistics.total_sent += success_count
            self._statistics.total_failed += failure_count
            
            total = self._statistics.total_sent + self._statistics.total_failed
            if total > 0:
                self._statistics.success_rate = self._statistics.total_sent / total * 100
            
            # 更新平均處理時間（指數移動平均）
            current_avg = self._statistics.average_processing_time_ms
            self._statistics.average_processing_time_ms = (
                processing_time if current_avg == 0 else (current_avg * 0.9) + (processing_time * 0.1)
            )
            
            # 按類型統計
            if request.type not in self._statistics.statistics_by_type:
                self._statistics.statistics_by_type[request.type] = {'sent': 0, 'failed': 0}
            
            self._statistics.statistics_by_type[request.type]['sent'] += success_count
            self._statistics.statistics_by_type[request.type]['failed'] += failure_count
            self._statistics.last_updated = datetime.now()
    
    def _record_history(self, request: NotificationRequest, results: List[NotificationResult], processing_time: float):
        """記錄通知歷史"""
        success_count = sum(1 for r in results if r.status == NotificationStatus.SENT)
        
        self._history.append(NotificationHistory(
            id=f"hist_{int(time.time())}_{request.id[:8]}",
            request_id=request.id, type=request.type,
            status=NotificationStatus.SENT if len(results) == success_count else NotificationStatus.FAILED,
            recipient_count=len(results), success_count=success_count,
            failure_count=len(results) - success_count,
            total_processing_time_ms=processing_time,
            created_at=request.created_at, completed_at=datetime.now()
        ))
    
    # 公開 API 方法
    def send_notification(self, request: NotificationRequest) -> str:
        """
        發送通知
        
        Args:
            request: 通知請求
            
        Returns:
            str: 通知ID
        """
        try:
            # 驗證請求
            self._validate_notification_request(request)
            
            # 建立佇列項目
            queue_item = NotificationQueue(
                id=f"queue_{int(time.time())}_{request.id[:8]}",
                request=request,
                priority=request.priority,
                scheduled_at=request.scheduled_at or datetime.now()
            )
            
            # 加入佇列
            self._schedule_notification(queue_item)
            
            self.logger.info(f"通知已加入佇列: {request.id}")
            return request.id
            
        except Exception as e:
            self.logger.error(f"發送通知失敗: {e}")
            raise
    
    def send_parsing_failure_notification(self, email_data: Dict[str, Any]) -> str:
        """
        發送解析失敗通知（企業級版本）
        
        Args:
            email_data: 郵件資料
            
        Returns:
            str: 通知ID
        """
        request = create_parsing_failure_notification(
            email_data=email_data,
            recipients=["default_admin"]  # 使用預設管理員
        )
        return self.send_notification(request)
    
    def send_parsing_success_notification(self, email_data: Dict[str, Any]) -> str:
        """
        發送解析成功通知（企業級版本）
        
        Args:
            email_data: 郵件資料
            
        Returns:
            str: 通知ID
        """
        request = create_parsing_success_notification(
            email_data=email_data,
            recipients=["default_admin"]  # 使用預設管理員
        )
        return self.send_notification(request)
    
    def _validate_notification_request(self, request: NotificationRequest):
        """驗證通知請求"""
        if not request.recipients:
            raise ValueError("通知請求必須包含至少一個接收者")
        if not request.message and not request.template_id:
            raise ValueError("通知請求必須包含訊息或範本ID")
        
        for recipient_id in request.recipients:
            if recipient_id not in self._recipients:
                raise ValueError(f"接收者不存在: {recipient_id}")
        
        if request.template_id and request.template_id not in self._templates:
            raise ValueError(f"範本不存在: {request.template_id}")
    
    def get_statistics(self) -> NotificationStatistics:
        """取得通知統計資料"""
        return self._statistics
    
    def get_history(self, limit: int = 50) -> List[NotificationHistory]:
        """取得通知歷史"""
        return list(self._history)[-limit:]
    
    def add_recipient(self, recipient: NotificationRecipient):
        """新增接收者"""
        self._recipients[recipient.id] = recipient
        self.logger.info(f"新增接收者: {recipient.id}")
    
    def add_template(self, template: NotificationTemplate):
        """新增通知範本"""
        self._templates[template.id] = template
        self.logger.info(f"新增通知範本: {template.id}")
    
    def shutdown(self):
        """關閉服務"""
        self.logger.info("正在關閉企業級 LINE 通知服務...")
        self._running = False
        
        if self._queue_processor_thread.is_alive():
            self._queue_processor_thread.join(timeout=5)
        
        self._executor.shutdown(wait=True)
        self.logger.info("企業級 LINE 通知服務已關閉")