"""
基礎解析器架構測試
TASK_005: 建立基礎解析器架構
遵循 TDD 原則 - 先寫失敗的測試
"""

import pytest
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, MagicMock
from abc import ABC, abstractmethod

from backend.email.parsers.base_parser import (
    BaseParser,
    ParserFactory,
    ParserRegistry,
    ParsingStrategy,
    ParsingError,
    VendorParser,
    ParsingContext
)
from backend.email.models.email_models import (
    EmailData,
    VendorIdentificationResult,
    EmailParsingResult,
    ProcessingStatus
)


class TestParsingStrategy:
    """測試解析策略枚舉"""

    def test_parsing_strategy_values(self):
        """測試解析策略值"""
        assert ParsingStrategy.SUBJECT_PATTERN.value == "subject_pattern"
        assert ParsingStrategy.BODY_CONTENT.value == "body_content"
        assert ParsingStrategy.SENDER_ANALYSIS.value == "sender_analysis"
        assert ParsingStrategy.ATTACHMENT_BASED.value == "attachment_based"
        assert ParsingStrategy.HYBRID.value == "hybrid"


class TestParsingError:
    """測試解析錯誤類別"""

    def test_parsing_error_creation(self):
        """測試解析錯誤建立"""
        error = ParsingError(
            message="解析失敗",
            error_code="PARSE_001",
            vendor_code="GTK",
            context_data={"subject": "invalid format"}
        )
        
        assert error.message == "解析失敗"
        assert error.error_code == "PARSE_001"
        assert error.vendor_code == "GTK"
        assert error.context_data["subject"] == "invalid format"

    def test_parsing_error_str_representation(self):
        """測試解析錯誤字串表示"""
        error = ParsingError("測試錯誤", "TEST_001")
        error_str = str(error)
        assert "測試錯誤" in error_str
        assert "TEST_001" in error_str


class TestParsingContext:
    """測試解析上下文"""

    def test_parsing_context_creation(self):
        """測試解析上下文建立"""
        email = EmailData(
            message_id="<EMAIL>",
            subject="GTK FT HOLD MO:F123456",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="GTK",
            parsing_strategy=ParsingStrategy.SUBJECT_PATTERN
        )
        
        assert context.email_data.subject == "GTK FT HOLD MO:F123456"
        assert context.vendor_code == "GTK"
        assert context.parsing_strategy == ParsingStrategy.SUBJECT_PATTERN

    def test_parsing_context_with_metadata(self):
        """測試包含元數據的解析上下文"""
        email = EmailData(
            message_id="<EMAIL>",
            subject="Test Subject",
            sender="<EMAIL>", 
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="TEST",
            parsing_strategy=ParsingStrategy.HYBRID,
            metadata={"priority": "high", "source": "automated"}
        )
        
        assert context.metadata["priority"] == "high"
        assert context.metadata["source"] == "automated"


class TestBaseParser:
    """測試基礎解析器"""

    def test_base_parser_is_abstract(self):
        """測試基礎解析器是抽象類別"""
        with pytest.raises(TypeError):
            BaseParser()

    def test_base_parser_interface(self):
        """測試基礎解析器介面"""
        # 建立測試實作
        class TestParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "TEST"
            
            @property
            def vendor_name(self) -> str:
                return "Test Vendor"
            
            @property
            def supported_patterns(self) -> list:
                return ["test pattern"]
            
            def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
                return VendorIdentificationResult(
                    vendor_code="TEST",
                    vendor_name="Test Vendor",
                    confidence_score=0.9,
                    matching_patterns=["test"],
                    is_identified=True
                )
            
            def parse_email(self, context: ParsingContext) -> EmailParsingResult:
                return EmailParsingResult(
                    is_success=True,
                    vendor_code="TEST",
                    mo_number="T123456"
                )
        
        parser = TestParser()
        assert parser.vendor_code == "TEST"
        assert parser.vendor_name == "Test Vendor"
        assert parser.supported_patterns == ["test pattern"]

    def test_base_parser_can_parse_method(self):
        """測試基礎解析器 can_parse 方法"""
        class TestParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "TEST"
            
            @property
            def vendor_name(self) -> str:
                return "Test Vendor"
            
            @property
            def supported_patterns(self) -> list:
                return ["<EMAIL>", "TEST SUBJECT"]
            
            def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
                # 檢查寄件者和主旨
                sender_match = any(pattern in email_data.sender for pattern in self.supported_patterns)
                subject_match = any(pattern.lower() in email_data.subject.lower() for pattern in self.supported_patterns)
                
                confidence = 0.0
                if sender_match:
                    confidence += 0.6
                if subject_match:
                    confidence += 0.4
                
                return VendorIdentificationResult(
                    vendor_code=self.vendor_code if confidence > 0.5 else None,
                    vendor_name=self.vendor_name if confidence > 0.5 else None,
                    confidence_score=confidence,
                    matching_patterns=[],
                    is_identified=confidence > 0.5
                )
            
            def parse_email(self, context: ParsingContext) -> EmailParsingResult:
                return EmailParsingResult(
                    is_success=True,
                    vendor_code="TEST"
                )
        
        parser = TestParser()
        
        # 測試可以解析的郵件
        email1 = EmailData(
            message_id="1",
            subject="Test Subject Content",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        assert parser.can_parse(email1) is True
        
        # 測試不能解析的郵件
        email2 = EmailData(
            message_id="2", 
            subject="Unknown Format",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        assert parser.can_parse(email2) is False


class TestVendorParser:
    """測試廠商解析器基類"""

    def test_vendor_parser_abstract_methods(self):
        """測試廠商解析器抽象方法"""
        with pytest.raises(TypeError):
            VendorParser()

    def test_vendor_parser_implementation(self):
        """測試廠商解析器實作"""
        class GTKParser(VendorParser):
            @property
            def vendor_code(self) -> str:
                return "GTK"
            
            @property
            def vendor_name(self) -> str:
                return "Greatek Technology"
            
            @property
            def supported_patterns(self) -> list:
                return ["@gtk.com", "ft hold", "mo:"]
            
            def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
                patterns_found = []
                confidence = 0.0
                
                if "@gtk.com" in email_data.sender:
                    patterns_found.append("@gtk.com")
                    confidence += 0.5
                
                if "ft hold" in email_data.subject.lower():
                    patterns_found.append("ft hold")
                    confidence += 0.3
                
                if "mo:" in email_data.subject.lower():
                    patterns_found.append("mo:")
                    confidence += 0.2
                
                return VendorIdentificationResult(
                    vendor_code=self.vendor_code if confidence >= 0.5 else None,
                    vendor_name=self.vendor_name if confidence >= 0.5 else None,
                    confidence_score=confidence,
                    matching_patterns=patterns_found,
                    is_identified=confidence >= 0.5
                )
            
            def parse_email(self, context: ParsingContext) -> EmailParsingResult:
                import re
                subject = context.email_data.subject
                
                # 解析 MO 編號
                mo_match = re.search(r'MO:([A-Z]\d{6})', subject)
                mo_number = mo_match.group(1) if mo_match else None
                
                # 解析 LOT 編號  
                lot_match = re.search(r'LOT:([A-Z0-9.]+)', subject)
                lot_number = lot_match.group(1) if lot_match else None
                
                return EmailParsingResult(
                    is_success=bool(mo_number),
                    vendor_code=self.vendor_code,
                    mo_number=mo_number,
                    lot_number=lot_number,
                    extracted_data={"test_type": "FT"} if "FT" in subject else {}
                )
        
        parser = GTKParser()
        
        # 測試廠商識別
        email = EmailData(
            message_id="test",
            subject="GTK FT HOLD MO:F123456 LOT:ABC.1",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        
        vendor_result = parser.identify_vendor(email)
        assert vendor_result.is_identified is True
        assert vendor_result.vendor_code == "GTK"
        assert vendor_result.confidence_score >= 0.5
        
        # 測試解析
        context = ParsingContext(
            email_data=email,
            vendor_code="GTK",
            parsing_strategy=ParsingStrategy.SUBJECT_PATTERN
        )
        
        parsing_result = parser.parse_email(context)
        assert parsing_result.is_success is True
        assert parsing_result.mo_number == "F123456"
        assert parsing_result.lot_number == "ABC.1"


class TestParserRegistry:
    """測試解析器註冊表"""

    def setup_method(self):
        """每個測試前清理註冊表"""
        registry = ParserRegistry()
        registry.clear()

    def test_parser_registry_singleton(self):
        """測試解析器註冊表單例模式"""
        registry1 = ParserRegistry()
        registry2 = ParserRegistry()
        assert registry1 is registry2

    def test_parser_registration(self):
        """測試解析器註冊"""
        registry = ParserRegistry()
        
        # 建立測試解析器
        class TestParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "TEST"
            
            @property
            def vendor_name(self) -> str:
                return "Test Vendor"
            
            @property
            def supported_patterns(self) -> list:
                return ["test"]
            
            def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
                return VendorIdentificationResult(
                    vendor_code="TEST",
                    vendor_name="Test",
                    confidence_score=0.9,
                    matching_patterns=[],
                    is_identified=True
                )
            
            def parse_email(self, context: ParsingContext) -> EmailParsingResult:
                return EmailParsingResult(
                    is_success=True,
                    vendor_code="TEST"
                )
        
        parser = TestParser()
        
        # 註冊解析器
        registry.register_parser("TEST", parser)
        
        # 檢查註冊
        assert registry.is_registered("TEST") is True
        assert registry.get_parser("TEST") is parser
        assert "TEST" in registry.get_registered_vendors()

    def test_parser_unregistration(self):
        """測試解析器取消註冊"""
        registry = ParserRegistry()
        
        # 建立有效的測試解析器
        class TempParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "TEMP"
            
            @property
            def vendor_name(self) -> str:
                return "Temp"
            
            @property
            def supported_patterns(self) -> list:
                return ["temp"]
            
            def identify_vendor(self, email_data) -> VendorIdentificationResult:
                return VendorIdentificationResult(
                    vendor_code="TEMP", vendor_name="Temp",
                    confidence_score=0.9, matching_patterns=[], is_identified=True
                )
            
            def parse_email(self, context) -> EmailParsingResult:
                return EmailParsingResult(is_success=True, vendor_code="TEMP")
        
        # 先註冊後取消註冊
        temp_parser = TempParser()
        registry.register_parser("TEMP", temp_parser)
        assert registry.is_registered("TEMP") is True
        
        registry.unregister_parser("TEMP")
        assert registry.is_registered("TEMP") is False

    def test_parser_registry_clear(self):
        """測試清空註冊表"""
        registry = ParserRegistry()
        
        # 註冊多個解析器
        for i in range(3):
            class TestParserN(BaseParser):
                def __init__(self, n):
                    self.n = n
                
                @property
                def vendor_code(self) -> str:
                    return f"TEST{self.n}"
                
                @property
                def vendor_name(self) -> str:
                    return f"Test{self.n}"
                
                @property
                def supported_patterns(self) -> list:
                    return [f"test{self.n}"]
                
                def identify_vendor(self, email_data) -> VendorIdentificationResult:
                    return VendorIdentificationResult(
                        vendor_code=f"TEST{self.n}", vendor_name=f"Test{self.n}",
                        confidence_score=0.9, matching_patterns=[], is_identified=True
                    )
                
                def parse_email(self, context) -> EmailParsingResult:
                    return EmailParsingResult(is_success=True, vendor_code=f"TEST{self.n}")
            
            registry.register_parser(f"TEST{i}", TestParserN(i))
        
        assert len(registry.get_registered_vendors()) >= 3
        
        # 清空
        registry.clear()
        assert len(registry.get_registered_vendors()) == 0

    def test_parser_registry_error_handling(self):
        """測試註冊表錯誤處理"""
        registry = ParserRegistry()
        
        # 測試取得不存在的解析器
        with pytest.raises(ValueError):
            registry.get_parser("NONEXISTENT")
        
        # 建立有效解析器用於重複註冊測試
        class DuplicateParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "DUPLICATE"
            
            @property
            def vendor_name(self) -> str:
                return "Duplicate"
            
            @property
            def supported_patterns(self) -> list:
                return ["duplicate"]
            
            def identify_vendor(self, email_data) -> VendorIdentificationResult:
                return VendorIdentificationResult(
                    vendor_code="DUPLICATE", vendor_name="Duplicate",
                    confidence_score=0.9, matching_patterns=[], is_identified=True
                )
            
            def parse_email(self, context) -> EmailParsingResult:
                return EmailParsingResult(is_success=True, vendor_code="DUPLICATE")
        
        # 測試重複註冊
        duplicate_parser = DuplicateParser()
        registry.register_parser("DUPLICATE", duplicate_parser)
        
        with pytest.raises(ValueError):
            registry.register_parser("DUPLICATE", duplicate_parser)


class TestParserFactory:
    """測試解析器工廠"""

    def setup_method(self):
        """每個測試前清理註冊表"""
        registry = ParserRegistry()
        registry.clear()

    def test_parser_factory_vendor_identification(self):
        """測試解析器工廠廠商識別"""
        factory = ParserFactory()
        
        # 建立測試解析器
        class MockParser(BaseParser):
            def __init__(self, vendor_code: str, confidence: float):
                self._vendor_code = vendor_code
                self._confidence = confidence
            
            @property
            def vendor_code(self) -> str:
                return self._vendor_code
            
            @property
            def vendor_name(self) -> str:
                return f"{self._vendor_code} Vendor"
            
            @property
            def supported_patterns(self) -> list:
                return [f"@{self._vendor_code.lower()}.com"]
            
            def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
                # 檢查是否匹配支援的模式
                patterns_match = any(pattern in email_data.sender.lower() for pattern in self.supported_patterns)
                actual_confidence = self._confidence if patterns_match else 0.1
                
                return VendorIdentificationResult(
                    vendor_code=self._vendor_code if actual_confidence > 0.5 else None,
                    vendor_name=self.vendor_name if actual_confidence > 0.5 else None,
                    confidence_score=actual_confidence,
                    matching_patterns=self.supported_patterns if patterns_match else [],
                    is_identified=actual_confidence > 0.5
                )
            
            def parse_email(self, context: ParsingContext) -> EmailParsingResult:
                return EmailParsingResult(
                    is_success=True,
                    vendor_code=self._vendor_code
                )
        
        # 註冊解析器
        factory.registry.register_parser("GTK", MockParser("GTK", 0.9))
        factory.registry.register_parser("ETD", MockParser("ETD", 0.3))
        
        # 測試廠商識別 (確保 GTK 解析器會匹配)
        email = EmailData(
            message_id="test",
            subject="Test Email",
            sender="<EMAIL>",  # 這會匹配 GTK 的 @gtk.com 模式
            received_time=datetime.now()
        )
        
        best_parser, vendor_result = factory.identify_vendor(email)
        assert best_parser is not None
        assert vendor_result.vendor_code == "GTK"
        assert vendor_result.confidence_score == 0.9

    def test_parser_factory_no_match(self):
        """測試解析器工廠無匹配情況"""
        factory = ParserFactory()
        
        email = EmailData(
            message_id="test",
            subject="Unknown Format",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        
        best_parser, vendor_result = factory.identify_vendor(email)
        assert best_parser is None
        assert vendor_result.is_identified is False

    def test_parser_factory_get_parser(self):
        """測試解析器工廠取得解析器"""
        factory = ParserFactory()
        
        # 建立有效的測試解析器
        class GetTestParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "GETTEST"
            
            @property
            def vendor_name(self) -> str:
                return "Get Test"
            
            @property
            def supported_patterns(self) -> list:
                return ["gettest"]
            
            def identify_vendor(self, email_data) -> VendorIdentificationResult:
                return VendorIdentificationResult(
                    vendor_code="GETTEST", vendor_name="Get Test",
                    confidence_score=0.9, matching_patterns=[], is_identified=True
                )
            
            def parse_email(self, context) -> EmailParsingResult:
                return EmailParsingResult(is_success=True, vendor_code="GETTEST")
        
        # 註冊測試解析器
        test_parser = GetTestParser()
        factory.registry.register_parser("GETTEST", test_parser)
        
        # 測試取得解析器
        parser = factory.get_parser("GETTEST")
        assert parser is test_parser

    def test_parser_factory_parse_email(self):
        """測試解析器工廠解析郵件"""
        factory = ParserFactory()
        
        # 建立完整的測試解析器
        class FullTestParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "FULL"
            
            @property
            def vendor_name(self) -> str:
                return "Full Test Vendor"
            
            @property
            def supported_patterns(self) -> list:
                return ["@full.com", "FULL"]
            
            def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
                is_match = "@full.com" in email_data.sender or "FULL" in email_data.subject
                return VendorIdentificationResult(
                    vendor_code="FULL" if is_match else None,
                    vendor_name="Full Test Vendor" if is_match else None,
                    confidence_score=0.95 if is_match else 0.1,
                    matching_patterns=["@full.com"] if is_match else [],
                    is_identified=is_match
                )
            
            def parse_email(self, context: ParsingContext) -> EmailParsingResult:
                return EmailParsingResult(
                    is_success=True,
                    vendor_code="FULL",
                    mo_number="F123456",
                    extracted_data={"parsed": True}
                )
        
        parser = FullTestParser()
        factory.registry.register_parser("FULL", parser)
        
        # 測試完整解析流程
        email = EmailData(
            message_id="test",
            subject="FULL Test Email",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        
        vendor_result, parsing_result = factory.parse_email(email)
        
        assert vendor_result.is_identified is True
        assert vendor_result.vendor_code == "FULL"
        assert parsing_result.is_success is True
        assert parsing_result.mo_number == "F123456"

    def test_parser_factory_parse_email_failure(self):
        """測試解析器工廠解析失敗"""
        factory = ParserFactory()
        
        # 註冊一個不會匹配的解析器
        class FailureTestParser(BaseParser):
            @property
            def vendor_code(self) -> str:
                return "FAILTEST"
            
            @property
            def vendor_name(self) -> str:
                return "Fail Test"
            
            @property
            def supported_patterns(self) -> list:
                return ["@failtest.com"]
            
            def identify_vendor(self, email_data) -> VendorIdentificationResult:
                # 只有 @failtest.com 的郵件才會被識別
                is_match = "@failtest.com" in email_data.sender
                return VendorIdentificationResult(
                    vendor_code="FAILTEST" if is_match else None,
                    vendor_name="Fail Test" if is_match else None,
                    confidence_score=0.9 if is_match else 0.1,
                    matching_patterns=["@failtest.com"] if is_match else [],
                    is_identified=is_match
                )
            
            def parse_email(self, context) -> EmailParsingResult:
                return EmailParsingResult(is_success=True, vendor_code="FAILTEST")
        
        factory.registry.register_parser("FAILTEST", FailureTestParser())
        
        # 測試無法識別廠商的情況 (使用不匹配的郵件)
        email = EmailData(
            message_id="test",
            subject="Unknown Email",
            sender="<EMAIL>",  # 不匹配任何註冊的解析器
            received_time=datetime.now()
        )
        
        vendor_result, parsing_result = factory.parse_email(email)
        
        assert vendor_result.is_identified is False
        assert parsing_result.is_success is False
        assert parsing_result.error_message is not None