"""
統一 LLM 客戶端
支持 Ollama 和 Grok，通過 .env 配置切換
整合方法2的分類器規則，提供統一的解析接口
"""

import json
import os
import re
import requests
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from dotenv import load_dotenv

# 加載環境變數
load_dotenv()

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.shared.infrastructure.adapters.llm.grok_client import GrokClient, GrokRequest


class LLMProvider(Enum):
    """LLM 提供者類型"""
    OLLAMA = "ollama"
    GROK = "grok"


@dataclass
class UnifiedLLMResult:
    """統一 LLM 解析結果"""
    vendor_code: Optional[str] = None
    vendor_name: Optional[str] = None
    product_code: Optional[str] = None
    lot_number: Optional[str] = None
    mo_number: Optional[str] = None
    yield_rate: Optional[float] = None
    test_batch: Optional[str] = None
    device_type: Optional[str] = None
    quantity: Optional[str] = None
    
    # 解析詳情
    confidence_score: float = 0.0
    extraction_method: str = "unified_llm"
    parsing_methods_used: List[str] = None
    analysis_reasoning: str = ""
    raw_response: Optional[str] = None
    error_message: Optional[str] = None
    is_success: bool = False
    
    # 服務資訊
    llm_provider: str = ""
    llm_model: str = ""
    
    def __post_init__(self):
        if self.parsing_methods_used is None:
            self.parsing_methods_used = []


class UnifiedLLMClient:
    """統一 LLM 客戶端 - 單例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(UnifiedLLMClient, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 避免重複初始化
        if self._initialized:
            return
            
        self.logger = LoggerManager().get_logger("UnifiedLLMClient")
        
        # 從環境變數讀取配置
        provider_str = os.getenv('LLM_PROVIDER', 'ollama').lower()
        self.provider = LLMProvider.OLLAMA if provider_str == 'ollama' else LLMProvider.GROK
        
        # 初始化對應的客戶端
        if self.provider == LLMProvider.GROK:
            self.grok_client = GrokClient()
            self.model = os.getenv('GROK_MODEL', 'grok-beta')
            self.client_info = {
                'provider': 'Grok',
                'model': self.model,
                'available': self.grok_client.is_enabled()
            }
        else:
            self.ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
            self.model = os.getenv('OLLAMA_MODEL', 'llama3:latest')
            self.timeout = int(os.getenv('LLM_TIMEOUT', '30'))
            self.max_retries = int(os.getenv('LLM_MAX_RETRIES', '3'))
            self.client_info = {
                'provider': 'Ollama',
                'model': self.model,
                'base_url': self.ollama_base_url,
                'available': self._test_ollama_connection()
            }
        
        self.confidence_threshold = float(os.getenv('LLM_CONFIDENCE_THRESHOLD', '0.7'))
        
        self.logger.info(f"統一 LLM 客戶端初始化: 提供者={self.provider.value}, 模型={self.model}")
        self._initialized = True
    
    def _test_ollama_connection(self) -> bool:
        """測試 Ollama 連接"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [m['name'] for m in models]
                return self.model in model_names
            return False
        except Exception:
            return False
    
    def get_client_info(self) -> Dict[str, Any]:
        """獲取客戶端信息"""
        return self.client_info.copy()
    
    def is_available(self) -> bool:
        """檢查服務是否可用"""
        return self.client_info.get('available', False)
    
    def parse_email(self, subject: str, body: str = "", sender: str = "") -> UnifiedLLMResult:
        """統一解析郵件接口"""
        if not self.is_available():
            return UnifiedLLMResult(
                error_message=f"{self.provider.value} 服務不可用",
                is_success=False,
                llm_provider=self.provider.value,
                llm_model=self.model
            )
        
        try:
            # 構建統一提示詞
            prompt = self._build_unified_prompt(subject, body, sender)
            
            # 根據提供者調用對應服務
            if self.provider == LLMProvider.GROK:
                response = self._call_grok(prompt)
            else:
                response = self._call_ollama(prompt)
            
            if not response:
                return UnifiedLLMResult(
                    error_message=f"{self.provider.value} API 調用失敗",
                    is_success=False,
                    llm_provider=self.provider.value,
                    llm_model=self.model
                )
            
            # 解析回應
            result = self._parse_response(response)
            result.llm_provider = self.provider.value
            result.llm_model = self.model
            result.raw_response = response
            
            return result
            
        except Exception as e:
            self.logger.error(f"統一 LLM 解析失敗: {e}")
            return UnifiedLLMResult(
                error_message=str(e),
                is_success=False,
                llm_provider=self.provider.value,
                llm_model=self.model
            )
    
    def _build_unified_prompt(self, subject: str, body: str, sender: str) -> str:
        """構建統一提示詞"""
        # 簡化內容預覽長度
        content_preview = body[:1500] if body else ""
        
        prompt_template = """你是專業的半導體郵件解析專家。分析郵件內容，識別廠商並提取關鍵資訊。

郵件資訊：
- 主題: {subject}
- 寄件者: {sender}
- 內容: {content_preview}

支援廠商：
1. JCET - 識別: "jcet", "致新", "chipson"
   - MO: KUI/GYC開頭編號 (如 KUID11M013-D007)
   - 產品: 從內文提取 (如 TNVP163201)
   - LOT: 從內文提取 (如 DNHC0.1)

2. GTK - 識別: "ft hold", "ft lot"
   - 格式: "Type: product, MO: mo, LOT: lot"

3. ETD - 識別: "anf"
   - ANF格式: 斜線分隔解析

4. LINGSEN - 識別: "lingsen", "lowyield"
   - 產品: G開頭代碼
   - MO: Run#後數字
   - LOT: Lot#後編號

5. XAHT - 識別: "tianshui", "西安"

回應JSON格式：
{{
  "vendor_code": "廠商代碼",
  "vendor_name": "廠商名稱",
  "product_code": "產品型號或null",
  "lot_number": "LOT編號或null",
  "mo_number": "MO編號或null",
  "yield_rate": "數字或null",
  "confidence_score": 0.95,
  "parsing_methods_used": ["解析方法"],
  "analysis_reasoning": "分析推理",
  "extraction_success": true
}}"""
        
        final_prompt = prompt_template.format(
            subject=subject,
            sender=sender,
            content_preview=content_preview
        )
        
        return final_prompt
    
    def _call_grok(self, prompt: str) -> Optional[str]:
        """調用 Grok API"""
        try:
            request = GrokRequest(
                prompt=prompt,
                temperature=0.1,
                max_tokens=2000
            )
            
            response = self.grok_client.send_request(request)
            
            if response.success:
                return response.content
            else:
                self.logger.error(f"Grok API 錯誤: {response.error}")
                return None
                
        except Exception as e:
            self.logger.error(f"Grok API 調用失敗: {e}")
            return None
    
    def _call_ollama(self, prompt: str) -> Optional[str]:
        """調用 Ollama API"""
        url = f"{self.ollama_base_url}/api/generate"
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "top_p": 0.9,
                "num_predict": 500
            }
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    url,
                    json=payload,
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get('response', '').strip()
                else:
                    self.logger.warning(f"Ollama API 錯誤 {response.status_code}: {response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Ollama API 超時 (嘗試 {attempt + 1}/{self.max_retries})")
            except Exception as e:
                self.logger.error(f"Ollama API 調用失敗: {e}")
                
            if attempt < self.max_retries - 1:
                time.sleep(2 ** attempt)
        
        return None
    
    def _parse_response(self, response: str) -> UnifiedLLMResult:
        """解析 LLM 回應"""
        try:
            # 提取 JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                raise ValueError("未找到 JSON 格式回應")
            
            json_str = json_match.group(0)
            data = json.loads(json_str)
            
            # 構建結果
            result = UnifiedLLMResult(
                vendor_code=data.get('vendor_code'),
                vendor_name=data.get('vendor_name'),
                product_code=data.get('product_code'),
                lot_number=data.get('lot_number'),
                mo_number=data.get('mo_number'),
                yield_rate=data.get('yield_rate'),
                test_batch=data.get('test_batch'),
                device_type=data.get('device_type'),
                quantity=data.get('quantity'),
                confidence_score=data.get('confidence_score', 0.0),
                parsing_methods_used=data.get('parsing_methods_used', []),
                analysis_reasoning=data.get('analysis_reasoning', ''),
                extraction_method="unified_llm"
            )
            
            # 判斷成功條件
            extraction_success = data.get('extraction_success', False)
            has_core_data = bool(result.vendor_code and (result.product_code or result.lot_number or result.mo_number))
            meets_confidence = result.confidence_score >= self.confidence_threshold
            
            result.is_success = extraction_success and has_core_data and meets_confidence
            
            if not result.is_success:
                result.error_message = f"解析不完整: 成功={extraction_success}, 核心資料={has_core_data}, 信心分數={result.confidence_score}"
            
            return result
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON 解析失敗: {e}")
            return UnifiedLLMResult(
                error_message=f"JSON 解析錯誤: {e}",
                is_success=False
            )
        except Exception as e:
            self.logger.error(f"回應解析失敗: {e}")
            return UnifiedLLMResult(
                error_message=str(e),
                is_success=False
            )