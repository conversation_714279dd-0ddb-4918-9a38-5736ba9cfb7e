#!/usr/bin/env python3
"""
測試 Summary 工作表生成器 - TDD 驅動開發
基於實際 KDD 資料進行測試
"""

import pytest
import pandas as pd
from unittest.mock import MagicMock
import sys
import os

# 添加 src 路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

from infrastructure.adapters.excel.summary_generator import SummaryGenerator


class TestSummaryGenerator:
    """Summary 工作表生成器測試 - 基於實際 KDD 資料"""
    
    @pytest.fixture
    def kdd_real_data(self):
        """
        實際 KDD 資料 - 基於 fixed_test.py 執行結果
        模擬 CSV 轉換後的 DataFrame 結構 (22行 x 1694欄)
        """
        # 建立基本 DataFrame 框架 (22行，包含設備資料)
        num_rows = 22
        num_cols = 7  # 簡化測試：只需要關鍵欄位
        
        # 初始化空的 DataFrame
        df = pd.DataFrame(index=range(num_rows), columns=range(num_cols))
        
        # 設定欄位名稱 (第8行，index 7)
        df.iloc[7, 0] = 'Test_Time'
        df.iloc[7, 1] = 'Index_Time' 
        df.iloc[7, 2] = 'Site_No'
        df.iloc[7, 3] = 'xvi_LXB1_13FR'  # 對應 BIN 102
        df.iloc[7, 4] = '0X50(w0x80)'    # 對應 BIN 298
        df.iloc[7, 5] = 'LS_OSC'         # 對應 BIN 601
        df.iloc[7, 6] = 'Bin#'           # BIN 分配結果欄
        
        # 設定設備資料 (從第12行開始，index 11-20，共10個設備)
        device_data = [
            # [Test_Time, Index_Time, Site_No, test1, test2, test3, Bin#]
            ['2025-06-05', '001', 1, 1.2, 2.1, 3.1, 1],    # Site 1, BIN 1
            ['2025-06-05', '002', 1, 1.1, 2.2, 3.2, 1],    # Site 1, BIN 1  
            ['2025-06-05', '003', 1, 1.0, 3.5, 3.3, 298],  # Site 1, BIN 298
            ['2025-06-05', '004', 2, 1.3, 2.0, 3.4, 1],    # Site 2, BIN 1
            ['2025-06-05', '005', 2, 1.4, 2.1, 3.5, 1],    # Site 2, BIN 1
            ['2025-06-05', '006', 2, 2.5, 2.2, 3.6, 102],  # Site 2, BIN 102
            ['2025-06-05', '007', 2, 1.2, 3.8, 3.7, 298],  # Site 2, BIN 298
            ['2025-06-05', '008', 2, 1.1, 2.0, 4.2, 1],    # Site 2, BIN 1
            ['2025-06-05', '009', 2, 1.2, 2.1, 3.2, 1],    # Site 2, BIN 1
            ['2025-06-05', '010', 2, 1.3, 2.2, 3.3, 601]   # Site 2, BIN 601
        ]
        
        # 填入設備資料
        for i, row_data in enumerate(device_data):
            row_idx = 11 + i  # 從第12行開始 (index 11)
            for col_idx, value in enumerate(row_data):
                df.iloc[row_idx, col_idx] = value
        
        # 設定欄位名稱 (為了 pandas 操作)
        df.columns = ['Test_Time', 'Index_Time', 'Site_No', 'xvi_LXB1_13FR', '0X50(w0x80)', 'LS_OSC', 'Bin#']
        
        return df
    
    @pytest.fixture
    def expected_bin_statistics(self):
        """
        預期 BIN 統計結果 - 基於實際 datalog.txt
        """
        return {
            'total_devices': 10,
            'pass_devices': 6,
            'fail_devices': 4,
            'yield_rate': 0.6,
            'bin_distribution': {
                1: {'count': 6, 'percentage': 0.6},      # BIN 1: 6個 (60%) - PASS
                102: {'count': 1, 'percentage': 0.1},    # BIN 102: 1個 (10%) - FAIL  
                298: {'count': 2, 'percentage': 0.2},    # BIN 298: 2個 (20%) - FAIL
                601: {'count': 1, 'percentage': 0.1}     # BIN 601: 1個 (10%) - FAIL
            },
            'site_statistics': {
                1: {
                    'total': 3,
                    'pass': 2,
                    'fail': 1,
                    'pass_rate': 0.6667,
                    'bins': {1: 2, 298: 1}
                },
                2: {
                    'total': 7, 
                    'pass': 4,
                    'fail': 3,
                    'pass_rate': 0.5714,
                    'bins': {1: 4, 102: 1, 298: 1, 601: 1}
                }
            }
        }
    
    def test_summary_generator_initialization(self):
        """測試 Summary 生成器初始化"""
        # Red: 測試應該失敗 (還沒實作)
        generator = SummaryGenerator()
        assert generator is not None
        assert hasattr(generator, 'generate_summary')
    
    def test_bin_statistics_calculation(self, kdd_real_data, expected_bin_statistics):
        """
        測試 BIN 統計計算 - 核心功能
        對照 VBA_TO_PYTHON_MAPPING.md 要求：
        1. BIN 排序規則：先按 Count 降序，再按 BIN 號碼升序
        2. 完整 BIN 列表顯示 (即使 Count=0)
        """
        # Red: 測試應該失敗
        generator = SummaryGenerator()
        
        # 計算 BIN 統計
        bin_stats = generator.calculate_bin_statistics(kdd_real_data)
        
        # 驗證基本統計
        assert bin_stats['total_devices'] == 10
        assert bin_stats['pass_devices'] == 6  
        assert bin_stats['fail_devices'] == 4
        assert abs(bin_stats['yield_rate'] - 0.6) < 0.001
        
        # 驗證 BIN 分佈 (按 Count 降序排列)
        expected_order = [1, 298, 102, 601]  # Count: 6, 2, 1, 1
        actual_order = list(bin_stats['bin_distribution'].keys())
        assert actual_order == expected_order
        
        # 驗證各 BIN 數量和百分比
        assert bin_stats['bin_distribution'][1]['count'] == 6
        assert abs(bin_stats['bin_distribution'][1]['percentage'] - 0.6) < 0.001
        assert bin_stats['bin_distribution'][298]['count'] == 2
        assert abs(bin_stats['bin_distribution'][298]['percentage'] - 0.2) < 0.001
    
    def test_site_statistics_calculation(self, kdd_real_data, expected_bin_statistics):
        """
        測試 Site 統計計算 - 多 Site 支援
        對照 VBA_TO_PYTHON_MAPPING.md：各 Site 分別統計 BIN 分布
        """
        # Red: 測試應該失敗
        generator = SummaryGenerator()
        
        # 計算 Site 統計 (Site 欄位在 index 2)
        site_stats = generator.calculate_site_statistics(kdd_real_data, site_column=2)
        
        # 驗證 Site 1 統計
        site1 = site_stats[1]
        assert site1['total'] == 3
        assert site1['pass'] == 2
        assert site1['fail'] == 1
        assert abs(site1['pass_rate'] - 0.6667) < 0.001
        assert site1['bins'] == {1: 2, 298: 1}
        
        # 驗證 Site 2 統計  
        site2 = site_stats[2]
        assert site2['total'] == 7
        assert site2['pass'] == 4
        assert site2['fail'] == 3
        assert abs(site2['pass_rate'] - 0.5714) < 0.001
        assert site2['bins'] == {1: 4, 102: 1, 298: 1, 601: 1}
    
    def test_bin_definition_mapping(self, kdd_real_data):
        """
        測試 BIN Definition 對應 - 測試項目名稱映射
        對照 VBA_TO_PYTHON_MAPPING.md：
        - BIN 1: "All Pass" (固定)
        - 其他 BIN: 對應第8行測試項目名稱
        """
        # Red: 測試應該失敗
        generator = SummaryGenerator()
        
        # 獲取 BIN Definition 映射
        bin_definitions = generator.get_bin_definitions(kdd_real_data)
        
        # 驗證 BIN 1 定義
        assert bin_definitions[1] == "All Pass"
        
        # 驗證其他 BIN 定義 (基於欄位名稱)
        assert bin_definitions[102] == "xvi_LXB1_13FR"
        assert bin_definitions[298] == "0X50(w0x80)"
        assert bin_definitions[601] == "LS_OSC"
    
    def test_percentage_calculation_format(self):
        """
        測試百分比計算與格式化
        對照 VBA_TO_PYTHON_MAPPING.md：使用小數點格式 0.6 = 60%
        """
        # Red: 測試應該失敗
        generator = SummaryGenerator()
        
        # 測試百分比計算
        assert abs(generator.calculate_percentage(6, 10) - 0.6) < 0.001
        assert abs(generator.calculate_percentage(2, 3) - 0.6667) < 0.001
        assert generator.calculate_percentage(0, 10) == 0.0
        assert generator.calculate_percentage(5, 0) == 0.0  # 防[EXCEPT_CHAR]零
    
    def test_dynamic_summary_headers_generation(self, kdd_real_data):
        """
        測試動態 Summary 標頭生成 - 多 Site 支援
        對照 VBA_TO_PYTHON_MAPPING.md：每個 Site 佔 2 欄 (Count + %)
        """
        # Red: 測試應該失敗
        generator = SummaryGenerator()
        
        # 生成動態標頭 (2 個 Site)
        headers = generator.generate_summary_headers(kdd_real_data, site_column=2)
        
        expected_headers = [
            'Bin', 'Count', '%', 'Definition', 'Note', 
            'Site 1', '%', 'Site 2', '%'
        ]
        assert headers == expected_headers
    
    def test_complete_summary_generation(self, kdd_real_data):
        """
        測試完整 Summary 工作表生成 - 整合測試
        對照 doc/KDD0530D3.D_sample1.xlsx 範本結構
        """
        # Red: 測試應該失敗
        generator = SummaryGenerator()
        
        # 生成完整 Summary
        summary_data = generator.generate_summary(
            df=kdd_real_data,
            site_column=2,
            csv_file_path="test.csv"
        )
        
        # 驗證 Summary 結構
        assert 'basic_stats' in summary_data
        assert 'headers' in summary_data
        assert 'bin_rows' in summary_data
        assert 'site_total_row' in summary_data
        
        # 驗證基本統計行 (第1-4行)
        basic_stats = summary_data['basic_stats']
        assert basic_stats[0] == ['Total', 10, '', '', '', '', '', '', '']
        assert basic_stats[1] == ['Pass', 6, '', '', '', '', '', '', '']
        assert basic_stats[2] == ['Fail', 4, '', '', '', '', '', '', '']
        assert basic_stats[3] == ['Yield', 0.6, '', '', '', '', '', '', '']
        
        # 驗證 Site 總計行 (第5行)
        site_total = summary_data['site_total_row']
        assert site_total == ['', '', '', '', '', 'Total', 3, 'Total', 7]
        
        # 驗證 BIN 資料行 (按 Count 降序)
        bin_rows = summary_data['bin_rows']
        assert len(bin_rows) >= 4  # 至少包含有資料的 BIN
        
        # 驗證第一行 (BIN 1, Count 最多)
        bin1_row = bin_rows[0]
        assert bin1_row[0] == 1      # BIN 號碼
        assert bin1_row[1] == 6      # Count
        assert abs(bin1_row[2] - 0.6) < 0.001  # 百分比
        assert bin1_row[3] == "All Pass"       # Definition
        assert bin1_row[5] == 2      # Site 1 count
        assert bin1_row[7] == 4      # Site 2 count


if __name__ == "__main__":
    pytest.main([__file__, "-v"])