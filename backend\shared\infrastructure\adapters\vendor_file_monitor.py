"""
廠商檔案監控服務 - 最小侵入性實作
專為廠商檔案下載系統提供監控和診斷功能，不改變現有處理流程

🎯 設計原則:
  - 零侵入: 不修改現有程式碼邏輯
  - 純監控: 只收集資料，不干預處理
  - 漸進式: 為後續最佳化提供資料基礎
  - 高效能: 最小化效能影響
"""

import os
import time
import threading
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from loguru import logger


class FileProcessingStatus(Enum):
    """檔案處理狀態"""
    STARTED = "started"
    DOWNLOADING = "downloading"
    VALIDATING = "validating"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    RETRY = "retry"


@dataclass
class VendorFileMetrics:
    """廠商檔案處理指標"""
    tracking_id: str
    file_path: str
    file_name: str
    file_size: int
    vendor_name: Optional[str] = None
    
    # 時間戳
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 狀態和進度
    status: FileProcessingStatus = FileProcessingStatus.STARTED
    progress_percentage: int = 0
    current_step: str = ""
    
    # 重試和錯誤
    retry_count: int = 0
    max_retries: int = 3
    error_messages: List[str] = field(default_factory=list)
    last_error: Optional[str] = None
    
    # 效能指標
    download_duration: Optional[float] = None
    processing_duration: Optional[float] = None
    total_duration: Optional[float] = None
    
    # 網路和IO指標
    download_speed_mbps: Optional[float] = None
    network_errors: int = 0
    io_errors: int = 0
    
    def calculate_durations(self):
        """計算處理時長"""
        if self.started_at and self.completed_at:
            self.total_duration = (self.completed_at - self.started_at).total_seconds()
    
    def is_timeout(self, timeout_seconds: int = 300) -> bool:
        """檢查是否超時"""
        if not self.started_at:
            return False
        return (datetime.now() - self.started_at).total_seconds() > timeout_seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'tracking_id': self.tracking_id,
            'file_path': self.file_path,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'vendor_name': self.vendor_name,
            'status': self.status.value,
            'progress_percentage': self.progress_percentage,
            'current_step': self.current_step,
            'retry_count': self.retry_count,
            'error_messages': self.error_messages,
            'last_error': self.last_error,
            'total_duration': self.total_duration,
            'download_speed_mbps': self.download_speed_mbps,
            'network_errors': self.network_errors,
            'io_errors': self.io_errors,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }


class VendorFileMonitor:
    """廠商檔案監控器 - 最小侵入性實現"""
    
    def __init__(self, 
                 max_tracking_records: int = 1000,
                 cleanup_interval_minutes: int = 60,
                 alert_threshold_errors: int = 5,
                 alert_threshold_timeout_seconds: int = 300):
        """
        初始化监控器
        
        Args:
            max_tracking_records: 最大追蹤記錄數
            cleanup_interval_minutes: 清理間隔(分鐘)
            alert_threshold_errors: 錯誤告警闾值
            alert_threshold_timeout_seconds: 超時告警闾值(秒)
        """
        self.metrics: Dict[str, VendorFileMetrics] = {}
        self.max_tracking_records = max_tracking_records
        self.alert_threshold_errors = alert_threshold_errors
        self.alert_threshold_timeout_seconds = alert_threshold_timeout_seconds
        
        # 執行緒安全鎖
        self._lock = threading.RLock()
        
        # 統計資訊
        self.stats = {
            'total_files_tracked': 0,
            'successful_completions': 0,
            'failed_completions': 0,
            'timeout_occurrences': 0,
            'total_retries': 0,
            'average_processing_time': 0.0,
            'last_cleanup': datetime.now()
        }
        
        # 啟動清理執行緒
        self._start_cleanup_thread(cleanup_interval_minutes)
        
        logger.info(f"✅ 廠商檔案監控器已初始化 - 最大記錄: {max_tracking_records}")
    
    def start_tracking(self, 
                      file_path: str, 
                      vendor_name: Optional[str] = None,
                      expected_size: Optional[int] = None) -> str:
        """
        開始追蹤檔案處理 - 純監控，不影響現有流程
        
        Args:
            file_path: 檔案路徑
            vendor_name: 廠商名稱
            expected_size: 預期檔案大小
            
        Returns:
            str: 追蹤ID
        """
        tracking_id = str(uuid.uuid4())
        file_path_obj = Path(file_path)
        
        # 獲取檔案資訊
        file_size = expected_size
        if not file_size and file_path_obj.exists():
            try:
                file_size = file_path_obj.stat().st_size
            except Exception as e:
                logger.debug(f"獲取檔案大小失敗: {e}")
                file_size = 0
        
        with self._lock:
            # 檢查是否需要清理舊記錄
            if len(self.metrics) >= self.max_tracking_records:
                self._cleanup_old_records()
            
            # 建立追蹤記錄
            self.metrics[tracking_id] = VendorFileMetrics(
                tracking_id=tracking_id,
                file_path=file_path,
                file_name=file_path_obj.name,
                file_size=file_size or 0,
                vendor_name=vendor_name,
                started_at=datetime.now()
            )
            
            self.stats['total_files_tracked'] += 1
        
        logger.debug(f"📊 開始追蹤廠商檔案: {file_path_obj.name} (ID: {tracking_id[:8]})")
        return tracking_id
    
    def update_progress(self, 
                       tracking_id: str, 
                       status: FileProcessingStatus,
                       progress: int = None,
                       step_description: str = ""):
        """
        更新處理進度
        
        Args:
            tracking_id: 追蹤ID
            status: 當前狀態
            progress: 進度百分比
            step_description: 步驟描述
        """
        with self._lock:
            if tracking_id not in self.metrics:
                logger.warning(f"⚠️ 追蹤ID不存在: {tracking_id}")
                return
            
            metric = self.metrics[tracking_id]
            metric.status = status
            
            if progress is not None:
                metric.progress_percentage = min(100, max(0, progress))
            
            if step_description:
                metric.current_step = step_description
            
            # 記錄特定狀態的時間戳
            if status == FileProcessingStatus.DOWNLOADING and not metric.started_at:
                metric.started_at = datetime.now()
        
        logger.debug(f"📊 更新進度 {tracking_id[:8]}: {status.value} - {progress}% - {step_description}")
    
    def record_error(self, 
                    tracking_id: str, 
                    error_message: str,
                    error_type: str = "general"):
        """
        記錄錯誤資訊
        
        Args:
            tracking_id: 追蹤ID
            error_message: 錯誤訊息
            error_type: 錯誤類型 (network, io, timeout, validation)
        """
        with self._lock:
            if tracking_id not in self.metrics:
                logger.warning(f"⚠️ 追蹤ID不存在: {tracking_id}")
                return
            
            metric = self.metrics[tracking_id]
            metric.error_messages.append(f"[{datetime.now().isoformat()}] {error_message}")
            metric.last_error = error_message
            
            # 分類錯誤計數
            if error_type == "network":
                metric.network_errors += 1
            elif error_type == "io":
                metric.io_errors += 1
            
            # 檢查是否需要告警
            if len(metric.error_messages) >= self.alert_threshold_errors:
                self._trigger_alert(tracking_id, f"檔案處理錯誤過多: {len(metric.error_messages)}")
        
        logger.warning(f"📊 記錄錯誤 {tracking_id[:8]} [{error_type}]: {error_message}")
    
    def record_retry(self, tracking_id: str, retry_reason: str):
        """
        記錄重試
        
        Args:
            tracking_id: 追蹤ID
            retry_reason: 重試原因
        """
        with self._lock:
            if tracking_id not in self.metrics:
                logger.warning(f"⚠️ 追蹤ID不存在: {tracking_id}")
                return
            
            metric = self.metrics[tracking_id]
            metric.retry_count += 1
            metric.status = FileProcessingStatus.RETRY
            metric.error_messages.append(f"[RETRY #{metric.retry_count}] {retry_reason}")
            
            self.stats['total_retries'] += 1
        
        logger.info(f"📊 記錄重試 {tracking_id[:8]} (#{metric.retry_count}): {retry_reason}")
    
    def complete_tracking(self, 
                         tracking_id: str, 
                         success: bool = True,
                         final_file_size: Optional[int] = None):
        """
        完成追蹤
        
        Args:
            tracking_id: 追蹤ID
            success: 是否成功
            final_file_size: 最終檔案大小
        """
        with self._lock:
            if tracking_id not in self.metrics:
                logger.warning(f"⚠️ 追蹤ID不存在: {tracking_id}")
                return
            
            metric = self.metrics[tracking_id]
            metric.completed_at = datetime.now()
            metric.status = FileProcessingStatus.COMPLETED if success else FileProcessingStatus.FAILED
            metric.progress_percentage = 100 if success else metric.progress_percentage
            
            # 更新檔案大小
            if final_file_size:
                metric.file_size = final_file_size
            
            # 計算時長
            metric.calculate_durations()
            
            # 計算下載速度
            if metric.total_duration and metric.file_size > 0:
                size_mb = metric.file_size / (1024 * 1024)
                metric.download_speed_mbps = size_mb / metric.total_duration
            
            # 更新統計
            if success:
                self.stats['successful_completions'] += 1
            else:
                self.stats['failed_completions'] += 1
            
            # 更新平均處理時間
            self._update_average_processing_time(metric.total_duration)
        
        status_msg = "成功" if success else "失敗"
        duration_msg = f"耗時 {metric.total_duration:.2f}s" if metric.total_duration else ""
        speed_msg = f"速度 {metric.download_speed_mbps:.2f}MB/s" if metric.download_speed_mbps else ""
        
        logger.info(f"📊 完成追蹤 {tracking_id[:8]}: {status_msg} {duration_msg} {speed_msg}")
    
    def get_metrics(self, tracking_id: str) -> Optional[VendorFileMetrics]:
        """獲取指定追蹤記錄"""
        with self._lock:
            return self.metrics.get(tracking_id)
    
    def get_active_trackings(self) -> List[VendorFileMetrics]:
        """獲取所有活躍的追蹤記錄"""
        with self._lock:
            return [
                metric for metric in self.metrics.values()
                if metric.status in [
                    FileProcessingStatus.STARTED,
                    FileProcessingStatus.DOWNLOADING,
                    FileProcessingStatus.VALIDATING,
                    FileProcessingStatus.PROCESSING,
                    FileProcessingStatus.RETRY
                ]
            ]
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取監控統計資訊"""
        with self._lock:
            active_count = len(self.get_active_trackings())
            timeout_count = len([m for m in self.metrics.values() if m.is_timeout(self.alert_threshold_timeout_seconds)])
            
            return {
                **self.stats,
                'current_active_trackings': active_count,
                'current_timeout_count': timeout_count,
                'total_tracked_files': len(self.metrics),
                'success_rate': (
                    self.stats['successful_completions'] / max(1, self.stats['successful_completions'] + self.stats['failed_completions'])
                ) * 100,
                'average_retry_rate': self.stats['total_retries'] / max(1, self.stats['total_files_tracked']),
                'last_updated': datetime.now().isoformat()
            }
    
    def get_vendor_statistics(self, vendor_name: str) -> Dict[str, Any]:
        """獲取特定廠商的統計資訊"""
        with self._lock:
            vendor_metrics = [m for m in self.metrics.values() if m.vendor_name == vendor_name]
            
            if not vendor_metrics:
                return {'vendor_name': vendor_name, 'total_files': 0}
            
            successful = len([m for m in vendor_metrics if m.status == FileProcessingStatus.COMPLETED])
            failed = len([m for m in vendor_metrics if m.status == FileProcessingStatus.FAILED])
            total_retries = sum(m.retry_count for m in vendor_metrics)
            
            durations = [m.total_duration for m in vendor_metrics if m.total_duration]
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            return {
                'vendor_name': vendor_name,
                'total_files': len(vendor_metrics),
                'successful_files': successful,
                'failed_files': failed,
                'success_rate': (successful / len(vendor_metrics)) * 100,
                'total_retries': total_retries,
                'average_processing_time': avg_duration,
                'average_file_size': sum(m.file_size for m in vendor_metrics) / len(vendor_metrics)
            }
    
    def _update_average_processing_time(self, duration: Optional[float]):
        """更新平均處理時間"""
        if not duration:
            return
        
        completed_count = self.stats['successful_completions'] + self.stats['failed_completions']
        if completed_count == 1:
            self.stats['average_processing_time'] = duration
        else:
            # 增量更新平均值
            current_avg = self.stats['average_processing_time']
            self.stats['average_processing_time'] = (current_avg * (completed_count - 1) + duration) / completed_count
    
    def _cleanup_old_records(self):
        """清理舊記錄"""
        if len(self.metrics) < self.max_tracking_records:
            return
        
        # 保留最近的記錄，刪除最舊的已完成記錄
        completed_metrics = [
            (tid, metric) for tid, metric in self.metrics.items()
            if metric.status in [FileProcessingStatus.COMPLETED, FileProcessingStatus.FAILED]
        ]
        
        if completed_metrics:
            # 按完成時間排序，刪除最舊的
            completed_metrics.sort(key=lambda x: x[1].completed_at or x[1].created_at)
            records_to_remove = len(self.metrics) - self.max_tracking_records + 100  # 額外清理100條
            
            for i in range(min(records_to_remove, len(completed_metrics))):
                tracking_id = completed_metrics[i][0]
                del self.metrics[tracking_id]
        
        logger.debug(f"🧹 清理舊追蹤記錄，當前記錄數: {len(self.metrics)}")
    
    def _start_cleanup_thread(self, interval_minutes: int):
        """啟動清理執行緒"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(interval_minutes * 60)
                    with self._lock:
                        # 檢查超時記錄
                        timeout_trackings = [
                            tid for tid, metric in self.metrics.items()
                            if metric.is_timeout(self.alert_threshold_timeout_seconds)
                            and metric.status not in [FileProcessingStatus.COMPLETED, FileProcessingStatus.FAILED]
                        ]
                        
                        # 標記超時記錄
                        for tid in timeout_trackings:
                            self.metrics[tid].status = FileProcessingStatus.TIMEOUT
                            self.stats['timeout_occurrences'] += 1
                            self._trigger_alert(tid, f"檔案處理超時 ({self.alert_threshold_timeout_seconds}s)")
                        
                        # 清理舊記錄
                        self._cleanup_old_records()
                        
                        self.stats['last_cleanup'] = datetime.now()
                        
                except Exception as e:
                    logger.error(f"❌ 監控清理執行緒錯誤: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info(f"🧹 監控清理執行緒已啟動，間隔: {interval_minutes}分鐘")
    
    def _trigger_alert(self, tracking_id: str, alert_message: str):
        """觸發告警"""
        metric = self.metrics.get(tracking_id)
        if not metric:
            return
        
        alert_info = {
            'alert_type': 'vendor_file_processing',
            'tracking_id': tracking_id,
            'file_name': metric.file_name,
            'vendor_name': metric.vendor_name,
            'message': alert_message,
            'timestamp': datetime.now().isoformat(),
            'retry_count': metric.retry_count,
            'error_count': len(metric.error_messages)
        }
        
        # 這裡可以整合到現有的告警系統
        logger.warning(f"🚨 廠商檔案處理告警: {alert_message} (檔案: {metric.file_name})")
        
        # 可以發送到外部監控系統
        try:
            self._send_to_monitoring_system(alert_info)
        except Exception as e:
            logger.debug(f"發送告警到監控系統失敗: {e}")
    
    def _send_to_monitoring_system(self, alert_info: Dict):
        """發送告警到外部監控系統 (可選實現)"""
        # 這裡可以整合到現有的監控和告警系統
        # 例如: Prometheus, Grafana, 釘釘, 郵件等
        pass


# 全域監控器實例
_vendor_file_monitor: Optional[VendorFileMonitor] = None


def get_vendor_file_monitor() -> VendorFileMonitor:
    """獲取全域廠商檔案監控器實例"""
    global _vendor_file_monitor
    if _vendor_file_monitor is None:
        _vendor_file_monitor = VendorFileMonitor()
    return _vendor_file_monitor


def track_vendor_file_processing(file_path: str, vendor_name: str = None):
    """
    上下文管理器：追蹤廠商檔案處理
    
    使用方法:
        with track_vendor_file_processing("/path/to/vendor/file.zip", "廠商A") as tracker:
            # 現有的檔案處理邏輯
            tracker.update_progress(FileProcessingStatus.DOWNLOADING, 25, "開始下載")
            # ... 處理檔案 ...
            tracker.update_progress(FileProcessingStatus.PROCESSING, 75, "處理資料")
            # ... 完成處理 ...
    """
    
    class VendorFileTracker:
        def __init__(self, file_path: str, vendor_name: str = None):
            self.monitor = get_vendor_file_monitor()
            self.tracking_id = None
            self.file_path = file_path
            self.vendor_name = vendor_name
        
        def __enter__(self):
            self.tracking_id = self.monitor.start_tracking(self.file_path, self.vendor_name)
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.tracking_id:
                success = exc_type is None
                if not success and exc_val:
                    self.monitor.record_error(self.tracking_id, str(exc_val))
                self.monitor.complete_tracking(self.tracking_id, success)
        
        def update_progress(self, status: FileProcessingStatus, progress: int = None, step: str = ""):
            if self.tracking_id:
                self.monitor.update_progress(self.tracking_id, status, progress, step)
        
        def record_error(self, error_message: str, error_type: str = "general"):
            if self.tracking_id:
                self.monitor.record_error(self.tracking_id, error_message, error_type)
        
        def record_retry(self, retry_reason: str):
            if self.tracking_id:
                self.monitor.record_retry(self.tracking_id, retry_reason)
    
    return VendorFileTracker(file_path, vendor_name)


# 裝飾器版本 - 最小侵入性使用
def monitor_vendor_file_processing(vendor_name: str = None, max_retries: int = 3):
    """
    裝飾器：監控廠商檔案處理函數
    
    使用方法:
        @monitor_vendor_file_processing(vendor_name="廠商A", max_retries=3)
        async def download_vendor_file(file_url: str, local_path: str):
            # 現有的下載邏輯
            pass
    """
    def decorator(func):
        from functools import wraps
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 嘗試從參數中提取檔案路徑
            file_path = None
            if 'file_path' in kwargs:
                file_path = kwargs['file_path']
            elif 'local_path' in kwargs:
                file_path = kwargs['local_path']
            elif args and isinstance(args[0], (str, Path)):
                file_path = str(args[0])
            
            if not file_path:
                # 如果找不到檔案路徑，直接執行原函數
                return await func(*args, **kwargs)
            
            monitor = get_vendor_file_monitor()
            tracking_id = monitor.start_tracking(file_path, vendor_name)
            
            try:
                result = await func(*args, **kwargs)
                monitor.complete_tracking(tracking_id, True)
                return result
                
            except Exception as e:
                monitor.record_error(tracking_id, str(e))
                monitor.complete_tracking(tracking_id, False)
                raise
        
        return wrapper
    return decorator