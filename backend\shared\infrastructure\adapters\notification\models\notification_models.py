"""
企業級通知資料模型
定義通知系統的核心資料結構和驗證邏輯
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, field_validator


class NotificationPriority(Enum):
    """通知優先級"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class NotificationStatus(Enum):
    """通知狀態"""
    PENDING = "pending"
    QUEUED = "queued"
    SENDING = "sending"
    SENT = "sent"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NotificationType(Enum):
    """通知類型"""
    PARSING_SUCCESS = "parsing_success"
    PARSING_FAILURE = "parsing_failure"
    SYSTEM_ALERT = "system_alert"
    BATCH_SUMMARY = "batch_summary"
    PERFORMANCE_REPORT = "performance_report"
    CUSTOM = "custom"


class NotificationChannel(Enum):
    """通知管道"""
    LINE = "line"
    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"


@dataclass
class NotificationRecipient:
    """通知接收者"""
    id: str
    name: str
    channel: NotificationChannel
    address: str  # LINE User ID, Email address, Slack channel, etc.
    preferences: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = True


@dataclass
class NotificationTemplate:
    """通知範本"""
    id: str
    name: str
    type: NotificationType
    channel: NotificationChannel
    subject_template: str
    body_template: str
    variables: List[str] = field(default_factory=list)
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


class NotificationRequest(BaseModel):
    """通知請求模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: NotificationType
    priority: NotificationPriority = NotificationPriority.NORMAL
    recipients: List[str] = Field(min_items=1)  # Recipient IDs
    template_id: Optional[str] = None
    subject: Optional[str] = None
    message: str
    variables: Dict[str, Any] = Field(default_factory=dict)
    channels: List[NotificationChannel] = Field(default=[NotificationChannel.LINE])
    scheduled_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    
    @field_validator('expires_at')
    @classmethod
    def validate_expires_at(cls, v, info):
        """驗證過期時間"""
        if v and hasattr(info, 'data') and 'created_at' in info.data:
            if v <= info.data['created_at']:
                raise ValueError('過期時間必須大於建立時間')
        return v

    @field_validator('scheduled_at')
    @classmethod
    def validate_scheduled_at(cls, v, info):
        """驗證排程時間"""
        if v and hasattr(info, 'data') and 'created_at' in info.data:
            if v <= info.data['created_at']:
                raise ValueError('排程時間必須大於建立時間')
        return v


@dataclass
class NotificationResult:
    """通知結果"""
    request_id: str
    recipient_id: str
    channel: NotificationChannel
    status: NotificationStatus
    sent_at: Optional[datetime] = None
    error_message: Optional[str] = None
    response_data: Optional[Dict[str, Any]] = None
    retry_count: int = 0
    processing_time_ms: Optional[float] = None


@dataclass
class BatchNotificationRequest:
    """批量通知請求"""
    name: str
    requests: List[NotificationRequest]
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    max_concurrent: int = 5
    delay_between_batches_ms: int = 100


@dataclass
class NotificationStatistics:
    """通知統計資料"""
    total_sent: int = 0
    total_failed: int = 0
    total_pending: int = 0
    success_rate: float = 0.0
    average_processing_time_ms: float = 0.0
    statistics_by_type: Dict[NotificationType, Dict[str, int]] = field(default_factory=dict)
    statistics_by_channel: Dict[NotificationChannel, Dict[str, int]] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)


class NotificationRule(BaseModel):
    """通知規則"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    is_active: bool = True
    
    # 觸發條件
    trigger_type: NotificationType
    conditions: Dict[str, Any] = Field(default_factory=dict)
    
    # 通知設定
    template_id: str
    recipients: List[str]
    priority: NotificationPriority = NotificationPriority.NORMAL
    channels: List[NotificationChannel] = Field(default=[NotificationChannel.LINE])
    
    # 限制設定  
    max_notifications_per_hour: Optional[int] = None
    cooldown_minutes: Optional[int] = None
    
    # 時間設定
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


@dataclass
class NotificationHistory:
    """通知歷史記錄"""
    id: str
    request_id: str
    type: NotificationType
    status: NotificationStatus
    recipient_count: int
    success_count: int
    failure_count: int
    total_processing_time_ms: float
    created_at: datetime
    completed_at: Optional[datetime] = None
    error_summary: Optional[str] = None


class NotificationConfig(BaseModel):
    """通知系統配置"""
    
    # LINE 設定
    line_channel_access_token: Optional[str] = None
    line_channel_secret: Optional[str] = None
    
    # 效能設定
    max_concurrent_notifications: int = Field(default=10, gt=0)
    max_retry_attempts: int = Field(default=3, ge=0)
    retry_delay_seconds: int = Field(default=5, gt=0)
    notification_timeout_seconds: int = Field(default=30, gt=0)
    
    # 儲存設定
    history_retention_days: int = Field(default=30, gt=0)
    max_history_records: int = Field(default=10000, gt=0)
    
    # 批量處理設定
    batch_size: int = Field(default=50, gt=0)
    batch_processing_delay_ms: int = Field(default=100, ge=0)
    
    # 通知限制
    rate_limit_per_minute: int = Field(default=60, gt=0)
    daily_notification_limit: int = Field(default=1000, gt=0)
    
    # 功能開關
    enable_notification_history: bool = True
    enable_notification_statistics: bool = True
    enable_notification_rules: bool = True
    enable_template_system: bool = True
    
    @validator('retry_delay_seconds')
    def validate_retry_delay(cls, v):
        """驗證重試延遲"""
        if v < 1:
            raise ValueError('重試延遲必須至少為 1 秒')
        return v


@dataclass
class NotificationQueue:
    """通知佇列項目"""
    id: str
    request: NotificationRequest
    priority: NotificationPriority
    scheduled_at: datetime
    retry_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    last_retry_at: Optional[datetime] = None
    
    def should_retry(self, max_retries: int) -> bool:
        """判斷是否應該重試"""
        return self.retry_count < max_retries
    
    def can_process_now(self) -> bool:
        """判斷是否可以立即處理"""
        return datetime.now() >= self.scheduled_at
    
    def __lt__(self, other):
        """支援 PriorityQueue 排序比較"""
        if not isinstance(other, NotificationQueue):
            return NotImplemented
        # 按排程時間排序
        return self.scheduled_at < other.scheduled_at
    
    def __eq__(self, other):
        """支援相等比較"""
        if not isinstance(other, NotificationQueue):
            return NotImplemented
        return self.id == other.id
    
    def __hash__(self):
        """支援雜湊"""
        return hash(self.id)


class TemplateVariable(BaseModel):
    """範本變數"""
    
    name: str
    type: str = "string"  # string, number, boolean, datetime
    required: bool = True
    default_value: Optional[Any] = None
    description: Optional[str] = None
    validation_pattern: Optional[str] = None  # For string type validation


@dataclass
class NotificationMetrics:
    """通知指標"""
    timestamp: datetime
    total_notifications: int
    successful_notifications: int
    failed_notifications: int
    average_response_time_ms: float
    notifications_by_type: Dict[str, int]
    notifications_by_channel: Dict[str, int]
    error_distribution: Dict[str, int]


# 工廠函數和輔助方法
def create_notification_request(
    notification_type: NotificationType,
    recipients: List[str],
    message: str,
    priority: NotificationPriority = NotificationPriority.NORMAL,
    **kwargs
) -> NotificationRequest:
    """建立通知請求的便利函數"""
    return NotificationRequest(
        type=notification_type,
        recipients=recipients,
        message=message,
        priority=priority,
        **kwargs
    )


def create_parsing_failure_notification(
    email_data: Dict[str, Any],
    recipients: List[str]
) -> NotificationRequest:
    """建立解析失敗通知"""
    message = f"""🚨 郵件解析失敗通知

📧 郵件資訊:
• ID: {email_data.get('id', 'N/A')}
• 主旨: {email_data.get('subject', 'N/A')}
• 寄件者: {email_data.get('sender', 'N/A')}
• 廠商代碼: {email_data.get('vendor_code', 'N/A')}

❌ 失敗原因:
{email_data.get('error_message', '未知錯誤')}

⏰ 通知時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
    
    return create_notification_request(
        notification_type=NotificationType.PARSING_FAILURE,
        recipients=recipients,
        message=message,
        priority=NotificationPriority.HIGH,
        metadata={'email_data': email_data}
    )


def create_parsing_success_notification(
    email_data: Dict[str, Any],
    recipients: List[str]
) -> NotificationRequest:
    """建立解析成功通知"""
    message = f"""✅ 郵件解析成功通知

📧 郵件資訊:
• ID: {email_data.get('id', 'N/A')}
• 主旨: {email_data.get('subject', 'N/A')}
• 寄件者: {email_data.get('sender', 'N/A')}

📊 解析結果:
• 廠商代碼: {email_data.get('vendor_code', 'N/A')}
• 產品代碼: {email_data.get('pd', 'N/A')}
• 批次編號: {email_data.get('lot', 'N/A')}
• 良率: {email_data.get('yield_value', 'N/A')}

⏰ 通知時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
    
    return create_notification_request(
        notification_type=NotificationType.PARSING_SUCCESS,
        recipients=recipients,
        message=message,
        priority=NotificationPriority.NORMAL,
        metadata={'email_data': email_data}
    )