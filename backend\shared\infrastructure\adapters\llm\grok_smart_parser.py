"""
Grok 智能解析器
結合所有廠商的解析方式，使用 Grok 提供智能解析功能
"""

import json
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from .grok_client import GrokClient, GrokRequest
from .grok_parsing_classifier import GrokParsingClassifier, VendorType, EmailAnalysis
from backend.email.models.email_models import EmailData, EmailParsingResult
from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.email.parsers.base_parser import BaseParser


@dataclass
class SmartParsingResult:
    """智能解析結果"""
    vendor_code: str
    is_success: bool
    extraction_method: str
    confidence_score: float
    
    # 解析出的資料
    mo_number: Optional[str] = None
    lot_number: Optional[str] = None
    product: Optional[str] = None
    yield_rate: Optional[str] = None
    test_batch: Optional[str] = None
    device_type: Optional[str] = None
    quantity: Optional[str] = None
    
    # 解析詳情
    parsing_methods_used: List[str] = None
    grok_analysis: Optional[str] = None
    traditional_backup: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.parsing_methods_used is None:
            self.parsing_methods_used = []


class GrokSmartParser(BaseParser):
    """
    Grok 智能解析器 - 單例模式
    整合所有廠商的解析方式，提供智能解析功能
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GrokSmartParser, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化智能解析器"""
        # 避免重複初始化
        if self._initialized:
            return
            
        self.logger = LoggerManager().get_logger("GrokSmartParser")
        self.grok_client = GrokClient()
        self.classifier = GrokParsingClassifier()
        
        # 解析統計
        self.parsing_stats = {
            "total_parsed": 0,
            "grok_success": 0,
            "traditional_fallback": 0,
            "failed": 0,
            "vendor_stats": {}
        }
        
        self.logger.info("Grok 智能解析器已初始化")
        self._initialized = True
    
    @property
    def vendor_code(self) -> str:
        """廠商代碼 - GROK 智能解析器支援所有廠商"""
        return "GROK_SMART"
    
    @property
    def vendor_name(self) -> str:
        """廠商名稱"""
        return "Grok 智能解析器"
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表 - 支援所有廠商模式"""
        return [
            "jcet", "gtk", "etd", "lingsen", "xaht", "tianshui", "西安",
            "ft hold", "ft lot", "anf", "grok", "smart"
        ]
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商"""
        from backend.email.models.email_models import VendorIdentificationResult
        
        try:
            # 使用分類器分析郵件
            analysis = self.classifier.classify_email(
                email_data.subject,
                email_data.body,
                email_data.sender
            )
            
            return VendorIdentificationResult(
                vendor_code=analysis.vendor.value if analysis.vendor != VendorType.UNKNOWN else "UNKNOWN",
                vendor_name=analysis.vendor.value,
                confidence_score=analysis.vendor_confidence,
                matching_patterns=[method.method_name for method in analysis.recommended_methods],
                is_identified=analysis.vendor != VendorType.UNKNOWN
            )
        except Exception as e:
            self.logger.error(f"廠商識別失敗: {e}")
            return VendorIdentificationResult(
                vendor_code="ERROR",
                vendor_name="錯誤",
                confidence_score=0.0,
                matching_patterns=[],
                is_identified=False
            )
    
    def parse_email(self, context: 'ParsingContext') -> 'EmailParsingResult':
        """解析郵件 - BaseParser 介面實現"""
        from backend.email.parsers.base_parser import ParsingContext
        
        # 使用智能解析方法
        smart_result = self.parse_email_smart(context.email_data)
        
        # 轉換為標準解析結果
        return self.convert_to_email_parsing_result(smart_result)
    
    def parse_email_smart(self, email_data: EmailData) -> SmartParsingResult:
        """
        智能解析郵件
        
        Args:
            email_data: 郵件資料
            
        Returns:
            SmartParsingResult: 智能解析結果
        """
        self.parsing_stats["total_parsed"] += 1
        
        try:
            # 1. 使用分類器分析郵件
            analysis = self.classifier.classify_email(
                email_data.subject,
                email_data.body,
                email_data.sender
            )
            
            # 2. 根據分析結果選擇解析策略
            if analysis.vendor != VendorType.UNKNOWN and analysis.vendor_confidence > 0.7:
                # 使用 Grok 智能解析
                result = self._parse_with_grok(email_data, analysis)
                
                if result.is_success:
                    self.parsing_stats["grok_success"] += 1
                    self._update_vendor_stats(analysis.vendor.value, "grok_success")
                    return result
            
            # 3. 後備策略：使用傳統解析
            self.logger.info("使用傳統解析作為後備")
            result = self._parse_with_traditional_fallback(email_data, analysis)
            
            if result.is_success:
                self.parsing_stats["traditional_fallback"] += 1
                self._update_vendor_stats(analysis.vendor.value, "traditional_fallback")
            else:
                self.parsing_stats["failed"] += 1
                self._update_vendor_stats(analysis.vendor.value, "failed")
            
            return result
            
        except Exception as e:
            self.logger.error(f"智能解析過程發生錯誤: {str(e)}")
            self.parsing_stats["failed"] += 1
            
            return SmartParsingResult(
                vendor_code="ERROR",
                is_success=False,
                extraction_method="error",
                confidence_score=0.0,
                error_message=str(e)
            )
    
    def _parse_with_grok(self, email_data: EmailData, analysis: EmailAnalysis) -> SmartParsingResult:
        """使用 Grok 進行智能解析"""
        try:
            # 構建解析提示
            prompt = self._build_parsing_prompt(email_data, analysis)
            
            # 發送 Grok 請求
            request = GrokRequest(
                prompt=prompt,
                temperature=0.1,
                max_tokens=2000
            )
            
            response = self.grok_client.send_request(request)
            
            if response.success:
                return self._parse_grok_extraction_response(response.content, analysis)
            else:
                self.logger.error(f"Grok 解析請求失敗: {response.error}")
                return SmartParsingResult(
                    vendor_code=analysis.vendor.value.upper(),
                    is_success=False,
                    extraction_method="grok_failed",
                    confidence_score=0.0,
                    error_message=response.error
                )
                
        except Exception as e:
            self.logger.error(f"Grok 解析過程發生錯誤: {str(e)}")
            return SmartParsingResult(
                vendor_code=analysis.vendor.value.upper(),
                is_success=False,
                extraction_method="grok_error",
                confidence_score=0.0,
                error_message=str(e)
            )
    
    def _build_parsing_prompt(self, email_data: EmailData, analysis: EmailAnalysis) -> str:
        """構建解析提示"""
        vendor_name = analysis.vendor.value.upper()
        
        prompt = f"""
你是專業的{vendor_name}郵件解析專家。分析郵件內容並提取關鍵資訊。

郵件資訊：
- 主題: {email_data.subject}
- 寄件者: {email_data.sender}
- 內容: {email_data.body[:1500]}

廠商: {vendor_name}

請提取以下資訊：
1. MO編號
2. LOT編號  
3. 產品型號
4. 良率
5. 測試批號
6. 裝置類型
7. 數量

JSON回應格式：
{{
  "extraction_success": true,
  "confidence_score": 0.95,
  "mo_number": "MO編號或null",
  "lot_number": "LOT編號或null",
  "product": "產品型號或null",
  "yield_rate": "良率或null",
  "test_batch": "測試批號或null",
  "device_type": "裝置類型或null",
  "quantity": "數量或null",
  "parsing_methods_used": ["解析方法"],
  "extraction_reasoning": "推理過程"
}}
"""
        return prompt
    
    def _get_vendor_parsing_guide(self, vendor: VendorType) -> str:
        """獲取廠商特定的解析指引"""
        guides = {
            VendorType.JCET: """
JCET 解析指引：
- KUI/GYC 長模式: 如果包含KUI或GYC且長度>4，取前15字符作為MO編號
- KUI/GYC 短模式: 如果是單獨的KUI或GYC，後面的詞是LOT和Product
- LOT模式: 尋找 "lot:" 或 "lot：" 後的編號
- 測試批號: 尋找 "测试批号:" 後的編號
- 產品型號: G+4數字+字母+2數字+字母格式 (如G2892K21D)
- 標準MO: 1字母+6數字格式
- JCET特有MO: 2-4字母+4-6數字+點+1-2字母格式
            """,
            VendorType.GTK: """
GTK 解析指引：
- 關鍵字提取: 尋找 "mo:", "lot:", "yield:", "product:" 後的值
- 裝置類型: 尋找 "type:" 或 "device type:" 後的值
- BIN1資料: 尋找包含 "BIN1:" 的行
- 入料數量: 尋找 "input quantity", "in qty", "total input" 等
- 主題必須包含 "ft hold" 或 "ft lot"
            """,
            VendorType.ETD: """
ETD 解析指引：
- ANF格式: 主題包含 "anf"，用 "/" 分隔，parts[1]=product, parts[4]=lot, parts[6]=mo
- 數量提取: 尋找 "input quantity", "quantity units", "total input" 等
- 良率提取: 尋找 "yield", "良率", "pass rate" 或百分比數字
- 異常檢測: 尋找包含 "異常" 的行
            """,
            VendorType.LINGSEN: """
LINGSEN 解析指引：
- 產品代碼: G, M, AT 開頭的代碼，優先順序 G > M > AT
- Run編號: 尋找 "Run#" 後的數字作為MO編號
- Lot編號: 尋找 "Lot#" 後的編號
- 良率: 尋找 "LowYield=" 後的百分比
- 內容必須包含 "LINGSEN"
            """,
            VendorType.XAHT: """
XAHT 解析指引：
- wa/GYC模式: 尋找包含 "wa" 或 "GYC" 且長度>4的詞，取前11字符作為MO
- 下劃線格式: 使用 "_" 分隔，parts[2]=product, parts[3]=lot, parts[4]=mo
- 雙重解析: 先用方法1，如果MO為空則用方法2
- 內容必須包含 "tianshui" 或 "西安"
            """,
            VendorType.UNKNOWN: """
未知廠商解析指引：
- 使用通用模式提取可能的資訊
- 注意常見的格式和關鍵字
- 提供最佳猜測結果
            """
        }
        
        return guides.get(vendor, guides[VendorType.UNKNOWN])
    
    def _parse_grok_extraction_response(self, content: str, analysis: EmailAnalysis) -> SmartParsingResult:
        """解析 Grok 提取回應"""
        try:
            # 嘗試提取JSON
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                data = json.loads(json_str)
                
                # 檢查提取是否成功
                extraction_success = data.get('extraction_success', False)
                confidence_score = data.get('confidence_score', 0.0)
                
                if extraction_success and confidence_score > 0.6:
                    return SmartParsingResult(
                        vendor_code=analysis.vendor.value.upper(),
                        is_success=True,
                        extraction_method="grok_smart",
                        confidence_score=confidence_score,
                        mo_number=data.get('mo_number'),
                        lot_number=data.get('lot_number'),
                        product=data.get('product'),
                        yield_rate=data.get('yield_rate'),
                        test_batch=data.get('test_batch'),
                        device_type=data.get('device_type'),
                        quantity=data.get('quantity'),
                        parsing_methods_used=data.get('parsing_methods_used', []),
                        grok_analysis=data.get('extraction_reasoning', '')
                    )
                else:
                    return SmartParsingResult(
                        vendor_code=analysis.vendor.value.upper(),
                        is_success=False,
                        extraction_method="grok_low_confidence",
                        confidence_score=confidence_score,
                        error_message=f"信心分數過低: {confidence_score}"
                    )
            else:
                raise ValueError("無法提取JSON格式回應")
                
        except Exception as e:
            self.logger.error(f"解析 Grok 提取回應失敗: {str(e)}")
            return SmartParsingResult(
                vendor_code=analysis.vendor.value.upper(),
                is_success=False,
                extraction_method="grok_parse_error",
                confidence_score=0.0,
                error_message=f"回應解析錯誤: {str(e)}"
            )
    
    def _parse_with_traditional_fallback(self, email_data: EmailData, analysis: EmailAnalysis) -> SmartParsingResult:
        """使用傳統解析作為後備"""
        try:
            # 基本的模式匹配解析
            vendor_code = analysis.vendor.value.upper() if analysis.vendor != VendorType.UNKNOWN else "UNKNOWN"
            
            # 提取基本資訊
            mo_number = self._extract_mo_traditional(email_data, analysis.vendor)
            lot_number = self._extract_lot_traditional(email_data, analysis.vendor)
            product = self._extract_product_traditional(email_data, analysis.vendor)
            yield_rate = self._extract_yield_traditional(email_data, analysis.vendor)
            
            # 判斷是否成功
            is_success = bool(mo_number or lot_number or product)
            confidence_score = 0.7 if is_success else 0.3
            
            return SmartParsingResult(
                vendor_code=vendor_code,
                is_success=is_success,
                extraction_method="traditional_fallback",
                confidence_score=confidence_score,
                mo_number=mo_number,
                lot_number=lot_number,
                product=product,
                yield_rate=yield_rate,
                parsing_methods_used=["pattern_matching", "regex_extraction"],
                traditional_backup={
                    "analysis_used": True,
                    "vendor_confidence": analysis.vendor_confidence
                }
            )
            
        except Exception as e:
            self.logger.error(f"傳統後備解析失敗: {str(e)}")
            return SmartParsingResult(
                vendor_code="ERROR",
                is_success=False,
                extraction_method="traditional_error",
                confidence_score=0.0,
                error_message=str(e)
            )
    
    def _extract_mo_traditional(self, email_data: EmailData, vendor: VendorType) -> Optional[str]:
        """傳統MO編號提取"""
        text = f"{email_data.subject} {email_data.body}"
        
        # 通用MO模式
        patterns = [
            r'\b[A-Z]\d{6}\b',  # 標準格式
            r'\b[A-Z]{2,4}\d{4,6}\.[A-Z]{1,2}\b',  # JCET格式
            r'mo\s*:\s*([^\s,~_]+)',  # GTK格式
            r'[Rr]un#(\d+)',  # LINGSEN格式
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1) if match.groups() else match.group(0)
        
        return None
    
    def _extract_lot_traditional(self, email_data: EmailData, vendor: VendorType) -> Optional[str]:
        """傳統LOT編號提取"""
        text = f"{email_data.subject} {email_data.body}"
        
        patterns = [
            r'lot[：:]\s*([A-Z0-9.]+)',  # JCET格式
            r'lot\s*(?:no\s*)?:\s*([^\s,~_]+)',  # GTK格式
            r'[Ll]ot#([^\s]+)',  # LINGSEN格式
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_product_traditional(self, email_data: EmailData, vendor: VendorType) -> Optional[str]:
        """傳統產品型號提取"""
        text = f"{email_data.subject} {email_data.body}"
        
        patterns = [
            r'(G\d{4}[A-Z]\d{2}[A-Z])',  # JCET格式
            r'product\s*:\s*([^\s,~_]+)',  # GTK格式
            r'\b(G|M|AT)\d+[A-Z0-9\(\)]*',  # LINGSEN格式
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1) if match.groups() else match.group(0)
        
        return None
    
    def _extract_yield_traditional(self, email_data: EmailData, vendor: VendorType) -> Optional[str]:
        """傳統良率提取"""
        text = f"{email_data.subject} {email_data.body}"
        
        patterns = [
            r'yield\s*:\s*([^\s,%~_]+)',  # GTK格式
            r'(?:yield|良率|pass\s*rate)\s*:\s*(\d+(?:\.\d+)?%?)',  # ETD格式
            r'[Ll]ow[Yy]ield\s*=?\s*(\d+(?:\.\d+)?%)',  # LINGSEN格式
            r'(\d+(?:\.\d+)?%)',  # 通用百分比
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _update_vendor_stats(self, vendor: str, status: str):
        """更新廠商統計"""
        if vendor not in self.parsing_stats["vendor_stats"]:
            self.parsing_stats["vendor_stats"][vendor] = {
                "grok_success": 0,
                "traditional_fallback": 0,
                "failed": 0
            }
        
        self.parsing_stats["vendor_stats"][vendor][status] += 1
    
    def get_parsing_stats(self) -> Dict[str, Any]:
        """獲取解析統計"""
        return self.parsing_stats.copy()
    
    def reset_stats(self):
        """重置統計"""
        self.parsing_stats = {
            "total_parsed": 0,
            "grok_success": 0,
            "traditional_fallback": 0,
            "failed": 0,
            "vendor_stats": {}
        }
        self.logger.info("解析統計已重置")
    
    def convert_to_email_parsing_result(self, smart_result: SmartParsingResult) -> EmailParsingResult:
        """轉換為標準解析結果"""
        # 構建解析出的數據
        extracted_data = {
            "product": smart_result.product,
            "yield_rate": smart_result.yield_rate,
            "test_batch": smart_result.test_batch,
            "device_type": smart_result.device_type,
            "quantity": smart_result.quantity,
            "parsing_methods_used": smart_result.parsing_methods_used,
            "grok_analysis": smart_result.grok_analysis,
            "traditional_backup": smart_result.traditional_backup,
            "confidence_score": smart_result.confidence_score
        }
        
        return EmailParsingResult(
            vendor_code=smart_result.vendor_code,
            is_success=smart_result.is_success,
            extraction_method=smart_result.extraction_method,
            mo_number=smart_result.mo_number,
            lot_number=smart_result.lot_number,
            extracted_data=extracted_data,
            error_message=smart_result.error_message
        )