#!/usr/bin/env python3
"""
測試錯誤處理機制
"""

import asyncio
from backend.eqc.services.eqc_session_manager import get_eqc_session_manager

def test_session_error_handling():
    """測試會話錯誤處理"""
    print("🧪 測試會話錯誤處理")
    
    session_manager = get_eqc_session_manager()
    
    try:
        # 測試獲取不存在的會話
        non_existent_session = session_manager.get_session("non-existent-id")
        if non_existent_session is None:
            print("✅ 正確處理不存在的會話ID")
        else:
            print("❌ 未正確處理不存在的會話ID")
            return False
        
        # 測試會話失敗標記
        session_id = session_manager.create_session("D:/test", "test_user")
        session_manager.fail_session(session_id, "測試錯誤")
        
        failed_session = session_manager.get_session(session_id)
        if failed_session and failed_session.status.value == 'failed':
            print("✅ 正確處理會話失敗狀態")
        else:
            print("❌ 未正確處理會話失敗狀態")
            return False
        
        # 測試錯誤訊息
        if failed_session.error_message == "測試錯誤":
            print("✅ 正確記錄錯誤訊息")
        else:
            print("❌ 未正確記錄錯誤訊息")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 會話錯誤處理測試異常: {e}")
        return False

def test_dramatiq_task_error_handling():
    """測試 Dramatiq 任務錯誤處理"""
    print("\n🧪 測試 Dramatiq 任務錯誤處理")

    try:
        from backend.tasks import get_task_manager

        # 檢查任務管理器
        task_manager = get_task_manager()
        print(f"✅ 任務管理器: {type(task_manager).__name__}")

        # 檢查系統狀態
        from backend.tasks import get_system_status
        status = get_system_status()
        print(f"✅ 系統狀態: {status}")

        # 測試無效路徑處理
        try:
            # 這應該會失敗但不會崩潰系統
            invalid_path = "/invalid/path/that/does/not/exist"
            print(f"📁 測試無效路徑: {invalid_path}")

            # 提交任務
            result = task_manager.submit_eqc_workflow(invalid_path, "test_session")
            print(f"📊 任務提交結果: {result.task_id}")

        except Exception as task_error:
            print(f"✅ 正確捕獲任務錯誤: {task_error}")

        return True

    except Exception as e:
        print(f"❌ Dramatiq 任務錯誤處理測試異常: {e}")
        return False

def test_api_error_handling():
    """測試 API 錯誤處理"""
    print("\n🧪 測試 API 錯誤處理")
    
    try:
        # 測試異步 API 導入
        from frontend.api.eqc_async_api import router
        
        # 檢查路由是否包含錯誤處理
        routes = [route for route in router.routes if hasattr(route, 'path')]
        if len(routes) > 0:
            print(f"✅ 異步 API 包含 {len(routes)} 個路由")
        else:
            print("❌ 異步 API 無有效路由")
            return False
        
        # 測試監控 API 導入
        from backend.monitoring.api.eqc_monitoring_api import router as monitoring_router
        
        monitoring_routes = [route for route in monitoring_router.routes if hasattr(route, 'path')]
        if len(monitoring_routes) > 0:
            print(f"✅ 監控 API 包含 {len(monitoring_routes)} 個路由")
        else:
            print("❌ 監控 API 無有效路由")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API 錯誤處理測試異常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_error_handling():
    """測試導入錯誤處理"""
    print("\n🧪 測試導入錯誤處理")
    
    try:
        # 測試所有關鍵模組的導入
        modules_to_test = [
            ("EQC Session Manager", "src.services.eqc_session_manager"),
            ("EQC Async API", "src.presentation.api.eqc_async_api"),
            ("EQC Monitoring API", "src.dashboard_monitoring.api.eqc_monitoring_api"),
            ("EQC Task Collector", "src.dashboard_monitoring.collectors.eqc_task_collector"),
            ("Dramatiq Tasks", "src.tasks")
        ]
        
        failed_imports = []
        
        for module_name, module_path in modules_to_test:
            try:
                __import__(module_path)
                print(f"✅ {module_name} 導入成功")
            except Exception as e:
                print(f"❌ {module_name} 導入失敗: {e}")
                failed_imports.append(module_name)
        
        if not failed_imports:
            print("✅ 所有關鍵模組導入成功")
            return True
        else:
            print(f"❌ {len(failed_imports)} 個模組導入失敗: {failed_imports}")
            return False
        
    except Exception as e:
        print(f"❌ 導入錯誤處理測試異常: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始錯誤處理測試")
    print("=" * 60)
    
    tests = [
        ("會話錯誤處理", test_session_error_handling),
        ("Dramatiq 任務錯誤處理", test_dramatiq_task_error_handling),
        ("API 錯誤處理", test_api_error_handling),
        ("導入錯誤處理", test_import_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 執行 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")
        
        print("-" * 40)
    
    print(f"\n📊 錯誤處理測試總結:")
    print(f"總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有錯誤處理測試通過！")
        return True
    else:
        print("⚠️ 部分錯誤處理測試失敗")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
