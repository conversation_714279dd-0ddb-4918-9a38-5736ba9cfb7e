"""
MSEC 廠商檔案處理器
對應 VBA 的 CopyFilesMSEC 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class MSECFileHandler(BaseFileHandler):
    """
    MSEC 廠商檔案處理器
    
    VBA 邏輯特性：
    - 主要搜尋壓縮檔案 (.zip, .rar, .7z)
    - 支援 MO 編號搜尋
    - 使用標準路徑結構
    - 支援 LOT 號碼搜尋備用
    """
    
    def __init__(self, source_base_path: str):
        """初始化 MSEC 檔案處理器"""
        super().__init__(source_base_path, "MSEC")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        MSEC 使用標準路徑結構
        
        構建可能的來源路徑：
        1. {source_base_path}/MSECZD/
        2. {source_base_path}/MSECZD/FT/ (如果有 FT 子目錄)
        3. {source_base_path}/MSECZD/{pd}/ (如果有產品特定目錄)
        """
        paths = []
        
        # 主要 MSECZD 目錄
        main_path = self.source_base_path / "MSECZD"
        paths.append(main_path)
        
        # FT 子目錄（常見的測試目錄）
        ft_path = main_path / "FT"
        paths.append(ft_path)
        
        # 產品特定目錄
        if pd != "default" and pd:
            pd_path = main_path / pd
            paths.append(pd_path)
            
            # 產品下的 FT 目錄
            pd_ft_path = pd_path / "FT"
            paths.append(pd_ft_path)
        
        # LOT 特定目錄
        if lot != "default" and lot:
            lot_path = main_path / lot
            paths.append(lot_path)
            
            # 產品+LOT 組合目錄
            if pd != "default" and pd:
                pd_lot_path = main_path / pd / lot
                paths.append(pd_lot_path)
        
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        MSEC 檔案搜尋模式
        
        優先順序：
        1. MO 編號匹配
        2. LOT 編號匹配
        3. 產品代碼匹配
        """
        patterns = []
        
        # MO 編號模式（最高優先級）
        if mo:
            patterns.append(f"*{mo}*")
            
        # LOT 編號模式
        if lot and lot != "default":
            patterns.append(f"*{lot}*")
            patterns.append(f"{lot}*")
            
        # 產品代碼模式
        if pd and pd != "default":
            patterns.append(f"*{pd}*")
            
        # 通用模式（如果沒有特定標識符）
        if not patterns:
            patterns.extend(["*.zip", "*.rar", "*.7z"])
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """MSEC 不支援資料夾複製，主要依賴壓縮檔案"""
        return False
        
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """
        MSEC 特殊化的 MO 搜尋邏輯
        專注於壓縮檔案搜尋
        """
        try:
            self.logger.info(f"MSEC MO 搜尋: 在 {source_path} 中搜尋 MO={mo}")
            
            success = False
            
            # 檢查是否有多個 MO（逗號分隔）
            if ',' in mo:
                # 多個 MO，分別搜尋
                mo_list = [m.strip() for m in mo.split(',')]
                self.logger.info(f"檢測到多個 MO: {mo_list}")
                
                for individual_mo in mo_list:
                    pattern = f"*{individual_mo}*"
                    self.logger.info(f"   搜尋 MO: {individual_mo}, 模式: {pattern}")
                    
                    newest_file = self._find_newest_archive(source_path, pattern)
                    if newest_file:
                        self.logger.info(f"   找到檔案: {newest_file.name}")
                        if self._copy_file_with_check(newest_file, dest_path):
                            success = True
                        else:
                            self.logger.warning(f"   複製失敗: {newest_file}")
                    else:
                        self.logger.warning(f"   未找到符合 MO {individual_mo} 的檔案")
                        
                return success
            else:
                # 單一 MO 搜尋
                pattern = f"*{mo}*"
                self.logger.info(f"   搜尋單一 MO: {mo}, 模式: {pattern}")
                
                # 尋找最新的壓縮檔案
                newest_file = self._find_newest_archive(source_path, pattern)
                
                if newest_file:
                    self.logger.info(f"   找到最新壓縮檔: {newest_file.name}")
                    return self._copy_file_with_check(newest_file, dest_path)
                else:
                    self.logger.warning(f"   未找到符合 MO {mo} 的壓縮檔")
                    
                    # 備用：搜尋所有檔案（不限壓縮檔）
                    all_files = list(source_path.glob(pattern))
                    if all_files:
                        self.logger.info(f"   發現 {len(all_files)} 個非壓縮檔案，嘗試複製最新的")
                        # 按修改時間排序，取最新的
                        newest_any_file = max(all_files, key=lambda f: f.stat().st_mtime)
                        return self._copy_file_with_check(newest_any_file, dest_path)
                
                return False
                
        except Exception as e:
            self.logger.error(f"MSEC MO 搜尋失敗: {e}")
            return False
            
    def _copy_by_lot(self, source_path: Path, dest_path: Path, lot: str) -> bool:
        """
        MSEC 特殊化的 LOT 搜尋邏輯
        """
        try:
            self.logger.info(f"MSEC LOT 搜尋: 在 {source_path} 中搜尋 LOT={lot}")
            
            # LOT 搜尋模式
            patterns = [
                f"*{lot}*",      # 包含 LOT 的檔案
                f"{lot}*",       # 以 LOT 開頭的檔案
                f"*{lot}.zip",   # LOT.zip 格式
                f"*{lot}.rar",   # LOT.rar 格式
                f"*{lot}.7z"     # LOT.7z 格式
            ]
            
            for pattern in patterns:
                self.logger.info(f"   嘗試模式: {pattern}")
                files = list(source_path.glob(pattern))
                
                if files:
                    self.logger.info(f"   找到 {len(files)} 個檔案")
                    
                    # 優先選擇壓縮檔案
                    archive_files = [f for f in files if f.suffix.lower() in self.archive_extensions]
                    
                    if archive_files:
                        # 選擇最新的壓縮檔案
                        newest_archive = max(archive_files, key=lambda f: f.stat().st_mtime)
                        self.logger.info(f"   選擇最新壓縮檔: {newest_archive.name}")
                        return self._copy_file_with_check(newest_archive, dest_path)
                    else:
                        # 沒有壓縮檔案，選擇最新的一般檔案
                        newest_file = max(files, key=lambda f: f.stat().st_mtime)
                        self.logger.info(f"   選擇最新檔案: {newest_file.name}")
                        return self._copy_file_with_check(newest_file, dest_path)
            
            self.logger.warning(f"   所有 LOT 搜尋模式都未找到檔案")
            return False
            
        except Exception as e:
            self.logger.error(f"MSEC LOT 搜尋失敗: {e}")
            return False