#!/usr/bin/env python3
"""
簡化的 EQC 負載測試
測試基本的 API 端點和會話管理功能
"""

import asyncio
import time
from datetime import datetime
from backend.eqc.services.eqc_session_manager import get_eqc_session_manager

async def test_session_creation_load():
    """測試會話創建負載"""
    print("🧪 開始會話創建負載測試")
    
    session_manager = get_eqc_session_manager()
    start_time = time.time()
    
    # 創建多個會話
    sessions = []
    for i in range(10):
        session_id = session_manager.create_session(
            folder_path=f"D:/test_folder_{i}",
            user_id=f"test_user_{i}"
        )
        sessions.append(session_id)
        print(f"✅ 創建會話 {i+1}/10: {session_id}")
    
    # 檢查統計
    stats = session_manager.get_session_stats()
    print(f"📊 會話統計: {stats}")
    
    # 測試會話檢索
    for session_id in sessions:
        session = session_manager.get_session(session_id)
        if session:
            print(f"✅ 檢索會話成功: {session_id}")
        else:
            print(f"❌ 檢索會話失敗: {session_id}")
    
    end_time = time.time()
    print(f"⏱️ 測試完成，耗時: {end_time - start_time:.2f}秒")
    
    return len(sessions) == 10

async def test_concurrent_sessions():
    """測試並發會話處理"""
    print("\n🧪 開始並發會話測試")
    
    session_manager = get_eqc_session_manager()
    
    async def create_and_process_session(user_id):
        """創建並處理單個會話"""
        try:
            # 創建會話
            session_id = session_manager.create_session(
                folder_path=f"D:/test_folder_{user_id}",
                user_id=f"user_{user_id}"
            )
            
            # 模擬處理過程
            session_manager.update_session_progress(session_id, 25, "Step 1/4: 處理中...")
            await asyncio.sleep(0.1)
            
            session_manager.update_session_progress(session_id, 50, "Step 2/4: 處理中...")
            await asyncio.sleep(0.1)
            
            session_manager.update_session_progress(session_id, 75, "Step 3/4: 處理中...")
            await asyncio.sleep(0.1)
            
            # 完成會話
            session_manager.complete_session(session_id, {"result": f"success_{user_id}"})
            
            print(f"✅ 用戶 {user_id} 會話處理完成")
            return True
            
        except Exception as e:
            print(f"❌ 用戶 {user_id} 會話處理失敗: {e}")
            return False
    
    # 並發執行多個會話
    start_time = time.time()
    tasks = [create_and_process_session(i) for i in range(5)]
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    success_count = sum(results)
    print(f"📊 並發測試結果: {success_count}/5 成功")
    print(f"⏱️ 並發測試耗時: {end_time - start_time:.2f}秒")
    
    # 檢查最終統計
    stats = session_manager.get_session_stats()
    print(f"📊 最終統計: {stats}")
    
    return success_count == 5

def test_celery_task_definition():
    """測試 Celery 任務定義"""
    print("\n🧪 測試 Celery 任務定義")
    
    try:
        from backend.tasks import process_complete_eqc_workflow_task
        
        # 檢查任務屬性
        print(f"✅ 任務名稱: {process_complete_eqc_workflow_task.name}")
        print(f"✅ 任務綁定: {process_complete_eqc_workflow_task.bind}")
        print(f"✅ 最大重試: {process_complete_eqc_workflow_task.max_retries}")
        
        # 檢查 Dramatiq 任務配置
        try:
            from dramatiq_config import get_queue_config
            queue_config = get_queue_config('eqc_queue')
            print(f"✅ Dramatiq 隊列配置: {queue_config}")
        except Exception as e:
            print(f"❌ Dramatiq 配置檢查失敗: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Celery 任務測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 開始 EQC 系統簡化負載測試")
    print("=" * 60)
    
    tests = [
        ("會話創建負載測試", test_session_creation_load()),
        ("並發會話測試", test_concurrent_sessions()),
        ("Celery 任務定義測試", test_celery_task_definition())
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_coro in tests:
        print(f"\n🔍 執行 {test_name}:")
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
                
            if result:
                passed += 1
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")
        
        print("-" * 40)
    
    print(f"\n📊 測試總結:")
    print(f"總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有測試通過！")
        return True
    else:
        print("⚠️ 部分測試失敗")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
