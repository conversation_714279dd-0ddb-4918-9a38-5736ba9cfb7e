"""
Dramatiq EQC 基礎功能測試

🎯 測試目標：
  - 驗證 Dramatiq 版本的 process_complete_eqc_workflow_task 能正確執行
  - 確認與現有 EQCProcessingService 的整合
  - 測試基本的 EQC 工作流程
"""

import asyncio
import time
import os
from pathlib import Path
from loguru import logger

# 設置環境變數使用內存代理進行測試
os.environ['USE_MEMORY_BROKER'] = 'true'

# 導入 Dramatiq 配置和任務
import dramatiq_config
from backend.tasks.services.dramatiq_tasks import process_complete_eqc_workflow_task


async def test_dramatiq_eqc_basic():
    """測試 Dramatiq EQC 基礎功能"""
    logger.info("🧪 開始 Dramatiq EQC 基礎功能測試")
    
    # 測試資料夾路徑 (使用測試資料)
    test_folder = r"D:\project\python\outlook_summary\test_data\eqc_sample"
    
    # 檢查測試資料夾是否存在
    if not Path(test_folder).exists():
        logger.warning(f"⚠️ 測試資料夾不存在: {test_folder}")
        logger.info("📁 創建測試資料夾結構...")
        Path(test_folder).mkdir(parents=True, exist_ok=True)
        
        # 創建基本測試文件
        test_files = [
            "EQCTOTALDATA.csv",
            "FT_data.csv", 
            "EQC_data.csv"
        ]
        
        for file_name in test_files:
            test_file = Path(test_folder) / file_name
            test_file.write_text("test,data\n1,2\n", encoding='utf-8')
            logger.info(f"✅ 創建測試文件: {file_name}")
    
    try:
        # 測試參數
        test_session_id = f"test_session_{int(time.time())}"
        test_options = {
            'main_start': 'A1',
            'main_end': 'Z100',
            'backup_start': 'AA1', 
            'backup_end': 'ZZ100'
        }
        
        logger.info(f"📋 測試參數:")
        logger.info(f"  - 資料夾: {test_folder}")
        logger.info(f"  - 會話ID: {test_session_id}")
        logger.info(f"  - 選項: {test_options}")
        
        # 提交 Dramatiq 任務
        logger.info("🚀 提交 Dramatiq 任務...")
        start_time = time.time()
        
        # 直接調用任務函數進行測試 (內存模式)
        # 獲取底層函數而不是 actor 裝飾器
        from backend.tasks.services.dramatiq_tasks import process_complete_eqc_workflow_task
        task_function = process_complete_eqc_workflow_task.fn  # 獲取原始函數

        result = await task_function(
            folder_path=test_folder,
            user_session_id=test_session_id,
            options=test_options
        )
        
        execution_time = time.time() - start_time
        
        # 驗證結果
        logger.info("📊 任務執行結果:")
        logger.info(f"  - 狀態: {result.get('status', 'unknown')}")
        logger.info(f"  - 執行時間: {execution_time:.2f} 秒")
        logger.info(f"  - 會話ID: {result.get('user_session_id', 'N/A')}")
        logger.info(f"  - 步驟完成: {result.get('steps_completed', 0)}/4")
        
        # 檢查結果完整性
        required_fields = ['status', 'folder_path', 'user_session_id', 'processing_time']
        missing_fields = [field for field in required_fields if field not in result]
        
        if missing_fields:
            logger.error(f"❌ 結果缺少必要欄位: {missing_fields}")
            return False
        
        # 檢查執行狀態
        if result.get('status') == 'completed' and result.get('success'):
            logger.success("✅ Dramatiq EQC 基礎功能測試通過!")
            logger.info("🎯 測試結論:")
            logger.info("  - ✅ Dramatiq 任務正確執行")
            logger.info("  - ✅ 與 EQCProcessingService 整合成功")
            logger.info("  - ✅ 4步驟工作流程完整執行")
            logger.info("  - ✅ 結果格式符合預期")
            return True
        else:
            logger.error("❌ 任務執行失敗")
            logger.error(f"  - 錯誤: {result.get('error', '未知錯誤')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 測試執行異常: {str(e)}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def test_dramatiq_concurrent():
    """測試 Dramatiq 併發能力"""
    logger.info("🔄 開始 Dramatiq 併發能力測試")
    
    # 創建多個測試會話
    test_sessions = []
    for i in range(3):
        session_id = f"concurrent_test_{i}_{int(time.time())}"
        test_folder = f"D:\\project\\python\\outlook_summary\\test_data\\eqc_concurrent_{i}"
        
        # 創建測試資料夾
        Path(test_folder).mkdir(parents=True, exist_ok=True)
        Path(test_folder, "test.csv").write_text("test,data\n1,2\n")
        
        test_sessions.append({
            'session_id': session_id,
            'folder_path': test_folder,
            'options': {'test_mode': True}
        })
    
    try:
        # 同時執行多個任務
        logger.info(f"🚀 同時執行 {len(test_sessions)} 個任務...")
        start_time = time.time()
        
        tasks = []
        for session in test_sessions:
            # 獲取底層函數
            task_function = process_complete_eqc_workflow_task.fn
            task = task_function(
                folder_path=session['folder_path'],
                user_session_id=session['session_id'],
                options=session['options']
            )
            tasks.append(task)
        
        # 等待所有任務完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        execution_time = time.time() - start_time
        
        # 分析結果
        successful_tasks = 0
        failed_tasks = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ 任務 {i} 異常: {str(result)}")
                failed_tasks += 1
            elif result.get('status') == 'completed':
                logger.info(f"✅ 任務 {i} 成功完成")
                successful_tasks += 1
            else:
                logger.warning(f"⚠️ 任務 {i} 失敗: {result.get('error', '未知')}")
                failed_tasks += 1
        
        logger.info("📊 併發測試結果:")
        logger.info(f"  - 總任務數: {len(test_sessions)}")
        logger.info(f"  - 成功任務: {successful_tasks}")
        logger.info(f"  - 失敗任務: {failed_tasks}")
        logger.info(f"  - 總執行時間: {execution_time:.2f} 秒")
        logger.info(f"  - 平均每任務: {execution_time/len(test_sessions):.2f} 秒")
        
        if successful_tasks == len(test_sessions):
            logger.success("✅ Dramatiq 併發測試通過!")
            return True
        else:
            logger.error("❌ 部分任務失敗，併發測試未完全通過")
            return False
            
    except Exception as e:
        logger.error(f"❌ 併發測試異常: {str(e)}")
        return False


async def main():
    """主測試函數"""
    logger.info("🎯 Dramatiq EQC 測試套件開始")
    
    # 測試 1: 基礎功能
    basic_test_passed = await test_dramatiq_eqc_basic()
    
    # 測試 2: 併發能力 (僅在基礎測試通過時執行)
    if basic_test_passed:
        concurrent_test_passed = await test_dramatiq_concurrent()
    else:
        logger.warning("⚠️ 基礎測試未通過，跳過併發測試")
        concurrent_test_passed = False
    
    # 總結
    logger.info("📋 測試總結:")
    logger.info(f"  - 基礎功能測試: {'✅ 通過' if basic_test_passed else '❌ 失敗'}")
    logger.info(f"  - 併發能力測試: {'✅ 通過' if concurrent_test_passed else '❌ 失敗'}")
    
    if basic_test_passed and concurrent_test_passed:
        logger.success("🎉 所有測試通過! Dramatiq EQC 系統準備就緒")
        return True
    else:
        logger.error("❌ 部分測試失敗，需要進一步調試")
        return False


if __name__ == "__main__":
    asyncio.run(main())
