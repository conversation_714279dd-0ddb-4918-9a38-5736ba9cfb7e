"""
統一監控儀表板 - 路由和中介軟體單元測試
測試路由註冊和中介軟體功能

符合需求 11：API 端點和整合功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi import FastAPI
from fastapi.testclient import TestClient

from backend.monitoring.api.dashboard_router_manager import (
    DashboardRouterManager,
    get_dashboard_router_manager,
    register_dashboard_routes
)
from backend.monitoring.api.dashboard_middleware import (
    DashboardMiddlewareManager,
    DashboardCORSMiddleware,
    DashboardRateLimitMiddleware,
    DashboardRequestLoggingMiddleware,
    DashboardSecurityMiddleware,
    RateLimitRule,
    SecurityConfig
)
from backend.monitoring.integration.dashboard_routing_integration import (
    DashboardRoutingIntegration,
    initialize_dashboard_routing
)
from backend.monitoring.config.dashboard_config import DashboardConfig


class TestDashboardRouterManager:
    """測試儀表板路由管理器"""
    
    def test_router_manager_initialization(self):
        """測試路由管理器初始化"""
        config = DashboardConfig()
        router_manager = DashboardRouterManager(config)
        
        assert router_manager.config == config
        assert router_manager.registered_routers == []
        assert router_manager.route_stats["total_routes"] == 0
    
    def test_register_all_routes(self):
        """測試註冊所有路由"""
        app = FastAPI()
        router_manager = DashboardRouterManager()
        
        # 執行路由註冊
        router_manager.register_all_routes(app)
        
        # 驗證路由已註冊
        assert len(router_manager.registered_routers) > 0
        assert router_manager.route_stats["total_routes"] > 0
        
        # 檢查特定路由器是否已註冊
        router_names = [r["name"] for r in router_manager.registered_routers]
        assert "monitoring_api" in router_names
        assert "websocket" in router_names
    
    def test_get_route_info(self):
        """測試獲取路由資訊"""
        router_manager = DashboardRouterManager()
        route_info = router_manager.get_route_info()
        
        assert "registered_routers" in route_info
        assert "statistics" in route_info
        assert "config" in route_info
        
        # 檢查統計資訊結構
        stats = route_info["statistics"]
        assert "total_routes" in stats
        assert "api_routes" in stats
        assert "websocket_routes" in stats
    
    def test_get_route_list(self):
        """測試獲取路由列表"""
        router_manager = DashboardRouterManager()
        routes = router_manager.get_route_list()
        
        assert isinstance(routes, list)
        assert len(routes) > 0
        
        # 檢查路由項目結構
        route = routes[0]
        assert "path" in route
        assert "method" in route
        assert "description" in route
    
    def test_singleton_pattern(self):
        """測試單例模式"""
        manager1 = get_dashboard_router_manager()
        manager2 = get_dashboard_router_manager()
        
        assert manager1 is manager2


class TestDashboardMiddleware:
    """測試儀表板中介軟體"""
    
    def test_cors_middleware_initialization(self):
        """測試 CORS 中介軟體初始化"""
        security_config = SecurityConfig()
        cors_middleware = DashboardCORSMiddleware(security_config)
        
        assert cors_middleware.config == security_config
    
    def test_rate_limit_rule(self):
        """測試速率限制規則"""
        rule = RateLimitRule(
            requests_per_minute=30,
            requests_per_hour=500,
            burst_limit=5,
            enabled=True
        )
        
        assert rule.requests_per_minute == 30
        assert rule.requests_per_hour == 500
        assert rule.burst_limit == 5
        assert rule.enabled is True
    
    @pytest.mark.asyncio
    async def test_rate_limit_middleware_stats(self):
        """測試速率限制中介軟體統計"""
        app = FastAPI()
        rule = RateLimitRule(requests_per_minute=10, requests_per_hour=100)
        middleware = DashboardRateLimitMiddleware(app, rule)
        
        stats = middleware.get_stats()
        
        assert "total_requests" in stats
        assert "rate_limited_requests" in stats
        assert "unique_clients" in stats
        assert "rule" in stats
        
        # 檢查規則資訊
        rule_info = stats["rule"]
        assert rule_info["requests_per_minute"] == 10
        assert rule_info["requests_per_hour"] == 100
    
    @pytest.mark.asyncio
    async def test_request_logging_middleware_stats(self):
        """測試請求日誌中介軟體統計"""
        app = FastAPI()
        middleware = DashboardRequestLoggingMiddleware(app)
        
        stats = middleware.get_stats()
        
        assert "summary" in stats
        assert "status_codes" in stats
        assert "slowest_endpoints" in stats
        assert "endpoint_count" in stats
        
        # 檢查摘要資訊
        summary = stats["summary"]
        assert "total_requests" in summary
        assert "success_rate" in summary
        assert "avg_response_time" in summary


class TestDashboardMiddlewareManager:
    """測試儀表板中介軟體管理器"""
    
    def test_middleware_manager_initialization(self):
        """測試中介軟體管理器初始化"""
        config = DashboardConfig()
        manager = DashboardMiddlewareManager(config)
        
        assert manager.config == config
        assert manager.cors_middleware is not None
        assert manager.rate_limit_middleware is None  # 初始化時為 None
        assert manager.logging_middleware is None
        assert manager.security_middleware is None
    
    def test_setup_all_middleware(self):
        """測試設定所有中介軟體"""
        app = FastAPI()
        manager = DashboardMiddlewareManager()
        
        # 執行中介軟體設定
        manager.setup_all_middleware(app)
        
        # 驗證中介軟體已設定
        assert manager.rate_limit_middleware is not None
        assert manager.logging_middleware is not None
        assert manager.security_middleware is not None
    
    def test_get_middleware_stats(self):
        """測試獲取中介軟體統計"""
        manager = DashboardMiddlewareManager()
        stats = manager.get_middleware_stats()
        
        assert "cors" in stats
        assert "security" in stats
        assert "rate_limiting" in stats
        assert "request_logging" in stats
        
        # 檢查 CORS 狀態
        cors_stats = stats["cors"]
        assert cors_stats["enabled"] is True
        assert cors_stats["status"] == "active"


class TestDashboardRoutingIntegration:
    """測試儀表板路由整合"""
    
    def test_routing_integration_initialization(self):
        """測試路由整合初始化"""
        config = DashboardConfig()
        integration = DashboardRoutingIntegration(config)
        
        assert integration.config == config
        assert integration.router_manager is None
        assert integration.middleware_manager is None
        assert integration.is_initialized is False
    
    @pytest.mark.asyncio
    async def test_initialize_integration(self):
        """測試初始化整合"""
        app = FastAPI()
        integration = DashboardRoutingIntegration()
        
        # 模擬初始化過程
        with patch.object(integration, '_setup_middleware') as mock_middleware, \
             patch.object(integration, '_register_routes') as mock_routes, \
             patch.object(integration, '_register_middleware_api') as mock_api, \
             patch.object(integration, '_setup_lifecycle_events') as mock_lifecycle:
            
            mock_middleware.return_value = None
            mock_routes.return_value = None
            mock_api.return_value = None
            mock_lifecycle.return_value = None
            
            result = await integration.initialize(app)
            
            assert result is True
            assert integration.is_initialized is True
            assert integration.initialization_time is not None
            
            # 驗證方法被調用
            mock_middleware.assert_called_once_with(app)
            mock_routes.assert_called_once_with(app)
            mock_api.assert_called_once_with(app)
            mock_lifecycle.assert_called_once_with(app)
    
    def test_get_integration_status(self):
        """測試獲取整合狀態"""
        integration = DashboardRoutingIntegration()
        status = integration.get_integration_status()
        
        assert "initialized" in status
        assert "initialization_time" in status
        assert "router_manager" in status
        assert "middleware_manager" in status
        
        # 檢查初始狀態
        assert status["initialized"] is False
        assert status["initialization_time"] is None
    
    def test_get_health_status(self):
        """測試獲取健康狀態"""
        integration = DashboardRoutingIntegration()
        health = integration.get_health_status()
        
        assert "overall_status" in health
        assert "components" in health
        
        # 檢查組件狀態
        components = health["components"]
        assert "integration" in components
        assert "router_manager" in components
        assert "middleware_manager" in components
        
        # 檢查初始健康狀態
        assert health["overall_status"] in ["healthy", "degraded", "unhealthy"]


class TestUtilityFunctions:
    """測試工具函數"""
    
    def test_register_dashboard_routes(self):
        """測試註冊儀表板路由函數"""
        app = FastAPI()
        config = DashboardConfig()
        
        router_manager = register_dashboard_routes(app, config)
        
        assert isinstance(router_manager, DashboardRouterManager)
        assert router_manager.config == config
        assert len(router_manager.registered_routers) > 0
    
    @pytest.mark.asyncio
    async def test_initialize_dashboard_routing(self):
        """測試初始化儀表板路由函數"""
        app = FastAPI()
        config = DashboardConfig()
        
        with patch.object(DashboardRoutingIntegration, 'initialize', return_value=True):
            integration = await initialize_dashboard_routing(app, config)
            
            assert isinstance(integration, DashboardRoutingIntegration)
            assert integration.config == config


class TestIntegrationWithFastAPI:
    """測試與 FastAPI 的整合"""
    
    def test_fastapi_integration(self):
        """測試與 FastAPI 的完整整合"""
        app = FastAPI()
        
        # 註冊路由和中介軟體
        router_manager = register_dashboard_routes(app)
        
        # 創建測試客戶端
        client = TestClient(app)
        
        # 測試健康檢查端點（如果存在）
        # 注意：這裡可能需要根據實際的端點進行調整
        try:
            response = client.get("/api/monitoring/health")
            # 如果端點存在，檢查響應
            if response.status_code != 404:
                assert response.status_code in [200, 503]
        except Exception:
            # 如果端點不存在或有其他問題，跳過測試
            pass
    
    def test_cors_headers(self):
        """測試 CORS 標頭"""
        app = FastAPI()
        
        # 設定中介軟體
        cors_middleware = DashboardCORSMiddleware()
        cors_middleware.setup_cors(app)
        
        client = TestClient(app)
        
        # 測試 OPTIONS 請求
        response = client.options("/")
        
        # 檢查 CORS 標頭（如果中介軟體正確設定）
        if "access-control-allow-origin" in response.headers:
            assert response.headers["access-control-allow-origin"] == "*"


@pytest.fixture
def sample_config():
    """提供測試用的配置"""
    return DashboardConfig()


@pytest.fixture
def sample_app():
    """提供測試用的 FastAPI 應用"""
    return FastAPI()


@pytest.fixture
def sample_router_manager(sample_config):
    """提供測試用的路由管理器"""
    return DashboardRouterManager(sample_config)


@pytest.fixture
def sample_middleware_manager(sample_config):
    """提供測試用的中介軟體管理器"""
    return DashboardMiddlewareManager(sample_config)


# 整合測試
class TestFullIntegration:
    """完整整合測試"""
    
    @pytest.mark.asyncio
    async def test_complete_setup(self, sample_app, sample_config):
        """測試完整的設定流程"""
        from backend.monitoring.integration.dashboard_routing_integration import (
            setup_dashboard_routing_and_middleware
        )
        
        # 模擬完整設定
        with patch('src.dashboard_monitoring.integration.dashboard_routing_integration.initialize_dashboard_routing') as mock_init:
            mock_integration = Mock()
            mock_integration.get_integration_status.return_value = {
                "initialized": True,
                "router_manager": {"available": True, "routes_registered": 25},
                "middleware_manager": {"available": True, "middleware_count": 4}
            }
            mock_init.return_value = mock_integration
            
            result = await setup_dashboard_routing_and_middleware(sample_app, sample_config)
            
            assert result["success"] is True
            assert result["integration"] is not None
            assert result["status"] is not None
            assert "成功" in result["message"]