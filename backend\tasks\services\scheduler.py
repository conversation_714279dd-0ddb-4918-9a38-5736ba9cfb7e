"""
檔案清理調度器
提供自動定時清理功能
"""

import os
import logging
from typing import Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger

from backend.shared.infrastructure.adapters.file_cleaner import FileCleaner


class FileCleanupScheduler:
    """檔案清理調度器類別"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化調度器
        
        Args:
            logger: 可選的日誌記錄器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.scheduler = BackgroundScheduler()
        self.file_cleaner = FileCleaner(logger=self.logger)
        self.is_running = False
        
    def start_cleanup_job(self, 
                         target_directories: list,
                         cleanup_interval_hours: int = 1,
                         file_retention_hours: int = 24):
        """
        啟動定時清理任務
        
        Args:
            target_directories: 要清理的目錄列表
            cleanup_interval_hours: 清理檢查間隔（小時）
            file_retention_hours: 檔案保留時間（小時）
        """
        if self.is_running:
            self.logger.warning("清理任務已在運行中")
            return
            
        self.logger.info(f"啟動檔案清理調度器")
        self.logger.info(f"目標目錄: {target_directories}")
        self.logger.info(f"檢查間隔: {cleanup_interval_hours}小時")
        self.logger.info(f"檔案保留: {file_retention_hours}小時")
        
        # 添加定時任務
        self.scheduler.add_job(
            func=self._cleanup_files,
            trigger=IntervalTrigger(hours=cleanup_interval_hours),
            args=[target_directories, file_retention_hours],
            id='file_cleanup_job',
            name='File Cleanup Job',
            replace_existing=True
        )
        
        # 啟動調度器
        self.scheduler.start()
        self.is_running = True
        
        self.logger.info("[OK] 檔案清理調度器已啟動")
        
    def stop_cleanup_job(self):
        """停止清理任務"""
        if not self.is_running:
            self.logger.warning("清理任務未在運行")
            return
            
        self.scheduler.shutdown()
        self.is_running = False
        self.logger.info("[OK] 檔案清理調度器已停止")
        
    def _cleanup_files(self, target_directories: list, retention_hours: int):
        """
        執行檔案清理
        
        Args:
            target_directories: 目錄列表
            retention_hours: 保留時間
        """
        self.logger.info("[REFRESH] 開始執行定時檔案清理...")
        
        total_cleaned = 0
        
        for directory in target_directories:
            try:
                if os.path.exists(directory):
                    # 使用遞歸清理方法，清理子目錄
                    cleaned_files = self.file_cleaner.clean_old_files_recursive(
                        directory, 
                        hours=retention_hours
                    )
                    total_cleaned += len(cleaned_files)
                    
                    if cleaned_files:
                        self.logger.info(f"[FOLDER] {directory}: 遞歸清理了 {len(cleaned_files)} 個檔案")
                else:
                    self.logger.warning(f"[WARNING] 目錄不存在: {directory}")
                    
            except Exception as e:
                self.logger.error(f"[ERROR] 清理目錄 {directory} 失敗: {e}")
                
        self.logger.info(f"[OK] 定時清理完成，共清理 {total_cleaned} 個檔案")
        
    def manual_cleanup(self, target_directories: list, retention_hours: int = 24) -> dict:
        """
        手動執行清理
        
        Args:
            target_directories: 目錄列表
            retention_hours: 保留時間
            
        Returns:
            清理結果字典
        """
        self.logger.info("[TOOL] 執行手動檔案清理...")
        
        results = {
            'total_cleaned': 0,
            'directories': {},
            'errors': []
        }
        
        for directory in target_directories:
            try:
                if os.path.exists(directory):
                    # 使用遞歸清理方法，清理子目錄
                    cleaned_files = self.file_cleaner.clean_old_files_recursive(
                        directory, 
                        hours=retention_hours
                    )
                    
                    results['directories'][directory] = {
                        'cleaned_count': len(cleaned_files),
                        'cleaned_files': [os.path.relpath(f, directory) for f in cleaned_files]
                    }
                    results['total_cleaned'] += len(cleaned_files)
                    
                    self.logger.info(f"[FOLDER] {directory}: 遞歸清理了 {len(cleaned_files)} 個檔案")
                else:
                    error_msg = f"目錄不存在: {directory}"
                    results['errors'].append(error_msg)
                    self.logger.warning(f"[WARNING] {error_msg}")
                    
            except Exception as e:
                error_msg = f"清理目錄 {directory} 失敗: {e}"
                results['errors'].append(error_msg)
                self.logger.error(f"[ERROR] {error_msg}")
                
        self.logger.info(f"[OK] 手動清理完成，共清理 {results['total_cleaned']} 個檔案")
        return results
        
    def get_status(self) -> dict:
        """
        獲取調度器狀態
        
        Returns:
            狀態資訊字典
        """
        if not self.is_running:
            return {
                'status': 'stopped',
                'jobs': []
            }
            
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': str(job.next_run_time) if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
            
        return {
            'status': 'running',
            'jobs': jobs
        }