"""
統一監控儀表板 - 資料模型測試

此檔案用於測試資料模型的基本功能，確保所有模型都能正常創建和序列化。
"""

from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from backend.monitoring.models.dashboard_metrics_models import (
    DashboardMetrics, EmailMetrics, DramatiqMetrics, SystemMetrics,
    FileMetrics, BusinessMetrics, create_empty_dashboard_metrics,
    validate_metrics, ServiceHealth
)
from backend.monitoring.models.dashboard_alert_models import (
    DashboardAlert, AlertRule, AlertLevel, AlertType, NotificationChannel,
    create_default_alert_rules, calculate_alert_summary
)
from backend.monitoring.models.dashboard_models import (
    DashboardConfig, UserPreferences, DashboardState,
    create_default_dashboard_config, create_default_user_preferences
)


def test_dashboard_metrics_creation():
    """測試儀表板指標創建"""
    print("測試儀表板指標創建...")
    
    # 創建空的指標實例
    metrics = create_empty_dashboard_metrics()
    assert metrics is not None
    assert isinstance(metrics.timestamp, datetime)
    
    # 測試 Dramatiq 指標 (移除 Celery)
    dramatiq_metrics = DramatiqMetrics()
    assert dramatiq_metrics.total_active == 0
    assert "code_comparison" in dramatiq_metrics.task_type_counts
    assert "csv_to_summary" in dramatiq_metrics.task_type_counts
    assert "compression" in dramatiq_metrics.task_type_counts
    assert "decompression" in dramatiq_metrics.task_type_counts
    assert "email_processing" in dramatiq_metrics.task_type_counts
    assert "data_analysis" in dramatiq_metrics.task_type_counts
    assert "file_processing" in dramatiq_metrics.task_type_counts
    assert "batch_processing" in dramatiq_metrics.task_type_counts
    
    # 測試序列化
    metrics_dict = metrics.to_dict()
    assert "timestamp" in metrics_dict
    assert "dramatiq_metrics" in metrics_dict
    assert "celery_metrics" not in metrics_dict  # 確保沒有 Celery
    
    print("✅ 儀表板指標創建測試通過")


def test_alert_models():
    """測試告警模型"""
    print("測試告警模型...")
    
    # 創建告警規則
    rules = create_default_alert_rules()
    assert len(rules) > 0
    
    # 測試告警創建
    alert = DashboardAlert(
        alert_type=AlertType.QUEUE_OVERFLOW,
        level=AlertLevel.WARNING,
        title="測試告警",
        message="這是一個測試告警",
        source="test_monitor"
    )
    
    assert alert.is_active()
    assert not alert.is_critical()
    
    # 測試告警確認
    alert.acknowledge("test_user")
    assert alert.is_acknowledged()
    
    # 測試序列化
    alert_dict = alert.to_dict()
    assert "id" in alert_dict
    assert "alert_type" in alert_dict
    assert alert_dict["level"] == "warning"
    
    print("✅ 告警模型測試通過")


def test_dashboard_config():
    """測試儀表板配置"""
    print("測試儀表板配置...")
    
    # 創建預設配置
    config = create_default_dashboard_config()
    assert config is not None
    assert config.title == "統一監控儀表板"
    
    # 測試閾值獲取
    cpu_warning = config.get_threshold("system_resources", "cpu", "warning")
    assert cpu_warning == 80
    
    # 測試閾值更新
    config.update_threshold("system_resources", "cpu", "warning", 85)
    updated_threshold = config.get_threshold("system_resources", "cpu", "warning")
    assert updated_threshold == 85
    
    # 測試序列化
    config_dict = config.to_dict()
    assert "title" in config_dict
    assert "alert_thresholds" in config_dict
    
    print("✅ 儀表板配置測試通過")


def test_user_preferences():
    """測試使用者偏好"""
    print("測試使用者偏好...")
    
    # 創建使用者偏好
    prefs = create_default_user_preferences("test_user")
    assert prefs.user_id == "test_user"
    
    # 測試活動更新
    prefs.update_activity()
    assert prefs.last_activity is not None
    assert prefs.login_count == 1
    
    # 測試序列化
    prefs_dict = prefs.to_dict()
    assert "user_id" in prefs_dict
    assert "theme" in prefs_dict
    
    print("✅ 使用者偏好測試通過")


def test_system_metrics():
    """測試系統指標"""
    print("測試系統指標...")
    
    # 創建系統指標
    system_metrics = SystemMetrics(
        cpu_percent=75.5,
        memory_percent=82.3,
        disk_percent=45.1
    )
    
    # 測試資源警告
    warnings = system_metrics.get_resource_warnings()
    # 記憶體82.3%沒有超過85%警告線，所以應該沒有警告
    assert len(warnings) == 0
    
    # 測試臨界狀態
    system_metrics.cpu_percent = 96
    assert system_metrics.is_resource_critical()
    
    # 測試服務健康狀態
    assert ServiceHealth.HEALTHY in system_metrics.service_health.values()
    
    print("✅ 系統指標測試通過")


def test_business_metrics():
    """測試業務指標"""
    print("測試業務指標...")
    
    # 創建業務指標
    business_metrics = BusinessMetrics(
        mo_processed_today=25,
        lot_processed_today=150,
        data_quality_score=88.5,
        validation_errors_count=3,
        duplicate_mo_count=2
    )
    
    # 測試資料品質問題摘要
    quality_issues = business_metrics.get_data_quality_issues()
    assert quality_issues["validation_errors"] == 3
    assert quality_issues["duplicate_mo"] == 2
    
    # 測試序列化
    business_dict = business_metrics.to_dict()
    assert "mo_processed_today" in business_dict
    assert "data_quality_score" in business_dict
    
    print("✅ 業務指標測試通過")


def test_dramatiq_metrics_functionality():
    """測試 Dramatiq 指標功能"""
    print("測試 Dramatiq 指標功能...")
    
    # 創建 Dramatiq 指標
    dramatiq_metrics = DramatiqMetrics(
        total_active=5,
        total_pending=12,
        total_completed=150,
        total_failed=8
    )
    
    # 更新任務類型計數
    dramatiq_metrics.task_type_counts["code_comparison"]["active"] = 2
    dramatiq_metrics.task_type_counts["csv_to_summary"]["pending"] = 3
    dramatiq_metrics.task_type_counts["compression"]["completed"] = 25
    
    # 測試總任務數計算
    total_tasks = dramatiq_metrics.get_total_tasks()
    assert total_tasks == 175  # 5 + 12 + 150 + 8
    
    # 測試成功率計算
    success_rate = dramatiq_metrics.get_overall_success_rate()
    assert success_rate > 0
    
    # 測試任務類型摘要
    code_comparison_summary = dramatiq_metrics.get_task_type_summary("code_comparison")
    assert "total_tasks" in code_comparison_summary
    assert code_comparison_summary["active"] == 2
    
    print("✅ Dramatiq 指標功能測試通過")


def run_all_tests():
    """執行所有測試"""
    print("🚀 開始執行資料模型測試...")
    print("=" * 50)
    
    try:
        test_dashboard_metrics_creation()
        test_alert_models()
        test_dashboard_config()
        test_user_preferences()
        test_system_metrics()
        test_business_metrics()
        test_dramatiq_metrics_functionality()
        
        print("=" * 50)
        print("🎉 所有資料模型測試通過！")
        print("✅ Dramatiq 架構統一完成")
        print("❌ Celery 相關模型已完全移除")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        raise


if __name__ == "__main__":
    run_all_tests()