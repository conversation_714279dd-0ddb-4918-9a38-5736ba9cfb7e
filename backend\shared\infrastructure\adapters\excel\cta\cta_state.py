#!/usr/bin/env python3
"""
CTA 處理狀態管理
對應 VBA 中的各種狀態變數
"""

from dataclasses import dataclass
from typing import Optional
from .cta_enums import TesterType, DataSectionState


@dataclass
class CTAProcessingState:
    """CTA 處理狀態管理 - 對應 VBA 變數"""
    
    # 核心狀態
    tester_type: TesterType = TesterType.UNKNOWN
    data_section_state: DataSectionState = DataSectionState.INITIAL
    
    # 行位置追蹤 - 對應 VBA 變數
    current_row: int = 12              # myRowN
    start_row: int = 13                # myStartRowN  
    stop_row: int = 0                  # myStopRowN
    source_sheet_index: int = 0        # mySourceSheetIndex
    total_device_number: int = 0       # myTotalDeviceNumber
    sum_end_row: int = 0              # mySumEndRowN
    
    # 關鍵欄位位置 - 對應 VBA 欄位變數
    serial_no_column: int = 0          # mySerial_NoColumnN
    dut_no_column: int = 0             # myDut_NoColumnN
    site_no_column: int = 0            # mySite_NoColumnN
    data_cnt_column: int = 0           # myColumnN (Data_Cnt/TEST_NUM)
    
    # 搜尋優化
    start_search_row: int = 0          # myStartSearchRowN
    
    # 額外狀態標記
    data11_device_count: int = 0       # myData11TotalDeviceNumber
    finded_row: int = 1                # myFindedRowN
    
    def reset(self):
        """重設狀態到初始值"""
        self.tester_type = TesterType.UNKNOWN
        self.data_section_state = DataSectionState.INITIAL
        self.current_row = 12
        self.start_row = 13
        self.stop_row = 0
        self.source_sheet_index = 0
        self.total_device_number = 0
        self.sum_end_row = 0
        
        self.serial_no_column = 0
        self.dut_no_column = 0
        self.site_no_column = 0
        self.data_cnt_column = 0
        
        self.start_search_row = 0
        self.data11_device_count = 0
        self.finded_row = 1


# VBA 常數對應
class CTAConstants:
    """CTA 常數定義 - 對應 VBA 常數"""
    MAX_ROW = 165536          # maxRowN
    MAX_COLUMN = 6810         # maxColumnN  
    MAX_PASS_BIN = 4          # maxPassBinN
    MAX_SITE = 32             # maxSiteN