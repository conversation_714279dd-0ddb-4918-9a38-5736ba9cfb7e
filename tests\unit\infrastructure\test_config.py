"""
配置管理系統測試
遵循 TDD 原則 - 先寫失敗的測試
"""

import pytest
from pathlib import Path
# 移[EXCEPT_CHAR]未使用的 import
from pydantic import ValidationError

from backend.shared.infrastructure.config.settings import Settings, DatabaseConfig, EmailConfig


class TestSettings:
    """測試配置管理系統"""

    def test_load_default_settings(self):
        """測試載入預設配置"""
        settings = Settings()

        # 檢查預設值
        assert settings.app_name == "Outlook Summary System"
        assert settings.debug is False
        assert settings.log_level == "INFO"
        assert settings.temp_dir == Path("/tmp/outlook_summary")

    def test_database_config_default_values(self):
        """測試資料庫配置預設值"""
        db_config = DatabaseConfig()

        assert db_config.driver == "sqlite"
        assert db_config.host == "localhost"
        assert db_config.port == 5432
        assert db_config.database_name == "outlook_summary"
        assert db_config.username == ""
        assert db_config.password == ""

    def test_database_config_connection_string(self):
        """測試資料庫連線字串生成"""
        # SQLite 連線字串
        sqlite_config = DatabaseConfig(driver="sqlite", database_name="test.db")
        expected_sqlite = "sqlite:///test.db"
        assert sqlite_config.connection_string == expected_sqlite

        # PostgreSQL 連線字串
        pg_config = DatabaseConfig(
            driver="postgresql",
            host="localhost",
            port=5432,
            database_name="test_db",
            username="user",
            password="pass",
        )
        expected_pg = "postgresql://user:pass@localhost:5432/test_db"
        assert pg_config.connection_string == expected_pg

    def test_email_config_default_values(self):
        """測試郵件配置預設值"""
        email_config = EmailConfig()

        assert email_config.monitor_enabled is True
        assert email_config.check_interval == 30
        assert email_config.max_retries == 3
        assert email_config.sender_address == ""
        assert email_config.sender_password == ""

    def test_settings_load_from_dict(self):
        """測試從字典載入配置"""
        config_data = {
            "app_name": "測試應用程式",
            "debug": True,
            "log_level": "DEBUG",
            "temp_dir": "/tmp/test",
            "database": {
                "driver": "postgresql",
                "host": "db.example.com",
                "port": 5432,
                "database_name": "production_db",
                "username": "prod_user",
                "password": "prod_pass",
            },
            "email": {
                "monitor_enabled": False,
                "check_interval": 60,
                "max_retries": 5,
                "sender_address": "<EMAIL>",
                "sender_password": "secret123",
            },
        }

        settings = Settings.from_dict(config_data)

        # 檢查主要設定
        assert settings.app_name == "測試應用程式"
        assert settings.debug is True
        assert settings.log_level == "DEBUG"
        assert settings.temp_dir == Path("/tmp/test")

        # 檢查資料庫設定
        assert settings.database.driver == "postgresql"
        assert settings.database.host == "db.example.com"
        assert settings.database.database_name == "production_db"

        # 檢查郵件設定
        assert settings.email.monitor_enabled is False
        assert settings.email.check_interval == 60
        assert settings.email.sender_address == "<EMAIL>"

    def test_settings_validation_invalid_log_level(self):
        """測試無效日誌級別驗證"""
        with pytest.raises(ValidationError):
            Settings(log_level="INVALID")

    def test_settings_validation_invalid_driver(self):
        """測試無效資料庫驅動驗證"""
        with pytest.raises(ValidationError):
            DatabaseConfig(driver="mysql")  # 目前只支援 sqlite 和 postgresql

    def test_settings_validation_negative_port(self):
        """測試負數埠號驗證"""
        with pytest.raises(ValidationError):
            DatabaseConfig(port=-1)

    def test_settings_validation_negative_check_interval(self):
        """測試負數檢查間隔驗證"""
        with pytest.raises(ValidationError):
            EmailConfig(check_interval=-1)

    def test_settings_environment_variable_override(self):
        """測試環境變數覆蓋配置"""
        import os

        # 設定環境變數
        os.environ["OUTLOOK_DEBUG"] = "true"
        os.environ["OUTLOOK_LOG_LEVEL"] = "WARNING"
        os.environ["OUTLOOK_DB_HOST"] = "env.example.com"

        try:
            settings = Settings.from_environment()

            assert settings.debug is True
            assert settings.log_level == "WARNING"
            assert settings.database.host == "env.example.com"

        finally:
            # 清理環境變數
            for key in ["OUTLOOK_DEBUG", "OUTLOOK_LOG_LEVEL", "OUTLOOK_DB_HOST"]:
                os.environ.pop(key, None)

    def test_settings_to_dict(self):
        """測試配置轉換為字典"""
        settings = Settings(
            app_name="測試應用",
            debug=True,
            database=DatabaseConfig(driver="postgresql", host="test.db"),
            email=EmailConfig(check_interval=45),
        )

        result = settings.to_dict()

        assert result["app_name"] == "測試應用"
        assert result["debug"] is True
        assert result["database"]["driver"] == "postgresql"
        assert result["database"]["host"] == "test.db"
        assert result["email"]["check_interval"] == 45
