#!/usr/bin/env python3
"""
一鍵完成程式碼對比處理 - 命令列工具 (完整流程版)

此工具提供完整的處理流程，從壓縮檔到最終報告：
1. 解壓縮處理（支援 ZIP, 7Z, RAR 等格式）
2. 遞迴解壓縮資料夾內的壓縮檔
3. 檔案預處理（SPD→CSV 轉換、刪除特定檔案）
4. EQC 兩階段處理：
   - 第一階段: EQCBin1FinalProcessor.process_complete_eqc_integration()
   - 第二階段: StandardEQCProcessor.process_from_stage2_only()
5. CSV to Summary 生成摘要報告

版本: v3.0.0 (完整流程版)
作者: AI Assistant
"""

import argparse
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List

# 動態添加 src 路徑
def add_src_path():
    """動態添加 src 路徑到 sys.path"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    src_path = os.path.join(current_dir, 'src')
    if os.path.exists(src_path):
        sys.path.insert(0, src_path)
    else:
        # 備用路徑
        parent_src = os.path.join(os.path.dirname(current_dir), 'src')
        if os.path.exists(parent_src):
            sys.path.insert(0, parent_src)

add_src_path()

try:
    from backend.shared.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor import EQCBin1FinalProcessorV2
    from backend.shared.infrastructure.adapters.excel.eqc.processors.eqc_standard_processor import StandardEQCProcessor
except ImportError as e:
    print(f"[ERROR] 錯誤: 無法匯入核心處理器: {e}")
    print("請確認以下檔案存在且可匯入:")
    print("  - src/infrastructure/adapters/excel/eqc/eqc_bin1_final_processor.py")
    print("  - src/infrastructure/adapters/excel/eqc/processors/eqc_standard_processor.py")
    sys.exit(1)

# 新增 imports - 解壓縮和預處理
try:
    from backend.file_management.adapters.file_upload.archive_extractor import ArchiveExtractor
    from backend.shared.infrastructure.adapters.excel.cta.cta_integrated_processor import extract_compressed_files
except ImportError as e:
    print(f"[WARNING] 警告: 無法匯入解壓縮模組: {e}")
    print("解壓縮功能將無法使用")
    ArchiveExtractor = None
    extract_compressed_files = None

# 新增 import - CSV to Summary
try:
    from csv_to_summary import main as csv_to_summary_main
except ImportError as e:
    print(f"[WARNING] 警告: 無法匯入 csv_to_summary: {e}")
    print("CSV to Summary 功能將無法使用")
    csv_to_summary_main = None

def parse_arguments():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(
        description="一鍵完成程式碼對比處理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例:
  code_comparison.py doc/20250523                           # 處理資料夾 (只產生 Summary)
  code_comparison.py doc/20250523 --excel                   # 處理資料夾 (產生 Excel + Summary)
  code_comparison.py data.zip                               # 處理壓縮檔 (只產生 Summary)
  code_comparison.py data.zip --excel                       # 處理壓縮檔 (產生 Excel + Summary)
  code_comparison.py doc/20250523 --code-region 298,335,1565,1600  # 自訂程式碼區間
  code_comparison.py data.7z --excel --verbose              # 完整模式 + 詳細輸出

完整流程:
  1. 解壓縮（如果是壓縮檔）
  2. 遞迴解壓縮資料夾內的壓縮檔
  3. 預處理（SPD→CSV、刪除特定檔案）
  4. EQC 兩階段處理
  5. CSV to Summary 生成報告（--excel 同時產生 Excel 檔案）
        """
    )
    
    parser.add_argument(
        'folder_path',
        help='CSV 檔案資料夾路徑或壓縮檔路徑'
    )
    
    parser.add_argument(
        '--code-region',
        dest='code_region',
        help='程式碼區間設定，格式：main_start,main_end,backup_start,backup_end (例: 298,335,1565,1600)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='詳細輸出模式'
    )
    
    parser.add_argument(
        '--excel',
        action='store_true',
        help='同時產生 Excel 檔案（預設只產生 Summary）'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='code_comparison.py v3.0.0'
    )
    
    return parser.parse_args()

def validate_folder_path(folder_path: str) -> str:
    """驗證資料夾路徑是否有效"""
    if not folder_path:
        raise ValueError("資料夾路徑不能為空")
    
    # 轉換為絕對路徑
    abs_path = os.path.abspath(folder_path)
    
    if not os.path.exists(abs_path):
        raise FileNotFoundError(f"資料夾不存在: {abs_path}")
    
    if not os.path.isdir(abs_path):
        raise NotADirectoryError(f"路徑不是資料夾: {abs_path}")
    
    return abs_path

def build_code_regions(args) -> Optional[Dict[str, int]]:
    """構建程式碼區間設定"""
    if not args.code_region:
        return None
    
    try:
        # 解析格式：main_start,main_end,backup_start,backup_end
        parts = [int(x.strip()) for x in args.code_region.split(',')]
        
        if len(parts) != 4:
            raise ValueError("程式碼區間格式錯誤，需要4個數字")
        
        main_start, main_end, backup_start, backup_end = parts
        
        # 驗證區間有效性
        if main_start >= main_end:
            raise ValueError("主要區間起始位置必須小於結束位置")
        
        if backup_start >= backup_end:
            raise ValueError("備用區間起始位置必須小於結束位置")
        
        return {
            'main_start': main_start,
            'main_end': main_end,
            'backup_start': backup_start,
            'backup_end': backup_end
        }
        
    except ValueError as e:
        raise ValueError(f"程式碼區間解析失敗: {e}")

def display_header(folder_path: str, code_regions: Optional[Dict[str, int]], verbose: bool):
    """顯示工具標題和設定資訊"""
    print("=" * 80)
    print("[ROCKET] 一鍵完成程式碼對比處理工具")
    print("=" * 80)
    print(f"[FOLDER] 處理資料夾: {folder_path}")
    
    if code_regions:
        print(f"[TARGET] 程式碼區間設定:")
        print(f"   主要區間: {code_regions['main_start']}-{code_regions['main_end']}")
        print(f"   備用區間: {code_regions['backup_start']}-{code_regions['backup_end']}")
    else:
        print("[TARGET] 程式碼區間設定: 使用預設值")
    
    print(f"[CLOCK] 開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"[SEARCH] 詳細模式: {'啟用' if verbose else '關閉'}")
    print("-" * 80)

def display_results(result: Dict[str, Any], execution_time: float):
    """顯示處理結果 (支援兩階段結果格式)"""
    print("\n" + "=" * 80)
    print("[CHART] 處理結果摘要")
    print("=" * 80)

    # 基本狀態
    status_icon = "[OK]" if result['status'] == 'success' else "[ERROR]"
    print(f"{status_icon} 狀態: {result['status']}")
    print(f"[EDIT] 訊息: {result['message']}")
    print(f"[TIMER] 總執行時間: {execution_time:.2f} 秒")

    if result['status'] == 'success':
        # 處理模式
        if 'processing_mode' in result:
            print(f"[REFRESH] 處理模式: {result['processing_mode']}")

        # 第一階段結果
        if 'stage1_result' in result:
            stage1 = result['stage1_result']
            print(f"\n[CHART] 第一階段結果:")
            print(f"   [OK] 狀態: {stage1['status']}")
            print(f"   [EDIT] 訊息: {stage1['message']}")
            if 'total_file' in stage1:
                print(f"   [FILE] EQCTOTALDATA.csv: {os.path.basename(stage1['total_file'])}")
            if 'raw_file' in stage1:
                print(f"   [FILE] EQCTOTALDATA_RAW.csv: {os.path.basename(stage1['raw_file'])}")

        # 第二階段結果
        if 'stage2_result' in result:
            stage2 = result['stage2_result']
            print(f"\n[REFRESH] 第二階段結果:")
            print(f"   [OK] 狀態: {stage2['status']}")
            if 'message' in stage2:
                print(f"   [EDIT] 訊息: {stage2['message']}")

            # 顯示處理步驟
            if 'processing_steps' in stage2:
                steps = stage2['processing_steps']
                print(f"   [NOTES] 處理步驟:")
                for step_name, step_info in steps.items():
                    if isinstance(step_info, dict) and 'status' in step_info:
                        step_icon = "[OK]" if step_info['status'] == 'success' else "[ERROR]"
                        print(f"      {step_icon} {step_name}: {step_info['status']}")

            # 顯示程式碼區間資訊
            if 'code_region_info' in stage2:
                region_info = stage2['code_region_info']
                print(f"   [TARGET] 程式碼區間:")
                if 'main_region' in region_info:
                    main = region_info['main_region']
                    print(f"      主要區間: {main.get('start', 'N/A')}-{main.get('end', 'N/A')}")
                if 'backup_region' in region_info:
                    backup = region_info['backup_region']
                    print(f"      備用區間: {backup.get('start', 'N/A')}-{backup.get('end', 'N/A')}")

        # 兼容舊格式 - 如果是單一結果格式
        if 'overall_success' in result:
            print(f"[TARGET] 整體成功: {result['overall_success']}")

        # 生成檔案資訊
        if 'files' in result and result['files']:
            print(f"\n[FILE] 生成檔案 ({len(result['files'])} 個):")
            for file_info in result['files']:
                size_info = f"({file_info['size_mb']:.1f}MB)" if 'size_mb' in file_info else ""
                print(f"   [NOTES] {file_info['name']} {size_info}")
                if 'path' in file_info:
                    print(f"      路徑: {file_info['path']}")

        # 統計資訊
        if 'statistics' in result:
            stats = result['statistics']
            print(f"\n[CHART] 處理統計:")
            for key, value in stats.items():
                print(f"   {key}: {value}")

    else:
        # 錯誤資訊
        if 'error_stage' in result:
            print(f"[ERROR] 失敗階段: {result['error_stage']}")

        if 'error_details' in result:
            print(f"[NOTES] 錯誤詳情: {result['error_details']}")

def save_result_json(result: Dict[str, Any], folder_path: str, execution_time: float):
    """保存結果到 JSON 檔案"""
    try:
        # 添加執行時間到結果中
        result['execution_time'] = execution_time
        result['timestamp'] = datetime.now().isoformat()
        
        # 生成結果檔案路徑
        result_filename = f"code_comparison_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        result_path = os.path.join(folder_path, result_filename)
        
        # 保存結果
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n[SAVE] 結果已保存: {result_path}")
        
    except Exception as e:
        print(f"[WARNING] 警告: 無法保存結果檔案: {e}")

def is_archive_file(path: str) -> bool:
    """判斷是否為支援的壓縮檔格式"""
    supported = ['.zip', '.7z', '.rar', '.tar', '.gz', '.bz2']
    return Path(path).suffix.lower() in supported

def process_csv_to_summary(folder_path: str, verbose: bool = False, with_excel: bool = False):
    """呼叫 csv_to_summary 處理"""
    if csv_to_summary_main is None:
        print("[WARNING] CSV to Summary 功能未載入")
        return False
        
    # 建立模擬的命令列參數
    import sys
    original_argv = sys.argv
    try:
        # 設定 csv_to_summary 的參數
        sys.argv = ['csv_to_summary.py', folder_path]
        
        # 只有指定 --excel 時才加入
        if with_excel:
            sys.argv.append('--excel')
            
        if verbose:
            sys.argv.append('--verbose')
        
        # 呼叫 csv_to_summary 的 main 函式
        result = csv_to_summary_main()
        return result == 0
    except Exception as e:
        print(f"[ERROR] CSV to Summary 執行失敗: {e}")
        return False
    finally:
        # 恢復原始參數
        sys.argv = original_argv

def main():
    """主執行函式"""
    try:
        # 解析命令列參數
        args = parse_arguments()
        
        # === 新增：處理輸入路徑 ===
        input_path = args.folder_path  # 保持原有參數名稱
        
        # 判斷是壓縮檔還是資料夾
        if is_archive_file(input_path):
            # 解壓縮
            print("[PACKAGE] 偵測到壓縮檔，開始解壓縮...")
            
            # 解壓到壓縮檔所在目錄，使用壓縮檔名稱作為資料夾名
            archive_dir = os.path.dirname(input_path)
            archive_name = Path(input_path).stem
            folder_path = os.path.join(archive_dir, archive_name)
            
            # 建立目標資料夾
            os.makedirs(folder_path, exist_ok=True)
            
            # 直接解壓縮到目標資料夾
            try:
                # 匯入解壓縮相關模組
                from backend.shared.infrastructure.adapters.excel.cta.cta_integrated_processor import (
                    extract_zip, extract_7z, extract_rar, extract_tar
                )
                
                # 根據副檔名選擇解壓縮方法
                file_ext = Path(input_path).suffix.lower()
                if file_ext == '.zip':
                    extracted_files = extract_zip(input_path, folder_path)
                elif file_ext == '.7z':
                    extracted_files = extract_7z(input_path, folder_path)
                elif file_ext == '.rar':
                    extracted_files = extract_rar(input_path, folder_path)
                elif file_ext in ['.tar', '.gz', '.tgz', '.bz2']:
                    extracted_files = extract_tar(input_path, folder_path)
                else:
                    print(f"[ERROR] 不支援的壓縮格式: {file_ext}")
                    return 1
                
                if extracted_files:
                    print(f"[OK] 解壓縮完成: {len(extracted_files)} 個檔案")
                    # 刪除原始壓縮檔
                    os.remove(input_path)
                    print("[TRASH] 已刪除原始壓縮檔")
                else:
                    print("[ERROR] 解壓縮失敗")
                    return 1
                    
            except Exception as e:
                print(f"[ERROR] 解壓縮失敗: {e}")
                return 1
        else:
            # 驗證資料夾路徑
            folder_path = validate_folder_path(input_path)
        
        # === 新增：遞迴解壓縮 ===
        if extract_compressed_files is not None:
            print("\n[SEARCH] 掃描並解壓縮資料夾內的壓縮檔...")
            extracted_files = extract_compressed_files(folder_path)
            if extracted_files:
                print(f"[OK] 解壓縮 {len(extracted_files)} 個檔案")
        
        # 構建程式碼區間設定
        code_regions = build_code_regions(args)
        
        # 顯示標題和設定
        display_header(folder_path, code_regions, args.verbose)
        
        # 記錄開始時間
        start_time = time.time()
        
        # 執行兩階段處理流程 (與Web按鈕相同的邏輯)
        print("[ROCKET] 開始執行兩階段程式碼對比處理...")
        print("-" * 80)

        # 第一階段：使用 EQCBin1FinalProcessor 生成 EQCTOTALDATA.csv
        print("[CHART] 第一階段：執行 EQC Bin1 Final Processor")
        print("[TOOL] 初始化第一階段處理器...")
        stage1_processor = EQCBin1FinalProcessorV2()

        print("[REFRESH] 執行 process_complete_eqc_integration()...")
        stage1_result_tuple = stage1_processor.process_complete_eqc_integration(
            folder_path,
            enable_debug_log=True  # 啟用詳細日誌
        )

        # 處理第一階段返回的tuple結果
        if stage1_result_tuple[0] is None or stage1_result_tuple[1] is None:
            print("[ERROR] 第一階段處理失敗: EQCTOTALDATA.csv生成失敗")
            return 1

        # 轉換為dict格式方便後續使用
        stage1_result = {
            'status': 'success',
            'total_file': stage1_result_tuple[0],
            'raw_file': stage1_result_tuple[1],
            'message': 'EQCTOTALDATA.csv生成成功'
        }

        print(f"[OK] 第一階段完成：{stage1_result['message']}")

        # 第二階段：使用 StandardEQCProcessor.process_from_stage2_only() 完整分析
        print("\n[REFRESH] 第二階段：執行 Standard EQC Processor (程式碼區間檢測與雙重搜尋)")
        print("[TOOL] 初始化第二階段處理器...")
        stage2_processor = StandardEQCProcessor()

        print("[REFRESH] 執行 process_from_stage2_only()...")
        print("[DEBUG] 傳入參數: include_inseqcrtdata2=True, include_step5_testflow=True, include_step6_excel=True")
        stage2_result = stage2_processor.process_from_stage2_only(
            folder_path,
            include_inseqcrtdata2=True,
            include_step5_testflow=True,
            include_step6_excel=True,
            include_final_excel_conversion=True,
            code_regions=code_regions
        )
        print(f"[DEBUG] stage2_result keys: {list(stage2_result.keys()) if stage2_result else 'None'}")

        # 檢查第二階段結果
        if stage2_result['status'] != 'success':
            print(f"[ERROR] 第二階段處理失敗: {stage2_result.get('message', '未知錯誤')}")
            return 1

        print(f"[OK] 第二階段完成：程式碼區間檢測與雙重搜尋完成")
        
        # 偵錯輸出：檢查 inseqcrtdata2_result 中的 step4_debug_log
        if 'inseqcrtdata2_result' in stage2_result:
            inseqc_result = stage2_result['inseqcrtdata2_result']
            print(f"[DEBUG] InsEqcRtData2 結果: {inseqc_result is not None}")
            if inseqc_result:
                print(f"[DEBUG] step4_debug_log 存在: {'step4_debug_log' in inseqc_result}")
                if 'step4_debug_log' in inseqc_result:
                    print(f"[DEBUG] step4_debug_log 路徑: {inseqc_result['step4_debug_log']}")
                else:
                    print(f"[DEBUG] inseqc_result keys: {list(inseqc_result.keys())}")
        
        # 偵錯輸出：檢查 step5_result 和 step6_result
        if 'step5_result' in stage2_result:
            step5 = stage2_result['step5_result']
            print(f"[DEBUG] Step5 結果: {step5 is not None}")
            if step5:
                print(f"[DEBUG] Step5 狀態: {step5.get('status')}")
        
        if 'step6_result' in stage2_result:
            step6 = stage2_result['step6_result']
            print(f"[DEBUG] Step6 結果: {step6 is not None}")
            if step6:
                print(f"[DEBUG] Step6 狀態: {step6.get('status')}")

        # 合併兩階段結果
        combined_result = {
            'status': 'success',
            'stage1_result': stage1_result,
            'stage2_result': stage2_result,
            'processing_mode': 'two_stage_flow',
            'message': '兩階段程式碼對比處理完成'
        }

        # 計算執行時間
        execution_time = time.time() - start_time

        # 顯示結果
        display_results(combined_result, execution_time)
        
        # 保存結果 JSON
        save_result_json(combined_result, folder_path, execution_time)

        # === 新增：執行 CSV to Summary ===
        print("\n" + "=" * 80)
        print("[CHART] 執行 CSV to Summary 處理...")
        print("=" * 80)
        
        if process_csv_to_summary(folder_path, args.verbose, args.excel):
            print("[OK] CSV to Summary 處理完成")
        else:
            print("[WARNING] CSV to Summary 處理失敗，但不影響主流程")
        
        # 根據結果決定退出代碼
        if combined_result['status'] == 'success':
            print(f"\n[PARTY] 完整流程處理完成！")
            return 0
        else:
            print(f"\n[ERROR] 程式碼對比處理失敗")
            return 1
            
    except KeyboardInterrupt:
        print(f"\n\n[WARNING] 使用者中斷執行")
        return 130
        
    except Exception as e:
        print(f"\n[ERROR] 執行失敗: {e}")
        if args.verbose if 'args' in locals() else False:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())