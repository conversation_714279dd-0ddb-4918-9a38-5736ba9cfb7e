"""錯誤分類測試
測試各種錯誤類型的分類、狀態碼和處理方式
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch

# 設置導入路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from fastapi import HTTPException


class TestServiceErrors:
    """測試服務相關錯誤"""
    
    def test_staging_service_unavailable(self):
        """測試：暫存服務不可用錯誤"""
        print("🧪 測試暫存服務不可用錯誤...")
        
        from frontend.api.error_handling import StagingServiceUnavailableError
        
        error = StagingServiceUnavailableError(
            details="資料庫連接失敗"
        )
        
        # 驗證錯誤屬性
        assert error.status_code == 503
        assert error.error_code == "STAGING_SERVICE_UNAVAILABLE"
        assert "暫存服務" in error.message
        assert error.details == "資料庫連接失敗"
        assert error.recovery_strategy_value == "retry"
        
        # 驗證錯誤響應格式
        response = error.to_error_response()
        assert response["success"] is False
        assert response["status_code"] == 503
        assert response["error"]["code"] == "STAGING_SERVICE_UNAVAILABLE"
        
        print("✅ 暫存服務不可用錯誤測試通過")
    
    def test_processing_service_unavailable(self):
        """測試：處理服務不可用錯誤"""
        print("🧪 測試處理服務不可用錯誤...")
        
        from frontend.api.error_handling import ProcessingServiceUnavailableError
        
        error = ProcessingServiceUnavailableError(
            details="LLM 服務連接超時"
        )
        
        assert error.status_code == 503
        assert error.error_code == "PROCESSING_SERVICE_UNAVAILABLE"
        assert "處理服務" in error.message
        assert error.details == "LLM 服務連接超時"
        
        print("✅ 處理服務不可用錯誤測試通過")
    
    def test_service_initialization_error(self):
        """測試：服務初始化錯誤"""
        print("🧪 測試服務初始化錯誤...")
        
        from frontend.api.error_handling import ServiceInitializationError
        
        error = ServiceInitializationError(
            service_name="檔案暫存服務",
            initialization_error="配置文件不存在"
        )
        
        assert error.status_code == 503
        assert error.error_code == "SERVICE_INITIALIZATION_ERROR"
        assert "檔案暫存服務" in error.message
        assert "配置文件不存在" in error.details
        assert error.recovery_strategy_value == "fail_fast"
        
        print("✅ 服務初始化錯誤測試通過")


class TestValidationErrors:
    """測試驗證錯誤"""
    
    def test_required_field_missing(self):
        """測試：必需字段缺失錯誤"""
        print("🧪 測試必需字段缺失錯誤...")
        
        from frontend.api.error_handling import RequiredFieldMissingError
        
        error = RequiredFieldMissingError(
            field_name="product_name"
        )
        
        assert error.status_code == 422
        assert error.error_code == "REQUIRED_FIELD_MISSING"
        assert "product_name" in error.message
        assert "必需" in error.message
        
        print("✅ 必需字段缺失錯誤測試通過")
    
    def test_invalid_field_format(self):
        """測試：字段格式無效錯誤"""
        print("🧪 測試字段格式無效錯誤...")
        
        from frontend.api.error_handling import InvalidFieldFormatError
        
        error = InvalidFieldFormatError(
            field_name="task_id",
            field_value="invalid-id-format",
            expected_format="UUID 格式"
        )
        
        assert error.status_code == 422
        assert error.error_code == "INVALID_FIELD_FORMAT"
        assert "task_id" in error.message
        assert "UUID 格式" in error.message
        
        print("✅ 字段格式無效錯誤測試通過")
    
    def test_field_value_out_of_range(self):
        """測試：字段值超出範圍錯誤"""
        print("🧪 測試字段值超出範圍錯誤...")
        
        from frontend.api.error_handling import FieldValueOutOfRangeError
        
        error = FieldValueOutOfRangeError(
            field_name="source_files_count",
            field_value=1001,
            min_value=1,
            max_value=1000
        )
        
        assert error.status_code == 422
        assert error.error_code == "FIELD_VALUE_OUT_OF_RANGE"
        assert "source_files_count" in error.message
        assert "1" in str(error.min_value)
        assert "1000" in str(error.max_value)
        
        print("✅ 字段值超出範圍錯誤測試通過")


class TestResourceErrors:
    """測試資源相關錯誤"""
    
    def test_task_not_found(self):
        """測試：任務不存在錯誤"""
        print("🧪 測試任務不存在錯誤...")
        
        from frontend.api.error_handling import TaskNotFoundError
        
        error = TaskNotFoundError(
            task_id="task-12345"
        )
        
        assert error.status_code == 404
        assert error.error_code == "TASK_NOT_FOUND"
        assert "task-12345" in error.message
        assert "任務" in error.message
        
        print("✅ 任務不存在錯誤測試通過")
    
    def test_file_not_found(self):
        """測試：檔案不存在錯誤"""
        print("🧪 測試檔案不存在錯誤...")
        
        from frontend.api.error_handling import FileNotFoundError
        
        error = FileNotFoundError(
            file_path="/path/to/missing/file.csv"
        )
        
        assert error.status_code == 404
        assert error.error_code == "FILE_NOT_FOUND"
        assert "/path/to/missing/file.csv" in error.message
        
        print("✅ 檔案不存在錯誤測試通過")
    
    def test_insufficient_permissions(self):
        """測試：權限不足錯誤"""
        print("🧪 測試權限不足錯誤...")
        
        from frontend.api.error_handling import InsufficientPermissionsError
        
        error = InsufficientPermissionsError(
            resource_path="/restricted/path",
            required_permission="read"
        )
        
        assert error.status_code == 403
        assert error.error_code == "INSUFFICIENT_PERMISSIONS"
        assert "/restricted/path" in error.message
        assert "read" in error.message
        
        print("✅ 權限不足錯誤測試通過")


class TestOperationErrors:
    """測試操作相關錯誤"""
    
    def test_operation_timeout(self):
        """測試：操作超時錯誤"""
        print("🧪 測試操作超時錯誤...")
        
        from frontend.api.error_handling import OperationTimeoutError
        
        error = OperationTimeoutError(
            operation="檔案處理",
            timeout_seconds=300
        )
        
        assert error.status_code == 408
        assert error.error_code == "OPERATION_TIMEOUT"
        assert "檔案處理" in error.message
        assert "300" in str(error.timeout_seconds)
        assert error.recovery_strategy_value == "retry"
        
        print("✅ 操作超時錯誤測試通過")
    
    def test_operation_cancelled(self):
        """測試：操作取消錯誤"""
        print("🧪 測試操作取消錯誤...")
        
        from frontend.api.error_handling import OperationCancelledError
        
        error = OperationCancelledError(
            operation="任務執行",
            reason="用戶主動取消"
        )
        
        assert error.status_code == 409
        assert error.error_code == "OPERATION_CANCELLED"
        assert "任務執行" in error.message
        assert "用戶主動取消" in error.reason
        
        print("✅ 操作取消錯誤測試通過")
    
    def test_concurrent_operation_conflict(self):
        """測試：並發操作衝突錯誤"""
        print("🧪 測試並發操作衝突錯誤...")
        
        from frontend.api.error_handling import ConcurrentOperationConflictError
        
        error = ConcurrentOperationConflictError(
            resource_id="task-12345",
            conflicting_operation="另一個處理任務正在執行"
        )
        
        assert error.status_code == 409
        assert error.error_code == "CONCURRENT_OPERATION_CONFLICT"
        assert "task-12345" in error.message
        assert "另一個處理任務正在執行" in error.message
        
        print("✅ 並發操作衝突錯誤測試通過")


class TestSystemErrors:
    """測試系統相關錯誤"""
    
    def test_disk_space_insufficient(self):
        """測試：磁碟空間不足錯誤"""
        print("🧪 測試磁碟空間不足錯誤...")
        
        from frontend.api.error_handling import DiskSpaceInsufficientError
        
        error = DiskSpaceInsufficientError(
            required_space_mb=1024,
            available_space_mb=512
        )
        
        assert error.status_code == 507
        assert error.error_code == "DISK_SPACE_INSUFFICIENT"
        assert "1024" in str(error.required_space_mb)
        assert "512" in str(error.available_space_mb)
        
        print("✅ 磁碟空間不足錯誤測試通過")
    
    def test_memory_insufficient(self):
        """測試：記憶體不足錯誤"""
        print("🧪 測試記憶體不足錯誤...")
        
        from frontend.api.error_handling import MemoryInsufficientError
        
        error = MemoryInsufficientError(
            required_memory_mb=2048,
            available_memory_mb=1024
        )
        
        assert error.status_code == 507
        assert error.error_code == "MEMORY_INSUFFICIENT"
        assert "2048" in str(error.required_memory_mb)
        assert "1024" in str(error.available_memory_mb)
        
        print("✅ 記憶體不足錯誤測試通過")
    
    def test_external_service_error(self):
        """測試：外部服務錯誤"""
        print("🧪 測試外部服務錯誤...")
        
        from frontend.api.error_handling import ExternalServiceError
        
        error = ExternalServiceError(
            service_name="LLM API",
            service_error="API 配額已用完"
        )
        
        assert error.status_code == 502
        assert error.error_code == "EXTERNAL_SERVICE_ERROR"
        assert "LLM API" in error.message
        assert "API 配額已用完" in error.service_error
        assert error.recovery_strategy_value == "circuit_breaker"
        
        print("✅ 外部服務錯誤測試通過")


class TestErrorInheritance:
    """測試錯誤繼承體系"""
    
    def test_base_error_class(self):
        """測試：基礎錯誤類別"""
        print("🧪 測試基礎錯誤類別...")
        
        from frontend.api.error_handling import BaseAPIError
        
        # 測試基礎錯誤類別的共同行為
        error = BaseAPIError(
            error_code="TEST_ERROR",
            message="測試錯誤",
            status_code=500
        )
        
        # 驗證基本屬性
        assert error.error_code == "TEST_ERROR"
        assert error.message == "測試錯誤"
        assert error.status_code == 500
        assert error.timestamp is not None
        assert error.trace_id is not None
        
        # 驗證方法
        http_exc = error.to_http_exception()
        assert isinstance(http_exc, HTTPException)
        assert http_exc.status_code == 500
        
        response = error.to_error_response()
        assert response["success"] is False
        assert response["status_code"] == 500
        
        print("✅ 基礎錯誤類別測試通過")
    
    def test_error_category_inheritance(self):
        """測試：錯誤分類繼承"""
        print("🧪 測試錯誤分類繼承...")
        
        from frontend.api.error_handling import (
            BaseAPIError, ServiceError, ValidationError, ResourceError
        )
        
        # 測試服務錯誤繼承
        service_error = ServiceError(
            error_code="SERVICE_TEST",
            message="服務測試錯誤",
            status_code=503
        )
        assert isinstance(service_error, BaseAPIError)
        
        # 測試驗證錯誤繼承
        validation_error = ValidationError(
            error_code="VALIDATION_TEST",
            message="驗證測試錯誤",
            status_code=422
        )
        assert isinstance(validation_error, BaseAPIError)
        
        # 測試資源錯誤繼承
        resource_error = ResourceError(
            error_code="RESOURCE_TEST",
            message="資源測試錯誤",
            status_code=404
        )
        assert isinstance(resource_error, BaseAPIError)
        
        print("✅ 錯誤分類繼承測試通過")


def run_all_error_category_tests():
    """運行所有錯誤分類測試"""
    print("🎯 錯誤分類測試")
    print("=" * 50)
    
    test_classes = [
        TestServiceErrors,
        TestValidationErrors,
        TestResourceErrors,
        TestOperationErrors,
        TestSystemErrors,
        TestErrorInheritance
    ]
    
    passed = 0
    failed = 0
    
    for test_class in test_classes:
        print(f"\n📋 運行 {test_class.__name__} 測試...")
        
        instance = test_class()
        methods = [method for method in dir(instance) if method.startswith('test_')]
        
        for method_name in methods:
            try:
                method = getattr(instance, method_name)
                method()
                passed += 1
            except Exception as e:
                print(f"❌ {method_name} 失敗: {e}")
                failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed} 通過, {failed} 失敗")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_error_category_tests()
    exit(0 if success else 1)
