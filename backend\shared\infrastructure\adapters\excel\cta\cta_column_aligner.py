#!/usr/bin/env python3
"""
CTA 檔案欄位智能對齊處理器
專門處理 FT 和 Online EQC 檔案的欄位結構對齊與電腦名稱提取
"""

import os
import pandas as pd
from typing import Dict, List, Tuple, Optional
import re


class CTAColumnAligner:
    """CTA 檔案欄位對齊器與資料處理器"""
    
    def __init__(self):
        """初始化對齊器"""
        self.standard_missing_columns = ['X_COORD', 'Y_COORD', 'Alarm']
        self.computer_name_keys = ['computer', 'tester_name']
    
    # === 欄位對齊功能 ===
    
    def compare_column_structures(self, ft_headers: List[str], eqc_headers: List[str]) -> Dict[str, int]:
        """
        比較 FT 和 EQC 的欄位結構，返回缺失欄位及其插入位置
        
        Args:
            ft_headers: FT 檔案的欄位標題列表
            eqc_headers: EQC 檔案的欄位標題列表
            
        Returns:
            Dict[str, int]: {缺失欄位名: 應插入的位置索引}
        """
        missing_columns = {}
        
        # 找出 EQC 檔案缺少的欄位
        eqc_set = set(eqc_headers)
        
        for i, ft_col in enumerate(ft_headers):
            if ft_col not in eqc_set:
                missing_columns[ft_col] = i
                
        print(f"[SEARCH] 欄位比較結果:")
        print(f"   FT 檔案欄位數: {len(ft_headers)}")
        print(f"   EQC 檔案欄位數: {len(eqc_headers)}")
        
        if missing_columns:
            print(f"   缺失欄位: {list(missing_columns.keys())}")
            for col, pos in missing_columns.items():
                print(f"     {col} → 位置 {pos}")
        else:
            print("   未發現缺失欄位")
            
        return missing_columns
    
    def validate_alignment_feasibility(self, ft_headers: List[str], eqc_headers: List[str]) -> Tuple[bool, str]:
        """
        驗證是否可以進行欄位對齊，返回驗證結果和錯誤訊息
        
        Args:
            ft_headers: FT 檔案的欄位標題列表
            eqc_headers: EQC 檔案的欄位標題列表
            
        Returns:
            Tuple[bool, str]: (是否可對齊, 錯誤訊息)
        """
        missing_columns = self.compare_column_structures(ft_headers, eqc_headers)
        
        if len(eqc_headers) == len(ft_headers):
            return True, "欄位數量已一致"
        
        # 檢查是否是標準的缺失欄位
        missing_keys = set(missing_columns.keys())
        standard_keys = set(self.standard_missing_columns)
        
        if missing_keys.issubset(standard_keys):
            return True, f"可補齊標準欄位: {list(missing_keys)}"
        
        # 檢查是否有未知的缺失欄位
        unknown_missing = missing_keys - standard_keys
        if unknown_missing:
            error_msg = f"""
未知的缺失欄位: {list(unknown_missing)}
已知可處理的欄位: {self.standard_missing_columns}
建議檢查 CTA 檔案結構或更新對齊邏輯
            """.strip()
            return False, error_msg
        
        return True, "可進行對齊處理"
    
    def smart_align_eqc_columns(self, eqc_data: pd.DataFrame, ft_data: pd.DataFrame) -> pd.DataFrame:
        """
        智能對齊 EQC 檔案欄位，確保與 FT 檔案欄數一致
        
        Args:
            eqc_data: EQC 檔案的資料
            ft_data: FT 檔案的資料（用於複製標頭結構）
            
        Returns:
            pd.DataFrame: 對齊後的 EQC 資料
        """
        ft_headers = list(ft_data.columns)
        eqc_headers = list(eqc_data.columns)
        
        print(f"[TOOL] 開始智能欄位對齊:")
        print(f"   目標結構: {len(ft_headers)} 欄")
        print(f"   當前結構: {len(eqc_headers)} 欄")
        
        # 檢查是否需要對齊
        if len(eqc_headers) == len(ft_headers):
            print("[OK] 欄位數量已一致，無需對齊")
            return eqc_data
        
        # 驗證對齊可行性
        can_align, message = self.validate_alignment_feasibility(ft_headers, eqc_headers)
        
        if not can_align:
            error_msg = f"""
[ERROR] CTA 欄位對齊失敗
FT 檔案欄位數: {len(ft_headers)}
EQC 檔案欄位數: {len(eqc_headers)}

錯誤原因: {message}

FT 檔案欄位: {ft_headers[:10]}...
EQC 檔案欄位: {eqc_headers[:10]}...
            """.strip()
            raise ValueError(error_msg)
        
        print(f"   對齊策略: {message}")
        
        # 識別缺失欄位
        missing_columns = self.compare_column_structures(ft_headers, eqc_headers)
        
        # 執行動態對齊
        aligned_data = self.align_eqc_with_ft_structure(eqc_data, ft_data, missing_columns)
        
        # 最終驗證
        self.validate_final_alignment(list(aligned_data.columns), ft_headers)
        
        print(f"[OK] 欄位對齊完成: {len(eqc_headers)} → {len(aligned_data.columns)} 欄")
        return aligned_data
    
    def insert_missing_columns_at_positions(self, df: pd.DataFrame, missing_cols: Dict[str, int]) -> pd.DataFrame:
        """
        在指定位置插入缺失欄位，填入預設值 0
        
        Args:
            df: 原始資料框
            missing_cols: {欄位名: 插入位置}
            
        Returns:
            pd.DataFrame: 插入欄位後的資料框
        """
        if not missing_cols:
            return df
        
        result_df = df.copy()
        
        # 按位置排序，從前往後插入
        sorted_cols = sorted(missing_cols.items(), key=lambda x: x[1])
        
        # 記錄插入的偏移量
        offset = 0
        
        for col_name, original_position in sorted_cols:
            adjusted_position = original_position + offset
            print(f"   插入欄位: {col_name} 於位置 {adjusted_position} (原位置: {original_position})")
            
            # 創建新欄位，填入預設值 0
            # 對於標頭行（前12行）使用字串 "0"，對於測試資料行使用數字 0
            new_column_data = []
            for i in range(len(result_df)):
                if i < 12:  # 標頭行（行1-12）
                    if i == 6:  # 第7行：版本號
                        new_column_data.append("0")
                    elif i == 7:  # 第8行：測試項目名稱  
                        new_column_data.append(col_name)
                    elif i == 8:  # 第9行：CTA標記
                        new_column_data.append("0")
                    elif i in [9, 10]:  # 第10-11行：Min/Max值
                        new_column_data.append("0")
                    elif i == 11:  # 第12行：單位
                        new_column_data.append("0")
                    else:  # 其他標頭行
                        new_column_data.append("")
                else:  # 測試資料行（第13行開始）
                    new_column_data.append(0)  # 數字 0
            
            new_column = pd.Series(new_column_data, name=col_name)
            
            # 在調整後的位置插入欄位
            cols = list(result_df.columns)
            cols.insert(adjusted_position, col_name)
            
            # 重新組織資料框
            result_df = pd.concat([
                result_df.iloc[:, :adjusted_position],
                new_column.to_frame(),
                result_df.iloc[:, adjusted_position:]
            ], axis=1)
            
            # 更新欄位名稱
            result_df.columns = cols
            
            # 更新偏移量
            offset += 1
        
        return result_df
    
    def align_eqc_with_ft_structure(self, eqc_data: pd.DataFrame, ft_data: pd.DataFrame, missing_cols: Dict[str, int]) -> pd.DataFrame:
        """
        動態對齊 EQC 檔案與 FT 檔案結構
        
        Args:
            eqc_data: EQC 檔案資料
            ft_data: FT 檔案資料（用於複製標頭）
            missing_cols: 缺失欄位及其位置 {欄位名: 位置}
            
        Returns:
            pd.DataFrame: 對齊後的 EQC 資料
        """
        if not missing_cols:
            return eqc_data
            
        print(f"[REFRESH] 執行動態對齊，插入 {len(missing_cols)} 個缺失欄位")
        
        target_columns = len(ft_data.columns)
        missing_positions = set(missing_cols.values())
        ft_headers = list(ft_data.columns)
        
        aligned_data = []
        
        # 處理每一行資料
        for row_index in range(len(eqc_data)):
            original_row = eqc_data.iloc[row_index].tolist()
            new_row = []
            original_col_idx = 0
            
            # 逐欄位建構新行
            for target_pos in range(target_columns):
                if target_pos in missing_positions:
                    # 插入缺失欄位
                    if row_index < 12:  # 標頭行：從 FT 檔案複製
                        ft_value = ft_data.iloc[row_index, target_pos]
                        new_row.append(ft_value)
                        print(f"   行{row_index+1} 位置{target_pos+1}: 從FT複製 '{ft_value}'")
                    else:  # 測試資料行：填入 0
                        new_row.append(0)
                else:
                    # 使用原始 EQC 資料
                    if original_col_idx < len(original_row):
                        new_row.append(original_row[original_col_idx])
                    else:
                        new_row.append("none")  # 防止索引越界
                    original_col_idx += 1
            
            aligned_data.append(new_row)
        
        # 建立對齊後的 DataFrame
        aligned_df = pd.DataFrame(aligned_data, columns=ft_headers)
        
        print(f"[OK] 動態對齊完成: {len(eqc_data.columns)} → {len(aligned_df.columns)} 欄")
        return aligned_df
    
    def validate_final_alignment(self, aligned_eqc_headers: List[str], ft_headers: List[str]) -> None:
        """
        最終驗證：確保對齊後欄數完全一致，否則拋出詳細錯誤
        
        Args:
            aligned_eqc_headers: 對齊後的 EQC 欄位列表
            ft_headers: FT 檔案的欄位列表
        """
        if len(aligned_eqc_headers) != len(ft_headers):
            error_details = self.generate_alignment_report(ft_headers, [], aligned_eqc_headers)
            raise ValueError(f"[ERROR] 欄位對齊驗證失敗\n{error_details}")
        
        print(f"[OK] 欄位對齊驗證通過: 兩檔案均為 {len(ft_headers)} 欄")
    
    # === 電腦名稱提取功能 ===
    
    def extract_computer_name_dynamic(self, file_path: str) -> str:
        """
        動態提取 CTA 檔案中的電腦名稱
        搜尋順序：Computer > Tester_Name > B31(舊格式) > 預設值
        
        Args:
            file_path: CTA 檔案路徑
            
        Returns:
            str: 電腦名稱
        """
        print(f"[SEARCH] 提取電腦名稱: {os.path.basename(file_path)}")
        
        # 第一優先：動態搜尋
        key_found, computer_name = self.search_key_value_in_file(file_path, self.computer_name_keys)
        
        if key_found:
            self.log_computer_name_extraction(file_path, f"動態搜尋({key_found})", computer_name)
            return computer_name
        
        # 第二優先：舊格式 B31
        legacy_name = self.extract_computer_name_legacy(file_path)
        if legacy_name and legacy_name not in ["Unknown", "Error", ""]:
            self.log_computer_name_extraction(file_path, "舊格式(B31)", legacy_name)
            return legacy_name
        
        # 最後：預設值
        self.log_computer_name_extraction(file_path, "預設值", "Unknown")
        return "Unknown"
    
    def search_key_value_in_file(self, file_path: str, target_keys: List[str]) -> Tuple[str, str]:
        """
        在檔案中搜尋指定的 key-value 對
        
        Args:
            file_path: 檔案路徑
            target_keys: 要搜尋的關鍵字列表（不區分大小寫）
            
        Returns:
            Tuple[str, str]: (找到的 key, 對應的 value)，找不到返回 ("", "")
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 跳過空行
                    if not line.strip():
                        continue
                    
                    elements = line.strip().split(',')
                    if len(elements) >= 2:
                        a_column = elements[0].strip().lower()
                        b_column = elements[1].strip()
                        
                        # 檢查是否匹配目標關鍵字
                        for target_key in target_keys:
                            if a_column == target_key.lower():
                                print(f"   [OK] 找到 {target_key}: '{b_column}' (第 {line_num} 行)")
                                return target_key, b_column
            
            print(f"   [WARNING] 未找到目標關鍵字: {target_keys}")
            return "", ""
            
        except Exception as e:
            print(f"   [ERROR] 搜尋檔案失敗: {e}")
            return "", ""
    
    def extract_computer_name_legacy(self, file_path: str) -> str:
        """
        舊格式：固定從 B31 提取電腦名稱（向後相容）
        
        Args:
            file_path: CTA 檔案路徑
            
        Returns:
            str: 電腦名稱，失敗返回 "Unknown"
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 檢查第31行是否存在
            if len(lines) >= 31:
                line31 = lines[30].strip()  # 第31行（索引30）
                elements = line31.split(',')
                
                if len(elements) >= 2:
                    computer_name = elements[1].strip()
                    if computer_name:
                        print(f"   [OK] 舊格式提取成功: '{computer_name}' (B31)")
                        return computer_name
            
            print(f"   [WARNING] 舊格式提取失敗: 第31行無效")
            return "Unknown"
            
        except Exception as e:
            print(f"   [ERROR] 舊格式提取錯誤: {e}")
            return "Error"
    
    # === 報告與日誌功能 ===
    
    def generate_alignment_report(self, ft_headers: List[str], eqc_headers: List[str], aligned_headers: List[str]) -> str:
        """
        生成詳細的對齊處理報告
        
        Args:
            ft_headers: FT 檔案欄位
            eqc_headers: 原始 EQC 檔案欄位
            aligned_headers: 對齊後的 EQC 檔案欄位
            
        Returns:
            str: 詳細報告
        """
        report = f"""
[CHART] CTA 欄位對齊詳細報告
{'='*50}
FT 檔案欄位數: {len(ft_headers)}
原始 EQC 欄位數: {len(eqc_headers)}
對齊後 EQC 欄位數: {len(aligned_headers)}

FT 檔案前10個欄位: {ft_headers[:10] if ft_headers else []}
EQC 檔案前10個欄位: {eqc_headers[:10] if eqc_headers else []}
對齊後前10個欄位: {aligned_headers[:10] if aligned_headers else []}
        """.strip()
        
        return report
    
    def log_computer_name_extraction(self, file_path: str, method: str, result: str) -> None:
        """
        記錄電腦名稱提取過程的日誌
        
        Args:
            file_path: 檔案路徑
            method: 提取方法
            result: 提取結果
        """
        filename = os.path.basename(file_path)
        print(f"   [BOARD] 電腦名稱提取: {filename}")
        print(f"      方法: {method}")
        print(f"      結果: '{result}'")
    
    # === 整合處理功能 ===
    
    def process_cta_column_alignment(self, ft_data: pd.DataFrame, eqc_data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        處理 CTA 檔案的欄位對齊
        
        Args:
            ft_data: FT 檔案資料
            eqc_data: EQC 檔案資料
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (FT 資料, 對齊後的 EQC 資料)
        """
        print("[REFRESH] 開始 CTA 欄位對齊處理")
        
        aligned_eqc_data = self.smart_align_eqc_columns(eqc_data, ft_data)
        
        print("[OK] CTA 欄位對齊處理完成")
        return ft_data, aligned_eqc_data
    
    def validate_column_consistency(self, ft_data: pd.DataFrame, eqc_data: pd.DataFrame) -> bool:
        """
        驗證兩個資料框的欄位一致性
        
        Args:
            ft_data: FT 檔案資料
            eqc_data: EQC 檔案資料
            
        Returns:
            bool: 是否一致
        """
        ft_cols = len(ft_data.columns)
        eqc_cols = len(eqc_data.columns)
        
        is_consistent = ft_cols == eqc_cols
        
        print(f"[SEARCH] 欄位一致性檢查:")
        print(f"   FT 檔案: {ft_cols} 欄")
        print(f"   EQC 檔案: {eqc_cols} 欄")
        print(f"   結果: {'[OK] 一致' if is_consistent else '[ERROR] 不一致'}")
        
        return is_consistent