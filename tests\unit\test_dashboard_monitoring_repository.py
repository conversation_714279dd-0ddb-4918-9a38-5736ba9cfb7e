"""
監控資料存取層單元測試

測試 DashboardMonitoringRepository 的所有功能：
- 資料表初始化
- 監控指標儲存
- 資料查詢和檢索
- 趨勢分析
- 資料清理
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

from backend.monitoring.repositories.dashboard_monitoring_repository import DashboardMonitoringRepository
from backend.monitoring.models.dashboard_metrics_models import (
    DashboardMetrics, EmailMetrics, CeleryMetrics, SystemMetrics, 
    FileMetrics, BusinessMetrics
)


class TestDashboardMonitoringRepository:
    """監控資料存取層測試類"""
    
    @pytest.fixture
    def temp_db_path(self):
        """創建臨時資料庫檔案"""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
            
    @pytest.fixture
    def repository(self, temp_db_path):
        """創建測試用的資料庫存取層實例"""
        return DashboardMonitoringRepository(temp_db_path)
        
    @pytest.fixture
    def sample_metrics(self):
        """創建測試用的監控指標資料"""
        timestamp = datetime.now()
        
        email_metrics = EmailMetrics(
            pending_count=5,
            processing_count=2,
            completed_count=100,
            failed_count=3,
            avg_processing_time_seconds=120.5,
            throughput_per_hour=25.0,
            vendor_queue_counts={"GTK": 3, "JCET": 2},
            vendor_success_rates={"GTK": 0.95, "JCET": 0.88},
            code_comparison_active=1,
            code_comparison_pending=4,
            code_comparison_avg_duration=180.0
        )
        
        celery_metrics = CeleryMetrics(
            total_active=3,
            total_pending=8,
            total_completed=150,
            total_failed=5,
            task_type_counts={
                "code_comparison": {"active": 1, "pending": 4, "completed": 80, "failed": 2},
                "csv_to_summary": {"active": 2, "pending": 4, "completed": 70, "failed": 3}
            },
            worker_status={"worker1": "online", "worker2": "offline"},
            worker_load={"worker1": 2, "worker2": 0},
            avg_task_duration={"code_comparison": 180.0, "csv_to_summary": 90.0},
            task_success_rate={"code_comparison": 0.95, "csv_to_summary": 0.92}
        )
        
        system_metrics = SystemMetrics(
            cpu_percent=75.5,
            memory_percent=68.2,
            disk_percent=45.0,
            memory_available_mb=2048.0,
            disk_free_gb=500.0,
            active_connections=25,
            websocket_connections=5,
            service_health={"email_service": "healthy", "celery_service": "warning"},
            database_connections=10,
            database_query_avg_time=0.05,
            database_size_mb=256.0
        )
        
        file_metrics = FileMetrics(
            attachments_downloaded=20,
            attachments_pending=3,
            attachments_failed=1,
            file_type_counts={"csv": 10, "excel": 8, "zip": 2},
            compression_active=1,
            compression_pending=2,
            decompression_active=0,
            decompression_pending=1,
            temp_folder_size_mb=512.0,
            upload_folder_size_mb=1024.0,
            processed_folder_size_mb=2048.0,
            avg_download_time=30.0,
            avg_compression_time=45.0,
            avg_decompression_time=25.0
        )
        
        business_metrics = BusinessMetrics(
            mo_processed_today=50,
            lot_processed_today=150,
            data_quality_score=92.5,
            validation_errors_count=3,
            duplicate_mo_count=1,
            vendor_processing_stats={
                "GTK": {"mo_count": 30, "lot_count": 90, "success_rate": 95},
                "JCET": {"mo_count": 20, "lot_count": 60, "success_rate": 88}
            },
            reports_generated_today=10,
            reports_pending=2,
            avg_report_generation_time=120.0
        )
        
        return DashboardMetrics(
            timestamp=timestamp,
            email_metrics=email_metrics,
            celery_metrics=celery_metrics,
            system_metrics=system_metrics,
            file_metrics=file_metrics,
            business_metrics=business_metrics
        )
        
    def test_init_tables(self, repository):
        """測試資料表初始化"""
        # 檢查資料表是否已創建
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            
            # 檢查所有必要的表格是否存在
            tables = [
                'dashboard_metrics_history',
                'dashboard_current_status',
                'task_execution_history',
                'system_health_checks',
                'file_processing_stats'
            ]
            
            for table in tables:
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table,))
                result = cursor.fetchone()
                assert result is not None, f"表格 {table} 未創建"
                
    @pytest.mark.asyncio
    async def test_store_metrics(self, repository, sample_metrics):
        """測試儲存監控指標"""
        # 儲存指標
        result = await repository.store_metrics(sample_metrics)
        assert result is True
        
        # 驗證資料是否正確儲存
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            
            # 檢查監控指標歷史表
            cursor.execute("SELECT COUNT(*) as count FROM dashboard_metrics_history")
            result = cursor.fetchone()
            assert result['count'] > 0
            
            # 檢查即時狀態表
            cursor.execute("SELECT COUNT(*) as count FROM dashboard_current_status")
            result = cursor.fetchone()
            assert result['count'] > 0
            
    @pytest.mark.asyncio
    async def test_get_current_status(self, repository, sample_metrics):
        """測試獲取當前狀態"""
        # 先儲存一些資料
        await repository.store_metrics(sample_metrics)
        
        # 獲取當前狀態
        status = await repository.get_current_status()
        
        assert isinstance(status, dict)
        assert len(status) > 0
        
        # 檢查特定狀態項目
        assert 'email_pending_count' in status
        assert status['email_pending_count']['value'] == 5
        
    @pytest.mark.asyncio
    async def test_get_trend_data(self, repository, sample_metrics):
        """測試獲取趨勢資料"""
        # 儲存多個時間點的資料
        for i in range(5):
            metrics = sample_metrics
            metrics.timestamp = datetime.now() - timedelta(minutes=i*10)
            await repository.store_metrics(metrics)
            
        # 獲取趨勢資料
        trend_data = await repository.get_trend_data('queue_count', '1h', 10)
        
        assert isinstance(trend_data, list)
        assert len(trend_data) > 0
        
        # 檢查資料結構
        for item in trend_data:
            assert 'metric_name' in item
            assert 'metric_value' in item
            assert 'timestamp' in item
            
    @pytest.mark.asyncio
    async def test_store_task_execution(self, repository):
        """測試儲存任務執行記錄"""
        task_data = {
            'task_id': 'test_task_123',
            'task_type': 'code_comparison',
            'task_name': 'Test Code Comparison',
            'status': 'completed',
            'started_at': datetime.now() - timedelta(minutes=5),
            'completed_at': datetime.now(),
            'duration_seconds': 300.0,
            'error_message': None,
            'result_data': {'processed_files': 5},
            'queue_name': 'default',
            'worker_name': 'worker1',
            'retry_count': 0
        }
        
        result = await repository.store_task_execution(task_data)
        assert result is True
        
        # 驗證資料是否正確儲存
        history = await repository.get_task_execution_history('code_comparison')
        assert len(history) == 1
        assert history[0]['task_id'] == 'test_task_123'
        assert history[0]['status'] == 'completed'
        
    @pytest.mark.asyncio
    async def test_get_task_execution_history(self, repository):
        """測試獲取任務執行歷史"""
        # 儲存多個任務記錄
        task_types = ['code_comparison', 'csv_to_summary', 'compression']
        statuses = ['completed', 'failed', 'running']
        
        for i, (task_type, status) in enumerate(zip(task_types, statuses)):
            task_data = {
                'task_id': f'task_{i}',
                'task_type': task_type,
                'task_name': f'Test Task {i}',
                'status': status,
                'started_at': datetime.now() - timedelta(minutes=i*10),
                'completed_at': datetime.now() - timedelta(minutes=i*5) if status == 'completed' else None,
                'duration_seconds': 300.0 if status == 'completed' else None,
                'queue_name': 'default',
                'worker_name': f'worker{i%2 + 1}',
                'retry_count': 0
            }
            await repository.store_task_execution(task_data)
            
        # 測試無篩選條件的查詢
        all_history = await repository.get_task_execution_history()
        assert len(all_history) == 3
        
        # 測試按任務類型篩選
        code_comparison_history = await repository.get_task_execution_history('code_comparison')
        assert len(code_comparison_history) == 1
        assert code_comparison_history[0]['task_type'] == 'code_comparison'
        
        # 測試按狀態篩選
        completed_history = await repository.get_task_execution_history(status='completed')
        assert len(completed_history) == 1
        assert completed_history[0]['status'] == 'completed'
        
    @pytest.mark.asyncio
    async def test_store_system_health_check(self, repository):
        """測試儲存系統健康檢查記錄"""
        health_data = {
            'service_name': 'email_service',
            'check_type': 'database',
            'status': 'healthy',
            'response_time_ms': 50.5,
            'error_message': None,
            'details': {'connection_count': 5, 'query_time': 0.02},
            'checked_at': datetime.now()
        }
        
        result = await repository.store_system_health_check(health_data)
        assert result is True
        
        # 驗證資料是否正確儲存
        history = await repository.get_system_health_history('email_service')
        assert len(history) == 1
        assert history[0]['service_name'] == 'email_service'
        assert history[0]['status'] == 'healthy'
        
    @pytest.mark.asyncio
    async def test_get_system_health_history(self, repository):
        """測試獲取系統健康檢查歷史"""
        # 儲存多個健康檢查記錄
        services = ['email_service', 'celery_service', 'database']
        statuses = ['healthy', 'warning', 'error']
        
        for i, (service, status) in enumerate(zip(services, statuses)):
            health_data = {
                'service_name': service,
                'check_type': 'connectivity',
                'status': status,
                'response_time_ms': 50.0 + i * 10,
                'error_message': f'Error {i}' if status == 'error' else None,
                'details': {'check_id': i},
                'checked_at': datetime.now() - timedelta(minutes=i*5)
            }
            await repository.store_system_health_check(health_data)
            
        # 測試獲取所有服務的健康歷史
        all_history = await repository.get_system_health_history()
        assert len(all_history) == 3
        
        # 測試獲取特定服務的健康歷史
        email_history = await repository.get_system_health_history('email_service')
        assert len(email_history) == 1
        assert email_history[0]['service_name'] == 'email_service'
        
    @pytest.mark.asyncio
    async def test_cleanup_old_data(self, repository, sample_metrics):
        """測試清理過期資料"""
        # 儲存一些舊資料
        old_metrics = sample_metrics
        old_metrics.timestamp = datetime.now() - timedelta(days=35)
        await repository.store_metrics(old_metrics)
        
        # 儲存一些新資料
        new_metrics = sample_metrics
        new_metrics.timestamp = datetime.now()
        await repository.store_metrics(new_metrics)
        
        # 執行清理（保留30天）
        result = await repository.cleanup_old_data(30)
        assert result is True
        
        # 驗證舊資料已被清理
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) as count FROM dashboard_metrics_history 
                WHERE timestamp < ?
            """, (datetime.now() - timedelta(days=30),))
            result = cursor.fetchone()
            assert result['count'] == 0
            
    @pytest.mark.asyncio
    async def test_get_database_stats(self, repository, sample_metrics):
        """測試獲取資料庫統計資訊"""
        # 儲存一些資料
        await repository.store_metrics(sample_metrics)
        
        # 獲取統計資訊
        stats = await repository.get_database_stats()
        
        assert isinstance(stats, dict)
        assert 'dashboard_metrics_history_count' in stats
        assert 'database_size_mb' in stats
        assert 'latest_data_timestamp' in stats
        
        # 檢查統計資料的合理性
        assert stats['dashboard_metrics_history_count'] > 0
        assert stats['database_size_mb'] >= 0
        
    @pytest.mark.asyncio
    async def test_error_handling(self, temp_db_path):
        """測試錯誤處理"""
        # 使用無效的資料庫路徑
        invalid_path = "/invalid/path/test.db"
        
        with pytest.raises(Exception):
            DashboardMonitoringRepository(invalid_path)
            
    @pytest.mark.asyncio
    async def test_concurrent_access(self, repository, sample_metrics):
        """測試並發存取"""
        import asyncio
        
        # 創建多個並發任務
        tasks = []
        for i in range(5):
            metrics = sample_metrics
            metrics.timestamp = datetime.now() + timedelta(seconds=i)
            tasks.append(repository.store_metrics(metrics))
            
        # 並發執行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 檢查所有任務都成功完成
        for result in results:
            assert result is True or not isinstance(result, Exception)
            
    def test_connection_context_manager(self, repository):
        """測試資料庫連接上下文管理器"""
        # 測試正常使用
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result is not None
            
        # 測試異常處理
        try:
            with repository._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("INVALID SQL")
        except Exception:
            pass  # 預期會有異常
            
    @pytest.mark.asyncio
    async def test_data_integrity(self, repository, sample_metrics):
        """測試資料完整性"""
        # 儲存指標
        await repository.store_metrics(sample_metrics)
        
        # 檢查各類指標是否都正確儲存
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            
            # 檢查郵件指標
            cursor.execute("""
                SELECT COUNT(*) as count FROM dashboard_metrics_history 
                WHERE metric_category = 'email'
            """)
            email_count = cursor.fetchone()['count']
            assert email_count > 0
            
            # 檢查 Celery 指標
            cursor.execute("""
                SELECT COUNT(*) as count FROM dashboard_metrics_history 
                WHERE metric_category = 'celery'
            """)
            celery_count = cursor.fetchone()['count']
            assert celery_count > 0
            
            # 檢查系統指標
            cursor.execute("""
                SELECT COUNT(*) as count FROM dashboard_metrics_history 
                WHERE metric_category = 'system'
            """)
            system_count = cursor.fetchone()['count']
            assert system_count > 0