"""測試依賴注入系統的核心功能
專門測試 dependencies.py 中的依賴函數實現

測試目標：
1. require_staging_service() 函數的正確行為
2. require_processing_service() 函數的正確行為  
3. 服務可用和不可用的情況處理
4. 錯誤訊息的正確性
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from fastapi import HTTPException
from fastapi.testclient import TestClient

# 測試用的 Mock 服務類別
class MockFileStagingService:
    """Mock 檔案暫存服務"""
    def __init__(self):
        self.service_id = "mock_staging_service"
        self.is_healthy = True
    
    def health_check(self):
        return {"status": "healthy", "service_id": self.service_id}
    
    def get_service_statistics(self):
        return {
            "total_tasks": 10,
            "active_tasks": 2,
            "completed_tasks": 8
        }


class MockFileProcessingService:
    """Mock 檔案處理服務"""
    def __init__(self):
        self.service_id = "mock_processing_service"
        self.is_healthy = True
    
    def health_check(self):
        return {"status": "healthy", "service_id": self.service_id}
    
    def get_service_statistics(self):
        return {
            "total_tasks": 5,
            "active_tasks": 1,
            "completed_tasks": 4
        }


class TestRequireStagingService:
    """測試 require_staging_service() 函數"""
    
    def test_require_staging_service_when_available(self):
        """測試：當暫存服務可用時，應該正確返回服務實例"""
        # 這個測試定義了我們期望的正確行為
        # 當前的實現是錯誤的，所以這個測試會失敗
        # 這正是 TDD 的 Red 階段
        
        with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
            # 安排：模擬服務可用
            mock_service = MockFileStagingService()
            mock_get.return_value = mock_service
            
            # 導入要測試的函數
            from frontend.api.dependencies import require_staging_service
            
            # 執行：調用依賴函數
            result = require_staging_service()
            
            # 驗證：應該返回服務實例
            assert result is mock_service
            assert result.service_id == "mock_staging_service"
            mock_get.assert_called_once()
    
    def test_require_staging_service_when_unavailable(self):
        """測試：當暫存服務不可用時，應該拋出 HTTPException"""
        
        with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
            # 安排：模擬服務不可用
            mock_get.return_value = None
            
            # 導入要測試的函數
            from frontend.api.dependencies import require_staging_service
            
            # 執行和驗證：應該拋出 HTTPException
            with pytest.raises(HTTPException) as exc_info:
                require_staging_service()
            
            # 驗證錯誤詳情
            assert exc_info.value.status_code == 503
            assert "檔案暫存服務不可用" in str(exc_info.value.detail)
            mock_get.assert_called_once()
    
    def test_require_staging_service_with_initialization_error(self):
        """測試：當服務初始化有錯誤時，應該包含錯誤訊息"""
        
        with patch('src.presentation.api.dependencies.get_staging_service') as mock_get, \
             patch('src.presentation.api.dependencies.get_service_container') as mock_container:
            
            # 安排：模擬服務不可用且有初始化錯誤
            mock_get.return_value = None
            mock_container_instance = Mock()
            mock_container_instance.get_initialization_errors.return_value = {
                'staging': '連接資料庫失敗'
            }
            mock_container.return_value = mock_container_instance
            
            # 導入要測試的函數
            from frontend.api.dependencies import require_staging_service
            
            # 執行和驗證：應該拋出包含詳細錯誤的 HTTPException
            with pytest.raises(HTTPException) as exc_info:
                require_staging_service()
            
            # 驗證錯誤詳情包含初始化錯誤
            assert exc_info.value.status_code == 503
            assert "連接資料庫失敗" in str(exc_info.value.detail)


class TestRequireProcessingService:
    """測試 require_processing_service() 函數"""
    
    def test_require_processing_service_when_available(self):
        """測試：當處理服務可用時，應該正確返回服務實例"""
        
        with patch('src.presentation.api.dependencies.get_processing_service') as mock_get:
            # 安排：模擬服務可用
            mock_service = MockFileProcessingService()
            mock_get.return_value = mock_service
            
            # 導入要測試的函數
            from frontend.api.dependencies import require_processing_service
            
            # 執行：調用依賴函數
            result = require_processing_service()
            
            # 驗證：應該返回服務實例
            assert result is mock_service
            assert result.service_id == "mock_processing_service"
            mock_get.assert_called_once()
    
    def test_require_processing_service_when_unavailable(self):
        """測試：當處理服務不可用時，應該拋出 HTTPException"""
        
        with patch('src.presentation.api.dependencies.get_processing_service') as mock_get:
            # 安排：模擬服務不可用
            mock_get.return_value = None
            
            # 導入要測試的函數
            from frontend.api.dependencies import require_processing_service
            
            # 執行和驗證：應該拋出 HTTPException
            with pytest.raises(HTTPException) as exc_info:
                require_processing_service()
            
            # 驗證錯誤詳情
            assert exc_info.value.status_code == 503
            assert "檔案處理服務不可用" in str(exc_info.value.detail)
            mock_get.assert_called_once()
    
    def test_require_processing_service_with_initialization_error(self):
        """測試：當服務初始化有錯誤時，應該包含錯誤訊息"""
        
        with patch('src.presentation.api.dependencies.get_processing_service') as mock_get, \
             patch('src.presentation.api.dependencies.get_service_container') as mock_container:
            
            # 安排：模擬服務不可用且有初始化錯誤
            mock_get.return_value = None
            mock_container_instance = Mock()
            mock_container_instance.get_initialization_errors.return_value = {
                'processing': 'LLM 服務連接超時'
            }
            mock_container.return_value = mock_container_instance
            
            # 導入要測試的函數
            from frontend.api.dependencies import require_processing_service
            
            # 執行和驗證：應該拋出包含詳細錯誤的 HTTPException
            with pytest.raises(HTTPException) as exc_info:
                require_processing_service()
            
            # 驗證錯誤詳情包含初始化錯誤
            assert exc_info.value.status_code == 503
            assert "LLM 服務連接超時" in str(exc_info.value.detail)


class TestDependencyFunctionBehavior:
    """測試依賴函數的整體行為"""
    
    def test_dependency_functions_are_callable(self):
        """測試：依賴函數應該是可調用的，而不是返回函數的函數"""
        
        # 導入要測試的函數
        from frontend.api.dependencies import require_staging_service, require_processing_service
        
        # 驗證：這些應該是函數，不是返回函數的函數
        assert callable(require_staging_service)
        assert callable(require_processing_service)
        
        # 驗證：調用時不應該返回另一個函數
        # 注意：這個測試會失敗，因為當前的實現是錯誤的
        with patch('src.presentation.api.dependencies.get_staging_service', return_value=None):
            try:
                result = require_staging_service()
                # 如果沒有拋出異常，結果不應該是函數
                assert not callable(result), "require_staging_service() 不應該返回函數"
            except HTTPException:
                # 拋出 HTTPException 是正確的行為
                pass
    
    def test_dependency_functions_work_with_fastapi_depends(self):
        """測試：依賴函數應該能夠與 FastAPI 的 Depends() 正常工作"""
        
        from fastapi import Depends, FastAPI
        from fastapi.testclient import TestClient
        from frontend.api.dependencies import require_staging_service, require_processing_service
        
        # 創建測試應用
        app = FastAPI()
        
        @app.get("/test-staging")
        async def test_staging_endpoint(
            staging_service = Depends(require_staging_service)
        ):
            return {"service_id": staging_service.service_id}
        
        @app.get("/test-processing")  
        async def test_processing_endpoint(
            processing_service = Depends(require_processing_service)
        ):
            return {"service_id": processing_service.service_id}
        
        # 這個測試驗證依賴注入在 FastAPI 中的正確工作
        # 當前會失敗，因為依賴函數實現錯誤
        
        client = TestClient(app)
        
        # 測試暫存服務端點
        with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
            mock_service = MockFileStagingService()
            mock_get.return_value = mock_service
            
            response = client.get("/test-staging")
            assert response.status_code == 200
            assert response.json()["service_id"] == "mock_staging_service"
        
        # 測試處理服務端點
        with patch('src.presentation.api.dependencies.get_processing_service') as mock_get:
            mock_service = MockFileProcessingService()
            mock_get.return_value = mock_service
            
            response = client.get("/test-processing")
            assert response.status_code == 200
            assert response.json()["service_id"] == "mock_processing_service"


if __name__ == "__main__":
    # 運行測試
    pytest.main([__file__, "-v"])
