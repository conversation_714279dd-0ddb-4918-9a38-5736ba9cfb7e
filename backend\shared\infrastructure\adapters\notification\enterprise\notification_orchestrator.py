"""
企業級通知協調器 - 精簡版
負載均衡、資源管理、批次執行協調
"""

import time
import threading
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor, Future
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from threading import RLock
from dataclasses import dataclass, field
import queue
import psutil

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from ..models.notification_models import (
    NotificationRequest, BatchNotificationRequest,
    NotificationPriority, NotificationConfig
)


@dataclass
class LoadBalancerNode:
    """負載均衡節點"""
    service_id: str
    service_instance: Any
    weight: int = 100
    current_load: int = 0
    max_capacity: int = 100
    is_healthy: bool = True
    last_used: datetime = field(default_factory=datetime.now)


class ServiceLoadBalancer:
    """服務負載均衡器 - 精簡版"""
    
    def __init__(self):
        self.nodes: Dict[str, LoadBalancerNode] = {}
        self._lock = RLock()
    
    def register_service(self, service_id: str, service_instance: Any, weight: int = 100, max_capacity: int = 100):
        """註冊服務節點"""
        with self._lock:
            self.nodes[service_id] = LoadBalancerNode(
                service_id=service_id,
                service_instance=service_instance,
                weight=weight,
                max_capacity=max_capacity
            )
    
    def get_best_service(self) -> Optional[LoadBalancerNode]:
        """取得最佳服務節點（最少連接）"""
        with self._lock:
            available_nodes = [
                node for node in self.nodes.values()
                if node.is_healthy and node.current_load < node.max_capacity
            ]
            
            if not available_nodes:
                return None
            
            # 選擇負載最少的節點
            best_node = min(available_nodes, key=lambda n: n.current_load / max(n.weight, 1))
            best_node.current_load += 1
            best_node.last_used = datetime.now()
            
            return best_node
    
    def release_service(self, service_id: str):
        """釋放服務節點負載"""
        with self._lock:
            if service_id in self.nodes:
                self.nodes[service_id].current_load = max(0, self.nodes[service_id].current_load - 1)
    
    def get_load_status(self) -> Dict[str, Any]:
        """取得負載狀態"""
        with self._lock:
            return {
                'total_nodes': len(self.nodes),
                'healthy_nodes': sum(1 for n in self.nodes.values() if n.is_healthy),
                'total_load': sum(n.current_load for n in self.nodes.values()),
                'nodes': {
                    node.service_id: {
                        'healthy': node.is_healthy,
                        'current_load': node.current_load,
                        'max_capacity': node.max_capacity,
                        'weight': node.weight
                    } for node in self.nodes.values()
                }
            }


class ResourceManager:
    """精簡資源管理器"""
    
    def __init__(self, max_memory_mb: int = 512, max_cpu_percent: float = 80.0):
        self.max_memory_mb = max_memory_mb
        self.max_cpu_percent = max_cpu_percent
    
    def check_resources(self) -> Dict[str, Any]:
        """檢查系統資源"""
        try:
            memory_usage = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent()
            process = psutil.Process()
            current_memory_mb = process.memory_info().rss / 1024 / 1024
            
            return {
                'timestamp': datetime.now(),
                'system_memory_percent': memory_usage.percent,
                'system_cpu_percent': cpu_percent,
                'process_memory_mb': current_memory_mb,
                'overall_available': (
                    current_memory_mb < self.max_memory_mb and 
                    cpu_percent < self.max_cpu_percent
                )
            }
        except Exception as e:
            return {
                'timestamp': datetime.now(),
                'error': str(e),
                'overall_available': True
            }


class NotificationOrchestrator:
    """企業級通知協調器 - 精簡版"""
    
    def __init__(
        self,
        config: Optional[NotificationConfig] = None,
        logger_manager: Optional[LoggerManager] = None,
        max_workers: int = 5
    ):
        self.config = config or NotificationConfig()
        self.logger_manager = logger_manager or LoggerManager()
        self.logger = self.logger_manager.get_logger("NotificationOrchestrator")
        
        # 核心元件
        self.load_balancer = ServiceLoadBalancer()
        self.resource_manager = ResourceManager()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 批次處理
        self.batch_queue: queue.PriorityQueue = queue.PriorityQueue()
        self._active_batches: Dict[str, Future] = {}
        self._batch_results: Dict[str, Any] = {}
        self._statistics = {
            'total_batches': 0, 'successful_batches': 0, 'failed_batches': 0,
            'total_notifications': 0, 'service_utilization': defaultdict(int)
        }
        
        # 控制
        self._lock = RLock()
        self._running = True
        self._coordinator_thread = threading.Thread(
            target=self._coordinate_batch_processing, daemon=True
        )
        self._coordinator_thread.start()
        
        self.logger.info("通知協調器已初始化")
    
    def register_notification_service(
        self, 
        service_id: str, 
        service_instance: Any, 
        weight: int = 100, 
        max_capacity: int = 100
    ):
        """註冊通知服務"""
        self.load_balancer.register_service(service_id, service_instance, weight, max_capacity)
        self.logger.info(f"通知服務已註冊: {service_id} (權重: {weight}, 容量: {max_capacity})")
    
    def orchestrate_batch_notification(
        self, 
        batch_request: BatchNotificationRequest,
        priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> str:
        """協調批量通知處理"""
        try:
            resource_status = self.resource_manager.check_resources()
            coordination_id = f"coord_{int(time.time())}_{batch_request.id[:8]}"
            
            priority_value = {
                NotificationPriority.LOW: 4, NotificationPriority.NORMAL: 3,
                NotificationPriority.HIGH: 2, NotificationPriority.URGENT: 1
            }.get(priority, 3)
            
            self.batch_queue.put((priority_value, {
                'coordination_id': coordination_id,
                'batch_request': batch_request,
                'submitted_at': datetime.now(),
                'resource_status': resource_status
            }))
            
            self.logger.info(f"批次已加入協調佇列: {batch_request.name}")
            return coordination_id
            
        except Exception as e:
            self.logger.error(f"協調批量通知失敗: {e}")
            raise
    
    def _coordinate_batch_processing(self):
        """協調批次處理的背景執行緒"""
        while self._running:
            try:
                try:
                    priority_item = self.batch_queue.get(timeout=1.0)
                    _, coordination_task = priority_item
                except queue.Empty:
                    continue
                
                # 提交批次協調任務
                future = self.executor.submit(self._execute_batch_coordination, coordination_task)
                
                coordination_id = coordination_task['coordination_id']
                self._active_batches[coordination_id] = future
                
                # 清理已完成的任務
                self._cleanup_completed_batches()
                
            except Exception as e:
                self.logger.error(f"批次協調錯誤: {e}")
                time.sleep(1)
    
    def _execute_batch_coordination(self, coordination_task: Dict[str, Any]) -> Dict[str, Any]:
        """執行批次協調"""
        start_time = time.time()
        coordination_id = coordination_task['coordination_id']
        batch_request = coordination_task['batch_request']
        
        try:
            service_node = self.load_balancer.get_best_service()
            if not service_node:
                raise RuntimeError("沒有可用的通知服務節點")
            
            try:
                # 執行批量通知
                if hasattr(service_node.service_instance, 'send_batch_notifications'):
                    result = service_node.service_instance.send_batch_notifications(batch_request)
                else:
                    result = self._fallback_individual_processing(service_node, batch_request)
                
                processing_time = (time.time() - start_time) * 1000
                self._update_statistics(batch_request, result, processing_time, service_node.service_id)
                
                final_result = {
                    'status': 'completed', 'result': result,
                    'service_used': service_node.service_id,
                    'processing_time_ms': processing_time,
                    'completed_at': datetime.now()
                }
                
                with self._lock:
                    self._batch_results[coordination_id] = final_result
                
                return final_result
                
            finally:
                self.load_balancer.release_service(service_node.service_id)
                
        except Exception as e:
            error_result = {
                'status': 'failed', 'error': str(e),
                'processing_time_ms': (time.time() - start_time) * 1000,
                'completed_at': datetime.now()
            }
            
            with self._lock:
                self._batch_results[coordination_id] = error_result
                self._statistics['failed_batches'] += 1
            
            return error_result
    
    def _fallback_individual_processing(self, service_node: LoadBalancerNode, batch_request: BatchNotificationRequest) -> Dict[str, Any]:
        """退回到逐一處理"""
        successful = failed = 0
        start_time = time.time()
        
        for request in batch_request.requests:
            try:
                if hasattr(service_node.service_instance, 'send_notification'):
                    if service_node.service_instance.send_notification(request):
                        successful += 1
                    else:
                        failed += 1
                else:
                    failed += 1
            except Exception:
                failed += 1
        
        return {
            'batch_id': batch_request.id, 'batch_name': batch_request.name,
            'status': 'completed', 'total_requests': len(batch_request.requests),
            'successful': successful, 'failed': failed,
            'success_rate': successful / max(successful + failed, 1),
            'processing_time_ms': (time.time() - start_time) * 1000,
            'processing_mode': 'individual_fallback'
        }
    
    def _cleanup_completed_batches(self):
        """清理已完成的批次任務"""
        completed_ids = []
        
        for coordination_id, future in self._active_batches.items():
            if future.done():
                completed_ids.append(coordination_id)
        
        for coordination_id in completed_ids:
            del self._active_batches[coordination_id]
    
    def _update_statistics(self, batch_request: BatchNotificationRequest, result: Dict[str, Any], processing_time: float, service_id: str):
        """更新統計"""
        with self._lock:
            self._statistics['total_batches'] += 1
            if result.get('status') == 'completed':
                self._statistics['successful_batches'] += 1
            else:
                self._statistics['failed_batches'] += 1
            self._statistics['total_notifications'] += len(batch_request.requests)
            self._statistics['service_utilization'][service_id] += 1
    
    def get_coordination_result(self, coordination_id: str) -> Optional[Dict[str, Any]]:
        """取得協調結果"""
        with self._lock:
            return self._batch_results.get(coordination_id)
    
    def get_orchestration_statistics(self) -> Dict[str, Any]:
        """取得統計資料"""
        with self._lock:
            stats = self._statistics.copy()
            stats['active_batches'] = len(self._active_batches)
            stats['queue_size'] = self.batch_queue.qsize()
            stats['load_balancer_status'] = self.load_balancer.get_load_status()
            stats['resource_status'] = self.resource_manager.check_resources()
            
            total_batches = stats['successful_batches'] + stats['failed_batches']
            stats['batch_success_rate'] = (
                stats['successful_batches'] / total_batches * 100 if total_batches > 0 else 0.0
            )
            return stats
    
    def get_system_health(self) -> Dict[str, Any]:
        """取得系統健康狀態"""
        resource_status = self.resource_manager.check_resources()
        load_status = self.load_balancer.get_load_status()
        
        health_score = 100.0
        if not resource_status.get('overall_available', True):
            health_score -= 30
        if load_status['healthy_nodes'] == 0:
            health_score -= 50
        elif load_status['healthy_nodes'] < load_status['total_nodes']:
            health_score -= 20
        
        return {
            'overall_health_score': max(0, health_score),
            'status': 'healthy' if health_score >= 80 else 'degraded' if health_score >= 50 else 'critical',
            'resource_status': resource_status, 'service_status': load_status,
            'active_tasks': len(self._active_batches), 'queue_size': self.batch_queue.qsize()
        }
    
    def shutdown(self):
        """關閉協調器"""
        self.logger.info("正在關閉通知協調器...")
        self._running = False
        
        if self._coordinator_thread.is_alive():
            self._coordinator_thread.join(timeout=5)
        
        self.executor.shutdown(wait=True)
        self.logger.info("通知協調器已關閉")