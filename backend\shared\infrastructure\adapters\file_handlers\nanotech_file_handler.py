"""
NANOTECH 廠商檔案處理器
對應 VBA 的 CopyFilesNanotech 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class NanotechFileHandler(BaseFileHandler):
    """
    NANOTECH 廠商檔案處理器

    VBA 邏輯：
    - 來源路徑：sourcePath & "\\Nano\\temp\\" & pd & "\\"
    - 搜尋模式：Dir(sourcePathNanotech & fileName & "\\*")
    - 檔案類型：資料夾下所有檔案
    - 特殊邏輯：從 pd 子目錄中的 fileName 資料夾複製所有檔案
    """

    def __init__(self, source_base_path: str):
        """初始化 NANOTECH 檔案處理器"""
        super().__init__(source_base_path, "NANOTECH")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        NANOTECH 的來源路徑
        
        VBA: sourcePathNanotech = sourcePath & "\\Nano\\temp\\" & pd & "\\"
        """
        paths = []
        
        # NANOTECH 需要 pd 參數來構建路徑
        if pd != "default":
            nanotech_path = self.source_base_path / "Nano" / "temp" / pd
            paths.append(nanotech_path)
            self.logger.info(f"🏭 NANOTECH 路徑: {nanotech_path}")
        else:
            self.logger.warning("⚠️  NANOTECH 需要有效的 PD 參數來構建路徑")
            self.logger.warning(f"   當前 PD: '{pd}'")
            
            # 如果缺少 PD，嘗試基礎路徑
            base_nanotech_path = self.source_base_path / "Nano" / "temp"
            if self._safe_path_exists(base_nanotech_path):
                paths.append(base_nanotech_path)
                self.logger.info(f"   退回使用基礎路徑: {base_nanotech_path}")
        
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        NANOTECH 的檔案搜尋模式
        
        VBA: file = Dir(sourcePathNanotech & fileName & "\\*")
        注意：NANOTECH 搜尋 MO 資料夾下的所有檔案
        """
        patterns = []
        
        if mo:
            # NANOTECH 特殊模式：搜尋 MO 資料夾下的所有檔案
            patterns.append(f"{mo}/*")  # 資料夾下的所有檔案
            
        return patterns
        
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """
        覆寫 MO 複製邏輯，實作 NANOTECH 特殊的資料夾內檔案複製邏輯
        
        VBA 邏輯：
        file = Dir(sourcePathNanotech & fileName & "\\*")
        複製 MO 資料夾下的所有檔案
        """
        try:
            self.logger.info(f"      🔍 NANOTECH 特殊邏輯: 複製 MO 資料夾 '{mo}' 下的所有檔案")
            
            if not mo:
                self.logger.warning(f"      ❌ MO 參數為空")
                return False
            
            # 構建 MO 資料夾路徑
            mo_folder_path = source_path / mo
            
            if not self._safe_path_exists(mo_folder_path):
                self.logger.warning(f"      ❌ MO 資料夾不存在: {mo_folder_path}")
                return False
                
            if not mo_folder_path.is_dir():
                self.logger.warning(f"      ❌ MO 路徑不是資料夾: {mo_folder_path}")
                return False
            
            # 取得 MO 資料夾中的所有檔案
            all_files = [f for f in mo_folder_path.iterdir() if f.is_file()]
            
            self.logger.info(f"      📁 找到 {len(all_files)} 個檔案在 MO 資料夾 '{mo}' 中")
            
            if not all_files:
                self.logger.warning(f"      ❌ MO 資料夾 '{mo}' 中沒有檔案")
                return False
            
            # 顯示找到的檔案
            for f in all_files[:5]:  # 只顯示前5個
                self.logger.info(f"         - {f.name}")
            if len(all_files) > 5:
                self.logger.info(f"         ... 還有 {len(all_files) - 5} 個檔案")
            
            # 複製所有檔案
            success = False
            copied_count = 0
            
            for file_path in all_files:
                try:
                    if self._copy_file_with_check(file_path, dest_path):
                        copied_count += 1
                        success = True
                        self.logger.info(f"      ✅ 成功複製: {file_path.name}")
                    else:
                        self.logger.warning(f"      ❌ 複製失敗: {file_path.name}")
                except Exception as e:
                    self.logger.error(f"      ❌ 複製檔案錯誤 {file_path.name}: {e}")
            
            if success:
                self.logger.info(f"      🎉 NANOTECH 複製完成: {copied_count}/{len(all_files)} 個檔案成功")
            
            return success
                
        except Exception as e:
            self.logger.error(f"NANOTECH MO 複製失敗: {e}")
            return False
        
    def _supports_folder_copy(self) -> bool:
        """NANOTECH 支援資料夾複製（如果 MO 複製失敗的話）"""
        return True
        
    def _copy_entire_folder(self, source_base: Path, dest_base: Path, 
                           pd: str, lot: str) -> bool:
        """
        覆寫資料夾複製邏輯，實作 NANOTECH 特殊的整個 PD 資料夾複製
        """
        try:
            self.logger.info(f"      🔍 NANOTECH 資料夾複製: 嘗試複製整個 PD 資料夾")
            
            # NANOTECH 的資料夾結構：\\Nano\\temp\\{pd}\\
            source_folder = source_base  # source_base 已經是 \\Nano\\temp\\{pd}\\
            
            if not self._safe_path_exists(source_folder) or not source_folder.is_dir():
                self.logger.warning(f"      ❌ 來源資料夾不存在或不是資料夾: {source_folder}")
                return False
            
            # 取得資料夾中的所有檔案和子資料夾
            all_items = list(source_folder.iterdir())
            files = [item for item in all_items if item.is_file()]
            dirs = [item for item in all_items if item.is_dir()]
            
            self.logger.info(f"      📁 資料夾包含: {len(files)} 個檔案, {len(dirs)} 個子資料夾")
            
            if not files and not dirs:
                self.logger.warning(f"      ❌ 資料夾是空的")
                return False
            
            # 複製所有檔案
            success = False
            copied_count = 0
            
            # 複製檔案
            for file_path in files:
                try:
                    if self._copy_file_with_check(file_path, dest_base):
                        copied_count += 1
                        success = True
                except Exception as e:
                    self.logger.error(f"      ❌ 複製檔案錯誤 {file_path.name}: {e}")
            
            # 如果有子資料夾，也嘗試複製
            for dir_path in dirs:
                try:
                    # 遞歸複製子資料夾中的檔案
                    subdir_files = [f for f in dir_path.rglob('*') if f.is_file()]
                    for subfile in subdir_files:
                        if self._copy_file_with_check(subfile, dest_base):
                            copied_count += 1
                            success = True
                except Exception as e:
                    self.logger.error(f"      ❌ 複製子資料夾錯誤 {dir_path.name}: {e}")
            
            if success:
                self.logger.info(f"      🎉 NANOTECH 資料夾複製完成: 總共複製 {copied_count} 個檔案")
            
            return success
                
        except Exception as e:
            self.logger.error(f"NANOTECH 資料夾複製失敗: {e}")
            return False