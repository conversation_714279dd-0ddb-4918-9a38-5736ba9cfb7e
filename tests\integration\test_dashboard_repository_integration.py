"""
監控儀表板資料存取層整合測試

測試監控資料存取層和告警資料存取層的整合功能：
- 資料庫共享和一致性
- 跨模組資料查詢
- 效能和並發處理
- 資料清理和維護
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

from backend.monitoring.repositories.dashboard_monitoring_repository import DashboardMonitoringRepository
from backend.monitoring.repositories.dashboard_alert_repository import DashboardAlertRepository
from backend.monitoring.models.dashboard_metrics_models import (
    DashboardMetrics, EmailMetrics, CeleryMetrics, SystemMetrics, 
    FileMetrics, BusinessMetrics
)
from backend.monitoring.models.dashboard_alert_models import DashboardAlert


class TestDashboardRepositoryIntegration:
    """監控儀表板資料存取層整合測試類"""
    
    @pytest.fixture
    def temp_db_path(self):
        """創建臨時資料庫檔案"""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
            
    @pytest.fixture
    def monitoring_repo(self, temp_db_path):
        """創建監控資料存取層實例"""
        return DashboardMonitoringRepository(temp_db_path)
        
    @pytest.fixture
    def alert_repo(self, temp_db_path):
        """創建告警資料存取層實例"""
        return DashboardAlertRepository(temp_db_path)
        
    @pytest.fixture
    def sample_metrics(self):
        """創建測試用的監控指標資料"""
        timestamp = datetime.now()
        
        email_metrics = EmailMetrics(
            pending_count=15,  # 超過告警閾值
            processing_count=3,
            completed_count=200,
            failed_count=5,
            avg_processing_time_seconds=350.0,  # 超過告警閾值
            throughput_per_hour=30.0,
            vendor_queue_counts={"GTK": 8, "JCET": 7},
            vendor_success_rates={"GTK": 0.92, "JCET": 0.85},
            code_comparison_active=2,
            code_comparison_pending=6,
            code_comparison_avg_duration=200.0
        )
        
        celery_metrics = CeleryMetrics(
            total_active=5,
            total_pending=25,  # 超過告警閾值
            total_completed=300,
            total_failed=15,
            task_type_counts={
                "code_comparison": {"active": 2, "pending": 10, "completed": 150, "failed": 8},
                "csv_to_summary": {"active": 3, "pending": 15, "completed": 150, "failed": 7}
            },
            worker_status={"worker1": "online", "worker2": "online", "worker3": "offline"},
            worker_load={"worker1": 3, "worker2": 2, "worker3": 0},
            avg_task_duration={"code_comparison": 200.0, "csv_to_summary": 120.0},
            task_success_rate={"code_comparison": 0.90, "csv_to_summary": 0.88}
        )
        
        system_metrics = SystemMetrics(
            cpu_percent=85.0,  # 超過告警閾值
            memory_percent=90.0,  # 超過告警閾值
            disk_percent=75.0,
            memory_available_mb=1024.0,
            disk_free_gb=200.0,
            active_connections=50,
            websocket_connections=8,
            service_health={"email_service": "warning", "celery_service": "healthy"},
            database_connections=15,
            database_query_avg_time=0.08,
            database_size_mb=512.0
        )
        
        file_metrics = FileMetrics(
            attachments_downloaded=40,
            attachments_pending=8,
            attachments_failed=2,
            file_type_counts={"csv": 20, "excel": 15, "zip": 5},
            compression_active=2,
            compression_pending=5,
            decompression_active=1,
            decompression_pending=3,
            temp_folder_size_mb=1200.0,  # 超過告警閾值
            upload_folder_size_mb=2048.0,
            processed_folder_size_mb=4096.0,
            avg_download_time=45.0,
            avg_compression_time=60.0,
            avg_decompression_time=35.0
        )
        
        business_metrics = BusinessMetrics(
            mo_processed_today=80,
            lot_processed_today=240,
            data_quality_score=88.5,
            validation_errors_count=8,
            duplicate_mo_count=3,
            vendor_processing_stats={
                "GTK": {"mo_count": 50, "lot_count": 150, "success_rate": 92},
                "JCET": {"mo_count": 30, "lot_count": 90, "success_rate": 85}
            },
            reports_generated_today=15,
            reports_pending=4,
            avg_report_generation_time=180.0
        )
        
        return DashboardMetrics(
            timestamp=timestamp,
            email_metrics=email_metrics,
            celery_metrics=celery_metrics,
            system_metrics=system_metrics,
            file_metrics=file_metrics,
            business_metrics=business_metrics
        )
        
    def test_shared_database_initialization(self, monitoring_repo, alert_repo):
        """測試共享資料庫初始化"""
        # 檢查兩個存取層是否使用相同的資料庫
        assert monitoring_repo.db_path == alert_repo.db_path
        
        # 檢查所有表格是否都已創建
        with monitoring_repo._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' 
                ORDER BY name
            """)
            tables = [row['name'] for row in cursor.fetchall()]
            
            # 監控相關表格
            monitoring_tables = [
                'dashboard_metrics_history',
                'dashboard_current_status',
                'task_execution_history',
                'system_health_checks',
                'file_processing_stats'
            ]
            
            # 告警相關表格
            alert_tables = [
                'alert_history',
                'dashboard_alert_rules',
                'alert_notifications',
                'alert_suppression_rules'
            ]
            
            for table in monitoring_tables + alert_tables:
                assert table in tables, f"表格 {table} 未創建"
                
    @pytest.mark.asyncio
    async def test_metrics_storage_and_alert_generation(self, monitoring_repo, alert_repo, sample_metrics):
        """測試監控指標儲存和告警生成的整合"""
        # 儲存監控指標
        result = await monitoring_repo.store_metrics(sample_metrics)
        assert result is True
        
        # 模擬基於指標生成告警
        alerts_to_generate = []
        
        # 檢查郵件佇列告警
        if sample_metrics.email_metrics.pending_count > 10:
            alerts_to_generate.append(DashboardAlert(
                id="email_queue_alert",
                alert_type="queue_overflow",
                level="warning",
                title="郵件佇列告警",
                message=f"郵件佇列待處理數量過多 ({sample_metrics.email_metrics.pending_count} > 10)",
                source="email_monitor",
                triggered_at=datetime.now(),
                threshold_value=10.0,
                current_value=float(sample_metrics.email_metrics.pending_count)
            ))
            
        # 檢查系統資源告警
        if sample_metrics.system_metrics.cpu_percent > 80:
            alerts_to_generate.append(DashboardAlert(
                id="cpu_high_alert",
                alert_type="resource_high",
                level="warning",
                title="CPU 使用率告警",
                message=f"CPU 使用率過高 ({sample_metrics.system_metrics.cpu_percent}% > 80%)",
                source="system_monitor",
                triggered_at=datetime.now(),
                threshold_value=80.0,
                current_value=sample_metrics.system_metrics.cpu_percent
            ))
            
        # 儲存生成的告警
        for alert in alerts_to_generate:
            result = await alert_repo.store_alert(alert)
            assert result is True
            
        # 驗證告警是否正確儲存
        active_alerts = await alert_repo.get_active_alerts()
        assert len(active_alerts) >= len(alerts_to_generate)
        
        # 驗證告警與指標的關聯
        alert_types = [alert.alert_type for alert in active_alerts]
        assert "queue_overflow" in alert_types
        assert "resource_high" in alert_types
        
    @pytest.mark.asyncio
    async def test_cross_repository_data_correlation(self, monitoring_repo, alert_repo, sample_metrics):
        """測試跨存取層的資料關聯"""
        # 儲存監控指標
        await monitoring_repo.store_metrics(sample_metrics)
        
        # 儲存相關告警
        alert = DashboardAlert(
            id="correlation_test_alert",
            alert_type="high_memory",
            level="critical",
            title="記憶體使用率告警",
            message="記憶體使用率嚴重過高",
            source="system_monitor",
            triggered_at=sample_metrics.timestamp,
            threshold_value=85.0,
            current_value=sample_metrics.system_metrics.memory_percent,
            metadata={
                "metric_timestamp": sample_metrics.timestamp.isoformat(),
                "related_metrics": ["system.memory_percent", "system.memory_available_mb"]
            }
        )
        await alert_repo.store_alert(alert)
        
        # 查詢相關的監控資料
        trend_data = await monitoring_repo.get_trend_data('resource', '1h', 50)
        memory_metrics = [item for item in trend_data if 'memory' in item['metric_name']]
        
        # 查詢相關的告警資料
        alert_history = await alert_repo.get_alert_history(1, source_system="system_monitor")
        
        # 驗證資料關聯
        assert len(memory_metrics) > 0
        assert len(alert_history) > 0
        
        # 檢查時間戳關聯
        alert_time = datetime.fromisoformat(alert_history[0]['triggered_at'])
        metric_times = [datetime.fromisoformat(item['timestamp']) for item in memory_metrics]
        
        # 告警時間應該與指標時間相近
        time_diffs = [abs((alert_time - mt).total_seconds()) for mt in metric_times]
        assert min(time_diffs) < 60  # 1分鐘內
        
    @pytest.mark.asyncio
    async def test_performance_with_large_dataset(self, monitoring_repo, alert_repo):
        """測試大資料集的效能"""
        import time
        
        # 生成大量測試資料
        start_time = time.time()
        
        # 儲存大量監控指標
        for i in range(100):
            timestamp = datetime.now() - timedelta(minutes=i)
            
            # 簡化的指標資料
            email_metrics = EmailMetrics(
                pending_count=i % 20,
                processing_count=i % 5,
                completed_count=i * 10,
                failed_count=i % 3,
                avg_processing_time_seconds=100.0 + i,
                throughput_per_hour=20.0 + i,
                vendor_queue_counts={"GTK": i % 10, "JCET": i % 8},
                vendor_success_rates={"GTK": 0.9 + (i % 10) * 0.01, "JCET": 0.85 + (i % 10) * 0.01},
                code_comparison_active=i % 3,
                code_comparison_pending=i % 8,
                code_comparison_avg_duration=150.0 + i
            )
            
            celery_metrics = CeleryMetrics(
                total_active=i % 10,
                total_pending=i % 30,
                total_completed=i * 5,
                total_failed=i % 5,
                task_type_counts={
                    "code_comparison": {"active": i % 3, "pending": i % 10, "completed": i * 2, "failed": i % 2}
                },
                worker_status={"worker1": "online"},
                worker_load={"worker1": i % 5},
                avg_task_duration={"code_comparison": 120.0 + i},
                task_success_rate={"code_comparison": 0.9 + (i % 10) * 0.01}
            )
            
            system_metrics = SystemMetrics(
                cpu_percent=50.0 + (i % 40),
                memory_percent=60.0 + (i % 30),
                disk_percent=40.0 + (i % 20),
                memory_available_mb=2048.0 - i * 10,
                disk_free_gb=1000.0 - i * 5,
                active_connections=20 + i % 30,
                websocket_connections=i % 10,
                service_health={"email_service": "healthy"},
                database_connections=10 + i % 5,
                database_query_avg_time=0.05 + (i % 10) * 0.01,
                database_size_mb=256.0 + i
            )
            
            file_metrics = FileMetrics(
                attachments_downloaded=i * 2,
                attachments_pending=i % 5,
                attachments_failed=i % 2,
                file_type_counts={"csv": i % 15, "excel": i % 10},
                compression_active=i % 3,
                compression_pending=i % 5,
                decompression_active=i % 2,
                decompression_pending=i % 3,
                temp_folder_size_mb=500.0 + i * 10,
                upload_folder_size_mb=1000.0 + i * 5,
                processed_folder_size_mb=2000.0 + i * 3,
                avg_download_time=30.0 + i,
                avg_compression_time=40.0 + i,
                avg_decompression_time=25.0 + i
            )
            
            business_metrics = BusinessMetrics(
                mo_processed_today=i * 3,
                lot_processed_today=i * 8,
                data_quality_score=85.0 + (i % 15),
                validation_errors_count=i % 5,
                duplicate_mo_count=i % 3,
                vendor_processing_stats={"GTK": {"mo_count": i * 2, "lot_count": i * 6, "success_rate": 90 + i % 10}},
                reports_generated_today=i % 20,
                reports_pending=i % 5,
                avg_report_generation_time=120.0 + i
            )
            
            metrics = DashboardMetrics(
                timestamp=timestamp,
                email_metrics=email_metrics,
                celery_metrics=celery_metrics,
                system_metrics=system_metrics,
                file_metrics=file_metrics,
                business_metrics=business_metrics
            )
            
            await monitoring_repo.store_metrics(metrics)
            
            # 每10個指標生成一個告警
            if i % 10 == 0:
                alert = DashboardAlert(
                    id=f"perf_test_alert_{i}",
                    alert_type="performance_test",
                    level="warning",
                    title=f"效能測試告警 {i}",
                    message=f"效能測試訊息 {i}",
                    source="performance_monitor",
                    triggered_at=timestamp
                )
                await alert_repo.store_alert(alert)
                
        storage_time = time.time() - start_time
        
        # 測試查詢效能
        query_start = time.time()
        
        # 查詢監控資料
        current_status = await monitoring_repo.get_current_status()
        trend_data = await monitoring_repo.get_trend_data('queue_count', '24h', 100)
        task_history = await monitoring_repo.get_task_execution_history(limit=50)
        
        # 查詢告警資料
        active_alerts = await alert_repo.get_active_alerts(50)
        alert_stats = await alert_repo.get_alert_statistics(7)
        
        query_time = time.time() - query_start
        
        # 驗證效能要求
        assert storage_time < 30.0, f"儲存100個指標耗時過長: {storage_time}秒"
        assert query_time < 5.0, f"查詢操作耗時過長: {query_time}秒"
        
        # 驗證資料完整性
        assert len(current_status) > 0
        assert len(trend_data) > 0
        assert len(active_alerts) > 0
        assert isinstance(alert_stats, dict)
        
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, monitoring_repo, alert_repo):
        """測試並發操作"""
        import asyncio
        
        # 創建並發任務
        tasks = []
        
        # 並發儲存監控指標
        for i in range(10):
            timestamp = datetime.now() + timedelta(seconds=i)
            
            email_metrics = EmailMetrics(
                pending_count=i,
                processing_count=1,
                completed_count=i * 10,
                failed_count=0,
                avg_processing_time_seconds=100.0,
                throughput_per_hour=20.0,
                vendor_queue_counts={"GTK": i},
                vendor_success_rates={"GTK": 0.95},
                code_comparison_active=1,
                code_comparison_pending=i,
                code_comparison_avg_duration=150.0
            )
            
            # 簡化其他指標
            celery_metrics = CeleryMetrics(
                total_active=i, total_pending=i*2, total_completed=i*10, total_failed=0,
                task_type_counts={}, worker_status={}, worker_load={},
                avg_task_duration={}, task_success_rate={}
            )
            
            system_metrics = SystemMetrics(
                cpu_percent=50.0, memory_percent=60.0, disk_percent=40.0,
                memory_available_mb=2048.0, disk_free_gb=1000.0,
                active_connections=20, websocket_connections=5,
                service_health={}, database_connections=10,
                database_query_avg_time=0.05, database_size_mb=256.0
            )
            
            file_metrics = FileMetrics(
                attachments_downloaded=i, attachments_pending=1, attachments_failed=0,
                file_type_counts={}, compression_active=0, compression_pending=0,
                decompression_active=0, decompression_pending=0,
                temp_folder_size_mb=500.0, upload_folder_size_mb=1000.0,
                processed_folder_size_mb=2000.0, avg_download_time=30.0,
                avg_compression_time=40.0, avg_decompression_time=25.0
            )
            
            business_metrics = BusinessMetrics(
                mo_processed_today=i, lot_processed_today=i*3, data_quality_score=90.0,
                validation_errors_count=0, duplicate_mo_count=0,
                vendor_processing_stats={}, reports_generated_today=i,
                reports_pending=0, avg_report_generation_time=120.0
            )
            
            metrics = DashboardMetrics(
                timestamp=timestamp,
                email_metrics=email_metrics,
                celery_metrics=celery_metrics,
                system_metrics=system_metrics,
                file_metrics=file_metrics,
                business_metrics=business_metrics
            )
            
            tasks.append(monitoring_repo.store_metrics(metrics))
            
        # 並發儲存告警
        for i in range(5):
            alert = DashboardAlert(
                id=f"concurrent_alert_{i}",
                alert_type="concurrent_test",
                level="warning",
                title=f"並發告警 {i}",
                message=f"並發測試訊息 {i}",
                source="concurrent_monitor",
                triggered_at=datetime.now() + timedelta(seconds=i)
            )
            tasks.append(alert_repo.store_alert(alert))
            
        # 執行所有並發任務
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 檢查所有任務都成功完成
        for result in results:
            assert result is True or not isinstance(result, Exception)
            
        # 驗證資料完整性
        current_status = await monitoring_repo.get_current_status()
        active_alerts = await alert_repo.get_active_alerts()
        
        assert len(current_status) > 0
        concurrent_alerts = [a for a in active_alerts if a.alert_type == "concurrent_test"]
        assert len(concurrent_alerts) == 5
        
    @pytest.mark.asyncio
    async def test_data_cleanup_coordination(self, monitoring_repo, alert_repo):
        """測試資料清理的協調"""
        # 儲存一些舊資料
        old_timestamp = datetime.now() - timedelta(days=100)
        
        # 舊監控指標
        old_email_metrics = EmailMetrics(
            pending_count=5, processing_count=1, completed_count=50, failed_count=1,
            avg_processing_time_seconds=100.0, throughput_per_hour=20.0,
            vendor_queue_counts={}, vendor_success_rates={},
            code_comparison_active=1, code_comparison_pending=2, code_comparison_avg_duration=150.0
        )
        
        old_metrics = DashboardMetrics(
            timestamp=old_timestamp,
            email_metrics=old_email_metrics,
            celery_metrics=CeleryMetrics(
                total_active=1, total_pending=2, total_completed=50, total_failed=1,
                task_type_counts={}, worker_status={}, worker_load={},
                avg_task_duration={}, task_success_rate={}
            ),
            system_metrics=SystemMetrics(
                cpu_percent=50.0, memory_percent=60.0, disk_percent=40.0,
                memory_available_mb=2048.0, disk_free_gb=1000.0,
                active_connections=20, websocket_connections=5,
                service_health={}, database_connections=10,
                database_query_avg_time=0.05, database_size_mb=256.0
            ),
            file_metrics=FileMetrics(
                attachments_downloaded=10, attachments_pending=1, attachments_failed=0,
                file_type_counts={}, compression_active=0, compression_pending=0,
                decompression_active=0, decompression_pending=0,
                temp_folder_size_mb=500.0, upload_folder_size_mb=1000.0,
                processed_folder_size_mb=2000.0, avg_download_time=30.0,
                avg_compression_time=40.0, avg_decompression_time=25.0
            ),
            business_metrics=BusinessMetrics(
                mo_processed_today=10, lot_processed_today=30, data_quality_score=90.0,
                validation_errors_count=0, duplicate_mo_count=0,
                vendor_processing_stats={}, reports_generated_today=5,
                reports_pending=0, avg_report_generation_time=120.0
            )
        )
        
        await monitoring_repo.store_metrics(old_metrics)
        
        # 舊告警
        old_alert = DashboardAlert(
            id="old_cleanup_alert",
            alert_type="old_test",
            level="warning",
            title="舊告警",
            message="這是一個舊告警",
            source="cleanup_monitor",
            triggered_at=old_timestamp,
            status="resolved"
        )
        await alert_repo.store_alert(old_alert)
        
        # 儲存一些新資料
        new_timestamp = datetime.now()
        new_metrics = old_metrics
        new_metrics.timestamp = new_timestamp
        await monitoring_repo.store_metrics(new_metrics)
        
        new_alert = DashboardAlert(
            id="new_cleanup_alert",
            alert_type="new_test",
            level="warning",
            title="新告警",
            message="這是一個新告警",
            source="cleanup_monitor",
            triggered_at=new_timestamp
        )
        await alert_repo.store_alert(new_alert)
        
        # 執行協調清理
        monitoring_cleanup = await monitoring_repo.cleanup_old_data(30)
        alert_cleanup = await alert_repo.cleanup_old_alerts(30)
        
        assert monitoring_cleanup is True
        assert alert_cleanup is True
        
        # 驗證清理結果
        current_status = await monitoring_repo.get_current_status()
        active_alerts = await alert_repo.get_active_alerts()
        
        # 新資料應該保留
        assert len(current_status) > 0
        new_alerts = [a for a in active_alerts if a.alert_type == "new_test"]
        assert len(new_alerts) == 1
        
        # 檢查資料庫統計
        db_stats = await monitoring_repo.get_database_stats()
        alert_stats = await alert_repo.get_alert_statistics(7)
        
        assert isinstance(db_stats, dict)
        assert isinstance(alert_stats, dict)
        
    @pytest.mark.asyncio
    async def test_database_consistency(self, monitoring_repo, alert_repo, sample_metrics):
        """測試資料庫一致性"""
        # 在事務中執行多個操作
        await monitoring_repo.store_metrics(sample_metrics)
        
        # 基於指標生成告警
        alert = DashboardAlert(
            id="consistency_test_alert",
            alert_type="consistency_test",
            level="warning",
            title="一致性測試告警",
            message="測試資料庫一致性",
            source="consistency_monitor",
            triggered_at=sample_metrics.timestamp,
            metadata={
                "related_metric_timestamp": sample_metrics.timestamp.isoformat(),
                "metric_values": {
                    "email_pending": sample_metrics.email_metrics.pending_count,
                    "cpu_percent": sample_metrics.system_metrics.cpu_percent
                }
            }
        )
        await alert_repo.store_alert(alert)
        
        # 驗證資料一致性
        # 1. 檢查時間戳一致性
        current_status = await monitoring_repo.get_current_status()
        active_alerts = await alert_repo.get_active_alerts()
        
        # 找到相關告警
        consistency_alert = None
        for a in active_alerts:
            if a.alert_type == "consistency_test":
                consistency_alert = a
                break
                
        assert consistency_alert is not None
        
        # 檢查時間戳是否一致
        metric_timestamp = sample_metrics.timestamp
        alert_timestamp = consistency_alert.triggered_at
        time_diff = abs((metric_timestamp - alert_timestamp).total_seconds())
        assert time_diff < 1.0  # 時間差應該小於1秒
        
        # 2. 檢查資料值一致性
        email_pending_status = current_status.get('email_pending_count', {}).get('value', 0)
        assert email_pending_status == sample_metrics.email_metrics.pending_count
        
        # 3. 檢查外鍵關聯（如果有的話）
        # 這裡可以添加更多的一致性檢查
        
        # 4. 檢查資料庫完整性
        with monitoring_repo._get_connection() as conn:
            cursor = conn.cursor()
            
            # 檢查是否有孤立的資料
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = cursor.fetchall()
            assert len(fk_violations) == 0, f"發現外鍵約束違反: {fk_violations}"
            
            # 檢查資料庫完整性
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()
            assert integrity_result['integrity_check'] == 'ok', "資料庫完整性檢查失敗"