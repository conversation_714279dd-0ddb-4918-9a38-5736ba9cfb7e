"""
FT-EQC 分組處理器
實作完整的資料夾掃描、檔案分類、智能配對和分組功能
基於 VBA module2.txt Compare_Onlineqc 函數邏輯
"""

import os
import time
import csv
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime


class FileFilterConfig:
    """檔案過濾配置管理器 - 從 .env 讀取配置（單例模式）"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        # 只在第一次初始化時載入 .env 檔案
        from dotenv import load_dotenv
        load_dotenv()
        
        FileFilterConfig._initialized = True
        
        self.excluded_folders = self._get_env_list('EXCLUDED_FOLDERS', 'correlation,ctacsv,backup')
        self.excluded_file_prefixes = self._get_env_list('EXCLUDED_FILE_PREFIXES', 'RG_,TEST_,BACKUP_,TEMP_')
        self.excluded_file_suffixes = self._get_env_list('EXCLUDED_FILE_SUFFIXES', '_old,_backup,_temp,_test')
        self.excluded_file_keywords = self._get_env_list('EXCLUDED_FILE_KEYWORDS', 'eqctotaldata,summary,correlation')
        
        # 記錄載入的配置，方便[EXCEPT_CHAR]錯
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"[FOLDER] 排[EXCEPT_CHAR]資料夾: {self.excluded_folders}")
        logger.info(f"[FILE] 排[EXCEPT_CHAR]檔案前綴: {self.excluded_file_prefixes}")
        logger.info(f"[FILE] 排[EXCEPT_CHAR]檔案後綴: {self.excluded_file_suffixes}")
        logger.info(f"[FILE] 排[EXCEPT_CHAR]檔案關鍵字: {self.excluded_file_keywords}")
        
    def _get_env_list(self, env_name: str, default_value: str) -> List[str]:
        """從環境變數獲取逗號分隔的列表"""
        import os
        env_value = os.getenv(env_name, default_value)
        return [item.strip().lower() for item in env_value.split(',') if item.strip()]
    
    def should_exclude_folder(self, folder_path: str) -> bool:
        """檢查是否應排[EXCEPT_CHAR]該資料夾"""
        folder_path_lower = folder_path.lower()
        return any(excluded in folder_path_lower for excluded in self.excluded_folders)
    
    def should_exclude_file(self, file_path: str) -> bool:
        """檢查是否應排[EXCEPT_CHAR]該檔案"""
        filename = os.path.basename(file_path).lower()
        
        # 檢查前綴
        if any(filename.startswith(prefix) for prefix in self.excluded_file_prefixes):
            return True
            
        # 檢查後綴  
        if any(filename.endswith(suffix) for suffix in self.excluded_file_suffixes):
            return True
            
        # 檢查關鍵字
        if any(keyword in filename for keyword in self.excluded_file_keywords):
            return True
            
        return False


@dataclass
class GroupingResult:
    """分組結果資料結構"""
    matched_pairs: List[Tuple[str, str]]  # (ft_file, eqc_file) 配對
    unmatched_eqc: List[str]              # 未配對的 EQC 檔案 (EQC RT)
    statistics: Dict[str, Any]            # 統計資料
    eqc_fail_result: Optional[Dict[str, Any]] = None  # Online EQC 失敗處理結果


class CSVFileDiscovery:
    """CSV 檔案發現和分類器 - 整合智能過濾機制"""
    
    def __init__(self):
        self.ft_markers = ['(ft)', '(auto_qc)']
        self.eqc_markers = ['(qc)', '.qa']
        self.filter_config = FileFilterConfig()  # 替換舊的 excluded_files
    
    def find_all_csv_files(self, folder_path: str) -> List[str]:
        """遞迴掃描資料夾，找出所有 CSV 檔案 - 整合智能過濾機制"""
        csv_files = []
        excluded_count = 0
        folder_excluded_count = 0
        
        for root, dirs, files in os.walk(folder_path):
            # 檢查是否應排[EXCEPT_CHAR]整個資料夾
            if self.filter_config.should_exclude_folder(root):
                folder_excluded_count += len([f for f in files if f.lower().endswith('.csv')])
                continue
                
            for file in files:
                if file.lower().endswith('.csv'):
                    file_path = os.path.join(root, file)
                    
                    # 檢查是否應排[EXCEPT_CHAR]該檔案
                    if self.filter_config.should_exclude_file(file_path):
                        excluded_count += 1
                        continue
                        
                    csv_files.append(file_path)
        
        print(f"[CHART] 檔案掃描統計:")
        print(f"   [OK] 有效CSV檔案: {len(csv_files)} 個")
        print(f"   [NO_ENTRY] 排[EXCEPT_CHAR]檔案: {excluded_count} 個")
        print(f"   [FOLDER] 排[EXCEPT_CHAR]資料夾檔案: {folder_excluded_count} 個")
        
        return self._remove_duplicates(csv_files)
    
    def _remove_duplicates(self, csv_files: List[str]) -> List[str]:
        """去[EXCEPT_CHAR]重複檔案 (基於檔案名稱)"""
        seen_names = set()
        unique_files = []
        
        for file_path in csv_files:
            filename = os.path.basename(file_path)
            if filename not in seen_names:
                seen_names.add(filename)
                unique_files.append(file_path)
        
        return unique_files
    
    def classify_ft_files(self, csv_files: List[str]) -> List[str]:
        """分類 FT 檔案"""
        ft_files = []
        
        for file_path in csv_files:
            if self._is_ft_file(file_path):
                ft_files.append(file_path)
        
        return ft_files
    
    def classify_eqc_files(self, csv_files: List[str]) -> List[str]:
        """分類 EQC 檔案 (包含 Online EQC)"""
        eqc_files = []
        
        for file_path in csv_files:
            if self._is_eqc_file(file_path):
                eqc_files.append(file_path)
        
        return eqc_files
    
    def _is_ft_file(self, file_path: str) -> bool:
        """檢測是否為 FT 檔案"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 檢查前兩行
                for i in range(2):
                    line = f.readline().lower()
                    if any(marker in line for marker in self.ft_markers):
                        return True
            return False
        except Exception:
            return False
    
    def _is_eqc_file(self, file_path: str) -> bool:
        """檢測是否為有效的生產 EQC 檔案 - 增強過濾邏輯"""
        try:
            # 預先檢查：確保檔案沒有被過濾配置排[EXCEPT_CHAR]
            if self.filter_config.should_exclude_file(file_path):
                return False
                
            # 預先檢查：確保檔案不在排[EXCEPT_CHAR]的資料夾中
            if self.filter_config.should_exclude_folder(file_path):
                return False
                
            filename_lower = file_path.lower()
            
            # 檢查檔案路徑是否包含特定標記
            # 注意：onlieEQC 是正確的檔案名格式（缺一個'n'的拼寫）
            if 'onlieeqc' in filename_lower or 'onlineeqc' in filename_lower or '.qa' in filename_lower:
                return True
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                # 檢查是否為 Correlation 測試檔案
                if len(lines) > 1:
                    test_program = lines[1].lower()
                    if '(qc)' in test_program and 'correlation' in file_path.lower():
                        return False  # 排[EXCEPT_CHAR]相關性測試檔案
                
                # 檢查前兩行
                for i in range(min(2, len(lines))):
                    line = lines[i].lower()
                    if '(qc)' in line:
                        return True
                
                # 檢查第三行
                if len(lines) > 2:
                    third_line = lines[2].lower()
                    if 'qa' in third_line:
                        return True
            
            return False
        except Exception:
            return False


class TimeBasedMatcher:
    """基於時間的檔案配對器"""
    
    def __init__(self):
        self.filename_threshold = 3600   # 檔案名稱時間戳閾值（14位時間戳：最多1小時差異）
        self.modified_threshold = 60     # 檔案修改時間閾值 (秒)
    
    def match_files(self, ft_files: List[str], eqc_files: List[str]) -> List[Tuple[str, str]]:
        """執行檔案配對"""
        matched_pairs = []
        used_eqc_files = set()
        
        print(f"[REFRESH] 開始執行檔案配對:")
        print(f"   [CHART] FT檔案數量: {len(ft_files)}")
        print(f"   [CHART] EQC檔案數量: {len(eqc_files)}")
        print(f"   [TARGET] 時間戳閾值: {self.filename_threshold}")
        
        for ft_file in ft_files:
            print(f"\n[SEARCH] 處理 FT檔案: {os.path.basename(ft_file)}")
            
            # 優先使用檔案名稱時間戳配對
            eqc_match = self._find_by_filename_timestamp(ft_file, eqc_files, used_eqc_files)
            
            # 備用：使用檔案修改時間配對
            if not eqc_match:
                print(f"   [REFRESH] 嘗試檔案修改時間配對...")
                eqc_match = self._find_by_modification_time(ft_file, eqc_files, used_eqc_files)
            
            if eqc_match:
                matched_pairs.append((ft_file, eqc_match))
                used_eqc_files.add(eqc_match)
                print(f"   [OK] 配對成功!")
            else:
                print(f"   [ERROR] 配對失敗")
        
        print(f"\n[BOARD] 配對結果總結:")
        print(f"   [OK] 成功配對: {len(matched_pairs)} 組")
        print(f"   [ERROR] 未配對FT檔案: {len(ft_files) - len(matched_pairs)} 個")
        print(f"   [ERROR] 未配對EQC檔案: {len(eqc_files) - len(used_eqc_files)} 個")
        
        return matched_pairs
    
    def _find_by_filename_timestamp(self, ft_file: str, eqc_files: List[str], 
                                   used_files: set) -> Optional[str]:
        """基於檔案名稱時間戳配對"""
        ft_timestamp = self._extract_filename_timestamp(ft_file)
        if ft_timestamp is None:
            print(f"   [WARNING] FT檔案無法提取時間戳: {os.path.basename(ft_file)}")
            return None
        
        print(f"   [SEARCH] FT檔案時間戳: {os.path.basename(ft_file)} → {ft_timestamp}")
        
        best_match = None
        best_diff = float('inf')
        
        for eqc_file in eqc_files:
            if eqc_file in used_files:
                continue
                
            eqc_timestamp = self._extract_filename_timestamp(eqc_file)
            if eqc_timestamp is not None:
                time_diff = abs(ft_timestamp - eqc_timestamp)
                print(f"      [CHART] EQC檔案: {os.path.basename(eqc_file)} → {eqc_timestamp}, 時間差: {time_diff}")
                
                if time_diff < self.filename_threshold and time_diff < best_diff:
                    best_match = eqc_file
                    best_diff = time_diff
        
        if best_match:
            print(f"   [OK] 最佳配對: {os.path.basename(ft_file)} [LEFT_RIGHT_ARROW] {os.path.basename(best_match)} (時間差: {best_diff})")
        else:
            print(f"   [ERROR] 未找到時間戳配對: {os.path.basename(ft_file)}")
            
        return best_match
    
    def _find_by_modification_time(self, ft_file: str, eqc_files: List[str],
                                  used_files: set) -> Optional[str]:
        """基於檔案修改時間配對"""
        try:
            ft_mtime = os.path.getmtime(ft_file)
            
            for eqc_file in eqc_files:
                if eqc_file in used_files:
                    continue
                    
                eqc_mtime = os.path.getmtime(eqc_file)
                time_diff = abs(ft_mtime - eqc_mtime)
                
                if time_diff < self.modified_threshold:
                    return eqc_file
            
            return None
        except Exception:
            return None
    
    def _extract_filename_timestamp(self, file_path: str) -> Optional[int]:
        """提取檔案名稱中的完整時間戳（優先14位，備用8位）"""
        try:
            filename = os.path.splitext(os.path.basename(file_path))[0]
            
            # 優先提取14位時間戳 (YYYYMMDDHHMMSS 格式)
            import re
            timestamp_14_match = re.search(r'(\d{14})', filename)
            if timestamp_14_match:
                timestamp_str = timestamp_14_match.group(1)
                # 驗證是否為合理的日期時間格式
                year = int(timestamp_str[:4])
                month = int(timestamp_str[4:6])
                day = int(timestamp_str[6:8])
                hour = int(timestamp_str[8:10])
                minute = int(timestamp_str[10:12])
                second = int(timestamp_str[12:14])
                
                if (2020 <= year <= 2030 and 1 <= month <= 12 and 1 <= day <= 31 and 
                    0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59):
                    return int(timestamp_str)
            
            # 備用：提取最後8位數字
            if len(filename) >= 8:
                timestamp_str = filename[-8:]
                if timestamp_str.isdigit():
                    return int(timestamp_str)
                    
        except Exception:
            pass
        return None


class CTAFormatHandler:
    """CTA 格式處理器"""
    
    def detect_cta_format(self, file_path: str) -> Dict[str, Any]:
        """檢測 CTA 格式"""
        format_info = {
            'format': 0,              # 0=一般, 8290, 8280
            'match_mode': 'serial_number',
            'bypass_columns': []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 第7行檢測 (index 6)
            if len(lines) > 6:
                line_7_elements = lines[6].strip().split(',')
                if len(line_7_elements) > 2:
                    if 'Serial_No' in line_7_elements[2]:
                        format_info['format'] = 8290
                        format_info['match_mode'] = 'three_column'
                    elif 'Index_No' in line_7_elements[2]:
                        format_info['format'] = 8280
                        format_info['match_mode'] = 'three_column_filtered'
                        
                        # 檢測需要過濾的座標欄位
                        if len(line_7_elements) > 8 and 'X_COORD' in line_7_elements[8]:
                            format_info['bypass_columns'].append(8)
                        if len(line_7_elements) > 9 and 'Y_COORD' in line_7_elements[9]:
                            format_info['bypass_columns'].append(9)
                        if len(line_7_elements) > 14 and 'Alarm' in line_7_elements[14]:
                            format_info['bypass_columns'].append(14)
            
            # 第8行檢測CTA標記 (index 7)
            if len(lines) > 7:
                line_8_elements = lines[7].strip().split(',')
                if line_8_elements and 'cta' in line_8_elements[0].lower():
                    # 確認CTA格式
                    pass
                    
        except Exception:
            pass
        
        return format_info


class OnlineEQCFailProcessor:
    """Online EQC 失敗檔案處理器"""
    
    def __init__(self):
        self.data_start_row = 12  # 資料從第13行開始 (0-based index)
    
    def find_first_non_one_row(self, csv_file_path: str) -> int:
        """
        檢查CSV檔案中第一個BIN不等於1的行
        對應VBA的FindFirstNonOneRow函數
        
        Returns:
            int: 失敗行A欄的值，如果沒有失敗行則返回0
        """
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 從第13行開始檢查 (index 12)
            for i in range(self.data_start_row, len(lines)):
                line = lines[i].strip()
                if not line:
                    continue
                
                elements = line.split(',')
                if len(elements) < 2:
                    continue
                
                # 檢查B欄（索引1）是否不等於1
                try:
                    bin_value = int(elements[1])
                    if bin_value != 1:
                        # 返回A欄（索引0）的值
                        return int(elements[0])
                except (ValueError, IndexError):
                    continue
            
            return 0  # 沒有找到失敗行
            
        except Exception as e:
            print(f"檢查檔案失敗 {csv_file_path}: {e}")
            return 0
    
    def find_first_non_one_row_with_count(self, csv_file_path: str) -> tuple[int, int]:
        """
        檢查CSV檔案中第一個BIN不等於1的行，並統計總失敗數量
        
        Returns:
            tuple: (第一個失敗行A欄的值, 總失敗數量)
        """
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            first_fail_row = 0
            fail_count = 0
            
            # 從第13行開始檢查 (index 12)
            for i in range(self.data_start_row, len(lines)):
                line = lines[i].strip()
                if not line:
                    continue
                
                elements = line.split(',')
                if len(elements) < 2:
                    continue
                
                # 檢查B欄（索引1）是否不等於1
                try:
                    bin_value = int(elements[1])
                    if bin_value != 1:
                        fail_count += 1
                        # 記錄第一個失敗行
                        if first_fail_row == 0:
                            first_fail_row = int(elements[0])
                except (ValueError, IndexError):
                    continue
            
            return first_fail_row, fail_count
            
        except Exception as e:
            print(f"檢查檔案失敗 {csv_file_path}: {e}")
            return 0, 0
    
    def find_online_eqc_fail_files(self, matched_pairs: List[Tuple[str, str]]) -> Dict[str, Any]:
        """
        找出Online EQC失敗檔案
        對應VBA的FindOnlieEQCFAILFiles函數
        
        Args:
            matched_pairs: FT-EQC配對列表 [(ft_file, eqc_file), ...]
            
        Returns:
            dict: 包含失敗檔案列表和統計資料
        """
        online_eqc_fail_files = []
        fail_analysis_files = []
        eqc_fail_count = 0
        fail_details = []  # 新增：失敗詳細資訊
        
        for ft_file, eqc_file in matched_pairs:
            # 檢查EQC檔案是否有失敗資料和失敗數量
            fail_row_value, fail_count = self.find_first_non_one_row_with_count(eqc_file)
            
            if fail_row_value > 0:  # 有找到失敗資料
                online_eqc_fail_files.append(eqc_file)
                eqc_fail_count += 1
                
                # 記錄失敗詳情
                fail_details.append({
                    'file_path': eqc_file,
                    'fail_count': fail_count,
                    'first_fail_row': fail_row_value
                })
                
                # 創建失敗分析檔案
                if ft_file:
                    try:
                        analysis_file = self._copy_rows_to_new_file(ft_file, eqc_file)
                        if analysis_file:
                            fail_analysis_files.append(analysis_file)
                    except Exception as e:
                        print(f"創建分析檔案失敗: {e}")
        
        return {
            'fail_files': online_eqc_fail_files,
            'analysis_files': fail_analysis_files,
            'fail_count': eqc_fail_count,
            'fail_details': fail_details,  # 新增：包含每個檔案的失敗數量
            'processing_timestamp': datetime.now().isoformat()
        }
    
    def _copy_rows_to_new_file(self, ft_file: str, eqc_file: str) -> Optional[str]:
        """
        從FT檔案複製對應的失敗行到新檔案
        簡化版的CopyRowsToNewFile實作
        
        Args:
            ft_file: FT檔案路徑
            eqc_file: EQC檔案路徑
            
        Returns:
            str: 新檔案路徑，失敗則返回None
        """
        try:
            # 生成輸出檔案名稱
            base_name = os.path.splitext(eqc_file)[0]
            output_file = f"{base_name}_EQCFAILDATA.csv"
            
            # 讀取EQC檔案找出失敗行
            with open(eqc_file, 'r', encoding='utf-8') as f:
                eqc_lines = f.readlines()
            
            # 收集失敗行的資料
            fail_rows = []
            header_lines = eqc_lines[:self.data_start_row]  # 前12行標頭
            
            for i in range(self.data_start_row, len(eqc_lines)):
                line = eqc_lines[i].strip()
                if not line:
                    break
                
                elements = line.split(',')
                if len(elements) < 2:
                    continue
                
                try:
                    bin_value = int(elements[1])
                    if bin_value != 1:  # 失敗行
                        fail_rows.append(elements)
                except (ValueError, IndexError):
                    continue
            
            if not fail_rows:
                return None
            
            # 寫入新檔案
            with open(output_file, 'w', encoding='utf-8', newline='') as f:
                # 寫入標頭
                for header_line in header_lines:
                    f.write(header_line)
                
                # 寫入失敗行
                csv_writer = csv.writer(f)
                for row in fail_rows:
                    csv_writer.writerow(row)
            
            return output_file
            
        except Exception as e:
            print(f"複製失敗行失敗: {e}")
            return None

    def find_online_eqc_bin1_datalog(self, eqc_files: List[str]) -> str:
        """
        從EQC檔案中找到第一個BIN=1的資料行
        對應VBA的FindOnlieEQCBin1datalog函數
        
        VBA邏輯：
        1. 遍歷EQC檔案列表
        2. 從第13行開始找第一個BIN=1的行
        3. 找到後立即返回：前12行標頭 + 處理過的BIN=1行
        4. 如果沒找到BIN=1，返回最後一個EQC檔案的前12行標頭
        
        Args:
            eqc_files: EQC檔案路徑列表
            
        Returns:
            str: 包含標頭和BIN=1行的完整內容，沒找到BIN=1則返回最後檔案的前12行標頭
        """
        try:
            last_valid_header = ""  # 儲存最後一個有效檔案的前12行標頭
            
            for eqc_file in eqc_files:
                if not os.path.exists(eqc_file):
                    continue
                
                with open(eqc_file, 'r', encoding='utf-8') as f:
                    file_contents = f.read()
                    lines = file_contents.split('\n')
                
                # 檢查檔案是否有足夠的行數
                if len(lines) <= 12:
                    continue
                
                # 儲存前12行標頭作為備用
                header_content = ""
                for kk in range(12):
                    if kk < len(lines):
                        header_content += lines[kk] + "\n"
                last_valid_header = header_content
                
                # [FIRE] 從第13行開始找BIN=1的行，但限制最大掃描行數
                MAX_SCAN_LINES = 10000
                max_scan_to = min(len(lines), 12 + MAX_SCAN_LINES)
                for i in range(12, max_scan_to):
                    line = lines[i].strip()
                    if len(line) < 1:  # 對應VBA: If Len(rowsInB(i)) < 1 Then Exit For
                        break
                    
                    elements = line.split(',')
                    if len(elements) < 2:
                        continue
                    
                    try:
                        bin_value = int(elements[1])
                        if bin_value == 1:  # 對應VBA: If val(lineElements(1)) = 1
                            # 找到BIN=1，返回：前12行標頭 + 處理過的BIN=1行
                            new_file_contents = header_content
                            
                            # 添加處理過的BIN=1行 (對應VBA AddFTToRowA)
                            # 修復：去掉逗號，避免欄位錯位
                            row_with_ft = self._add_ft_to_row(line, 0, "9876543210")
                            new_file_contents += row_with_ft + "\n"
                            
                            return new_file_contents  # 對應VBA: Exit Function
                            
                    except (ValueError, IndexError):
                        continue
            
            # 沒找到BIN=1，返回最後一個有效檔案的前12行標頭
            return last_valid_header
            
        except Exception as e:
            print(f"提取BIN=1資料失敗: {e}")
            return ""
    
    def _add_ft_to_row(self, target_row: str, column_index: int, replacement_text: str) -> str:
        """
        在指定欄位插入標記文字
        對應VBA的AddFTToRowA函數
        
        Args:
            target_row: 目標行
            column_index: 要替換的欄位索引
            replacement_text: 替換文字
            
        Returns:
            str: 處理後的行
        """
        columns = target_row.split(',')
        
        # 確保有足夠的欄位
        while len(columns) <= column_index:
            columns.append("")
        
        # 替換指定欄位
        columns[column_index] = replacement_text
        
        return ','.join(columns)
    
    def generate_bin1_excel_for_review(self, folder_path: str) -> Optional[str]:
        """
        生成BIN=1資料的Excel檔案供檢視
        
        Args:
            folder_path: 資料夾路徑
            
        Returns:
            str: Excel檔案路徑，失敗則返回None
        """
        try:
            # 先進行FT-EQC分組處理
            processor = FTEQCGroupingProcessor()
            result = processor.process_folder(folder_path)
            
            # 收集所有配對成功的EQC檔案
            eqc_files = []
            for ft_file, eqc_file in result.matched_pairs:
                eqc_files.append(eqc_file)
            
            if not eqc_files:
                print("沒有找到配對成功的EQC檔案")
                return None
            
            # 提取BIN=1資料
            bin1_data = self.find_online_eqc_bin1_datalog(eqc_files)
            if not bin1_data:
                print("[WARNING] 沒有找到BIN=1資料，將只包含失敗資料")
                # 繼續處理，因為VBA也是如此
            
            # 生成臨時CSV檔案
            import tempfile
            temp_csv = os.path.join(tempfile.gettempdir(), "eqc_bin1_data.csv")
            with open(temp_csv, 'w', encoding='utf-8') as f:
                f.write(bin1_data)
            
            # 直接使用openpyxl生成Excel
            excel_file = os.path.join(folder_path, "EQC_BIN1_REVIEW.xlsx")
            
            # 讀取CSV資料並轉換為Excel
            import csv
            try:
                from openpyxl import Workbook
                wb = Workbook()
                ws = wb.active
                ws.title = "BIN1_Data"
                
                # 讀取CSV並寫入Excel
                with open(temp_csv, 'r', encoding='utf-8') as csvfile:
                    csv_reader = csv.reader(csvfile)
                    for row_num, row in enumerate(csv_reader, 1):
                        for col_num, value in enumerate(row, 1):
                            ws.cell(row=row_num, column=col_num, value=value)
                
                wb.save(excel_file)
                
            except ImportError:
                # 如果沒有openpyxl，直接複製CSV檔案並重命名
                import shutil
                excel_file = os.path.join(folder_path, "EQC_BIN1_REVIEW.csv")
                shutil.copy2(temp_csv, excel_file)
            
            # 清理臨時檔案
            if os.path.exists(temp_csv):
                os.remove(temp_csv)
            
            print(f"[OK] BIN=1資料Excel檔案已生成: {excel_file}")
            return excel_file
            
        except Exception as e:
            print(f"生成BIN=1 Excel檔案失敗: {e}")
            return None
    
    def generate_eqc_total_data(self, folder_path: str) -> Optional[str]:
        """
        生成完整的EQCTOTALDATA.csv檔案
        結合BIN=1資料和FAIL資料
        對應VBA的total_line_string邏輯
        
        Args:
            folder_path: 資料夾路徑
            
        Returns:
            str: EQCTOTALDATA.csv檔案路徑，失敗則返回None
        """
        try:
            # 先進行FT-EQC分組處理
            processor = FTEQCGroupingProcessor()
            result = processor.process_folder(folder_path)
            
            # 收集所有配對成功的EQC檔案
            eqc_files = []
            for ft_file, eqc_file in result.matched_pairs:
                eqc_files.append(eqc_file)
            
            if not eqc_files:
                print("沒有找到配對成功的EQC檔案")
                return None
            
            # 步驟1: 提取BIN=1資料 (FindOnlieEQCBin1datalog)
            bin1_data = self.find_online_eqc_bin1_datalog(eqc_files)
            if not bin1_data:
                print("[WARNING] 沒有找到BIN=1資料，將只包含失敗資料")
                # 繼續處理，符合VBA邏輯：total_line_string = "" + FAIL資料
            
            # 步驟2: 提取失敗資料 (FindEQCFAILDATALOG - 已在find_online_eqc_fail_files中實作)
            fail_result = self.find_online_eqc_fail_files(result.matched_pairs)
            
            # 生成EQCTOTALDATA.csv
            total_data_file = os.path.join(folder_path, "EQCTOTALDATA.csv")
            
            with open(total_data_file, 'w', encoding='utf-8') as f:
                # 寫入BIN=1資料 (包含標頭)
                f.write(bin1_data)
                
                # 如果有失敗檔案，讀取並追加失敗資料
                if fail_result['fail_files']:
                    for fail_file in fail_result['analysis_files']:
                        if fail_file and os.path.exists(fail_file):
                            with open(fail_file, 'r', encoding='utf-8') as fail_f:
                                fail_lines = fail_f.readlines()
                                
                                # 跳過標頭，只加入失敗資料行
                                for i, line in enumerate(fail_lines):
                                    if i >= self.data_start_row:  # 跳過前12行標頭
                                        if line.strip():
                                            f.write(line)
            
            print(f"[OK] EQCTOTALDATA.csv已生成: {total_data_file}")
            print(f"[CHART] 統計: BIN=1資料 + {len(fail_result['fail_files'])}個失敗檔案的資料")
            
            return total_data_file
            
        except Exception as e:
            print(f"生成EQCTOTALDATA.csv失敗: {e}")
            return None
    
    def find_online_eqc_fail_count(self, total_data_file: str) -> int:
        """
        統計EQCTOTALDATA.csv中的Online EQC失敗數量
        對應VBA的FindOnlineEQCFailCnt函數
        
        Args:
            total_data_file: EQCTOTALDATA.csv檔案路徑
            
        Returns:
            int: 失敗行數量
        """
        try:
            if not os.path.exists(total_data_file):
                return 0
                
            with open(total_data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            fail_count = 0
            # 從第13行開始計算失敗行數 (跳過標頭12行 + BIN=1行)
            for i in range(13, len(lines)):
                line = lines[i].strip()
                if not line:
                    break
                    
                elements = line.split(',')
                if len(elements) >= 2:
                    try:
                        bin_value = int(elements[1])
                        if bin_value != 1:
                            fail_count += 1
                    except (ValueError, IndexError):
                        continue
            
            return fail_count
            
        except Exception as e:
            print(f"統計Online EQC失敗數量失敗: {e}")
            return 0
    
    def append_eqc_rt_data_to_total(self, folder_path: str, unmatched_eqc_files: List[str]) -> Optional[str]:
        """
        將EQC RT資料追加到EQCTOTALDATA.csv
        對應VBA的WriteCSVFilesToExcel函數
        
        Args:
            folder_path: 資料夾路徑
            unmatched_eqc_files: 未配對的EQC檔案列表 (EQC RT檔案)
            
        Returns:
            str: 更新後的EQCTOTALDATA.csv路徑，失敗則返回None
        """
        try:
            total_data_file = os.path.join(folder_path, "EQCTOTALDATA.csv")
            if not os.path.exists(total_data_file):
                print("EQCTOTALDATA.csv不存在")
                return None
            
            # 篩選有效的EQC RT檔案
            valid_eqc_rt_files = []
            for eqc_file in unmatched_eqc_files:
                if self._is_valid_eqc_rt_file(eqc_file):
                    valid_eqc_rt_files.append(eqc_file)
            
            if not valid_eqc_rt_files:
                print("沒有找到有效的EQC RT檔案")
                return total_data_file
            
            # 讀取現有的EQCTOTALDATA.csv
            with open(total_data_file, 'r', encoding='utf-8') as f:
                existing_lines = f.readlines()
            
            # 追加EQC RT資料
            with open(total_data_file, 'w', encoding='utf-8') as f:
                # 寫入原有內容
                for line in existing_lines:
                    f.write(line)
                
                # 追加EQC RT檔案的資料
                for eqc_rt_file in valid_eqc_rt_files:
                    try:
                        with open(eqc_rt_file, 'r', encoding='utf-8') as rt_f:
                            rt_lines = rt_f.readlines()
                        
                        # 跳過標頭，只加入資料行
                        for i, line in enumerate(rt_lines):
                            if i >= self.data_start_row:  # 跳過前12行標頭
                                if line.strip():
                                    f.write(line)
                    except Exception as e:
                        print(f"處理EQC RT檔案失敗 {os.path.basename(eqc_rt_file)}: {e}")
            
            print(f"[OK] 已追加{len(valid_eqc_rt_files)}個EQC RT檔案到EQCTOTALDATA.csv")
            return total_data_file
            
        except Exception as e:
            print(f"追加EQC RT資料失敗: {e}")
            return None
    
    def _is_valid_eqc_rt_file(self, file_path: str) -> bool:
        """
        檢查是否為有效的EQC RT檔案
        對應VBA的CheckEQCRTCSVFile函數
        """
        try:
            if not file_path or not os.path.exists(file_path):
                return False
            
            # 排[EXCEPT_CHAR]特定檔案
            filename = os.path.basename(file_path).lower()
            if any(excluded in filename for excluded in ['eqctotaldata', 'eqcfaildata', 'eqc_bin1_review']):
                return False
            
            # 檢查檔案內容
            with open(file_path, 'r', encoding='utf-8') as f:
                # 檢查前兩行是否包含(qc)標記
                for i in range(2):
                    try:
                        line = f.readline().lower()
                        if '(qc)' in line:
                            return True
                    except:
                        break
            
            return False
            
        except Exception:
            return False
    
    def add_statistics_to_total_data(self, total_data_file: str, online_eqc_fail_count: int) -> Optional[str]:
        """
        在EQCTOTALDATA.csv中添加統計資訊
        對應VBA的FindEQCRTPaSSCnt函數邏輯
        
        Args:
            total_data_file: EQCTOTALDATA.csv檔案路徑
            online_eqc_fail_count: Online EQC失敗數量
            
        Returns:
            str: 更新後的檔案路徑，失敗則返回None
        """
        try:
            if not os.path.exists(total_data_file):
                return None
            
            # 讀取檔案內容
            with open(total_data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 統計EQC RT通過數量
            eqc_rt_pass_count = 0
            data_start_index = 13  # 第13行開始是資料行
            MAX_SCAN_LINES = 10000  # [FIRE] 最大掃描行數限制
            
            max_scan_to = min(len(lines), data_start_index + MAX_SCAN_LINES)
            for i in range(data_start_index, max_scan_to):
                line = lines[i].strip()
                if not line:
                    break
                
                elements = line.split(',')
                if len(elements) >= 2:
                    try:
                        bin_value = int(elements[1])
                        if bin_value == 1:  # BIN=1表示通過
                            eqc_rt_pass_count += 1
                    except (ValueError, IndexError):
                        continue
            
            # 在第9行和第10行插入統計資訊
            statistics_lines = [
                f"OnlineEQC_Fail:,{online_eqc_fail_count}" + "," * (len(lines[7].split(',')) - 2) + "\n",
                f"EQC_RT_FINAL_PASS:,{eqc_rt_pass_count}" + "," * (len(lines[7].split(',')) - 2) + "\n"
            ]
            
            # 插入統計行
            updated_lines = lines[:8] + statistics_lines + lines[8:]
            
            # 寫回檔案
            with open(total_data_file, 'w', encoding='utf-8') as f:
                for line in updated_lines:
                    f.write(line)
            
            print(f"[OK] 已添加統計資訊到EQCTOTALDATA.csv")
            print(f"   - Online EQC失敗數: {online_eqc_fail_count}")
            print(f"   - EQC RT通過數: {eqc_rt_pass_count}")
            
            return total_data_file
            
        except Exception as e:
            print(f"添加統計資訊失敗: {e}")
            return None
    
    
    def _append_eqc_rt_data_sorted(self, folder_path: str, unmatched_eqc_files: List[str]) -> Optional[str]:
        """
        按時間排序並追加EQC RT資料到EQCTOTALDATA.csv
        修復時間排序問題
        """
        try:
            total_data_file = os.path.join(folder_path, "EQCTOTALDATA.csv")
            if not os.path.exists(total_data_file):
                print("EQCTOTALDATA.csv不存在")
                return None
            
            # 篩選並按時間排序EQC RT檔案
            valid_eqc_rt_files = []
            for eqc_file in unmatched_eqc_files:
                if self._is_valid_eqc_rt_file(eqc_file):
                    # 提取檔案內部時間戳
                    timestamp = self._extract_internal_timestamp(eqc_file)
                    valid_eqc_rt_files.append((eqc_file, timestamp))
            
            if not valid_eqc_rt_files:
                print("沒有找到有效的EQC RT檔案")
                return total_data_file
            
            # 按時間戳排序 (最晚到最近)
            valid_eqc_rt_files.sort(key=lambda x: x[1] if x[1] else 0, reverse=True)
            print(f"[REFRESH] 已按時間排序{len(valid_eqc_rt_files)}個EQC RT檔案")
            
            # 讀取現有的EQCTOTALDATA.csv
            with open(total_data_file, 'r', encoding='utf-8') as f:
                existing_lines = f.readlines()
            
            # 追加EQC RT資料
            with open(total_data_file, 'w', encoding='utf-8') as f:
                # 寫入原有內容
                for line in existing_lines:
                    f.write(line)
                
                # 追加排序後的EQC RT檔案資料
                for eqc_rt_file, _ in valid_eqc_rt_files:
                    try:
                        with open(eqc_rt_file, 'r', encoding='utf-8') as rt_f:
                            rt_lines = rt_f.readlines()
                        
                        # 跳過標頭，只加入資料行
                        for i, line in enumerate(rt_lines):
                            if i >= self.data_start_row:  # 跳過前12行標頭
                                if line.strip():
                                    f.write(line)
                    except Exception as e:
                        print(f"處理EQC RT檔案失敗 {os.path.basename(eqc_rt_file)}: {e}")
            
            print(f"[OK] 已按時間順序追加{len(valid_eqc_rt_files)}個EQC RT檔案到EQCTOTALDATA.csv")
            return total_data_file
            
        except Exception as e:
            print(f"時間排序追加EQC RT資料失敗: {e}")
            return None
    
    def _extract_internal_timestamp(self, file_path: str) -> Optional[int]:
        """提取檔案內部時間戳"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 檢查第6行 (Date行)
            if len(lines) > 5:
                date_line = lines[5]  # 第6行 (索引5)
                if "Date:" in date_line:
                    # 提取時間字串並轉換為時間戳
                    parts = date_line.split(',')
                    if len(parts) > 1:
                        date_str = parts[1].strip()
                        try:
                            # 修復2位年份解析問題 - 使用50年分界點
                            # 格式為 "05/22/25 18:44:13"
                            dt = datetime.strptime(date_str, "%m/%d/%y %H:%M:%S")
                            
                            # 修正年份邏輯：2位年份智能判斷
                            # 0-49 -> 2000-2049, 50-99 -> 1950-1999
                            if dt.year < 1970:  # Unix timestamp開始年份
                                # 如果解析出的年份小於1970，調整到正確的21世紀年份
                                corrected_year = dt.year + 100
                                dt = dt.replace(year=corrected_year)
                            
                            return int(dt.timestamp())
                        except ValueError:
                            # 如果日期格式解析失敗，嘗試其他常見格式
                            try:
                                dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                                return int(dt.timestamp())
                            except ValueError:
                                pass
                        except:
                            pass
            
            # 備用：使用檔案名稱時間戳
            filename = os.path.splitext(os.path.basename(file_path))[0]
            if len(filename) >= 14:  # 檢查是否有時間戳
                timestamp_str = filename[-14:]  # 最後14位數字
                if timestamp_str.isdigit():
                    return int(timestamp_str)
            
            return None
            
        except Exception:
            return None
    
    def _add_statistics_fixed(self, total_data_file: str, online_eqc_fail_count: int) -> Optional[str]:
        """
        修復統計資訊格式問題 - 保留EQC BIN=1資料格式結構
        替換第9行和第10行的空行，並從C欄位開始保留原始EQC BIN=1的資料格式
        """
        try:
            if not os.path.exists(total_data_file):
                return None
            
            # 讀取檔案內容
            with open(total_data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 統計EQC RT通過數量並尋找EQC BIN=1資料行
            eqc_rt_pass_count = 0
            bin1_data_format = ""
            data_start_index = 13  # 第13行開始是資料行
            
            for i in range(data_start_index, len(lines)):
                line = lines[i].strip()
                if not line:
                    break
                
                elements = line.split(',')
                if len(elements) >= 2:
                    try:
                        bin_value = int(elements[1])
                        if bin_value == 1:  # BIN=1表示通過
                            eqc_rt_pass_count += 1
                            # 儲存第一個BIN=1資料行的格式（從第3欄開始）
                            if not bin1_data_format and len(elements) > 2:
                                bin1_data_format = ','.join(elements[2:])
                    except (ValueError, IndexError):
                        continue
            
            # 確保有足夠的行數
            while len(lines) < 12:
                lines.append(",\n")
            
            # 如果沒有找到BIN=1資料格式，使用預設空格式
            if not bin1_data_format:
                # 取得第8行的欄位數量作為基準
                if len(lines) > 7:
                    base_columns = len(lines[7].split(','))
                else:
                    base_columns = 1000
                bin1_data_format = "," * (base_columns - 2)
            
            # 替換第9行 (索引8) - 保留EQC BIN=1資料格式
            if len(lines) > 8:
                lines[8] = f"OnlineEQC_Fail:,{online_eqc_fail_count},{bin1_data_format}\n"
            else:
                lines.append(f"OnlineEQC_Fail:,{online_eqc_fail_count},{bin1_data_format}\n")
            
            # 替換第10行 (索引9) - 保留EQC BIN=1資料格式
            if len(lines) > 9:
                lines[9] = f"EQC_RT_FINAL_PASS:,{eqc_rt_pass_count},{bin1_data_format}\n"
            else:
                lines.append(f"EQC_RT_FINAL_PASS:,{eqc_rt_pass_count},{bin1_data_format}\n")
            
            # 寫回檔案
            with open(total_data_file, 'w', encoding='utf-8') as f:
                for line in lines:
                    f.write(line)
            
            print(f"[OK] 已修復統計資訊格式到EQCTOTALDATA.csv（保留EQC BIN=1資料格式）")
            print(f"   - A9,B9: Online EQC失敗數: {online_eqc_fail_count}")
            print(f"   - A10,B10: EQC RT通過數: {eqc_rt_pass_count}")
            print(f"   - 格式保留: 從C欄位開始保留原始EQC BIN=1資料結構")
            
            return total_data_file
            
        except Exception as e:
            print(f"修復統計資訊格式失敗: {e}")
            return None
    
    def _add_hyperlinks_to_file(self, total_data_file: str, folder_path: str):
        """
        添加超連結到C14+欄位
        格式: \\************\temp_7days\[product]\[batch]\[folder]\[filename]
        """
        try:
            # 解析資料夾結構獲取產品和批號資訊
            folder_parts = folder_path.replace('\\', '/').split('/')
            product = "G2726"  # 預設產品名稱，可以從路徑解析
            batch = "20250523"  # 預設批號，可以從路徑解析
            
            # 嘗試從路徑解析實際的產品和批號
            for part in folder_parts:
                if part.startswith('G') and len(part) >= 5:
                    product = part
                elif part.isdigit() and len(part) == 8:
                    batch = part
            
            # 讀取檔案
            with open(total_data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 處理資料行，從第13行開始
            updated_lines = lines[:]
            hyperlink_count = 0
            
            for i in range(13, len(lines)):  # 從第13行開始
                line = lines[i].strip()
                if not line:
                    break
                
                elements = line.split(',')
                if len(elements) > 14:  # 確保有C欄位 (索引2，但我們需要14+)
                    # 在C14位置 (索引13) 添加超連結
                    if len(elements) > 13:
                        # 生成檔案名稱 (這裡需要根據實際檔案名稱邏輯調整)
                        filename = f"sample_file_{i-12}.csv"  # 簡化的檔案名稱
                        hyperlink = f"\\\\************\\temp_7days\\{product}\\{batch}\\ALL CSV\\{filename}"
                        elements[13] = hyperlink  # C14欄位
                        
                        updated_lines[i] = ','.join(elements) + '\n'
                        hyperlink_count += 1
            
            # 寫回檔案
            with open(total_data_file, 'w', encoding='utf-8') as f:
                for line in updated_lines:
                    f.write(line)
            
            print(f"[OK] 已添加{hyperlink_count}個超連結到C14+欄位")
            
        except Exception as e:
            print(f"添加超連結失敗: {e}")

    def _show_final_statistics(self, raw_file: str):
        """顯示最終處理統計"""
        try:
            with open(raw_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            file_size = os.path.getsize(raw_file)
            
            # 提取統計資訊
            online_eqc_fail = "N/A"
            eqc_rt_pass = "N/A"
            
            if len(lines) > 9:
                line_9 = lines[8].split(',')  # 第9行 (索引8)
                if len(line_9) >= 2:
                    online_eqc_fail = line_9[1]
                    
            if len(lines) > 10:
                line_10 = lines[9].split(',')  # 第10行 (索引9)
                if len(line_10) >= 2:
                    eqc_rt_pass = line_10[1]
            
            # 統計資料行
            total_data_lines = len(lines) - 12  # 扣[EXCEPT_CHAR]12行標頭
            
            print(f"\n[CHART] EQCTOTALDATA_RAW.csv 最終統計:")
            print(f"   [FOLDER] 檔案大小: {file_size:,} bytes")
            print(f"   [BOARD] 總行數: {len(lines)}")
            print(f"   [STATS] 資料行數: {total_data_lines}")
            print(f"   [ERROR] Online EQC失敗數 (A9,B9): {online_eqc_fail}")
            print(f"   [OK] EQC RT通過數 (A10,B10): {eqc_rt_pass}")
            
        except Exception as e:
            print(f"顯示統計失敗: {e}")


class FTEQCGroupingProcessor:
    """FT-EQC 分組處理器 - 主要入口類別"""
    
    def __init__(self):
        self.file_discovery = CSVFileDiscovery()
        self.time_matcher = TimeBasedMatcher()
        self.cta_handler = CTAFormatHandler()
        self.fail_processor = OnlineEQCFailProcessor()
    
    def process_folder(self, folder_path: str) -> GroupingResult:
        """處理整個資料夾 - 主要入口方法"""
        if not os.path.exists(folder_path):
            raise FileNotFoundError(f"資料夾不存在: {folder_path}")
        
        # 步驟1: 遞迴發現所有 CSV 檔案
        csv_files = self.discover_csv_files(folder_path)
        
        # 步驟2: 分類 FT 和 EQC 檔案
        ft_files = self.classify_ft_files(csv_files)
        eqc_files = self.classify_eqc_files(csv_files)
        
        # 步驟3: 執行時間戳配對
        matched_pairs = self.time_matcher.match_files(ft_files, eqc_files)
        
        # 步驟4: 找出未配對的 EQC 檔案 (EQC RT)
        unmatched_eqc = self._find_unmatched_eqc(eqc_files, matched_pairs)
        
        # 步驟5: 處理 Online EQC 失敗檔案
        eqc_fail_result = self.fail_processor.find_online_eqc_fail_files(matched_pairs)
        
        # 步驟6: 生成統計資料
        statistics = self._generate_statistics(csv_files, ft_files, eqc_files, matched_pairs, unmatched_eqc)
        
        return GroupingResult(
            matched_pairs=matched_pairs,
            unmatched_eqc=unmatched_eqc,
            statistics=statistics,
            eqc_fail_result=eqc_fail_result
        )
    
    def discover_csv_files(self, folder_path: str) -> List[str]:
        """發現 CSV 檔案"""
        return self.file_discovery.find_all_csv_files(folder_path)
    
    def classify_ft_files(self, csv_files: List[str]) -> List[str]:
        """分類 FT 檔案"""
        return self.file_discovery.classify_ft_files(csv_files)
    
    def classify_eqc_files(self, csv_files: List[str]) -> List[str]:
        """分類 EQC 檔案"""
        return self.file_discovery.classify_eqc_files(csv_files)
    
    def match_by_filename_timestamp(self, ft_files: List[str], eqc_files: List[str]) -> List[Tuple[str, str]]:
        """基於檔案名稱時間戳配對"""
        return self.time_matcher.match_files(ft_files, eqc_files)
    
    def match_by_modification_time(self, ft_files: List[str], eqc_files: List[str]) -> List[Tuple[str, str]]:
        """基於檔案修改時間配對"""
        return self.time_matcher.match_files(ft_files, eqc_files)
    
    def detect_cta_format(self, file_path: str) -> Dict[str, Any]:
        """檢測 CTA 格式"""
        return self.cta_handler.detect_cta_format(file_path)
    
    def _find_unmatched_eqc(self, eqc_files: List[str], 
                           matched_pairs: List[Tuple[str, str]]) -> List[str]:
        """找出未配對的 EQC 檔案"""
        matched_eqc_files = {pair[1] for pair in matched_pairs}
        return [eqc_file for eqc_file in eqc_files if eqc_file not in matched_eqc_files]
    
    def _generate_statistics(self, csv_files: List[str], ft_files: List[str], 
                           eqc_files: List[str], matched_pairs: List[Tuple[str, str]],
                           unmatched_eqc: List[str]) -> Dict[str, Any]:
        """生成統計資料"""
        return {
            'total_csv_files': len(csv_files),
            'ft_files_count': len(ft_files),
            'eqc_files_count': len(eqc_files),
            'successful_matches': len(matched_pairs),
            'eqc_rt_count': len(unmatched_eqc),
            'matching_rate': len(matched_pairs) / len(eqc_files) if eqc_files else 0.0,
            'processing_timestamp': datetime.now().isoformat()
        }


# 便捷函數
def process_ft_eqc_grouping(folder_path: str) -> GroupingResult:
    """便捷函數：處理 FT-EQC 分組"""
    processor = FTEQCGroupingProcessor()
    return processor.process_folder(folder_path)


if __name__ == "__main__":
    # 測試用例
    import sys
    
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
        print(f"[REFRESH] 處理資料夾: {folder_path}")
        
        try:
            result = process_ft_eqc_grouping(folder_path)
            
            print(f"\n[CHART] 處理結果:")
            print(f"  [FOLDER] 總 CSV 檔案數: {result.statistics['total_csv_files']}")
            print(f"  [TOOL] FT 檔案數: {result.statistics['ft_files_count']}")
            print(f"  [BOARD] EQC 檔案數: {result.statistics['eqc_files_count']}")
            print(f"  [OK] 成功配對數: {result.statistics['successful_matches']}")
            print(f"  [FILE] EQC RT 檔案數: {result.statistics['eqc_rt_count']}")
            print(f"  [STATS] 配對成功率: {result.statistics['matching_rate']:.2%}")
            
            print(f"\n[LINK] 配對結果:")
            for i, (ft_file, eqc_file) in enumerate(result.matched_pairs, 1):
                print(f"  {i}. {os.path.basename(ft_file)} [LEFT_RIGHT_ARROW] {os.path.basename(eqc_file)}")
            
            if result.unmatched_eqc:
                print(f"\n[BOARD] 未配對的 EQC RT 檔案:")
                for i, eqc_file in enumerate(result.unmatched_eqc, 1):
                    print(f"  {i}. {os.path.basename(eqc_file)}")
                    
        except Exception as e:
            print(f"[ERROR] 處理失敗: {e}")
    else:
        print("使用方式: python ft_eqc_grouping_processor.py <folder_path>")