"""
測試 Dramatiq 管道系統
用於驗證管道語法和功能是否正確實作
"""

import os
import asyncio
import sys
from pathlib import Path

# 添加項目根目錄到路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 設置環境變數
os.environ.setdefault('USE_VENDOR_PIPELINE', 'true')
os.environ.setdefault('PIPELINE_INCLUDE_CODE_COMPARISON', 'true')
os.environ.setdefault('FILE_TEMP_BASE_PATH', 'D:\\temp')
os.environ.setdefault('FILE_SOURCE_BASE_PATH', '/mnt/share')

from loguru import logger


def test_pipeline_imports():
    """測試管道相關模組的導入"""
    logger.info("🧪 測試管道模組導入...")
    
    try:
        # 測試管道任務導入
        from backend.tasks.pipeline_tasks import (
            process_vendor_files_task,
            create_vendor_processing_pipeline,
            create_full_processing_pipeline
        )
        logger.success("✅ 管道任務模組導入成功")
        
        # 測試管道工具導入
        from backend.tasks.pipeline_utils import (
            get_pipeline_manager,
            PipelineStatus,
            PipelineManager
        )
        logger.success("✅ 管道工具模組導入成功")
        
        # 測試 Dramatiq 任務導入
        from backend.tasks.services.dramatiq_tasks import run_code_comparison_task
        logger.success("✅ Dramatiq 任務導入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模組導入失敗: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


def test_pipeline_manager():
    """測試管道管理器功能"""
    logger.info("🧪 測試管道管理器...")
    
    try:
        from backend.tasks.pipeline_utils import get_pipeline_manager, PipelineStatus
        
        # 獲取管道管理器
        manager = get_pipeline_manager()
        logger.info(f"管道管理器類型: {type(manager)}")
        
        # 創建測試管道上下文
        pipeline_id = manager.create_pipeline_context(
            pipeline_type="test_pipeline",
            metadata={'test': True}
        )
        logger.success(f"✅ 創建管道上下文成功: {pipeline_id}")
        
        # 獲取管道上下文
        context = manager.get_pipeline_context(pipeline_id)
        if context:
            logger.success(f"✅ 獲取管道上下文成功: {context.pipeline_type}")
        else:
            logger.warning("⚠️ 無法獲取管道上下文 (Redis 可能不可用)")
        
        # 更新管道狀態
        success = manager.update_pipeline_context(
            pipeline_id,
            status=PipelineStatus.RUNNING,
            task_count=5
        )
        if success:
            logger.success("✅ 更新管道狀態成功")
        else:
            logger.warning("⚠️ 更新管道狀態失敗 (Redis 可能不可用)")
        
        # 獲取管道狀態
        status = manager.get_pipeline_status(pipeline_id)
        logger.info(f"管道狀態: {status}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 管道管理器測試失敗: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


def test_create_pipeline():
    """測試創建管道功能"""
    logger.info("🧪 測試創建管道...")
    
    try:
        from backend.tasks.pipeline_tasks import create_vendor_processing_pipeline
        
        # 準備測試數據
        vendor_files = [
            {
                'vendor_code': 'TEST_VENDOR',
                'mo': 'MO12345',
                'temp_path': 'D:\\temp\\test\\MO12345',
                'pd': 'TEST_PRODUCT',
                'lot': 'LOT001',
                'email_subject': 'Test Subject',
                'email_body': 'Test Body'
            }
        ]
        
        # 創建管道
        pipeline_id = create_vendor_processing_pipeline(
            vendor_files=vendor_files,
            pipeline_context={'test_mode': True}
        )
        
        logger.success(f"✅ 創建廠商處理管道成功: {pipeline_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 創建管道測試失敗: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


def test_full_pipeline():
    """測試完整管道功能"""
    logger.info("🧪 測試完整管道...")
    
    try:
        from backend.tasks.pipeline_tasks import create_full_processing_pipeline
        
        # 準備測試數據
        vendor_files = [
            {
                'vendor_code': 'TEST_VENDOR',
                'mo': 'MO12345',
                'temp_path': 'D:\\temp\\test\\MO12345',
                'pd': 'TEST_PRODUCT',
                'lot': 'LOT001',
                'email_subject': 'Test Subject',
                'email_body': 'Test Body'
            }
        ]
        
        # 創建完整管道（包含代碼比較）
        pipeline_id = create_full_processing_pipeline(
            vendor_files=vendor_files,
            include_code_comparison=True,
            pipeline_context={'test_mode': True}
        )
        
        logger.success(f"✅ 創建完整處理管道成功: {pipeline_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 完整管道測試失敗: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def test_unified_email_processor():
    """測試統一郵件處理器的管道功能"""
    logger.info("🧪 測試統一郵件處理器管道功能...")
    
    try:
        from backend.shared.application.services.unified_email_processor import UnifiedEmailProcessor
        from backend.email.models.email_models import EmailData
        from backend.email.parsers.base_parser import EmailParsingResult
        from datetime import datetime
        
        # 初始化處理器
        processor = UnifiedEmailProcessor()
        logger.info(f"管道模式: {'啟用' if processor.use_pipeline else '禁用'}")
        
        # 創建測試郵件數據
        email_data = EmailData(
            message_id="test-message-id",
            subject="Test Email Subject",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now(),
            attachments=[]
        )
        
        # 創建測試解析結果
        parsing_result = EmailParsingResult(
            is_success=True,
            vendor_code="TEST_VENDOR",
            product_code="TEST_PRODUCT",
            mo_number="MO12345",
            lot_number="LOT001",
            extraction_method="test",
            extracted_data={}
        )
        
        # 測試管道觸發
        if processor.use_pipeline:
            result = await processor._trigger_full_pipeline(
                email_data, parsing_result, "test-email-id"
            )
            logger.info(f"管道觸發結果: {result}")
            
            if result.get('pipeline_triggered'):
                logger.success(f"✅ 管道觸發成功: {result['pipeline_id']}")
                
                # 測試狀態查詢
                status = processor.get_pipeline_status(result['pipeline_id'])
                logger.info(f"管道狀態: {status}")
                
            else:
                logger.warning("⚠️ 管道未觸發")
        else:
            logger.info("ℹ️ 管道模式未啟用，跳過測試")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 統一郵件處理器測試失敗: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


def test_dramatiq_pipeline_syntax():
    """測試 Dramatiq pipeline 語法"""
    logger.info("🧪 測試 Dramatiq pipeline 語法...")
    
    try:
        import dramatiq
        from dramatiq import pipeline, actor
        
        # 測試創建簡單的 pipeline
        from backend.tasks.pipeline_tasks import process_vendor_files_task
        from backend.tasks.services.dramatiq_tasks import run_code_comparison_task
        
        # 創建任務消息
        vendor_task = process_vendor_files_task.message(
            vendor_code="TEST",
            mo="MO001",
            temp_path="D:\\temp\\test",
            pd="TEST_PD",
            lot="LOT001"
        )
        
        code_task = run_code_comparison_task.message()
        
        # 測試 pipeline 語法
        pipe = pipeline([
            vendor_task,
            code_task  # 空參數，自動接收上一個任務結果
        ])
        
        logger.success("✅ Dramatiq pipeline 語法測試成功")
        logger.info(f"Pipeline 類型: {type(pipe)}")
        
        # 注意：這裡不實際運行 pipeline，只測試語法
        # pipe.run()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Dramatiq pipeline 語法測試失敗: {e}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def main():
    """主測試函數"""
    logger.info("🚀 開始 Dramatiq 管道系統測試")
    logger.info("=" * 50)
    
    tests = [
        ("模組導入", test_pipeline_imports),
        ("管道管理器", test_pipeline_manager),
        ("創建管道", test_create_pipeline),
        ("完整管道", test_full_pipeline),
        ("Dramatiq 語法", test_dramatiq_pipeline_syntax),
        ("郵件處理器", test_unified_email_processor),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 執行測試: {test_name}")
        logger.info("-" * 30)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                success = await test_func()
            else:
                success = test_func()
            
            results[test_name] = success
            
            if success:
                logger.success(f"✅ {test_name} 測試通過")
            else:
                logger.error(f"❌ {test_name} 測試失敗")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 測試異常: {e}")
            results[test_name] = False
    
    # 測試總結
    logger.info("\n" + "=" * 50)
    logger.info("📊 測試結果總結:")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通過" if success else "❌ 失敗"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed == total:
        logger.success("🎉 所有測試都通過了！管道系統實作成功！")
        return True
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個測試失敗，請檢查相關配置")
        return False


if __name__ == "__main__":
    # 運行測試
    success = asyncio.run(main())
    
    # 退出代碼
    exit_code = 0 if success else 1
    logger.info(f"\n退出代碼: {exit_code}")
    sys.exit(exit_code)