#!/usr/bin/env python3
"""
最小化測試
"""

print("開始測試...")

try:
    print("1. 測試檔案鎖定管理器導入...")
    from backend.shared.infrastructure.adapters.filesystem.file_lock_manager import get_file_lock_manager
    print("✅ 檔案鎖定管理器導入成功")
    
    print("2. 測試檔案鎖定管理器創建...")
    manager = get_file_lock_manager()
    print("✅ 檔案鎖定管理器創建成功")
    
    print("3. 測試獲取活躍鎖定...")
    locks = manager.get_active_locks()
    print(f"✅ 獲取活躍鎖定成功: {locks}")
    
except Exception as e:
    print(f"❌ 錯誤: {e}")
    import traceback
    traceback.print_exc()

print("測試完成")
