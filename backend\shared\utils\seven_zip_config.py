#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
7zip 配置管理模組
處理 7zip 路徑的設定、測試和儲存
"""

import os
import json
import subprocess
from pathlib import Path
from typing import Tuple, Optional
from loguru import logger


class SevenZipConfig:
    """7zip 配置管理器"""
    
    def __init__(self, config_file: str = "config/7zip_config.json"):
        """
        初始化 7zip 配置管理器
        
        Args:
            config_file: 配置文件路徑
        """
        self.config_file = Path(config_file)
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 預設 7zip 路徑
        self.default_paths = [
            r"C:\Program Files\7-Zip",
            r"C:\Program Files (x86)\7-Zip",
            r"D:\Program Files\7-Zip",
            r"D:\Program Files (x86)\7-Zip"
        ]
    
    def get_7zip_path(self) -> str:
        """
        獲取當前配置的 7zip 路徑
        
        Returns:
            str: 7zip 路徑
        """
        try:
            # 首先嘗試從配置文件讀取
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    saved_path = config.get('7zip_path', '')
                    if saved_path and os.path.exists(saved_path):
                        return saved_path
            
            # 如果配置文件不存在或路徑無效，嘗試預設路徑
            for default_path in self.default_paths:
                if os.path.exists(default_path):
                    # 自動儲存找到的路徑
                    self.save_7zip_path(default_path)
                    return default_path
            
            # 如果都找不到，返回第一個預設路徑
            return self.default_paths[0]
            
        except Exception as e:
            logger.error(f"獲取 7zip 路徑失敗: {e}")
            return self.default_paths[0]
    
    def test_7zip_path(self, path: str) -> Tuple[bool, str]:
        """
        測試 7zip 路徑是否有效
        
        Args:
            path: 要測試的路徑
            
        Returns:
            Tuple[bool, str]: (是否有效, 訊息)
        """
        try:
            # 檢查路徑是否存在
            if not os.path.exists(path):
                return False, f"路徑不存在: {path}"
            
            # 檢查是否為目錄
            if not os.path.isdir(path):
                return False, f"路徑不是目錄: {path}"
            
            # 檢查 7z.exe 是否存在
            seven_zip_exe = os.path.join(path, "7z.exe")
            if not os.path.exists(seven_zip_exe):
                return False, f"未找到 7z.exe: {seven_zip_exe}"
            
            # 測試 7z.exe 是否可執行
            try:
                result = subprocess.run(
                    [seven_zip_exe], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                
                # 7z.exe 在沒有參數時會返回非零退出碼，但這是正常的
                if "7-Zip" in result.stdout or "7-Zip" in result.stderr:
                    return True, f"7zip 測試成功: {seven_zip_exe}"
                else:
                    return False, f"7z.exe 執行異常: {seven_zip_exe}"
                    
            except subprocess.TimeoutExpired:
                return False, f"7z.exe 執行超時: {seven_zip_exe}"
            except Exception as e:
                return False, f"7z.exe 執行失敗: {e}"
                
        except Exception as e:
            logger.error(f"測試 7zip 路徑異常: {e}")
            return False, f"測試失敗: {e}"
    
    def save_7zip_path(self, path: str) -> Tuple[bool, str]:
        """
        儲存 7zip 路徑配置
        
        Args:
            path: 要儲存的路徑
            
        Returns:
            Tuple[bool, str]: (是否成功, 訊息)
        """
        try:
            # 先測試路徑是否有效
            is_valid, test_message = self.test_7zip_path(path)
            if not is_valid:
                return False, f"路徑無效: {test_message}"
            
            # 準備配置數據
            config = {}
            
            # 如果配置文件已存在，先讀取現有配置
            if self.config_file.exists():
                try:
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                except Exception as e:
                    logger.warning(f"讀取現有配置失敗，將創建新配置: {e}")
                    config = {}
            
            # 更新 7zip 路徑
            config['7zip_path'] = path
            config['last_updated'] = str(Path().cwd())  # 記錄更新時間
            
            # 儲存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"7zip 路徑配置已儲存: {path}")
            return True, f"7zip 路徑儲存成功: {path}"
            
        except Exception as e:
            logger.error(f"儲存 7zip 路徑失敗: {e}")
            return False, f"儲存失敗: {e}"
    
    def get_7zip_executable(self) -> str:
        """
        獲取 7z.exe 的完整路徑
        
        Returns:
            str: 7z.exe 的完整路徑
        """
        seven_zip_dir = self.get_7zip_path()
        return os.path.join(seven_zip_dir, "7z.exe")
    
    def is_7zip_available(self) -> bool:
        """
        檢查 7zip 是否可用
        
        Returns:
            bool: 是否可用
        """
        try:
            seven_zip_exe = self.get_7zip_executable()
            return os.path.exists(seven_zip_exe)
        except Exception:
            return False
    
    def auto_detect_7zip(self) -> Optional[str]:
        """
        自動檢測 7zip 安裝路徑
        
        Returns:
            Optional[str]: 檢測到的路徑，如果沒找到則返回 None
        """
        try:
            # 檢查預設路徑
            for path in self.default_paths:
                is_valid, _ = self.test_7zip_path(path)
                if is_valid:
                    logger.info(f"自動檢測到 7zip: {path}")
                    return path
            
            # 檢查系統 PATH
            try:
                result = subprocess.run(
                    ["where", "7z"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                
                if result.returncode == 0 and result.stdout.strip():
                    seven_zip_exe = result.stdout.strip().split('\n')[0]
                    seven_zip_dir = os.path.dirname(seven_zip_exe)
                    is_valid, _ = self.test_7zip_path(seven_zip_dir)
                    if is_valid:
                        logger.info(f"從 PATH 檢測到 7zip: {seven_zip_dir}")
                        return seven_zip_dir
                        
            except Exception as e:
                logger.debug(f"PATH 檢測失敗: {e}")
            
            logger.warning("未能自動檢測到 7zip 安裝路徑")
            return None
            
        except Exception as e:
            logger.error(f"自動檢測 7zip 失敗: {e}")
            return None
    
    def get_config_info(self) -> dict:
        """
        獲取配置資訊
        
        Returns:
            dict: 配置資訊
        """
        try:
            current_path = self.get_7zip_path()
            is_available = self.is_7zip_available()
            
            info = {
                "current_path": current_path,
                "is_available": is_available,
                "config_file": str(self.config_file),
                "config_exists": self.config_file.exists(),
                "default_paths": self.default_paths
            }
            
            if is_available:
                is_valid, message = self.test_7zip_path(current_path)
                info["test_result"] = {
                    "is_valid": is_valid,
                    "message": message
                }
            
            return info
            
        except Exception as e:
            logger.error(f"獲取配置資訊失敗: {e}")
            return {
                "error": str(e),
                "current_path": "",
                "is_available": False
            }


# 全局實例
seven_zip_config = SevenZipConfig()


def get_7zip_executable() -> str:
    """
    獲取 7z.exe 的完整路徑（便捷函數）
    
    Returns:
        str: 7z.exe 的完整路徑
    """
    return seven_zip_config.get_7zip_executable()


def is_7zip_available() -> bool:
    """
    檢查 7zip 是否可用（便捷函數）
    
    Returns:
        bool: 是否可用
    """
    return seven_zip_config.is_7zip_available()


def test_7zip_installation() -> dict:
    """
    測試 7zip 安裝狀態（便捷函數）
    
    Returns:
        dict: 測試結果
    """
    config_info = seven_zip_config.get_config_info()
    
    # 如果當前路徑不可用，嘗試自動檢測
    if not config_info.get("is_available", False):
        auto_detected = seven_zip_config.auto_detect_7zip()
        if auto_detected:
            config_info["auto_detected_path"] = auto_detected
            config_info["suggestion"] = f"建議設定路徑為: {auto_detected}"
    
    return config_info
