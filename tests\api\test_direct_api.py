#!/usr/bin/env python3
"""
直接測試 API 函數
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_file_locks_function():
    """直接測試檔案鎖定函數"""
    print("🔒 直接測試檔案鎖定函數...")
    
    try:
        from backend.shared.infrastructure.adapters.filesystem.file_lock_manager import get_file_lock_manager
        file_lock_manager = get_file_lock_manager()
        
        active_locks = file_lock_manager.get_active_locks()
        
        result = {
            "status": "success",
            "locks": active_locks,
            "total_locks": len(active_locks),
            "timestamp": "test"
        }
        
        print(f"✅ 成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_concurrent_status_function():
    """直接測試並發狀態函數"""
    print("📊 直接測試並發狀態函數...")
    
    try:
        from backend.eqc.services.eqc_session_manager import get_eqc_session_manager
        from backend.shared.infrastructure.adapters.filesystem.file_lock_manager import get_file_lock_manager
        
        session_manager = get_eqc_session_manager()
        file_lock_manager = get_file_lock_manager()
        
        # 獲取會話統計
        session_stats = session_manager.get_session_stats()
        
        # 獲取檔案鎖定信息
        active_locks = file_lock_manager.get_active_locks()
        
        # 計算並發指標
        concurrent_users = len(set(lock['user_id'] for lock in active_locks.values()))
        
        result = {
            "status": "success",
            "concurrent_status": {
                "active_sessions": session_stats["active_sessions"],
                "processing_sessions": session_stats["processing_sessions"],
                "concurrent_users": concurrent_users,
                "file_locks": len(active_locks),
                "system_load": "normal"
            },
            "session_stats": session_stats,
            "file_locks": active_locks,
            "timestamp": "test"
        }
        
        print(f"✅ 成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_functions():
    """測試 API 函數"""
    print("🧪 直接測試 API 函數")
    print("=" * 40)
    
    tests = [
        ("檔案鎖定函數", test_file_locks_function),
        ("並發狀態函數", test_concurrent_status_function),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
                
        except Exception as e:
            print(f"💥 {test_name} 異常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 40)
    print("📊 測試結果總結")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 總計: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有函數測試通過！")
        return True
    else:
        print("⚠️ 部分函數測試失敗")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_api_functions())
    sys.exit(0 if success else 1)
