#!/usr/bin/env python3
"""
啟動 csv_to_summary.py 的簡單腳本
用於 FT-Summary UI 功能
"""

import sys
import subprocess
import os
from pathlib import Path

def main():
    """啟動 csv_to_summary.py"""
    try:
        # 獲取專案根目錄
        project_root = Path(__file__).parent
        
        # csv_to_summary.py 的路徑
        csv_to_summary_path = project_root / "csv_to_summary.py"
        
        if not csv_to_summary_path.exists():
            print(f"❌ 找不到 csv_to_summary.py 檔案: {csv_to_summary_path}")
            return 1
        
        print(f"🚀 啟動 FT-Summary 處理工具...")
        print(f"📁 檔案路徑: {csv_to_summary_path}")
        
        # 啟動 csv_to_summary.py
        result = subprocess.run([
            sys.executable, 
            str(csv_to_summary_path)
        ], cwd=str(project_root))
        
        return result.returncode
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
