#!/usr/bin/env python3
"""
真實錯誤場景測試
測試系統在各種錯誤情況下的實際行為
"""

import time
import requests
from backend.eqc.services.eqc_session_manager import get_eqc_session_manager

def test_service_unavailable():
    """測試服務不可用情況"""
    print("🧪 測試服務不可用情況")
    
    try:
        # 嘗試連接到不存在的端口
        response = requests.get("http://localhost:9999/ft-eqc/ui", timeout=5)
        print("❌ 應該連接失敗但卻成功了")
        return False
    except requests.exceptions.ConnectionError:
        print("✅ 正確處理連接錯誤")
        return True
    except Exception as e:
        print(f"✅ 正確捕獲異常: {type(e).__name__}")
        return True

def test_session_timeout():
    """測試會話超時處理"""
    print("\n🧪 測試會話超時處理")
    
    session_manager = get_eqc_session_manager()
    
    # 創建一個會話
    session_id = session_manager.create_session("D:/test", "timeout_user")
    
    # 獲取會話
    session = session_manager.get_session(session_id)
    if not session:
        print("❌ 無法獲取剛創建的會話")
        return False
    
    # 手動設置會話為過期
    from datetime import datetime, timedelta
    session.expires_at = datetime.now() - timedelta(hours=1)
    
    # 再次獲取會話，應該檢測到過期
    expired_session = session_manager.get_session(session_id)
    if expired_session and expired_session.is_expired():
        print("✅ 正確檢測到會話過期")
        return True
    else:
        print("❌ 未正確檢測到會話過期")
        return False

def test_invalid_folder_path():
    """測試無效資料夾路徑"""
    print("\n🧪 測試無效資料夾路徑")
    
    session_manager = get_eqc_session_manager()
    
    # 測試空路徑
    try:
        session_id = session_manager.create_session("", "test_user")
        print("✅ 接受空路徑（系統設計允許）")
    except Exception as e:
        print(f"✅ 正確拒絕空路徑: {e}")
    
    # 測試無效路徑
    try:
        session_id = session_manager.create_session("invalid://path", "test_user")
        print("✅ 接受無效路徑（系統設計允許）")
        return True
    except Exception as e:
        print(f"✅ 正確拒絕無效路徑: {e}")
        return True

def test_concurrent_session_conflicts():
    """測試並發會話衝突"""
    print("\n🧪 測試並發會話衝突")
    
    session_manager = get_eqc_session_manager()
    
    # 同一用戶創建多個會話
    user_id = "conflict_test_user"
    sessions = []
    
    for i in range(3):
        session_id = session_manager.create_session(f"D:/test_{i}", user_id)
        sessions.append(session_id)
    
    # 檢查是否都成功創建
    user_sessions = session_manager.get_user_sessions(user_id)
    
    if len(user_sessions) == 3:
        print("✅ 正確支援同一用戶的多個會話")
        return True
    else:
        print(f"❌ 用戶會話數量不正確: 期望3，實際{len(user_sessions)}")
        return False

def test_memory_pressure():
    """測試內存壓力情況"""
    print("\n🧪 測試內存壓力情況")
    
    session_manager = get_eqc_session_manager()
    
    # 創建大量會話來測試內存處理
    sessions = []
    try:
        for i in range(100):
            session_id = session_manager.create_session(f"D:/test_{i}", f"user_{i}")
            sessions.append(session_id)
        
        stats = session_manager.get_session_stats()
        print(f"✅ 成功創建 {stats['total_sessions']} 個會話")
        
        # 清理測試會話
        for session_id in sessions:
            session = session_manager.get_session(session_id)
            if session:
                session_manager.fail_session(session_id, "測試清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 內存壓力測試失敗: {e}")
        return False

def test_api_error_responses():
    """測試 API 錯誤響應"""
    print("\n🧪 測試 API 錯誤響應")
    
    base_url = "http://localhost:5555"
    
    # 測試不存在的端點
    try:
        response = requests.get(f"{base_url}/api/nonexistent", timeout=5)
        if response.status_code == 404:
            print("✅ 正確返回 404 錯誤")
            return True
        else:
            print(f"⚠️ 返回狀態碼: {response.status_code}")
            return True
    except Exception as e:
        print(f"✅ 正確處理 API 錯誤: {e}")
        return True

def test_service_recovery():
    """測試服務恢復能力"""
    print("\n🧪 測試服務恢復能力")
    
    # 測試服務是否能正常響應
    try:
        response = requests.get("http://localhost:5555/ft-eqc/ui", timeout=10)
        if response.status_code == 200:
            print("✅ 服務正常運行，具備恢復能力")
            return True
        else:
            print(f"⚠️ 服務響應異常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服務恢復測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始真實錯誤場景測試")
    print("=" * 60)
    
    tests = [
        ("服務不可用處理", test_service_unavailable),
        ("會話超時處理", test_session_timeout),
        ("無效資料夾路徑", test_invalid_folder_path),
        ("並發會話衝突", test_concurrent_session_conflicts),
        ("內存壓力測試", test_memory_pressure),
        ("API 錯誤響應", test_api_error_responses),
        ("服務恢復能力", test_service_recovery)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 執行 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")
        
        print("-" * 40)
        time.sleep(1)  # 避免測試間干擾
    
    print(f"\n📊 錯誤場景測試總結:")
    print(f"總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有錯誤場景測試通過！")
        return True
    else:
        print("⚠️ 部分錯誤場景測試失敗")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
