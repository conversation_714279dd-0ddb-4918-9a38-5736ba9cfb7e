#!/usr/bin/env python3
"""
內存模式多人多工測試
雖然不是真正的並發，但可以測試會話隔離和檔案鎖定機制
"""

import os
import sys
import time
from pathlib import Path

# 確保使用內存模式
os.environ['USE_MEMORY_BROKER'] = 'true'

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_session_isolation():
    """測試會話隔離機制"""
    print("🔒 測試會話隔離機制")
    print("=" * 50)
    
    try:
        from backend.eqc.services.eqc_session_manager import get_eqc_session_manager
        
        session_manager = get_eqc_session_manager()
        
        # 創建多個用戶會話
        users = [
            ("user_1", "D:\\project\\python\\outlook_summary\\doc\\20250523"),
            ("user_2", "D:\\project\\python\\outlook_summary\\doc\\202505231"),
            ("user_3", "D:\\project\\python\\outlook_summary\\doc\\20250523")  # 重複路徑
        ]
        
        sessions = []
        for user_id, folder_path in users:
            session_id = session_manager.create_session(folder_path, user_id)
            sessions.append((user_id, session_id, folder_path))
            print(f"✅ 創建會話 - 用戶: {user_id}, 會話: {session_id[:8]}...")
        
        # 檢查會話統計
        stats = session_manager.get_session_stats()
        print(f"\n📊 會話統計: {stats}")
        
        # 檢查會話列表
        all_sessions = session_manager.get_all_sessions()
        print(f"📋 總會話數: {len(all_sessions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 會話隔離測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_lock_mechanism():
    """測試檔案鎖定機制"""
    print("\n🔐 測試檔案鎖定機制")
    print("=" * 50)
    
    try:
        from backend.shared.infrastructure.adapters.filesystem.file_lock_manager import get_file_lock_manager
        
        lock_manager = get_file_lock_manager()
        
        # 測試路徑
        test_path = "D:\\project\\python\\outlook_summary\\doc\\20250523"
        
        # 第一個用戶獲取鎖定
        result1 = lock_manager.acquire_lock(test_path, "session_1", "user_1")
        print(f"👤 用戶1 獲取鎖定: {result1}")
        
        # 第二個用戶嘗試獲取相同路徑的鎖定
        result2 = lock_manager.acquire_lock(test_path, "session_2", "user_2")
        print(f"👤 用戶2 獲取鎖定: {result2}")
        
        # 檢查活躍鎖定
        active_locks = lock_manager.get_active_locks()
        print(f"🔒 活躍鎖定數: {len(active_locks)}")
        
        # 釋放鎖定
        release_result = lock_manager.release_lock(test_path, "session_1")
        print(f"🔓 釋放鎖定: {release_result}")
        
        # 再次檢查
        active_locks_after = lock_manager.get_active_locks()
        print(f"🔒 釋放後活躍鎖定數: {len(active_locks_after)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 檔案鎖定測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sequential_eqc_tasks():
    """測試順序 EQC 任務執行"""
    print("\n⚙️ 測試順序 EQC 任務執行")
    print("=" * 50)
    
    try:
        from backend.tasks import process_complete_eqc_workflow_task
        
        # 測試路徑
        test_paths = [
            "D:\\project\\python\\outlook_summary\\doc\\20250523",
            "D:\\project\\python\\outlook_summary\\doc\\202505231"
        ]
        
        results = []
        for i, path in enumerate(test_paths):
            print(f"\n📤 用戶 {i+1} 提交任務: {path}")
            start_time = time.time()
            
            try:
                task = process_complete_eqc_workflow_task.delay(
                    folder_path=path,
                    user_session_id=f"test_user_{i+1}_{int(time.time())}",
                    options={}
                )
                
                result = task.get(timeout=60)
                elapsed = time.time() - start_time
                
                print(f"✅ 用戶 {i+1} 任務完成，耗時: {elapsed:.2f}秒")
                print(f"📊 狀態: {result.get('status', 'unknown')}")
                results.append(True)
                
            except Exception as e:
                print(f"❌ 用戶 {i+1} 任務失敗: {e}")
                results.append(False)
        
        success_count = sum(results)
        print(f"\n📈 成功任務: {success_count}/{len(results)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ EQC 任務測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """測試 API 端點"""
    print("\n🌐 測試 API 端點")
    print("=" * 50)
    
    try:
        import requests
        
        base_url = "http://localhost:5555/ft-eqc/api/eqc/async"
        
        # 測試端點
        endpoints = [
            "/sessions",
            "/stats"
        ]
        
        results = []
        for endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                print(f"🔍 測試: {url}")
                
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint}: 成功")
                    results.append(True)
                else:
                    print(f"❌ {endpoint}: 狀態碼 {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ {endpoint}: {e}")
                results.append(False)
        
        success_count = sum(results)
        print(f"\n📈 成功端點: {success_count}/{len(results)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ API 端點測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 內存模式多人多工測試")
    print("=" * 60)
    
    # 確認模式
    try:
        from backend.tasks import celery_app
        print(f"🔧 當前模式: {'內存模式' if celery_app.conf.task_always_eager else '生產模式'}")
        print(f"📋 Broker: {celery_app.conf.broker_url}")
    except Exception as e:
        print(f"❌ 無法檢查 Celery 配置: {e}")
        return False
    
    # 執行測試
    tests = [
        ("會話隔離機制", test_session_isolation),
        ("檔案鎖定機制", test_file_lock_mechanism),
        ("順序 EQC 任務", test_sequential_eqc_tasks),
        ("API 端點", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
                
        except Exception as e:
            print(f"💥 {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 內存模式多人多工測試結果")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 總計: {passed}/{total} 測試通過")
    
    if passed >= total * 0.75:  # 75% 通過率
        print("🎉 內存模式多人多工基本功能正常")
        print("\n💡 下一步建議：")
        print("   1. 修復生產模式 Worker 啟動問題")
        print("   2. 實現真正的並發多人多工")
        return True
    else:
        print("⚠️ 內存模式測試失敗過多，需要修復基本問題")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
