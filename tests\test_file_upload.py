"""
檔案上傳功能測試模組

測試檔案上傳、解壓縮、暫存管理等功能。
"""

import pytest
import tempfile
import zipfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import UploadFile
import io

# 測試用的檔案上傳功能模組
from backend.shared.infrastructure.adapters.file_upload import (
    UploadConfig,
    TempFileManager,
    UploadProcessor,
    ArchiveExtractor,
    FileSizeError,
    FileFormatError,
    ExtractionError
)


class TestUploadConfig:
    """測試上傳配置功能"""

    def test_default_config_values(self):
        """測試預設配置值"""
        config = UploadConfig()
        
        assert config.max_upload_size_mb == 1000
        assert config.auto_cleanup_hours == 24
        assert "zip" in config.supported_archive_formats
        assert "7z" in config.supported_archive_formats
        assert "rar" in config.supported_archive_formats

    def test_max_upload_size_bytes_calculation(self):
        """測試檔案大小位元組計算"""
        config = UploadConfig(max_upload_size_mb=100)
        expected_bytes = 100 * 1024 * 1024
        
        assert config.max_upload_size_bytes == expected_bytes

    def test_is_supported_format(self):
        """測試檔案格式支援檢查"""
        config = UploadConfig()
        
        assert config.is_supported_format("zip") == True
        assert config.is_supported_format(".zip") == True
        assert config.is_supported_format("ZIP") == True
        assert config.is_supported_format("txt") == False

    def test_config_validation(self):
        """測試配置驗證"""
        # 測試無效的檔案大小限制
        with pytest.raises(ValueError):
            UploadConfig(max_upload_size_mb=0)
        
        # 測試無效的清理時間
        with pytest.raises(ValueError):
            UploadConfig(auto_cleanup_hours=0)


class TestTempFileManager:
    """測試暫存檔案管理功能"""

    def test_temp_file_manager_initialization(self):
        """測試暫存檔案管理器初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(
                upload_temp_dir=Path(temp_dir) / "uploads",
                extract_temp_dir=Path(temp_dir) / "extracted"
            )
            
            manager = TempFileManager(config)
            
            # 檢查目錄是否已創建
            assert config.upload_temp_dir.exists()
            assert config.extract_temp_dir.exists()

    def test_create_unique_upload_path(self):
        """測試創建唯一上傳路徑"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(upload_temp_dir=Path(temp_dir))
            manager = TempFileManager(config)
            
            path1 = manager.create_unique_upload_path("test.zip")
            path2 = manager.create_unique_upload_path("test.zip")
            
            # 路徑應該不同
            assert path1 != path2
            assert path1.suffix == ".zip"
            assert path2.suffix == ".zip"

    def test_create_unique_extract_dir(self):
        """測試創建唯一解壓縮目錄"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(extract_temp_dir=Path(temp_dir))
            manager = TempFileManager(config)
            
            dir1 = manager.create_unique_extract_dir()
            dir2 = manager.create_unique_extract_dir()
            
            # 目錄應該不同且存在
            assert dir1 != dir2
            assert dir1.exists()
            assert dir2.exists()

    def test_cleanup_old_files(self):
        """測試清理過期檔案"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(
                upload_temp_dir=Path(temp_dir),
                auto_cleanup_hours=0  # 立即清理
            )
            manager = TempFileManager(config)
            
            # 創建測試檔案
            test_file = config.upload_temp_dir / "test.txt"
            test_file.write_text("test")
            
            # 執行清理
            manager.cleanup_old_files()
            
            # 檔案應該被刪[EXCEPT_CHAR]
            assert not test_file.exists()

    def test_remove_file_or_dir(self):
        """測試刪[EXCEPT_CHAR]檔案或目錄"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = TempFileManager()
            
            # 測試刪[EXCEPT_CHAR]檔案
            test_file = Path(temp_dir) / "test.txt"
            test_file.write_text("test")
            result = manager.remove_file_or_dir(test_file)
            
            assert result == True
            assert not test_file.exists()

    def test_get_directory_size(self):
        """測試計算目錄大小"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = TempFileManager()
            
            # 創建測試檔案
            test_file = Path(temp_dir) / "test.txt"
            test_content = "test content"
            test_file.write_text(test_content)
            
            size = manager.get_directory_size(Path(temp_dir))
            
            assert size >= len(test_content.encode())

    def test_check_disk_space(self):
        """測試磁碟空間檢查"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(upload_temp_dir=Path(temp_dir))
            manager = TempFileManager(config)
            
            # 檢查是否有足夠空間（應該為True）
            result = manager.check_disk_space(1)  # 1MB
            assert result == True


class TestUploadProcessor:
    """測試檔案上傳處理功能"""

    def test_validate_file_size_success(self):
        """測試檔案大小驗證成功"""
        config = UploadConfig(max_upload_size_mb=10)
        processor = UploadProcessor(config)
        
        file_size = 5 * 1024 * 1024  # 5MB
        
        # 不應該拋出異常
        processor.validate_file_size(file_size)

    def test_validate_file_size_failure(self):
        """測試檔案大小驗證失敗"""
        config = UploadConfig(max_upload_size_mb=10)
        processor = UploadProcessor(config)
        
        file_size = 15 * 1024 * 1024  # 15MB
        
        with pytest.raises(FileSizeError):
            processor.validate_file_size(file_size)

    def test_validate_file_format_success(self):
        """測試檔案格式驗證成功"""
        processor = UploadProcessor()
        
        # 不應該拋出異常
        processor.validate_file_format("test.zip")
        processor.validate_file_format("archive.7z")
        processor.validate_file_format("data.rar")

    def test_validate_file_format_failure(self):
        """測試檔案格式驗證失敗"""
        processor = UploadProcessor()
        
        with pytest.raises(FileFormatError):
            processor.validate_file_format("document.txt")

    @pytest.mark.asyncio
    async def test_process_upload_success(self):
        """測試檔案上傳處理成功"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(upload_temp_dir=Path(temp_dir))
            processor = UploadProcessor(config)
            
            # 創建模擬的上傳檔案
            file_content = b"test zip content"
            mock_file = Mock(spec=UploadFile)
            mock_file.filename = "test.zip"
            mock_file.read = AsyncMock(return_value=file_content)
            
            result = await processor.process_upload(mock_file)
            
            assert result['success'] == True
            assert result['original_filename'] == "test.zip"
            assert result['file_size'] == len(file_content)

    @pytest.mark.asyncio
    async def test_process_upload_invalid_format(self):
        """測試上傳無效格式檔案"""
        processor = UploadProcessor()
        
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "document.txt"
        mock_file.read = AsyncMock(return_value=b"content")
        
        with pytest.raises(Exception):  # 應該拋出HTTP異常
            await processor.process_upload(mock_file)

    def test_get_upload_info(self):
        """測試取得上傳資訊"""
        config = UploadConfig(max_upload_size_mb=500)
        processor = UploadProcessor(config)
        
        info = processor.get_upload_info()
        
        assert info['max_upload_size_mb'] == 500
        assert 'supported_formats' in info
        assert 'upload_temp_dir' in info

    def test_cleanup_upload_file(self):
        """測試清理上傳檔案"""
        with tempfile.TemporaryDirectory() as temp_dir:
            processor = UploadProcessor()
            
            # 創建測試檔案
            test_file = Path(temp_dir) / "test.zip"
            test_file.write_text("test")
            
            result = processor.cleanup_upload_file(str(test_file))
            
            assert result == True
            assert not test_file.exists()


class TestArchiveExtractor:
    """測試壓縮檔解壓縮功能"""

    def test_archive_extractor_initialization(self):
        """測試壓縮檔解壓縮器初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(extract_temp_dir=Path(temp_dir))
            extractor = ArchiveExtractor(config)
            
            assert extractor.config.extract_temp_dir == Path(temp_dir)

    def test_extract_zip_archive(self):
        """測試解壓縮ZIP檔案"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = UploadConfig(extract_temp_dir=Path(temp_dir))
            extractor = ArchiveExtractor(config)
            
            # 創建測試ZIP檔案
            zip_path = Path(temp_dir) / "test.zip"
            with zipfile.ZipFile(zip_path, 'w') as zf:
                zf.writestr("test.txt", "test content")
                zf.writestr("folder/nested.txt", "nested content")
            
            result = extractor.extract_archive(str(zip_path))
            
            assert result['success'] == True
            assert "test.txt" in result['extracted_files']
            assert "folder/nested.txt" in result['extracted_files']

    def test_extract_nonexistent_archive(self):
        """測試解壓縮不存在的檔案"""
        extractor = ArchiveExtractor()
        
        with pytest.raises(ExtractionError):
            extractor.extract_archive("/nonexistent/file.zip")

    def test_get_archive_info_zip(self):
        """測試取得ZIP檔案資訊"""
        with tempfile.TemporaryDirectory() as temp_dir:
            extractor = ArchiveExtractor()
            
            # 創建測試ZIP檔案
            zip_path = Path(temp_dir) / "test.zip"
            with zipfile.ZipFile(zip_path, 'w') as zf:
                zf.writestr("file1.txt", "content1")
                zf.writestr("file2.txt", "content2")
            
            info = extractor.get_archive_info(str(zip_path))
            
            assert info['filename'] == "test.zip"
            assert info['format'] == "zip"
            assert info['file_count'] == 2
            assert "file1.txt" in info['files']

    def test_get_archive_info_nonexistent(self):
        """測試取得不存在檔案的資訊"""
        extractor = ArchiveExtractor()
        
        with pytest.raises(ExtractionError):
            extractor.get_archive_info("/nonexistent/file.zip")

    def test_cleanup_extracted_dir(self):
        """測試清理解壓縮目錄"""
        with tempfile.TemporaryDirectory() as temp_dir:
            extractor = ArchiveExtractor()
            
            # 創建測試目錄和檔案
            test_dir = Path(temp_dir) / "extracted"
            test_dir.mkdir()
            (test_dir / "test.txt").write_text("test")
            
            result = extractor.cleanup_extracted_dir(str(test_dir))
            
            assert result == True
            assert not test_dir.exists()


@pytest.mark.asyncio
class TestFileUploadAPI:
    """測試檔案上傳API端點"""

    def test_upload_config_endpoint(self):
        """測試上傳配置端點"""
        # 這裡需要導入實際的API應用
        from frontend.api.ft_eqc_api import app
        
        client = TestClient(app)
        response = client.get("/api/upload_config")
        
        assert response.status_code == 200
        data = response.json()
        assert "max_upload_size_mb" in data
        assert "supported_formats" in data

    def test_upload_archive_endpoint(self):
        """測試檔案上傳端點"""
        from frontend.api.ft_eqc_api import app
        
        client = TestClient(app)
        
        # 創建測試ZIP檔案
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w') as zf:
            zf.writestr("test.txt", "test content")
        zip_buffer.seek(0)
        
        response = client.post(
            "/api/upload_archive",
            files={"file": ("test.zip", zip_buffer, "application/zip")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"

    def test_upload_invalid_file_format(self):
        """測試上傳無效格式檔案"""
        from frontend.api.ft_eqc_api import app
        
        client = TestClient(app)
        
        # 創建測試TXT檔案
        txt_content = io.BytesIO(b"test content")
        
        response = client.post(
            "/api/upload_archive",
            files={"file": ("test.txt", txt_content, "text/plain")}
        )
        
        assert response.status_code == 400

    def test_upload_oversized_file(self):
        """測試上傳超大檔案"""
        from frontend.api.ft_eqc_api import app
        
        client = TestClient(app)
        
        # 創建大型檔案內容（模擬）
        large_content = b"x" * (1024 * 1024 * 1500)  # 1500MB
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w') as zf:
            zf.writestr("large_file.txt", large_content)
        zip_buffer.seek(0)
        
        response = client.post(
            "/api/upload_archive",
            files={"file": ("large.zip", zip_buffer, "application/zip")}
        )
        
        assert response.status_code == 400

    def test_cleanup_temp_files_endpoint(self):
        """測試清理暫存檔案端點"""
        from frontend.api.ft_eqc_api import app
        
        client = TestClient(app)
        response = client.post("/api/cleanup_temp_files")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"

    def test_temp_files_info_endpoint(self):
        """測試取得暫存檔案資訊端點"""
        from frontend.api.ft_eqc_api import app
        
        client = TestClient(app)
        response = client.get("/api/temp_files_info")
        
        assert response.status_code == 200
        data = response.json()
        assert "files_info" in data
        assert "total_files" in data


class TestIntegration:
    """整合測試"""

    @pytest.mark.asyncio
    async def test_complete_upload_and_extract_workflow(self):
        """測試完整的上傳和解壓縮流程"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 設定配置
            config = UploadConfig(
                upload_temp_dir=Path(temp_dir) / "uploads",
                extract_temp_dir=Path(temp_dir) / "extracted"
            )
            
            # 創建處理器
            upload_processor = UploadProcessor(config)
            extractor = ArchiveExtractor(config)
            
            # 創建測試ZIP檔案
            file_content = b"test zip content"
            mock_file = Mock(spec=UploadFile)
            mock_file.filename = "test.zip"
            
            # 創建實際的ZIP內容
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w') as zf:
                zf.writestr("test.txt", "test content")
                zf.writestr("data/info.txt", "data content")
            zip_content = zip_buffer.getvalue()
            
            mock_file.read = AsyncMock(return_value=zip_content)
            
            # 執行上傳
            upload_result = await upload_processor.process_upload(mock_file)
            assert upload_result['success'] == True
            
            # 執行解壓縮
            extract_result = extractor.extract_archive(upload_result['upload_path'])
            assert extract_result['success'] == True
            assert len(extract_result['extracted_files']) == 2
            
            # 清理
            upload_processor.cleanup_upload_file(upload_result['upload_path'])
            extractor.cleanup_extracted_dir(extract_result['extract_dir'])


if __name__ == "__main__":
    pytest.main([__file__, "-v"])