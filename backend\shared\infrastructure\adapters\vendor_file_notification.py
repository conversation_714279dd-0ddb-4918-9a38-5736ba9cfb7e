"""
全廠商檔案處理失敗通知服務
當廠商檔案處理失敗時發送 LINE 通知，涵蓋所有支援的廠商

🎯 支援的廠商：
  - GTK, ETD, CHUZHOU, SUQIAN, LINGSEN, NANOTECH, MSEC, NFME, JCET, XAHT, TSHT

🔧 功能：
  - 統一的失敗通知格式
  - 顯示所有廠商的預期路徑
  - 集成現有的 LINE 通知系統
  - 防重複通知機制
"""

import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from backend.shared.infrastructure.adapters.notification.line_notification_service import LineNotificationService
from backend.shared.infrastructure.logging.logger_manager import LoggerManager


class VendorFileNotificationService:
    """全廠商檔案處理失敗通知服務"""
    
    def __init__(self):
        """初始化通知服務"""
        self.logger = LoggerManager().get_logger("VendorFileNotificationService")
        # 初始化必要屬性
        self.notification_log_file = "line_notifications.json"  # 通知記錄檔案
        # 🔧 修復：完整的廠商路徑配置模板 (支援12家廠商)
        self.VENDOR_PATH_CONFIGS = {
            'GTK': [
                '\\\\192.168.1.60\\test_log\\GTK\\temp\\',
                '\\\\192.168.1.60\\test_log\\GTK\\FT\\{pd}\\{lot}\\'
            ],
            'ETD': [
                '\\\\192.168.1.60\\test_log\\ETD\\FT\\{pd}\\{lot}\\*{mo}* (搜尋包含MO的檔案)',
                '\\\\192.168.1.60\\test_log\\Etrend\\FT\\{pd}\\{lot}\\*{mo}* (搜尋包含MO的檔案)'
            ],
            'JCET': [
                '\\\\192.168.1.60\\test_log\\JCET\\JCET\\',
                '\\\\192.168.1.60\\test_log\\JCET\\SUQIAN\\{pd}\\{lot}\\'
            ],
            'NANOTECH': [
                '\\\\192.168.1.60\\test_log\\NANOTECH\\temp\\'
            ],
            'SUQIAN': [
                '\\\\192.168.1.60\\test_log\\JCET\\SUQIAN\\{pd}\\{lot}\\'
            ],
            'LINGSEN': [
                '\\\\192.168.1.60\\test_log\\LINGSEN\\FT\\{pd}\\{lot}\\',
                '\\\\192.168.1.60\\test_log\\LINGSEN\\temp\\'
            ],
            'MSEC': [
                '\\\\192.168.1.60\\test_log\\MSEC\\FT\\{pd}\\{lot}\\',
                '\\\\192.168.1.60\\test_log\\MSEC\\temp\\'
            ],
            'NFME': [
                '\\\\192.168.1.60\\test_log\\NFME\\FT\\{pd}\\{lot}\\',
                '\\\\192.168.1.60\\test_log\\NFME\\temp\\'
            ],
            'TSHT': [
                '\\\\192.168.1.60\\test_log\\TSHT\\FT\\{pd}\\{lot}\\',
                '\\\\192.168.1.60\\test_log\\TSHT\\temp\\'
            ],
            'XAHT': [
                '\\\\192.168.1.60\\test_log\\XAHT\\FT\\{pd}\\{lot}\\',
                '\\\\192.168.1.60\\test_log\\XAHT\\temp\\'
            ],
            'CHUZHOU': [
                '\\\\192.168.1.60\\test_log\\CHUZHOU\\FT\\{pd}\\{lot}\\',
                '\\\\192.168.1.60\\test_log\\CHUZHOU\\temp\\'
            ],
            'ETREND': [
                '\\\\192.168.1.60\\test_log\\Etrend\\FT\\{pd}\\{lot}\\'
            ]
        }  # 廠商路徑配置模板 (支援12家廠商)
        
        try:
            self.line_service = LineNotificationService()
            self.notification_available = True
            self.notification_enabled = True  # 新增屬性
            self.logger.info("✅ LineNotificationService 初始化成功")
        except ValueError as e:
            self.logger.error(f"❌ LineNotificationService 初始化失敗: {e}。請檢查 LINE_CHANNEL_ACCESS_TOKEN 和 LINE_USER_ID 環境變數是否已設定。")
            self.line_service = None # 修正變數名稱
            self.notification_available = False
            self.notification_enabled = False  # 新增屬性
        except Exception as e:
            self.logger.error(f"❌ LineNotificationService 初始化時發生未預期錯誤: {e}")
            self.line_service = None # 修正變數名稱
            self.notification_available = False
            self.notification_enabled = False  # 新增屬性
    
    
    def notify_vendor_file_processing_failure(
        self,
        vendor_code: str,
        mo: str,
        temp_path: str,
        pd: str = "default",
        lot: str = "default",
        error_message: str = "",
        email_subject: str = "",
        email_body: str = "",
        task_id: str = "",
        tracking_id: str = "",
        processing_time: float = 0.0,
        retry_count: int = 0
    ) -> bool:
        """
        發送廠商檔案處理失敗通知
        
        Args:
            vendor_code: 廠商代碼
            mo: MO 編號
            temp_path: 暫存檔案路徑
            pd: 產品代碼
            lot: 批號
            error_message: 錯誤訊息
            email_subject: 郵件主旨
            email_body: 郵件內文
            task_id: 任務ID
            tracking_id: 追蹤ID
            processing_time: 處理時間
            retry_count: 重試次數
            
        Returns:
            bool: 是否成功發送通知
        """
        try:
            # 檢查是否啟用通知
            if not self.notification_enabled:
                self.logger.info("廠商檔案處理失敗通知已停用，跳過通知")
                return True
            
            # 檢查 LineNotificationService 是否可用
            if not self.line_service:
                self.logger.warning("LineNotificationService 未初始化或初始化失敗，無法發送通知")
                return False
            
            # 🔧 修復：改進通知去重邏輯，使用vendor_code+mo+pd+lot作為唯一鍵值
            # 不使用task_id，因為每次重試都有不同的task_id
            notification_key = f"{vendor_code}_{mo}_{pd}_{lot}"
            if self._is_already_notified(notification_key):
                self.logger.info(f"廠商檔案處理失敗已通知過: {notification_key}")
                return True
            
            # 構建通知訊息
            message = self._build_failure_notification_message(
                vendor_code=vendor_code,
                mo=mo,
                temp_path=temp_path,
                pd=pd,
                lot=lot,
                error_message=error_message,
                email_subject=email_subject,
                email_body=email_body,
                task_id=task_id,
                tracking_id=tracking_id,
                processing_time=processing_time,
                retry_count=retry_count
            )
            
            # 發送 LINE 通知
            success = self.line_service._send_message(message)
            
            if success:
                # 記錄通知
                self._record_notification(
                    notification_key=notification_key,
                    vendor_code=vendor_code,
                    mo=mo,
                    error_message=error_message,
                    task_id=task_id
                )
                self.logger.info(f"廠商檔案處理失敗通知已發送: {vendor_code} - {mo}")
            else:
                self.logger.error(f"廠商檔案處理失敗通知發送失敗: {vendor_code} - {mo}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"發送廠商檔案處理失敗通知時發生錯誤: {e}")
            return False
    
    def _build_failure_notification_message(
        self,
        vendor_code: str,
        mo: str,
        temp_path: str,
        pd: str,
        lot: str,
        error_message: str,
        email_subject: str,
        email_body: str,
        task_id: str,
        tracking_id: str,
        processing_time: float,
        retry_count: int
    ) -> str:
        """
        構建廠商檔案處理失敗通知訊息
        
        Returns:
            str: 格式化的通知訊息
        """
        # 取得廠商的預期路徑
        expected_paths = self._get_vendor_expected_paths(vendor_code, pd, lot)
        
        # 格式化錯誤訊息
        simplified_error = self._simplify_error_message(error_message)
        
        # 格式化處理時間
        if processing_time > 0:
            time_str = f"{processing_time:.2f}秒"
        else:
            time_str = "N/A"
        
        # 重試資訊
        retry_info = f"重試 {retry_count} 次" if retry_count > 0 else "首次處理"
        
        # 構建主要通知訊息
        message = f"""🚨 廠商檔案處理失敗通知

📦 處理資訊:
• 廠商: {vendor_code}
• MO編號: {mo}
• 產品代碼: {pd}
• 批號: {lot}
• 暫存路徑: {temp_path}

⚠️ 失敗詳情:
• 錯誤訊息: {simplified_error}
• 處理時間: {time_str}
• 重試狀態: {retry_info}
• 任務ID: {task_id[:8]}...
• 追蹤ID: {tracking_id[:8]}..."""

        # 添加郵件資訊（如果有）
        if email_subject:
            message += f"\n• 郵件主旨: {email_subject[:50]}..."
        
        # 添加預期路徑資訊
        if expected_paths:
            message += f"\n\n📁 {vendor_code} 廠商預期路徑:"
            for i, path in enumerate(expected_paths[:3], 1):  # 最多顯示3個路徑
                message += f"\n  {i}. {path}"
            
            if len(expected_paths) > 3:
                message += f"\n  ... 等共 {len(expected_paths)} 個路徑"
        
        # 添加所有廠商概覽
        message += "\n\n🏭 所有支援廠商:"
        supported_vendors = list(self.VENDOR_PATH_CONFIGS.keys())
        vendor_chunks = [supported_vendors[i:i+4] for i in range(0, len(supported_vendors), 4)]
        
        for chunk in vendor_chunks:
            message += f"\n• {' | '.join(chunk)}"
        
        # 添加建議處理
        message += f"\n\n🔧 建議處理:"
        message += f"\n• 檢查 {vendor_code} 廠商路徑是否存在"
        message += f"\n• 確認檔案是否在預期位置"
        message += f"\n• 檢查網路連線和權限設定"
        message += f"\n• 考慮手動處理或聯絡系統管理員"
        
        # 添加時間戳
        message += f"\n\n⏰ 通知時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return message
    
    def _get_vendor_expected_paths(self, vendor_code: str, pd: str, lot: str) -> List[str]:
        """
        取得廠商的預期路徑列表
        
        Args:
            vendor_code: 廠商代碼
            pd: 產品代碼
            lot: 批號
            
        Returns:
            List[str]: 格式化的路徑列表
        """
        vendor_paths = self.VENDOR_PATH_CONFIGS.get(vendor_code.upper(), [])
        
        formatted_paths = []
        for path_template in vendor_paths:
            try:
                # 替換路徑中的變數
                formatted_path = path_template.format(
                    pd=pd,
                    lot=lot,
                    model=pd  # ETD 使用 model 參數
                )
                formatted_paths.append(formatted_path)
            except KeyError:
                # 如果格式化失敗，保持原始路徑
                formatted_paths.append(path_template)
        
        return formatted_paths
    
    def _simplify_error_message(self, error_message: str) -> str:
        """
        簡化錯誤訊息，讓使用者更容易理解
        
        Args:
            error_message: 原始錯誤訊息
            
        Returns:
            str: 簡化的錯誤訊息
        """
        if not error_message or error_message.strip() == "":
            return "未知錯誤"
        
        error_lower = error_message.lower()
        
        # 常見錯誤類型的友善提示
        error_mappings = {
            'filenotfounderror': '檔案或路徑不存在',
            'permission': '權限不足',
            'network': '網路連線問題',
            'timeout': '處理超時',
            'invalid': '無效的參數或格式',
            'not supported': '不支援的廠商',
            'copy': '檔案複製失敗',
            'validation': '資料驗證失敗'
        }
        
        for keyword, friendly_msg in error_mappings.items():
            if keyword in error_lower:
                return friendly_msg
        
        # 如果錯誤訊息太長，截取前100個字符
        if len(error_message) > 100:
            return error_message[:100] + "..."
        
        return error_message
    
    def _is_already_notified(self, notification_key: str) -> bool:
        """
        檢查是否已經發送過通知
        
        Args:
            notification_key: 通知的唯一鍵值
            
        Returns:
            bool: 是否已通知
        """
        try:
            if not os.path.exists(self.notification_log_file):
                return False
            
            with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)
            
            # 🔧 修復：檢查最近30分鐘內是否有相同的通知（縮短時間窗口）
            for notification in notifications:
                if notification.get('notification_key') == notification_key:
                    # 檢查通知時間，30分鐘內不重複通知
                    notification_time = datetime.fromisoformat(notification.get('timestamp', ''))
                    time_diff = (datetime.now() - notification_time).total_seconds()
                    if time_diff < 1800:  # 30分鐘 = 1800秒
                        self.logger.info(f"通知去重: {notification_key} 在 {time_diff:.0f} 秒前已通知過")
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"檢查通知記錄時發生錯誤: {e}")
            return False
    
    def _record_notification(
        self,
        notification_key: str,
        vendor_code: str,
        mo: str,
        error_message: str,
        task_id: str
    ):
        """
        記錄通知發送歷史
        
        Args:
            notification_key: 通知唯一鍵值
            vendor_code: 廠商代碼
            mo: MO編號
            error_message: 錯誤訊息
            task_id: 任務ID
        """
        try:
            # 載入現有記錄
            notifications = []
            if os.path.exists(self.notification_log_file):
                with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                    notifications = json.load(f)
            
            # 新增通知記錄
            notification_record = {
                'notification_key': notification_key,
                'vendor_code': vendor_code,
                'mo': mo,
                'task_id': task_id,
                'error_message': error_message,
                'timestamp': datetime.now().isoformat(),
                'type': 'vendor_file_processing_failure'
            }
            
            notifications.append(notification_record)
            
            # 保留最近200筆記錄，避免檔案過大
            if len(notifications) > 200:
                notifications = notifications[-200:]
            
            # 儲存記錄
            with open(self.notification_log_file, 'w', encoding='utf-8') as f:
                json.dump(notifications, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"通知記錄已儲存: {notification_key}")
            
        except Exception as e:
            self.logger.error(f"記錄通知時發生錯誤: {e}")
    
    def get_notification_history(self, limit: int = 50) -> List[Dict]:
        """
        獲取通知歷史記錄
        
        Args:
            limit: 返回記錄數量限制
            
        Returns:
            List[Dict]: 通知歷史記錄
        """
        try:
            if not os.path.exists(self.notification_log_file):
                return []
            
            with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)
            
            # 按時間戳排序並返回指定數量
            notifications.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return notifications[:limit]
            
        except Exception as e:
            self.logger.error(f"獲取通知歷史記錄時發生錯誤: {e}")
            return []
    
    def get_vendor_failure_stats(self) -> Dict[str, Any]:
        """
        獲取廠商失敗統計
        
        Returns:
            Dict[str, Any]: 廠商失敗統計資訊
        """
        try:
            notifications = self.get_notification_history(1000)  # 取得最近1000筆記錄
            
            if not notifications:
                return {'total_failures': 0, 'vendor_stats': {}}
            
            vendor_stats = {}
            for notification in notifications:
                vendor = notification.get('vendor_code', 'UNKNOWN')
                if vendor not in vendor_stats:
                    vendor_stats[vendor] = {
                        'failure_count': 0,
                        'last_failure': None,
                        'common_errors': []
                    }
                
                vendor_stats[vendor]['failure_count'] += 1
                vendor_stats[vendor]['last_failure'] = notification.get('timestamp')
                
                error_msg = notification.get('error_message', '')
                if error_msg and error_msg not in vendor_stats[vendor]['common_errors']:
                    vendor_stats[vendor]['common_errors'].append(error_msg)
            
            return {
                'total_failures': len(notifications),
                'vendor_stats': vendor_stats,
                'supported_vendors': list(self.VENDOR_PATH_CONFIGS.keys()),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"獲取廠商失敗統計時發生錯誤: {e}")
            return {'error': str(e)}


# 全局服務實例
_vendor_notification_service: Optional[VendorFileNotificationService] = None


def get_vendor_file_notification_service() -> VendorFileNotificationService:
    """取得全域廠商檔案通知服務實例"""
    global _vendor_notification_service
    if _vendor_notification_service is None:
        _vendor_notification_service = VendorFileNotificationService()
    return _vendor_notification_service