"""
全廠商檔案處理失敗通知服務測試
"""

import pytest
import os
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from backend.shared.infrastructure.adapters.vendor_file_notification import (
    VendorFileNotificationService, 
    get_vendor_file_notification_service
)


class TestVendorFileNotificationService:
    """測試全廠商檔案處理失敗通知服務"""
    
    @pytest.fixture
    def notification_service(self):
        """創建通知服務實例"""
        with patch.dict(os.environ, {
            'LINE_NOTIFY_VENDOR_FILE_FAILURE': 'true',
            'LINE_CHANNEL_ACCESS_TOKEN': 'test_token',
            'LINE_USER_ID': 'test_user',
            'FILE_SOURCE_BASE_PATH': '/mnt/share'  # 設定測試用基礎路徑
        }):
            service = VendorFileNotificationService()
            # 清理測試記錄檔案
            if os.path.exists(service.notification_log_file):
                os.remove(service.notification_log_file)
            return service
    
    @pytest.fixture
    def mock_line_service(self, notification_service):
        """模擬 LINE 服務"""
        with patch.object(notification_service.line_service, '_send_message', return_value=True) as mock:
            yield mock
    
    def test_vendor_path_configs_complete(self, notification_service):
        """測試所有廠商路徑配置是否完整"""
        expected_vendors = [
            'GTK', 'ETD', 'ETREND', 'CHUZHOU', 'SUQIAN', 
            'LINGSEN', 'NANOTECH', 'MSEC', 'NFME', 'JCET', 'XAHT', 'TSHT'
        ]
        
        for vendor in expected_vendors:
            assert vendor in notification_service.VENDOR_PATH_CONFIGS, f"缺少廠商 {vendor} 的路徑配置"
            assert len(notification_service.VENDOR_PATH_CONFIGS[vendor]) > 0, f"廠商 {vendor} 的路徑配置為空"
    
    def test_get_vendor_expected_paths(self, notification_service):
        """測試取得廠商預期路徑"""
        base_path = notification_service.base_path
        
        # 測試 GTK 廠商
        gtk_paths = notification_service._get_vendor_expected_paths('GTK', 'product1', 'lot1')
        assert f'{base_path}/GTK/temp/' in gtk_paths
        assert f'{base_path}/GTK/FT/product1/lot1/' in gtk_paths
        
        # 測試 ETD 廠商
        etd_paths = notification_service._get_vendor_expected_paths('ETD', 'product2', 'lot2')
        assert f'{base_path}/ETD/FT/product2/lot2/' in etd_paths
        assert f'{base_path}/Etrend/FT/product2/lot2/' in etd_paths
        
        # 測試不存在的廠商
        unknown_paths = notification_service._get_vendor_expected_paths('UNKNOWN', 'pd', 'lot')
        assert len(unknown_paths) == 0
    
    def test_simplify_error_message(self, notification_service):
        """測試錯誤訊息簡化"""
        test_cases = [
            ("FileNotFoundError: No such file", "檔案或路徑不存在"),
            ("Permission denied", "權限不足"),
            ("Network timeout", "網路連線問題"),
            ("Invalid parameter format", "無效的參數或格式"),
            ("不支援的廠商", "不支援的廠商"),
            ("Very long error message " * 10, "Very long error message " * 3 + "..."),
            ("", "未知錯誤"),
            (None, "未知錯誤")
        ]
        
        for original, expected in test_cases:
            result = notification_service._simplify_error_message(original)
            if "..." in expected:
                assert len(result) <= 103  # 100 + "..."
            else:
                assert result == expected
    
    def test_build_failure_notification_message(self, notification_service):
        """測試構建失敗通知訊息"""
        message = notification_service._build_failure_notification_message(
            vendor_code="GTK",
            mo="MO12345",
            temp_path="/tmp/test.zip",
            pd="product1",
            lot="lot1",
            error_message="檔案處理失敗",
            email_subject="測試郵件",
            email_body="測試內文",
            task_id="task_123",
            tracking_id="track_456",
            processing_time=1.5,
            retry_count=2
        )
        
        # 檢查訊息內容
        assert "GTK" in message
        assert "MO12345" in message
        assert "檔案處理失敗" in message
        assert "重試 2 次" in message
        assert "1.50秒" in message
        assert "測試郵件" in message
        assert "GTK 廠商預期路徑" in message
        assert f"{notification_service.base_path}/GTK/temp/" in message
    
    def test_notify_vendor_file_processing_failure(self, notification_service, mock_line_service):
        """測試發送廠商檔案處理失敗通知"""
        # 測試成功發送通知
        result = notification_service.notify_vendor_file_processing_failure(
            vendor_code="GTK",
            mo="MO12345",
            temp_path="/tmp/test.zip",
            pd="product1",
            lot="lot1",
            error_message="檔案處理失敗",
            task_id="task_123"
        )
        
        assert result is True
        mock_line_service.assert_called_once()
        
        # 檢查記錄檔案是否創建
        assert os.path.exists(notification_service.notification_log_file)
        
        with open(notification_service.notification_log_file, 'r', encoding='utf-8') as f:
            records = json.load(f)
        
        assert len(records) == 1
        assert records[0]['vendor_code'] == 'GTK'
        assert records[0]['mo'] == 'MO12345'
    
    def test_prevent_duplicate_notifications(self, notification_service, mock_line_service):
        """測試防重複通知機制"""
        params = {
            'vendor_code': 'GTK',
            'mo': 'MO12345',
            'temp_path': '/tmp/test.zip',
            'task_id': 'task_123',
            'error_message': '檔案處理失敗'
        }
        
        # 第一次發送
        result1 = notification_service.notify_vendor_file_processing_failure(**params)
        assert result1 is True
        assert mock_line_service.call_count == 1
        
        # 第二次發送（應該被跳過）
        result2 = notification_service.notify_vendor_file_processing_failure(**params)
        assert result2 is True
        assert mock_line_service.call_count == 1  # 沒有增加
    
    def test_notification_disabled(self):
        """測試通知停用情況"""
        with patch.dict(os.environ, {
            'LINE_NOTIFY_VENDOR_FILE_FAILURE': 'false',
            'LINE_CHANNEL_ACCESS_TOKEN': 'test_token',
            'LINE_USER_ID': 'test_user',
            'FILE_SOURCE_BASE_PATH': '/mnt/share'
        }):
            service = VendorFileNotificationService()
            
            with patch.object(service.line_service, '_send_message') as mock_send:
                result = service.notify_vendor_file_processing_failure(
                    vendor_code="GTK",
                    mo="MO12345",
                    temp_path="/tmp/test.zip",
                    error_message="測試錯誤"
                )
                
                assert result is True
                mock_send.assert_not_called()
    
    def test_notification_history(self, notification_service, mock_line_service):
        """測試通知歷史記錄"""
        # 發送幾個通知
        for i in range(3):
            notification_service.notify_vendor_file_processing_failure(
                vendor_code=f"GTK{i}",
                mo=f"MO{i}",
                temp_path=f"/tmp/test{i}.zip",
                task_id=f"task_{i}",
                error_message=f"錯誤{i}"
            )
        
        # 檢查歷史記錄
        history = notification_service.get_notification_history(limit=10)
        assert len(history) == 3
        
        # 檢查按時間排序（最新的在前）
        timestamps = [record['timestamp'] for record in history]
        assert timestamps == sorted(timestamps, reverse=True)
    
    def test_vendor_failure_stats(self, notification_service, mock_line_service):
        """測試廠商失敗統計"""
        # 發送不同廠商的失敗通知
        vendors_data = [
            ('GTK', 'MO1', 'task1', '錯誤1'),
            ('GTK', 'MO2', 'task2', '錯誤2'),
            ('ETD', 'MO3', 'task3', '錯誤3')
        ]
        
        for vendor, mo, task_id, error in vendors_data:
            notification_service.notify_vendor_file_processing_failure(
                vendor_code=vendor,
                mo=mo,
                temp_path=f"/tmp/{mo}.zip",
                task_id=task_id,
                error_message=error
            )
        
        # 檢查統計資料
        stats = notification_service.get_vendor_failure_stats()
        assert stats['total_failures'] == 3
        assert 'GTK' in stats['vendor_stats']
        assert 'ETD' in stats['vendor_stats']
        assert stats['vendor_stats']['GTK']['failure_count'] == 2
        assert stats['vendor_stats']['ETD']['failure_count'] == 1
        assert len(stats['supported_vendors']) > 0
    
    def test_global_service_instance(self):
        """測試全域服務實例"""
        with patch.dict(os.environ, {
            'LINE_NOTIFY_VENDOR_FILE_FAILURE': 'true',
            'LINE_CHANNEL_ACCESS_TOKEN': 'test_token',
            'LINE_USER_ID': 'test_user',
            'FILE_SOURCE_BASE_PATH': '/mnt/share'
        }):
            service1 = get_vendor_file_notification_service()
            service2 = get_vendor_file_notification_service()
            
            # 應該是同一個實例
            assert service1 is service2
    
    def test_dynamic_base_path(self):
        """測試動態基礎路徑功能"""
        custom_base_path = '/custom/share/path'
        
        with patch.dict(os.environ, {
            'LINE_NOTIFY_VENDOR_FILE_FAILURE': 'true',
            'LINE_CHANNEL_ACCESS_TOKEN': 'test_token',
            'LINE_USER_ID': 'test_user',
            'FILE_SOURCE_BASE_PATH': custom_base_path
        }):
            service = VendorFileNotificationService()
            
            # 檢查基礎路徑是否正確設定
            assert service.base_path == custom_base_path
            
            # 檢查廠商配置是否使用新的基礎路徑
            gtk_paths = service._get_vendor_expected_paths('GTK', 'product1', 'lot1')
            assert f'{custom_base_path}/GTK/temp/' in gtk_paths
            assert f'{custom_base_path}/GTK/FT/product1/lot1/' in gtk_paths
            
        # 測試預設情況（沒有設定 FILE_SOURCE_BASE_PATH）
        default_env = {
            'LINE_NOTIFY_VENDOR_FILE_FAILURE': 'true',
            'LINE_CHANNEL_ACCESS_TOKEN': 'test_token', 
            'LINE_USER_ID': 'test_user'
        }
        
        with patch.dict(os.environ, default_env, clear=True):
            service_default = VendorFileNotificationService()
            assert service_default.base_path == '/mnt/share'  # 預設值
    
    def test_all_vendor_paths_in_message(self, notification_service):
        """測試通知訊息中包含所有廠商資訊"""
        message = notification_service._build_failure_notification_message(
            vendor_code="GTK",
            mo="MO12345",
            temp_path="/tmp/test.zip",
            pd="product1",
            lot="lot1",
            error_message="測試錯誤",
            email_subject="",
            email_body="",
            task_id="task_123",
            tracking_id="track_456",
            processing_time=1.0,
            retry_count=0
        )
        
        # 檢查是否包含所有廠商
        expected_vendors = ['GTK', 'ETD', 'CHUZHOU', 'SUQIAN', 'LINGSEN', 'NANOTECH', 'MSEC', 'NFME', 'JCET', 'XAHT', 'TSHT']
        for vendor in expected_vendors:
            assert vendor in message, f"通知訊息中缺少廠商 {vendor}"
    
    def test_error_handling_in_notification(self, notification_service):
        """測試通知過程中的錯誤處理"""
        with patch.object(notification_service.line_service, '_send_message', side_effect=Exception("LINE服務錯誤")):
            # 即使 LINE 服務出錯，也不應該拋出異常
            result = notification_service.notify_vendor_file_processing_failure(
                vendor_code="GTK",
                mo="MO12345",
                temp_path="/tmp/test.zip",
                error_message="測試錯誤"
            )
            
            assert result is False  # 發送失敗但不拋出異常
    
    def tearDown(self):
        """清理測試檔案"""
        test_files = [
            'vendor_file_failure_notifications.json',
            'line_notifications.json'
        ]
        
        for file_name in test_files:
            if os.path.exists(file_name):
                try:
                    os.remove(file_name)
                except Exception:
                    pass


if __name__ == '__main__':
    pytest.main([__file__])