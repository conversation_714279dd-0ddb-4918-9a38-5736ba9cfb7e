"""
Dramatiq 簡化測試 - 直接測試業務邏輯

🎯 測試目標：
  - 驗證 EQC 處理服務能正常工作
  - 確認 Dramatiq 任務定義正確
  - 測試基本的業務邏輯流程
"""

import asyncio
import time
import os
import sys
from pathlib import Path
from loguru import logger

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_eqc_service_direct():
    """直接測試 EQC 處理服務"""
    logger.info("🧪 開始直接測試 EQC 處理服務")
    
    try:
        # 導入 EQC 處理服務
        from frontend.api.services.eqc_processing_service import EQCProcessingService
        from frontend.api.models import OnlineEQCProcessRequest
        
        # 創建測試資料夾
        test_folder = r"D:\project\python\outlook_summary\test_data\eqc_simple"
        Path(test_folder).mkdir(parents=True, exist_ok=True)
        
        # 創建基本測試文件
        test_files = [
            "EQCTOTALDATA.csv",
            "FT_data.csv", 
            "EQC_data.csv"
        ]
        
        for file_name in test_files:
            test_file = Path(test_folder) / file_name
            test_file.write_text("test,data\n1,2\n", encoding='utf-8')
        
        logger.info(f"✅ 測試資料夾準備完成: {test_folder}")
        
        # 初始化服務
        eqc_service = EQCProcessingService()
        logger.info("✅ EQC 處理服務初始化成功")
        
        # 測試 Step 1: process_online_eqc
        logger.info("🚀 測試 Step 1: process_online_eqc")
        step1_request = OnlineEQCProcessRequest(
            folder_path=test_folder,
            processing_mode='1'  # EQC BIN1統計
        )
        
        start_time = time.time()
        step1_result = await eqc_service.process_online_eqc(step1_request)
        step1_time = time.time() - start_time
        
        logger.info(f"✅ Step 1 完成，耗時: {step1_time:.2f}秒")
        logger.info(f"   結果狀態: {step1_result.status if hasattr(step1_result, 'status') else 'N/A'}")
        
        # 測試 Step 2: process_eqc_advanced
        logger.info("🚀 測試 Step 2: process_eqc_advanced")
        step2_request = {
            'folder_path': test_folder,
            'include_step123': False,
            'main_start': 'A1',
            'main_end': 'Z100'
        }
        
        start_time = time.time()
        step2_result = await eqc_service.process_eqc_advanced(step2_request)
        step2_time = time.time() - start_time
        
        logger.info(f"✅ Step 2 完成，耗時: {step2_time:.2f}秒")
        logger.info(f"   結果狀態: {step2_result.get('status', 'N/A')}")
        
        # 測試 Step 3: analyze_real_data
        logger.info("🚀 測試 Step 3: analyze_real_data")
        step3_request = {"folder_path": test_folder}
        
        start_time = time.time()
        step3_result = await eqc_service.analyze_real_data(step3_request)
        step3_time = time.time() - start_time
        
        logger.info(f"✅ Step 3 完成，耗時: {step3_time:.2f}秒")
        logger.info(f"   結果狀態: {step3_result.get('status', 'N/A')}")
        
        # 總結
        total_time = step1_time + step2_time + step3_time
        logger.success("🎉 EQC 處理服務測試完成!")
        logger.info("📊 測試總結:")
        logger.info(f"  - Step 1 耗時: {step1_time:.2f}秒")
        logger.info(f"  - Step 2 耗時: {step2_time:.2f}秒") 
        logger.info(f"  - Step 3 耗時: {step3_time:.2f}秒")
        logger.info(f"  - 總耗時: {total_time:.2f}秒")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ EQC 處理服務測試失敗: {str(e)}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def test_dramatiq_task_structure():
    """測試 Dramatiq 任務結構"""
    logger.info("🧪 開始測試 Dramatiq 任務結構")
    
    try:
        # 導入 Dramatiq 任務
        from backend.tasks.services.dramatiq_tasks import (
            process_complete_eqc_workflow_task,
            search_product_task,
            generate_csv_summary_task,
            compare_code_task,
            health_check_task
        )
        
        logger.info("✅ Dramatiq 任務導入成功")
        
        # 檢查任務屬性
        tasks = [
            ('EQC 工作流程', process_complete_eqc_workflow_task),
            ('產品搜索', search_product_task),
            ('CSV 摘要', generate_csv_summary_task),
            ('代碼比較', compare_code_task),
            ('健康檢查', health_check_task)
        ]
        
        for task_name, task in tasks:
            logger.info(f"📋 檢查任務: {task_name}")
            logger.info(f"   - 隊列: {task.queue_name}")
            logger.info(f"   - 最大重試: {task.max_retries}")
            logger.info(f"   - 時間限制: {task.time_limit}")
            logger.info(f"   - 函數名: {task.actor_name}")
        
        logger.success("✅ Dramatiq 任務結構檢查完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ Dramatiq 任務結構測試失敗: {str(e)}")
        return False


async def test_dramatiq_config():
    """測試 Dramatiq 配置"""
    logger.info("🧪 開始測試 Dramatiq 配置")
    
    try:
        # 導入配置
        import dramatiq_config
        
        broker = dramatiq_config.get_broker()
        result_backend = dramatiq_config.get_result_backend()
        
        logger.info("✅ Dramatiq 配置導入成功")
        logger.info(f"   - Broker 類型: {type(broker).__name__}")
        logger.info(f"   - Result Backend 類型: {type(result_backend).__name__}")
        
        # 檢查隊列配置
        queue_configs = dramatiq_config.QUEUE_CONFIG
        logger.info("📋 隊列配置:")
        for queue_name, config in queue_configs.items():
            logger.info(f"   - {queue_name}: {config}")
        
        # 檢查 Worker 配置
        worker_config = dramatiq_config.WORKER_CONFIG
        logger.info(f"📋 Worker 配置: {worker_config}")
        
        logger.success("✅ Dramatiq 配置檢查完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ Dramatiq 配置測試失敗: {str(e)}")
        return False


async def main():
    """主測試函數"""
    logger.info("🎯 Dramatiq 簡化測試套件開始")
    
    # 測試 1: Dramatiq 配置
    config_test_passed = await test_dramatiq_config()
    
    # 測試 2: Dramatiq 任務結構
    structure_test_passed = await test_dramatiq_task_structure()
    
    # 測試 3: EQC 處理服務
    service_test_passed = await test_eqc_service_direct()
    
    # 總結
    logger.info("📋 測試總結:")
    logger.info(f"  - Dramatiq 配置測試: {'✅ 通過' if config_test_passed else '❌ 失敗'}")
    logger.info(f"  - 任務結構測試: {'✅ 通過' if structure_test_passed else '❌ 失敗'}")
    logger.info(f"  - EQC 服務測試: {'✅ 通過' if service_test_passed else '❌ 失敗'}")
    
    all_passed = config_test_passed and structure_test_passed and service_test_passed
    
    if all_passed:
        logger.success("🎉 所有測試通過! Dramatiq 系統基礎功能正常")
        logger.info("🚀 下一步: 可以開始 API 層整合和實際部署測試")
        return True
    else:
        logger.error("❌ 部分測試失敗，需要進一步調試")
        return False


if __name__ == "__main__":
    asyncio.run(main())
