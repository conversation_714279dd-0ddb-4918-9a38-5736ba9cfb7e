"""
增強任務排程器核心模組
GTK特殊排程邏輯和基礎資料結構
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum

# 動態添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
src_path = os.path.join(project_root, 'src')
backend_path = os.path.join(project_root, 'backend')
if src_path not in sys.path:
    sys.path.insert(0, src_path)
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

from .concurrent_task_core import TaskPriority


class ScheduledTaskStatus(Enum):
    """排程任務狀態枚舉"""
    PENDING = "pending"         # 等待排程執行
    SCHEDULED = "scheduled"     # 已加入排程
    EXECUTING = "executing"     # 執行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 執行失敗
    CANCELLED = "cancelled"     # 已取消
    EXPIRED = "expired"         # 已過期


class VendorType(Enum):
    """廠商類型枚舉"""
    GTK = "GTK"                 # GTK廠商（特殊排程邏輯）
    OTHER = "OTHER"             # 其他廠商（立即處理）


@dataclass
class ScheduledTask:
    """排程任務資料類"""
    task_id: str
    vendor_name: str
    vendor_type: VendorType
    email_data: Dict[str, Any]
    processing_params: Dict[str, Any]
    scheduled_time: datetime
    created_time: datetime
    status: ScheduledTaskStatus
    priority: TaskPriority = TaskPriority.NORMAL
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None
    executed_time: Optional[datetime] = None
    completion_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        data = asdict(self)
        # 處理 datetime 和 enum 序列化
        data['vendor_type'] = self.vendor_type.value
        data['status'] = self.status.value
        data['priority'] = self.priority.value
        data['scheduled_time'] = self.scheduled_time.isoformat()
        data['created_time'] = self.created_time.isoformat()
        data['executed_time'] = self.executed_time.isoformat() if self.executed_time else None
        data['completion_time'] = self.completion_time.isoformat() if self.completion_time else None
        return data


class SchedulingError(Exception):
    """排程錯誤"""
    pass


class GTKSchedulingLogic:
    """GTK廠商特殊排程邏輯"""
    
    @staticmethod
    def calculate_next_execution_time(received_time: datetime) -> datetime:
        """
        計算GTK郵件的下一次執行時間
        
        邏輯：
        - 01-30分收到 → 31分執行
        - 31-59分收到 → 下一小時01分執行
        
        Args:
            received_time: 郵件接收時間
            
        Returns:
            datetime: 下一次執行時間
        """
        minute = received_time.minute
        
        if 1 <= minute <= 30:
            # 在01-30分收到，安排在31分執行
            next_time = received_time.replace(minute=31, second=0, microsecond=0)
        else:
            # 在31-59分收到，安排在下一小時01分執行
            next_hour = received_time + timedelta(hours=1)
            next_time = next_hour.replace(minute=1, second=0, microsecond=0)
        
        return next_time
    
    @staticmethod
    def is_gtk_vendor(vendor_code: str) -> bool:
        """判斷是否為GTK廠商"""
        return vendor_code.upper() in ['GTK', 'GTK_VENDOR']
    
    @staticmethod
    def should_use_scheduling(vendor_code: str) -> bool:
        """判斷是否應該使用排程邏輯"""
        return GTKSchedulingLogic.is_gtk_vendor(vendor_code)


class VendorClassifier:
    """廠商分類器"""
    
    # GTK廠商列表
    GTK_VENDORS = {'GTK', 'GTK_VENDOR'}
    
    # 其他廠商列表（立即處理）
    IMMEDIATE_VENDORS = {
        'JCET', 'AMIC', 'OSE', 'SPIL', 'ASE', 'MSEC',
        'UTAC', 'KYEC', 'SIGURD', 'CHIPMOS', 'POWERTECH'
    }
    
    @classmethod
    def classify_vendor(cls, vendor_code: str) -> VendorType:
        """分類廠商類型"""
        vendor_upper = vendor_code.upper()
        
        if vendor_upper in cls.GTK_VENDORS:
            return VendorType.GTK
        else:
            return VendorType.OTHER
    
    @classmethod
    def get_vendor_processing_strategy(cls, vendor_code: str) -> str:
        """獲取廠商處理策略"""
        vendor_type = cls.classify_vendor(vendor_code)
        
        if vendor_type == VendorType.GTK:
            return "scheduled"  # 使用排程邏輯
        else:
            return "immediate"  # 立即處理


class TaskPriorityCalculator:
    """任務優先級計算器"""
    
    @staticmethod
    def calculate_priority(vendor_type: VendorType, 
                         email_data: Dict[str, Any],
                         time_diff_minutes: Optional[int] = None) -> TaskPriority:
        """
        計算任務優先級
        
        Args:
            vendor_type: 廠商類型
            email_data: 郵件資料
            time_diff_minutes: 距離執行時間的分鐘數
            
        Returns:
            TaskPriority: 任務優先級
        """
        # 基礎優先級
        if vendor_type == VendorType.GTK:
            base_priority = TaskPriority.HIGH
        else:
            base_priority = TaskPriority.NORMAL
        
        # 根據關鍵字調整優先級
        subject = email_data.get('subject', '').lower()
        if any(keyword in subject for keyword in ['urgent', '緊急', 'critical', '重要']):
            if base_priority == TaskPriority.NORMAL:
                base_priority = TaskPriority.HIGH
            elif base_priority == TaskPriority.HIGH:
                base_priority = TaskPriority.CRITICAL
        
        # 根據時間差調整優先級（GTK排程邏輯）
        if vendor_type == VendorType.GTK and time_diff_minutes is not None:
            if time_diff_minutes <= 5:  # 即將執行
                if base_priority.value < TaskPriority.CRITICAL.value:
                    base_priority = TaskPriority.CRITICAL
        
        return base_priority


class ScheduleCalculator:
    """排程計算器"""
    
    @staticmethod
    def calculate_schedule_time(vendor_code: str, 
                              received_time: datetime) -> datetime:
        """
        計算排程時間
        
        Args:
            vendor_code: 廠商代碼
            received_time: 接收時間
            
        Returns:
            datetime: 排程執行時間
        """
        vendor_type = VendorClassifier.classify_vendor(vendor_code)
        
        if vendor_type == VendorType.GTK:
            return GTKSchedulingLogic.calculate_next_execution_time(received_time)
        else:
            # 其他廠商立即執行（添加小延遲避免競爭）
            return received_time + timedelta(seconds=1)
    
    @staticmethod
    def get_cron_expression(execution_time: datetime) -> str:
        """獲取Cron表達式"""
        return f"{execution_time.second} {execution_time.minute} {execution_time.hour} {execution_time.day} {execution_time.month} ? {execution_time.year}"
    
    @staticmethod
    def validate_schedule_time(schedule_time: datetime) -> bool:
        """驗證排程時間是否有效"""
        now = datetime.now()
        
        # 不能是過去的時間
        if schedule_time <= now:
            return False
        
        # 不能超過24小時後
        if schedule_time > now + timedelta(hours=24):
            return False
        
        return True


# 輔助函數
def create_scheduled_task(
    vendor_code: str,
    email_data: Dict[str, Any],
    processing_params: Dict[str, Any] = None,
    received_time: Optional[datetime] = None
) -> ScheduledTask:
    """
    創建排程任務的便利函數
    
    Args:
        vendor_code: 廠商代碼
        email_data: 郵件資料
        processing_params: 處理參數
        received_time: 接收時間（默認為當前時間）
        
    Returns:
        ScheduledTask: 排程任務物件
    """
    import uuid
    
    received_time = received_time or datetime.now()
    processing_params = processing_params or {}
    
    # 分類廠商
    vendor_type = VendorClassifier.classify_vendor(vendor_code)
    
    # 計算排程時間
    schedule_time = ScheduleCalculator.calculate_schedule_time(vendor_code, received_time)
    
    # 計算優先級
    time_diff = int((schedule_time - received_time).total_seconds() / 60)
    priority = TaskPriorityCalculator.calculate_priority(vendor_type, email_data, time_diff)
    
    return ScheduledTask(
        task_id=str(uuid.uuid4()),
        vendor_name=vendor_code,
        vendor_type=vendor_type,
        email_data=email_data,
        processing_params=processing_params,
        scheduled_time=schedule_time,
        created_time=received_time,
        status=ScheduledTaskStatus.PENDING,
        priority=priority
    )


def get_vendor_info(vendor_code: str) -> Dict[str, Any]:
    """
    獲取廠商資訊
    
    Args:
        vendor_code: 廠商代碼
        
    Returns:
        Dict: 廠商資訊
    """
    vendor_type = VendorClassifier.classify_vendor(vendor_code)
    strategy = VendorClassifier.get_vendor_processing_strategy(vendor_code)
    
    return {
        'vendor_code': vendor_code,
        'vendor_type': vendor_type.value,
        'processing_strategy': strategy,
        'is_gtk_vendor': vendor_type == VendorType.GTK,
        'requires_scheduling': strategy == "scheduled"
    }