"""
Dramatiq 監控收集器單元測試

🎯 測試目標：
  - 驗證 Dramatiq 監控收集器功能
  - 測試指標收集和處理
  - 確認錯誤處理機制
  - 驗證健康檢查功能

🔧 測試範圍：
  - DashboardDramatiqCollector 類
  - 指標收集方法
  - Redis 連接處理
  - 降級模式功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from typing import Dict, Any

# 測試目標模組
from backend.monitoring.collectors.dashboard_dramatiq_collector import (
    DramatiqWorkerInfo,
    DramatiqQueueInfo,
    DashboardDramatiqCollector,
    create_dramatiq_collector
)
from backend.monitoring.models.dashboard_metrics_models import TaskQueueMetrics


class TestDramatiqWorkerInfo:
    """測試 DramatiqWorkerInfo 資料類"""
    
    def test_worker_info_initialization(self):
        """測試 Worker 資訊初始化"""
        worker_info = DramatiqWorkerInfo(
            worker_id="worker_001",
            status="active",
            current_task="test_task",
            tasks_processed=10,
            last_heartbeat=datetime.now(),
            memory_usage=75.5,
            cpu_usage=45.2
        )
        
        assert worker_info.worker_id == "worker_001"
        assert worker_info.status == "active"
        assert worker_info.current_task == "test_task"
        assert worker_info.tasks_processed == 10
        assert worker_info.memory_usage == 75.5
        assert worker_info.cpu_usage == 45.2
    
    def test_worker_info_defaults(self):
        """測試 Worker 資訊預設值"""
        worker_info = DramatiqWorkerInfo(
            worker_id="worker_002",
            status="idle"
        )
        
        assert worker_info.worker_id == "worker_002"
        assert worker_info.status == "idle"
        assert worker_info.current_task is None
        assert worker_info.tasks_processed == 0
        assert worker_info.last_heartbeat is None
        assert worker_info.memory_usage == 0.0
        assert worker_info.cpu_usage == 0.0


class TestDramatiqQueueInfo:
    """測試 DramatiqQueueInfo 資料類"""
    
    def test_queue_info_initialization(self):
        """測試隊列資訊初始化"""
        queue_info = DramatiqQueueInfo(
            queue_name="eqc_queue",
            pending_count=5,
            processing_count=2,
            completed_count=100,
            failed_count=3,
            avg_processing_time=120.5,
            last_task_time=datetime.now()
        )
        
        assert queue_info.queue_name == "eqc_queue"
        assert queue_info.pending_count == 5
        assert queue_info.processing_count == 2
        assert queue_info.completed_count == 100
        assert queue_info.failed_count == 3
        assert queue_info.avg_processing_time == 120.5
        assert queue_info.last_task_time is not None
    
    def test_queue_info_defaults(self):
        """測試隊列資訊預設值"""
        queue_info = DramatiqQueueInfo(queue_name="test_queue")
        
        assert queue_info.queue_name == "test_queue"
        assert queue_info.pending_count == 0
        assert queue_info.processing_count == 0
        assert queue_info.completed_count == 0
        assert queue_info.failed_count == 0
        assert queue_info.avg_processing_time == 0.0
        assert queue_info.last_task_time is None


class TestDashboardDramatiqCollector:
    """測試 DashboardDramatiqCollector 類"""
    
    def test_collector_initialization(self):
        """測試收集器初始化"""
        collector = DashboardDramatiqCollector("redis://localhost:6379/0")
        
        assert collector.redis_url == "redis://localhost:6379/0"
        assert collector.redis_client is None
        assert collector.last_collection_time is None
        assert collector.collection_interval == 30
        assert collector.task_stats_cache == {}
        assert collector.worker_stats_cache == {}
    
    @pytest.mark.asyncio
    @patch('redis.from_url')
    async def test_initialize_success(self, mock_redis_from_url):
        """測試成功初始化"""
        mock_redis_client = Mock()
        mock_redis_client.ping.return_value = True
        mock_redis_from_url.return_value = mock_redis_client
        
        collector = DashboardDramatiqCollector()
        result = await collector.initialize()
        
        assert result == True
        assert collector.redis_client == mock_redis_client
        mock_redis_client.ping.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('redis.from_url')
    async def test_initialize_connection_failure(self, mock_redis_from_url):
        """測試初始化時連接失敗"""
        mock_redis_client = Mock()
        mock_redis_client.ping.side_effect = Exception("Connection failed")
        mock_redis_from_url.return_value = mock_redis_client
        
        collector = DashboardDramatiqCollector()
        result = await collector.initialize()
        
        assert result == True  # 降級模式仍然可用
        assert collector.redis_client is None
    
    @pytest.mark.asyncio
    async def test_collect_metrics_without_redis(self):
        """測試無 Redis 連接時收集指標"""
        collector = DashboardDramatiqCollector()
        collector.redis_client = None
        
        metrics = await collector.collect_metrics()
        
        assert isinstance(metrics, TaskQueueMetrics)
        assert metrics.queue_type == "dramatiq"
        assert metrics.total_active == 0
        assert metrics.total_pending == 0
    
    @pytest.mark.asyncio
    @patch('asyncio.get_event_loop')
    async def test_collect_queue_metrics_success(self, mock_get_loop):
        """測試成功收集隊列指標"""
        mock_redis_client = Mock()
        mock_redis_client.llen.return_value = 5
        mock_redis_client.scard.return_value = 2
        mock_redis_client.get.side_effect = ["100", "3"]
        
        mock_loop = Mock()
        mock_loop.run_in_executor.side_effect = [5, 2, 100, 3] * 4  # 4個隊列
        mock_get_loop.return_value = mock_loop
        
        collector = DashboardDramatiqCollector()
        collector.redis_client = mock_redis_client
        
        queue_metrics = await collector._collect_queue_metrics()
        
        assert queue_metrics['pending_tasks'] == 20  # 5 * 4 隊列
        assert queue_metrics['active_tasks'] == 8    # 2 * 4 隊列
        assert queue_metrics['completed_tasks'] == 400  # 100 * 4 隊列
        assert queue_metrics['failed_tasks'] == 12     # 3 * 4 隊列
    
    @pytest.mark.asyncio
    async def test_collect_queue_metrics_no_redis(self):
        """測試無 Redis 時收集隊列指標"""
        collector = DashboardDramatiqCollector()
        collector.redis_client = None
        
        queue_metrics = await collector._collect_queue_metrics()
        
        assert queue_metrics == collector._get_fallback_queue_metrics()
    
    @pytest.mark.asyncio
    @patch('asyncio.get_event_loop')
    async def test_collect_worker_metrics_success(self, mock_get_loop):
        """測試成功收集 Worker 指標"""
        mock_redis_client = Mock()
        mock_redis_client.keys.return_value = [
            "dramatiq:worker:worker1:heartbeat",
            "dramatiq:worker:worker2:heartbeat"
        ]
        
        current_time = datetime.now().timestamp()
        mock_redis_client.get.side_effect = [
            str(current_time - 30),  # worker1 heartbeat (30秒前)
            str(current_time - 90),  # worker2 heartbeat (90秒前)
            "0.5",  # worker1 load
            None    # worker2 load (不存在)
        ]
        
        mock_loop = Mock()
        mock_loop.run_in_executor.side_effect = [
            ["dramatiq:worker:worker1:heartbeat", "dramatiq:worker:worker2:heartbeat"],
            str(current_time - 30), "0.5",
            str(current_time - 90), None
        ]
        mock_get_loop.return_value = mock_loop
        
        collector = DashboardDramatiqCollector()
        collector.redis_client = mock_redis_client
        
        worker_metrics = await collector._collect_worker_metrics()
        
        assert 'worker_status' in worker_metrics
        assert 'worker_load' in worker_metrics
        assert 'worker1' in worker_metrics['worker_status']
        assert worker_metrics['worker_status']['worker1'] == 'active'
        assert worker_metrics['worker_load']['worker1'] == 0.5
    
    @pytest.mark.asyncio
    async def test_collect_worker_metrics_no_workers(self):
        """測試無 Worker 時收集指標"""
        mock_redis_client = Mock()
        mock_redis_client.keys.return_value = []
        
        collector = DashboardDramatiqCollector()
        collector.redis_client = mock_redis_client
        
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor.return_value = []
            mock_get_loop.return_value = mock_loop
            
            worker_metrics = await collector._collect_worker_metrics()
        
        assert worker_metrics['worker_status']['default'] == 'unknown'
        assert worker_metrics['worker_load']['default'] == 0.0
    
    @pytest.mark.asyncio
    async def test_collect_task_metrics(self):
        """測試收集任務指標"""
        collector = DashboardDramatiqCollector()
        
        task_metrics = await collector._collect_task_metrics()
        
        assert 'task_type_counts' in task_metrics
        assert 'avg_task_duration' in task_metrics
        assert 'task_success_rate' in task_metrics
        
        # 檢查預設任務類型
        assert 'eqc_workflow' in task_metrics['task_type_counts']
        assert 'product_search' in task_metrics['task_type_counts']
        assert 'csv_summary' in task_metrics['task_type_counts']
        assert 'code_comparison' in task_metrics['task_type_counts']
    
    @pytest.mark.asyncio
    async def test_health_check_with_redis(self):
        """測試有 Redis 連接的健康檢查"""
        mock_redis_client = Mock()
        mock_redis_client.ping.return_value = True
        
        collector = DashboardDramatiqCollector()
        collector.redis_client = mock_redis_client
        collector.last_collection_time = datetime.now()
        
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor.return_value = True
            mock_get_loop.return_value = mock_loop
            
            health = await collector.health_check()
        
        assert health['status'] == 'healthy'
        assert health['redis_connection'] == 'active'
        assert health['collector_type'] == 'dramatiq'
        assert 'last_collection' in health
    
    @pytest.mark.asyncio
    async def test_health_check_without_redis(self):
        """測試無 Redis 連接的健康檢查"""
        collector = DashboardDramatiqCollector()
        collector.redis_client = None
        
        health = await collector.health_check()
        
        assert health['status'] == 'degraded'
        assert health['redis_connection'] == 'unavailable'
        assert health['collector_type'] == 'dramatiq'
    
    @pytest.mark.asyncio
    async def test_health_check_exception(self):
        """測試健康檢查異常"""
        mock_redis_client = Mock()
        mock_redis_client.ping.side_effect = Exception("Health check failed")
        
        collector = DashboardDramatiqCollector()
        collector.redis_client = mock_redis_client
        
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor.side_effect = Exception("Health check failed")
            mock_get_loop.return_value = mock_loop
            
            health = await collector.health_check()
        
        assert health['status'] == 'unhealthy'
        assert 'error' in health
        assert health['collector_type'] == 'dramatiq'
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """測試清理資源"""
        mock_redis_client = Mock()
        
        collector = DashboardDramatiqCollector()
        collector.redis_client = mock_redis_client
        
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor.return_value = None
            mock_get_loop.return_value = mock_loop
            
            await collector.cleanup()
        
        # 驗證 close 方法被調用
        mock_loop.run_in_executor.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cleanup_exception(self):
        """測試清理時發生異常"""
        mock_redis_client = Mock()
        
        collector = DashboardDramatiqCollector()
        collector.redis_client = mock_redis_client
        
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor.side_effect = Exception("Cleanup failed")
            mock_get_loop.return_value = mock_loop
            
            # 應該不拋出異常
            await collector.cleanup()
    
    def test_get_cached_metrics(self):
        """測試獲取快取指標"""
        collector = DashboardDramatiqCollector()
        
        metrics = collector._get_cached_metrics()
        
        assert isinstance(metrics, TaskQueueMetrics)
        assert metrics.queue_type == "dramatiq"
        assert 'cached' in metrics.worker_status
    
    def test_get_fallback_metrics(self):
        """測試獲取降級指標"""
        collector = DashboardDramatiqCollector()
        
        metrics = collector._get_fallback_metrics()
        
        assert isinstance(metrics, TaskQueueMetrics)
        assert metrics.queue_type == "dramatiq"
        assert 'fallback' in metrics.worker_status


class TestCreateDramatiqCollector:
    """測試工廠函數"""
    
    def test_create_dramatiq_collector_default(self):
        """測試使用預設參數創建收集器"""
        collector = create_dramatiq_collector()
        
        assert isinstance(collector, DashboardDramatiqCollector)
        assert collector.redis_url == "redis://localhost:6379/0"
    
    def test_create_dramatiq_collector_custom_url(self):
        """測試使用自定義 URL 創建收集器"""
        custom_url = "redis://custom-host:6380/1"
        collector = create_dramatiq_collector(custom_url)
        
        assert isinstance(collector, DashboardDramatiqCollector)
        assert collector.redis_url == custom_url


@pytest.mark.integration
class TestDramatiqCollectorIntegration:
    """測試 Dramatiq 收集器整合功能"""
    
    @pytest.mark.asyncio
    @patch('redis.from_url')
    async def test_full_collection_cycle(self, mock_redis_from_url):
        """測試完整收集週期"""
        # 設置模擬 Redis 客戶端
        mock_redis_client = Mock()
        mock_redis_client.ping.return_value = True
        mock_redis_client.llen.return_value = 3
        mock_redis_client.scard.return_value = 1
        mock_redis_client.get.return_value = "50"
        mock_redis_client.keys.return_value = ["dramatiq:worker:test:heartbeat"]
        mock_redis_from_url.return_value = mock_redis_client
        
        # 創建收集器並初始化
        collector = DashboardDramatiqCollector()
        await collector.initialize()
        
        # 執行指標收集
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor.side_effect = [
                3, 1, 50, 0,  # 隊列指標
                3, 1, 50, 0,  # 重複4個隊列
                3, 1, 50, 0,
                3, 1, 50, 0,
                ["dramatiq:worker:test:heartbeat"],  # Worker keys
                str(datetime.now().timestamp()),     # Worker heartbeat
                "0.5"                               # Worker load
            ]
            mock_get_loop.return_value = mock_loop
            
            metrics = await collector.collect_metrics()
        
        # 驗證結果
        assert isinstance(metrics, TaskQueueMetrics)
        assert metrics.queue_type == "dramatiq"
        assert metrics.total_pending > 0
        assert metrics.total_active > 0
        
        # 執行健康檢查
        health = await collector.health_check()
        assert health['status'] == 'healthy'
        
        # 清理資源
        await collector.cleanup()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
