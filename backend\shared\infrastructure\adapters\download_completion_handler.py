"""
下載完成處理器 - 自動觸發 Worker 任務
當檔案下載/暫存完成後，自動加入 code_comparison.py 處理任務到 Dramatiq 工作佇列

🎯 功能：
  - 監控下載/暫存完成事件
  - 自動分析路徑結構 (d:\temp\{pd}\{mo})
  - 觸發 Dramatiq code comparison 任務
  - 支援重試和錯誤處理
  - 提供狀態追蹤和監控

🚀 使用方式：
  1. 下載完成後調用 handle_download_completion()
  2. 系統自動分析路徑並排程處理任務
  3. 後台 Dramatiq worker 執行 code_comparison.py
"""

import os
import re
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

import dramatiq
from loguru import logger

# 導入 Dramatiq 任務
from backend.tasks.services.dramatiq_tasks import run_code_comparison_task


class TriggerStatus(Enum):
    """觸發狀態"""
    PENDING = "pending"
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class TriggerReason(Enum):
    """觸發原因"""
    DOWNLOAD_COMPLETE = "download_complete"
    STAGING_COMPLETE = "staging_complete"
    MANUAL_TRIGGER = "manual_trigger"
    RETRY_TRIGGER = "retry_trigger"


@dataclass
class DownloadCompletionEvent:
    """下載完成事件"""
    event_id: str
    file_path: str
    vendor_code: Optional[str] = None
    mo_code: Optional[str] = None
    trigger_reason: TriggerReason = TriggerReason.DOWNLOAD_COMPLETE
    status: TriggerStatus = TriggerStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    queued_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    dramatiq_task_id: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    metadata: Dict[str, Any] = field(default_factory=dict)


class DownloadCompletionHandler:
    """下載完成處理器"""
    
    def __init__(self, 
                 enable_auto_trigger: bool = True,
                 max_concurrent_tasks: int = 3,
                 temp_path_pattern: str = r'd:\\temp\\([^\\]+)\\([^\\]+)',
                 min_wait_seconds: int = 5):
        """
        初始化下載完成處理器
        
        Args:
            enable_auto_trigger: 是否啟用自動觸發
            max_concurrent_tasks: 最大並發任務數
            temp_path_pattern: 臨時路徑模式 (用於提取 pd/mo)
            min_wait_seconds: 最小等待時間 (避免重複觸發)
        """
        self.enable_auto_trigger = enable_auto_trigger
        self.max_concurrent_tasks = max_concurrent_tasks
        self.temp_path_pattern = re.compile(temp_path_pattern, re.IGNORECASE)
        self.min_wait_seconds = min_wait_seconds
        
        # 事件追蹤
        self.events: Dict[str, DownloadCompletionEvent] = {}
        self.processing_events: set = set()
        
        # 統計資料
        self.stats = {
            'total_events': 0,
            'successful_triggers': 0,
            'failed_triggers': 0,
            'skipped_triggers': 0,
            'current_processing': 0
        }
        
        logger.info(f"✅ 下載完成處理器已初始化")
        logger.info(f"   自動觸發: {enable_auto_trigger}")
        logger.info(f"   最大並發: {max_concurrent_tasks}")
        logger.info(f"   路徑模式: {temp_path_pattern}")
    
    async def handle_download_completion(self,
                                       file_path: str,
                                       vendor_code: Optional[str] = None,
                                       mo_code: Optional[str] = None,
                                       trigger_reason: TriggerReason = TriggerReason.DOWNLOAD_COMPLETE,
                                       force_trigger: bool = False,
                                       metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        處理下載完成事件
        
        Args:
            file_path: 檔案路徑 (例: d:\temp\GTK123\MO456789)
            vendor_code: 廠商代碼 (可選，會自動從路徑解析)
            mo_code: MO代碼 (可選，會自動從路徑解析)
            trigger_reason: 觸發原因
            force_trigger: 強制觸發 (忽略重複檢查)
            metadata: 附加元數據
            
        Returns:
            事件ID
        """
        import uuid
        event_id = str(uuid.uuid4())
        
        logger.info(f"🎯 處理下載完成事件: {event_id}")
        logger.info(f"   檔案路徑: {file_path}")
        logger.info(f"   觸發原因: {trigger_reason.value}")
        
        try:
            # 解析路徑獲取 vendor_code 和 mo_code
            parsed_codes = self._parse_path_codes(file_path)
            vendor_code = vendor_code or parsed_codes.get('vendor_code')
            mo_code = mo_code or parsed_codes.get('mo_code')
            
            logger.info(f"   解析結果: vendor={vendor_code}, mo={mo_code}")
            
            # 創建事件
            event = DownloadCompletionEvent(
                event_id=event_id,
                file_path=file_path,
                vendor_code=vendor_code,
                mo_code=mo_code,
                trigger_reason=trigger_reason,
                metadata=metadata or {}
            )
            
            self.events[event_id] = event
            self.stats['total_events'] += 1
            
            # 檢查是否應該觸發
            should_trigger = await self._should_trigger_processing(event, force_trigger)
            
            if not should_trigger:
                event.status = TriggerStatus.SKIPPED
                self.stats['skipped_triggers'] += 1
                logger.info(f"⏭️ 跳過事件處理: {event_id}")
                return event_id
            
            # 觸發處理任務
            await self._trigger_code_comparison_task(event)
            
            return event_id
            
        except Exception as e:
            error_msg = f"處理下載完成事件失敗: {e}"
            logger.error(f"❌ {error_msg}")
            
            if event_id in self.events:
                self.events[event_id].status = TriggerStatus.FAILED
                self.events[event_id].error_message = error_msg
            
            self.stats['failed_triggers'] += 1
            raise
    
    def _parse_path_codes(self, file_path: str) -> Dict[str, Optional[str]]:
        """
        從檔案路徑解析廠商代碼和MO代碼
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            解析結果 {'vendor_code': str, 'mo_code': str}
        """
        try:
            # 標準化路徑
            normalized_path = str(Path(file_path)).replace('/', '\\')
            
            # 使用正則表達式匹配
            match = self.temp_path_pattern.search(normalized_path)
            
            if match:
                vendor_code = match.group(1)  # 第一個捕獲組
                mo_code = match.group(2)      # 第二個捕獲組
                
                logger.debug(f"路徑解析成功: {normalized_path} -> vendor={vendor_code}, mo={mo_code}")
                return {'vendor_code': vendor_code, 'mo_code': mo_code}
            else:
                logger.warning(f"路徑解析失敗，未匹配模式: {normalized_path}")
                return {'vendor_code': None, 'mo_code': None}
                
        except Exception as e:
            logger.error(f"路徑解析錯誤: {e}")
            return {'vendor_code': None, 'mo_code': None}
    
    async def _should_trigger_processing(self, event: DownloadCompletionEvent, force_trigger: bool) -> bool:
        """檢查是否應該觸發處理"""
        
        if not self.enable_auto_trigger and not force_trigger:
            logger.debug(f"自動觸發已停用，跳過事件: {event.event_id}")
            return False
        
        # 檢查檔案是否存在
        file_path = Path(event.file_path)
        if not file_path.exists():
            logger.warning(f"檔案不存在，跳過觸發: {event.file_path}")
            return False
        
        # 檢查並發限制
        if len(self.processing_events) >= self.max_concurrent_tasks:
            logger.info(f"達到最大並發限制 ({self.max_concurrent_tasks})，稍後重試")
            return False
        
        # 檢查重複處理 (基於路徑)
        if not force_trigger:
            recent_events = [
                e for e in self.events.values()
                if e.file_path == event.file_path
                and e.status in [TriggerStatus.QUEUED, TriggerStatus.PROCESSING]
                and (datetime.now() - e.created_at).total_seconds() < 300  # 5分鐘內
            ]
            
            if recent_events:
                logger.info(f"發現最近的處理事件，跳過重複觸發: {event.file_path}")
                return False
        
        return True
    
    async def _trigger_code_comparison_task(self, event: DownloadCompletionEvent):
        """觸發代碼比較任務 - 修復版：確保vendor files下載完成後才執行"""
        try:
            logger.info(f"🚀 觸發完整處理管道: {event.event_id}")
            logger.info(f"   路徑: {event.file_path}")

            # 🔧 修復：使用完整處理管道，確保執行順序
            from backend.tasks.pipeline_tasks import create_full_processing_pipeline

            # 🔧 修復：正確解析PD和廠商代碼
            # 從路徑中解析PD: D:\temp\{PD}\{MO} -> PD是第一個目錄名
            path_parts = Path(event.file_path).parts
            pd_from_path = None
            actual_vendor_code = None

            # 嘗試從路徑提取PD
            if len(path_parts) >= 3:  # D:\temp\{PD}\{MO}
                pd_from_path = path_parts[-2]  # 倒數第二個部分是PD
                logger.info(f"從路徑提取PD: {pd_from_path}")

            # 🔧 根據PD判斷實際廠商代碼
            if pd_from_path:
                # ETD廠商的PD通常包含特定模式
                if any(pattern in pd_from_path.upper() for pattern in ['G27', 'K(BA', 'TEMP']):
                    actual_vendor_code = 'ETD'
                elif 'GTK' in pd_from_path.upper():
                    actual_vendor_code = 'GTK'
                elif 'JCET' in pd_from_path.upper():
                    actual_vendor_code = 'JCET'
                else:
                    # 如果無法從PD判斷，使用原始的vendor_code
                    actual_vendor_code = event.vendor_code
            else:
                actual_vendor_code = event.vendor_code

            logger.info(f"廠商代碼修正: {event.vendor_code} -> {actual_vendor_code}, PD: {pd_from_path}")

            # 準備vendor files數據
            vendor_files = [{
                'vendor_code': actual_vendor_code,  # 🔧 使用修正後的廠商代碼
                'mo': event.mo_code,
                'temp_path': event.file_path,
                'pd': pd_from_path or event.vendor_code,  # 🔧 使用從路徑提取的PD
                'lot': 'default',
                'email_subject': f'Download completion for {actual_vendor_code}/{event.mo_code}',
                'email_body': f'Files downloaded to {event.file_path}'
            }]

            # 管道上下文
            pipeline_context = {
                'pipeline_id': 'download_completion',
                'event_id': event.event_id,
                'trigger_reason': event.trigger_reason.value,
                'triggered_at': datetime.now().isoformat(),
                'triggered_by': 'download_completion_handler'
            }

            # 更新事件狀態
            event.status = TriggerStatus.QUEUED
            event.queued_at = datetime.now()
            self.processing_events.add(event.event_id)

            # 🔧 關鍵修復：創建序列化管道，確保vendor files處理完成後才執行code_comparison
            logger.info(f"📤 創建完整處理管道 (vendor files -> code comparison)...")

            # 🔧 額外檢查：確保pipeline_tasks模組已正確導入
            try:
                pipeline_id = create_full_processing_pipeline(
                    vendor_files=vendor_files,
                    include_code_comparison=True,
                    pipeline_context=pipeline_context
                )
                logger.info(f"✅ 管道創建成功: {pipeline_id}")
            except Exception as pipeline_error:
                logger.error(f"❌ 管道創建失敗: {pipeline_error}")
                # 降級到單獨的vendor files處理
                from backend.tasks.pipeline_tasks import process_vendor_files_task
                message = process_vendor_files_task.send(vendor_files[0])
                pipeline_id = message.message_id if hasattr(message, 'message_id') else 'fallback_task'
                logger.warning(f"⚠️ 降級到單獨任務: {pipeline_id}")

            # 儲存管道ID
            event.dramatiq_task_id = pipeline_id
            logger.info(f"✅ 完整處理管道已排程: {pipeline_id}")

            # 更新統計
            self.stats['successful_triggers'] += 1
            self.stats['current_processing'] = len(self.processing_events)

            logger.success(f"🎉 完整處理管道觸發成功: {event.event_id}")
            
        except Exception as e:
            error_msg = f"觸發代碼比較任務失敗: {e}"
            logger.error(f"❌ {error_msg}")
            
            # 更新事件狀態
            event.status = TriggerStatus.FAILED
            event.error_message = error_msg
            self.processing_events.discard(event.event_id)
            
            # 檢查重試
            if event.retry_count < event.max_retries:
                event.retry_count += 1
                logger.info(f"🔄 安排重試 ({event.retry_count}/{event.max_retries})")
                # 這裡可以安排延遲重試
            
            raise
    
    async def handle_task_completion(self, event_id: str, success: bool, result: Optional[Dict[str, Any]] = None):
        """處理任務完成回調"""
        if event_id not in self.events:
            logger.warning(f"收到未知事件的完成回調: {event_id}")
            return
        
        event = self.events[event_id]
        event.completed_at = datetime.now()
        event.status = TriggerStatus.COMPLETED if success else TriggerStatus.FAILED
        
        if result:
            event.metadata['completion_result'] = result
        
        # 從處理中事件移除
        self.processing_events.discard(event_id)
        self.stats['current_processing'] = len(self.processing_events)
        
        processing_time = (event.completed_at - event.created_at).total_seconds()
        status_msg = "完成" if success else "失敗"
        
        logger.info(f"🏁 事件處理{status_msg}: {event_id} (耗時: {processing_time:.2f}秒)")
    
    def get_event_status(self, event_id: str) -> Optional[Dict[str, Any]]:
        """獲取事件狀態"""
        if event_id not in self.events:
            return None
        
        event = self.events[event_id]
        return {
            'event_id': event.event_id,
            'file_path': event.file_path,
            'vendor_code': event.vendor_code,
            'mo_code': event.mo_code,
            'status': event.status.value,
            'trigger_reason': event.trigger_reason.value,
            'created_at': event.created_at.isoformat(),
            'queued_at': event.queued_at.isoformat() if event.queued_at else None,
            'completed_at': event.completed_at.isoformat() if event.completed_at else None,
            'dramatiq_task_id': event.dramatiq_task_id,
            'error_message': event.error_message,
            'retry_count': event.retry_count,
            'metadata': event.metadata
        }
    
    def list_recent_events(self, hours: int = 24) -> List[Dict[str, Any]]:
        """列出最近事件"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_events = [
            event for event in self.events.values()
            if event.created_at >= cutoff_time
        ]
        
        # 按時間排序 (最新的在前)
        recent_events.sort(key=lambda e: e.created_at, reverse=True)
        
        return [self.get_event_status(event.event_id) for event in recent_events]
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計資料"""
        return {
            **self.stats,
            'total_tracked_events': len(self.events),
            'enable_auto_trigger': self.enable_auto_trigger,
            'max_concurrent_tasks': self.max_concurrent_tasks,
            'processing_events_count': len(self.processing_events),
            'success_rate': (
                self.stats['successful_triggers'] / max(1, self.stats['total_events']) * 100
            )
        }
    
    def cleanup_old_events(self, days: int = 7):
        """清理舊事件記錄"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        events_to_remove = [
            event_id for event_id, event in self.events.items()
            if event.created_at < cutoff_time
            and event.status in [TriggerStatus.COMPLETED, TriggerStatus.FAILED, TriggerStatus.SKIPPED]
        ]
        
        for event_id in events_to_remove:
            del self.events[event_id]
        
        logger.info(f"🧹 清理了 {len(events_to_remove)} 個舊事件記錄")


# 全局實例
_download_completion_handler: Optional[DownloadCompletionHandler] = None


def get_download_completion_handler() -> DownloadCompletionHandler:
    """獲取下載完成處理器實例"""
    global _download_completion_handler
    if _download_completion_handler is None:
        # 從環境變數讀取配置
        enable_auto = os.getenv('ENABLE_AUTO_CODE_COMPARISON', 'true').lower() == 'true'
        max_concurrent = int(os.getenv('MAX_CONCURRENT_CODE_TASKS', '3'))
        
        _download_completion_handler = DownloadCompletionHandler(
            enable_auto_trigger=enable_auto,
            max_concurrent_tasks=max_concurrent
        )
    
    return _download_completion_handler


# 便捷函數
async def trigger_code_comparison_from_path(file_path: str, 
                                          vendor_code: str = None,
                                          mo_code: str = None,
                                          force: bool = False) -> str:
    """
    便捷函數：從路徑觸發代碼比較
    
    Args:
        file_path: 檔案路徑 (例: d:\temp\GTK123\MO456789)
        vendor_code: 廠商代碼 (可選)
        mo_code: MO代碼 (可選)
        force: 強制觸發
        
    Returns:
        事件ID
    """
    handler = get_download_completion_handler()
    return await handler.handle_download_completion(
        file_path=file_path,
        vendor_code=vendor_code,
        mo_code=mo_code,
        force_trigger=force,
        trigger_reason=TriggerReason.MANUAL_TRIGGER
    )