"""
增強後服務的測試驗證
測試並發安全性、錯誤處理、效能優化和監控診斷功能
"""

import asyncio
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import time

from backend.shared.infrastructure.adapters.file_staging_service import (
    FileStagingService, 
    StagingStatus,
    StagingError,
    StagingTimeoutError,
    TaskCancellationError
)
from backend.shared.infrastructure.adapters.processing import (
    FileProcessingService,
    ProcessingStatus,
    ProcessingTool,
    RetryConfig,
    ProcessingError
)


class TestFileStagingServiceEnhancements:
    """測試檔案暫存服務的增強功能"""
    
    @pytest.fixture
    def temp_dir(self):
        """建立臨時目錄"""
        temp_path = tempfile.mkdtemp()
        yield Path(temp_path)
        shutil.rmtree(temp_path, ignore_errors=True)
    
    @pytest.fixture
    def staging_service(self, temp_dir):
        """建立暫存服務實例"""
        return FileStagingService(
            base_staging_path=str(temp_dir / "staging"),
            max_workers=2,
            chunk_size=1024,  # 小區塊用於測試
            enable_progress_batching=True,
            progress_update_interval=5
        )
    
    @pytest.fixture
    def test_files(self, temp_dir):
        """建立測試檔案"""
        files = []
        for i in range(3):
            file_path = temp_dir / f"test_{i}.txt"
            file_path.write_text(f"測試內容 {i}" * 100)  # 建立一些內容
            files.append(file_path)
        return files
    
    def test_concurrent_safety_file_locking(self, staging_service, test_files):
        """測試檔案鎖定機制"""
        async def run_test():
            # 建立任務
            task_id = staging_service.create_staging_task(
                product_name="concurrent_test",
                source_files=test_files
            )
            
            # 執行任務
            result = await staging_service.execute_staging_task(task_id)
            assert result.success
            
            # 驗證檔案已正確複製
            assert len(result.staged_files) == len(test_files)
            for staged_file in result.staged_files:
                assert Path(staged_file).exists()
        
        asyncio.run(run_test())
    
    def test_progress_batching(self, staging_service, test_files):
        """測試進度批次更新"""
        async def run_test():
            task_id = staging_service.create_staging_task(
                product_name="progress_test",
                source_files=test_files
            )
            
            # 監控進度更新
            progress_updates = []
            
            async def monitor_progress():
                while True:
                    progress = staging_service.get_task_progress(task_id)
                    if progress.get("error"):
                        break
                    progress_updates.append(progress["progress"])
                    if progress["status"] in ["completed", "failed"]:
                        break
                    await asyncio.sleep(0.1)
            
            # 同時執行任務和監控
            monitor_task = asyncio.create_task(monitor_progress())
            result = await staging_service.execute_staging_task(task_id)
            monitor_task.cancel()
            
            assert result.success
            # 驗證進度批次更新減少了更新次數
            assert len(progress_updates) < 50  # 應該比沒有批次更新少很多
        
        asyncio.run(run_test())
    
    def test_task_cancellation(self, staging_service, test_files):
        """測試任務取消功能"""
        async def run_test():
            # 建立大量檔案以確保任務執行時間足夠長
            large_files = []
            for i in range(10):
                file_path = test_files[0].parent / f"large_{i}.txt"
                file_path.write_text("大檔案內容" * 1000)
                large_files.append(file_path)
            
            task_id = staging_service.create_staging_task(
                product_name="cancel_test",
                source_files=large_files
            )
            
            # 開始執行任務
            execution_task = asyncio.create_task(
                staging_service.execute_staging_task(task_id)
            )
            
            # 等待任務開始
            await asyncio.sleep(0.5)
            
            # 取消任務
            cancelled = await staging_service.cancel_task(task_id)
            assert cancelled
            
            # 等待執行任務完成
            result = await execution_task
            assert not result.success
            assert "取消" in result.error_message
        
        asyncio.run(run_test())
    
    def test_timeout_handling(self, staging_service, test_files):
        """測試超時處理"""
        async def run_test():
            # 建立帶超時的任務
            task_id = staging_service.create_staging_task(
                product_name="timeout_test",
                source_files=test_files,
                timeout=0.1  # 很短的超時時間
            )
            
            # 執行任務（應該超時）
            result = await staging_service.execute_staging_task(task_id)
            
            # 驗證任務因超時而失敗
            task = staging_service.get_task_status(task_id)
            assert task.status == StagingStatus.CANCELLED  # 超時會觸發取消
        
        asyncio.run(run_test())
    
    def test_concurrent_task_limit(self, staging_service, test_files):
        """測試並發任務限制"""
        async def run_test():
            # 建立多個任務
            task_ids = []
            for i in range(5):  # 超過 max_concurrent_tasks
                task_id = staging_service.create_staging_task(
                    product_name=f"concurrent_{i}",
                    source_files=[test_files[0]]
                )
                task_ids.append(task_id)
            
            # 嘗試同時執行所有任務
            tasks = [
                staging_service.execute_staging_task(task_id) 
                for task_id in task_ids
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 應該有一些任務因並發限制而失敗
            successful_count = sum(1 for r in results if isinstance(r, type(results[0])) and r.success)
            error_count = sum(1 for r in results if isinstance(r, Exception))
            
            # 至少應該有一些任務成功
            assert successful_count > 0
        
        asyncio.run(run_test())
    
    def test_service_statistics(self, staging_service, test_files):
        """測試服務統計功能"""
        async def run_test():
            # 執行一些任務
            task_id1 = staging_service.create_staging_task(
                product_name="stats_test_1",
                source_files=[test_files[0]]
            )
            result1 = await staging_service.execute_staging_task(task_id1)
            
            task_id2 = staging_service.create_staging_task(
                product_name="stats_test_2",
                source_files=[test_files[1]]
            )
            result2 = await staging_service.execute_staging_task(task_id2)
            
            # 獲取統計資料
            stats = staging_service.get_service_statistics()
            
            assert stats["total_tasks"] >= 2
            assert stats["completed_tasks"] >= 2
            assert stats["success_rate"] > 0
            assert "total_data_processed_mb" in stats
        
        asyncio.run(run_test())


class TestFileProcessingServiceEnhancements:
    """測試檔案處理服務的增強功能"""
    
    @pytest.fixture
    def temp_dir(self):
        """建立臨時目錄"""
        temp_path = tempfile.mkdtemp()
        yield Path(temp_path)
        shutil.rmtree(temp_path, ignore_errors=True)
    
    @pytest.fixture
    def processing_service(self):
        """建立處理服務實例"""
        return FileProcessingService(
            default_timeout=10.0,
            max_concurrent_tasks=2,
            enable_rollback=True,
            default_retry_config=RetryConfig(
                max_retries=2,
                initial_delay=0.5,
                exponential_base=1.5
            )
        )
    
    @pytest.fixture
    def test_csv_file(self, temp_dir):
        """建立測試 CSV 檔案"""
        csv_file = temp_dir / "test.csv"
        csv_file.write_text("Name,Age,City\nJohn,25,New York\nJane,30,London")
        return csv_file
    
    def test_retry_mechanism(self, processing_service, test_csv_file):
        """測試重試機制"""
        async def run_test():
            # 模擬工具執行失敗
            with patch('subprocess.run') as mock_run:
                # 前兩次失敗，第三次成功
                mock_run.side_effect = [
                    Mock(returncode=1, stderr="模擬錯誤"),
                    Mock(returncode=1, stderr="模擬錯誤"),
                    Mock(returncode=0, stdout="成功")
                ]
                
                task_id = processing_service.create_task(
                    ProcessingTool.CSV_SUMMARY,
                    str(test_csv_file),
                    retry_config=RetryConfig(max_retries=3, initial_delay=0.1)
                )
                
                result = await processing_service.execute_csv_summary(
                    str(test_csv_file), 
                    task_id
                )
                
                # 驗證重試機制運作
                task = processing_service.get_task_status(task_id)
                assert task.retry_count > 0
        
        asyncio.run(run_test())
    
    def test_timeout_handling(self, processing_service, test_csv_file):
        """測試超時處理"""
        async def run_test():
            # 建立帶短超時的任務
            task_id = processing_service.create_task(
                ProcessingTool.CSV_SUMMARY,
                str(test_csv_file),
                timeout=0.1  # 很短的超時
            )
            
            # 模擬長時間執行
            with patch('asyncio.create_subprocess_exec') as mock_exec:
                async def slow_process(*args, **kwargs):
                    process = Mock()
                    process.pid = 12345
                    process.communicate = Mock(return_value=asyncio.sleep(5))  # 模擬長時間執行
                    return process
                
                mock_exec.side_effect = slow_process
                
                result = await processing_service.execute_csv_summary(
                    str(test_csv_file),
                    task_id
                )
                
                # 驗證任務因超時而失敗
                task = processing_service.get_task_status(task_id)
                assert task.status in [ProcessingStatus.TIMEOUT, ProcessingStatus.CANCELLED]
        
        asyncio.run(run_test())
    
    def test_task_cancellation(self, processing_service, test_csv_file):
        """測試任務取消"""
        async def run_test():
            task_id = processing_service.create_task(
                ProcessingTool.CSV_SUMMARY,
                str(test_csv_file)
            )
            
            # 模擬長時間執行的進程
            with patch('asyncio.create_subprocess_exec') as mock_exec:
                process = Mock()
                process.pid = 12345
                process.communicate = Mock(side_effect=lambda: asyncio.sleep(10))
                process.terminate = Mock()
                process.wait = Mock(return_value=asyncio.sleep(0.1))
                mock_exec.return_value = process
                
                # 開始執行任務
                execution_task = asyncio.create_task(
                    processing_service.execute_csv_summary(str(test_csv_file), task_id)
                )
                
                # 等待任務開始
                await asyncio.sleep(0.5)
                
                # 取消任務
                cancelled = await processing_service.cancel_task(task_id)
                assert cancelled
                
                # 驗證進程被終止
                process.terminate.assert_called_once()
                
                # 等待執行完成
                result = await execution_task
                assert not result.success
        
        asyncio.run(run_test())
    
    def test_rollback_mechanism(self, processing_service, test_csv_file):
        """測試回滾機制"""
        async def run_test():
            task_id = processing_service.create_task(
                ProcessingTool.CSV_SUMMARY,
                str(test_csv_file)
            )
            
            # 模擬任務失敗
            with patch('asyncio.create_subprocess_exec') as mock_exec:
                process = Mock()
                process.pid = 12345
                process.returncode = 1
                process.communicate = Mock(return_value=("", "模擬失敗"))
                mock_exec.return_value = process
                
                result = await processing_service.execute_csv_summary(
                    str(test_csv_file),
                    task_id
                )
                
                assert not result.success
                assert result.rollback_performed
                
                # 驗證回滾已完成
                task = processing_service.get_task_status(task_id)
                assert task.rollback_completed
        
        asyncio.run(run_test())
    
    def test_concurrent_task_management(self, processing_service, test_csv_file):
        """測試並發任務管理"""
        async def run_test():
            # 建立多個任務
            task_ids = []
            for i in range(5):  # 超過 max_concurrent_tasks
                task_id = processing_service.create_task(
                    ProcessingTool.CSV_SUMMARY,
                    str(test_csv_file)
                )
                task_ids.append(task_id)
            
            # 嘗試同時執行
            tasks = []
            for task_id in task_ids:
                tasks.append(
                    processing_service.execute_csv_summary(str(test_csv_file), task_id)
                )
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 驗證並發控制正常運作
            success_count = sum(1 for r in results if not isinstance(r, Exception) and r.success)
            error_count = sum(1 for r in results if isinstance(r, Exception))
            
            # 應該有一些任務成功執行
            assert success_count > 0
        
        asyncio.run(run_test())
    
    def test_service_statistics(self, processing_service, test_csv_file):
        """測試服務統計功能"""
        async def run_test():
            # 執行一些任務
            for i in range(3):
                task_id = processing_service.create_task(
                    ProcessingTool.CSV_SUMMARY,
                    str(test_csv_file)
                )
                await processing_service.execute_csv_summary(str(test_csv_file), task_id)
            
            # 獲取統計資料
            stats = processing_service.get_service_statistics()
            
            assert stats["total_tasks"] >= 3
            assert "status_counts" in stats
            assert "success_rate" in stats
            assert "average_execution_time" in stats
            assert stats["rollback_enabled"] == True
        
        asyncio.run(run_test())


class TestIntegrationScenarios:
    """整合測試場景"""
    
    @pytest.fixture
    def temp_dir(self):
        temp_path = tempfile.mkdtemp()
        yield Path(temp_path)
        shutil.rmtree(temp_path, ignore_errors=True)
    
    @pytest.fixture
    def services(self, temp_dir):
        staging_service = FileStagingService(
            base_staging_path=str(temp_dir / "staging"),
            max_workers=2
        )
        processing_service = FileProcessingService(
            max_concurrent_tasks=2,
            enable_rollback=True
        )
        return staging_service, processing_service
    
    @pytest.fixture
    def test_files(self, temp_dir):
        files = []
        for i in range(3):
            file_path = temp_dir / f"data_{i}.csv"
            file_path.write_text(f"Name,Value\nTest{i},{i * 10}")
            files.append(file_path)
        return files
    
    def test_end_to_end_workflow(self, services, test_files):
        """測試端到端工作流程"""
        staging_service, processing_service = services
        
        async def run_test():
            # 1. 建立帶暫存的處理任務
            task_id = processing_service.create_task_with_staging(
                tool=ProcessingTool.CSV_SUMMARY,
                source_files=test_files,
                product_name="e2e_test"
            )
            
            # 2. 執行任務
            result = await processing_service.execute_with_staging(
                task_id=task_id,
                source_files=test_files,
                product_name="e2e_test"
            )
            
            # 3. 驗證結果
            task = processing_service.get_task_status(task_id)
            assert task.status == ProcessingStatus.COMPLETED
            assert task.staging_task_id is not None
            
            # 4. 檢查暫存服務統計
            staging_stats = staging_service.get_service_statistics()
            assert staging_stats["total_tasks"] >= 1
            
            # 5. 檢查處理服務統計
            processing_stats = processing_service.get_service_statistics()
            assert processing_stats["total_tasks"] >= 1
        
        asyncio.run(run_test())
    
    def test_failure_recovery_workflow(self, services, test_files):
        """測試失敗恢復工作流程"""
        staging_service, processing_service = services
        
        async def run_test():
            # 建立任務
            task_id = processing_service.create_task_with_staging(
                tool=ProcessingTool.CSV_SUMMARY,
                source_files=test_files,
                product_name="recovery_test",
                retry_config=RetryConfig(max_retries=2, initial_delay=0.1)
            )
            
            # 模擬工具執行失敗然後成功
            with patch('asyncio.create_subprocess_exec') as mock_exec:
                call_count = 0
                
                async def mock_process(*args, **kwargs):
                    nonlocal call_count
                    call_count += 1
                    
                    process = Mock()
                    process.pid = 12345
                    
                    if call_count <= 1:
                        # 第一次失敗
                        process.returncode = 1
                        process.communicate = Mock(return_value=("", "模擬失敗"))
                    else:
                        # 第二次成功
                        process.returncode = 0
                        process.communicate = Mock(return_value=("成功", ""))
                    
                    return process
                
                mock_exec.side_effect = mock_process
                
                result = await processing_service.execute_with_staging(
                    task_id=task_id,
                    source_files=test_files,
                    product_name="recovery_test"
                )
                
                # 驗證重試機制運作
                task = processing_service.get_task_status(task_id)
                assert task.retry_count > 0
                
                # 最終應該成功
                assert result.success or task.status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED]
        
        asyncio.run(run_test())


if __name__ == "__main__":
    # 執行特定測試
    pytest.main([__file__, "-v"])