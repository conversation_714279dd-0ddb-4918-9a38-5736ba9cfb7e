"""
郵件監控收集器整合測試

測試 DashboardEmailCollector 與現有系統的整合：
1. 與 EmailDatabase 的整合
2. 與 ConcurrentTaskManager 的整合
3. 與資料庫模型的整合
4. 端到端的指標收集流程
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import patch

from backend.monitoring.collectors.dashboard_email_collector import (
    DashboardEmailCollector,
    get_email_collector,
    collect_email_metrics
)
from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
from backend.shared.infrastructure.database.models import EmailDB, EmailProcessStatusDB, db_engine
from backend.email.models.email_models import EmailData
from backend.tasks.services.concurrent_task_manager import get_task_manager


class TestDashboardEmailCollectorIntegration:
    """郵件監控收集器整合測試類"""
    
    @pytest.fixture(autouse=True)
    def setup_test_database(self):
        """設置測試資料庫"""
        # 使用臨時資料庫檔案
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # 設置測試資料庫 URL
        test_db_url = f"sqlite:///{self.temp_db.name}"
        
        # 初始化測試資料庫
        self.email_db = EmailDatabase(test_db_url)
        
        yield
        
        # 清理測試資料庫
        try:
            os.unlink(self.temp_db.name)
        except OSError:
            pass
    
    def create_test_email_data(self, subject: str, sender: str, vendor_code: str = None) -> EmailData:
        """創建測試郵件資料"""
        return EmailData(
            message_id=f"test-{hash(subject + sender)}@test.com",
            sender=sender,
            subject=subject,
            body=f"Test email body for {subject}",
            received_time=datetime.now(),
            attachments=[]
        )
    
    def create_test_process_status(self, email_id: int, step_name: str, status: str):
        """創建測試處理狀態"""
        with self.email_db.get_session() as session:
            process_status = EmailProcessStatusDB(
                email_id=email_id,
                step_name=step_name,
                status=status,
                started_at=datetime.now() - timedelta(minutes=5) if status != 'pending' else None,
                completed_at=datetime.now() if status == 'completed' else None,
                progress_percentage=100 if status == 'completed' else 50 if status == 'processing' else 0
            )
            session.add(process_status)
            session.commit()
            return process_status.id
    
    @pytest.mark.asyncio
    async def test_collect_queue_metrics_with_real_database(self):
        """測試與真實資料庫的佇列指標收集"""
        collector = DashboardEmailCollector()
        collector.email_db = self.email_db  # 使用測試資料庫
        
        # 創建測試資料
        # 1. 待處理郵件
        pending_email = self.create_test_email_data("Pending Email", "<EMAIL>")
        pending_id = self.email_db.save_email(pending_email)
        
        # 2. 處理中郵件
        processing_email = self.create_test_email_data("Processing Email", "<EMAIL>")
        processing_id = self.email_db.save_email(processing_email)
        self.create_test_process_status(processing_id, "code_detection", "processing")
        
        # 3. 已完成郵件
        completed_email = self.create_test_email_data("Completed Email", "<EMAIL>")
        completed_id = self.email_db.save_email(completed_email)
        self.create_test_process_status(completed_id, "report_generation", "completed")
        
        # 4. 失敗郵件
        failed_email = self.create_test_email_data("Failed Email", "<EMAIL>")
        failed_id = self.email_db.save_email(failed_email)
        self.create_test_process_status(failed_id, "dual_search", "failed")
        
        # 執行測試
        result = await collector._collect_queue_metrics()
        
        # 驗證結果
        assert result['pending_count'] >= 1  # 至少有一個待處理郵件
        assert result['processing_count'] >= 1  # 至少有一個處理中郵件
        assert result['completed_count'] >= 1  # 至少有一個已完成郵件
        assert result['failed_count'] >= 1  # 至少有一個失敗郵件
    
    @pytest.mark.asyncio
    async def test_collect_performance_metrics_with_real_database(self):
        """測試與真實資料庫的效能指標收集"""
        collector = DashboardEmailCollector()
        collector.email_db = self.email_db  # 使用測試資料庫
        
        # 創建測試資料 - 已完成的處理記錄
        email = self.create_test_email_data("Performance Test Email", "<EMAIL>")
        email_id = self.email_db.save_email(email)
        
        # 創建處理狀態記錄（模擬不同的處理時間）
        with self.email_db.get_session() as session:
            # 快速處理的任務
            fast_process = EmailProcessStatusDB(
                email_id=email_id,
                step_name="fast_step",
                status="completed",
                started_at=datetime.now() - timedelta(minutes=2),
                completed_at=datetime.now() - timedelta(minutes=1),
                progress_percentage=100
            )
            session.add(fast_process)
            
            # 慢速處理的任務
            slow_process = EmailProcessStatusDB(
                email_id=email_id,
                step_name="slow_step",
                status="completed",
                started_at=datetime.now() - timedelta(minutes=10),
                completed_at=datetime.now() - timedelta(minutes=5),
                progress_percentage=100
            )
            session.add(slow_process)
            
            session.commit()
        
        # 執行測試
        result = await collector._collect_performance_metrics()
        
        # 驗證結果
        assert 'avg_processing_time_seconds' in result
        assert 'throughput_per_hour' in result
        assert result['avg_processing_time_seconds'] >= 0
        assert result['throughput_per_hour'] >= 0
    
    @pytest.mark.asyncio
    async def test_collect_vendor_metrics_with_real_database(self):
        """測試與真實資料庫的廠商指標收集"""
        collector = DashboardEmailCollector()
        collector.email_db = self.email_db  # 使用測試資料庫
        
        # 創建不同廠商的測試郵件
        # GTK 廠商郵件
        gtk_email1 = self.create_test_email_data("GTK ft hold test", "<EMAIL>")
        gtk_email2 = self.create_test_email_data("GTK ft lot test", "<EMAIL>")
        
        # JCET 廠商郵件
        jcet_email = self.create_test_email_data("JCET test email", "<EMAIL>")
        
        # 儲存郵件並設置 vendor_code
        gtk_id1 = self.email_db.save_email(gtk_email1)
        gtk_id2 = self.email_db.save_email(gtk_email2)
        jcet_id = self.email_db.save_email(jcet_email)
        
        # 更新 vendor_code
        with self.email_db.get_session() as session:
            session.query(EmailDB).filter_by(id=gtk_id1).update({'vendor_code': 'GTK', 'is_processed': True})
            session.query(EmailDB).filter_by(id=gtk_id2).update({'vendor_code': 'GTK', 'is_processed': False})
            session.query(EmailDB).filter_by(id=jcet_id).update({'vendor_code': 'JCET', 'is_processed': True})
            session.commit()
        
        # 執行測試
        result = await collector._collect_vendor_metrics()
        
        # 驗證結果
        assert 'vendor_queue_counts' in result
        assert 'vendor_success_rates' in result
        
        # 檢查是否有廠商資料
        if result['vendor_queue_counts']:
            assert isinstance(result['vendor_queue_counts'], dict)
            # GTK 應該有一個待處理郵件
            if 'GTK' in result['vendor_queue_counts']:
                assert result['vendor_queue_counts']['GTK'] >= 0
        
        if result['vendor_success_rates']:
            assert isinstance(result['vendor_success_rates'], dict)
            # 成功率應該在 0-1 之間
            for vendor, rate in result['vendor_success_rates'].items():
                assert 0 <= rate <= 1
    
    @pytest.mark.asyncio
    async def test_collect_code_comparison_metrics_with_task_manager(self):
        """測試與任務管理器的 code_comparison 指標收集"""
        collector = DashboardEmailCollector()
        
        # 獲取任務管理器實例
        task_manager = get_task_manager()
        collector.task_manager = task_manager
        
        # 提交一些測試任務（如果可能的話）
        try:
            # 提交 code_comparison 任務
            task_id1 = task_manager.submit_task('code_comparison', {'test': 'data1'})
            task_id2 = task_manager.submit_task('code_comparison', {'test': 'data2'})
            
            # 等待一小段時間讓任務開始執行
            await asyncio.sleep(0.1)
            
            # 執行測試
            result = await collector._collect_code_comparison_metrics()
            
            # 驗證結果
            assert 'code_comparison_active' in result
            assert 'code_comparison_pending' in result
            assert 'code_comparison_avg_duration' in result
            
            assert result['code_comparison_active'] >= 0
            assert result['code_comparison_pending'] >= 0
            assert result['code_comparison_avg_duration'] >= 0
            
        except Exception as e:
            # 如果任務提交失敗（例如沒有註冊處理器），跳過這個測試
            pytest.skip(f"無法提交測試任務: {e}")
    
    @pytest.mark.asyncio
    async def test_full_metrics_collection_integration(self):
        """測試完整的指標收集整合流程"""
        collector = DashboardEmailCollector()
        collector.email_db = self.email_db  # 使用測試資料庫
        
        # 創建綜合測試資料
        # 1. 不同狀態的郵件
        emails_data = [
            ("Pending Email 1", "<EMAIL>", None, False),
            ("Processing Email 1", "<EMAIL>", "processing", False),
            ("Completed Email 1", "<EMAIL>", "completed", True),
            ("Failed Email 1", "<EMAIL>", "failed", False),
            ("GTK Email", "<EMAIL>", "completed", True),
            ("JCET Email", "<EMAIL>", "processing", False),
        ]
        
        for subject, sender, process_status, is_processed in emails_data:
            email_data = self.create_test_email_data(subject, sender)
            email_id = self.email_db.save_email(email_data)
            
            # 更新處理狀態
            with self.email_db.get_session() as session:
                session.query(EmailDB).filter_by(id=email_id).update({
                    'is_processed': is_processed,
                    'vendor_code': 'GTK' if 'GTK' in subject else 'JCET' if 'JCET' in subject else None
                })
                session.commit()
            
            # 創建處理狀態記錄
            if process_status:
                self.create_test_process_status(email_id, "test_step", process_status)
        
        # 執行完整的指標收集
        metrics = await collector.collect_metrics()
        
        # 驗證結果結構
        assert hasattr(metrics, 'pending_count')
        assert hasattr(metrics, 'processing_count')
        assert hasattr(metrics, 'completed_count')
        assert hasattr(metrics, 'failed_count')
        assert hasattr(metrics, 'avg_processing_time_seconds')
        assert hasattr(metrics, 'throughput_per_hour')
        assert hasattr(metrics, 'vendor_queue_counts')
        assert hasattr(metrics, 'vendor_success_rates')
        assert hasattr(metrics, 'code_comparison_active')
        assert hasattr(metrics, 'code_comparison_pending')
        assert hasattr(metrics, 'code_comparison_avg_duration')
        
        # 驗證資料類型
        assert isinstance(metrics.pending_count, int)
        assert isinstance(metrics.processing_count, int)
        assert isinstance(metrics.completed_count, int)
        assert isinstance(metrics.failed_count, int)
        assert isinstance(metrics.avg_processing_time_seconds, float)
        assert isinstance(metrics.throughput_per_hour, float)
        assert isinstance(metrics.vendor_queue_counts, dict)
        assert isinstance(metrics.vendor_success_rates, dict)
        assert isinstance(metrics.code_comparison_active, int)
        assert isinstance(metrics.code_comparison_pending, int)
        assert isinstance(metrics.code_comparison_avg_duration, float)
        
        # 驗證邏輯合理性
        assert metrics.pending_count >= 0
        assert metrics.processing_count >= 0
        assert metrics.completed_count >= 0
        assert metrics.failed_count >= 0
        assert metrics.avg_processing_time_seconds >= 0
        assert metrics.throughput_per_hour >= 0
        assert metrics.code_comparison_active >= 0
        assert metrics.code_comparison_pending >= 0
        assert metrics.code_comparison_avg_duration >= 0
    
    def test_health_status_integration(self):
        """測試健康狀態檢查整合"""
        collector = DashboardEmailCollector()
        collector.email_db = self.email_db  # 使用測試資料庫
        
        # 創建一些測試資料
        email_data = self.create_test_email_data("Health Check Email", "<EMAIL>")
        self.email_db.save_email(email_data)
        
        # 執行健康檢查
        health_status = collector.get_health_status()
        
        # 驗證結果
        assert 'status' in health_status
        assert 'database_connection' in health_status
        assert 'task_manager_connection' in health_status
        assert 'total_emails' in health_status
        assert 'total_tasks' in health_status
        assert 'last_check' in health_status
        
        # 應該是健康狀態
        assert health_status['status'] == 'healthy'
        assert health_status['database_connection'] == 'ok'
        assert health_status['task_manager_connection'] == 'ok'
        assert health_status['total_emails'] >= 1  # 至少有我們創建的測試郵件
    
    @pytest.mark.asyncio
    async def test_error_resilience_integration(self):
        """測試錯誤恢復能力整合"""
        collector = DashboardEmailCollector()
        
        # 測試資料庫連接失敗的情況
        with patch.object(collector.email_db, 'get_session', side_effect=Exception("Database connection failed")):
            metrics = await collector.collect_metrics()
            
            # 應該返回預設值而不是拋出異常
            assert metrics.pending_count == 0
            assert metrics.processing_count == 0
            assert metrics.completed_count == 0
            assert metrics.failed_count == 0
        
        # 測試任務管理器失敗的情況
        with patch.object(collector.task_manager, 'list_tasks', side_effect=Exception("Task manager failed")):
            metrics = await collector.collect_metrics()
            
            # code_comparison 相關指標應該是預設值
            assert metrics.code_comparison_active == 0
            assert metrics.code_comparison_pending == 0
            assert metrics.code_comparison_avg_duration == 0.0
    
    @pytest.mark.asyncio
    async def test_singleton_pattern_integration(self):
        """測試單例模式整合"""
        # 獲取多個實例
        collector1 = get_email_collector()
        collector2 = get_email_collector()
        
        # 應該是同一個實例
        assert collector1 is collector2
        
        # 測試便利函數
        with patch.object(collector1, 'collect_metrics') as mock_collect:
            mock_collect.return_value = collector1._get_default_metrics()
            
            metrics = await collect_email_metrics()
            
            # 應該調用了同一個實例的方法
            mock_collect.assert_called_once()
            assert metrics is not None


class TestEmailCollectorPerformance:
    """郵件收集器效能測試"""
    
    @pytest.mark.asyncio
    async def test_large_dataset_performance(self):
        """測試大資料集的效能"""
        # 這個測試需要大量資料，在實際環境中運行
        collector = DashboardEmailCollector()
        
        # 記錄開始時間
        start_time = datetime.now()
        
        # 執行指標收集
        metrics = await collector.collect_metrics()
        
        # 記錄結束時間
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 驗證效能要求（應該在合理時間內完成）
        assert duration < 10.0  # 應該在10秒內完成
        assert metrics is not None
    
    @pytest.mark.asyncio
    async def test_concurrent_collection_safety(self):
        """測試並發收集的安全性"""
        collector = DashboardEmailCollector()
        
        # 並發執行多個收集任務
        tasks = [collector.collect_metrics() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 驗證所有任務都成功完成
        for result in results:
            assert not isinstance(result, Exception)
            assert result is not None


if __name__ == "__main__":
    # 運行測試
    pytest.main([__file__, "-v", "-s"])