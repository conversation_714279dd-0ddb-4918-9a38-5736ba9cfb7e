"""
企業級 LINE 通知服務
提供高級 LINE 訊息推送功能：批量通知、通知範本、統計管理
"""

import os
import json
import requests
import asyncio
import threading
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from pathlib import Path
from dotenv import load_dotenv

from backend.shared.infrastructure.logging.logger_manager import LoggerManager


class LineNotificationService:
    """
    LINE 通知服務類
    負責發送 LINE 通知訊息
    
    支援優雅降級：當配置不正確時，服務會禁用但不影響其他系統功能
    """
    
    def __init__(self, auto_load_env: bool = True):
        """
        初始化 LINE 通知服務
        
        Args:
            auto_load_env: 是否自動載入環境變數
        """
        self.logger = LoggerManager().get_logger("LineNotificationService")
        
        # 確保載入環境變數
        if auto_load_env:
            self._load_environment_variables()
        
        # 服務狀態
        self.is_enabled = False
        self.config_error = None
        
        # 載入配置
        self._load_config()
        
        # 通知記錄檔案路徑
        self.notification_log_file = "line_notifications.json"
        
        # 驗證設定（非阻塞）
        self._validate_config_safe()
        
        if self.is_enabled:
            self.logger.info(f"LINE 通知服務已啟用 - 失敗通知: {self.notify_parsing_failure}, 成功通知: {self.notify_parsing_success}")
        else:
            self.logger.warning(f"LINE 通知服務已禁用 - 原因: {self.config_error}")
    
    def _load_environment_variables(self):
        """
        載入環境變數，支援多種路徑
        """
        try:
            # 嘗試載入不同位置的 .env 檔案
            env_paths = [
                Path.cwd() / '.env',
                Path(__file__).parent.parent.parent.parent / '.env',
                Path('.env'),
            ]
            
            loaded = False
            for env_path in env_paths:
                if env_path.exists():
                    load_dotenv(env_path, override=True)
                    self.logger.debug(f"已載入環境變數檔案: {env_path}")
                    loaded = True
                    break
            
            if not loaded:
                self.logger.warning("未找到 .env 檔案，使用系統環境變數")
                
        except Exception as e:
            self.logger.warning(f"載入環境變數時發生錯誤: {e}")
    
    def _load_config(self):
        """
        載入 LINE 配置
        """
        try:
            self.channel_access_token = os.getenv('LINE_CHANNEL_ACCESS_TOKEN')
            self.user_id = os.getenv('LINE_USER_ID')
            self.base_url = 'https://api.line.me/v2/bot/message/push'
            
            # 通知控制設定
            self.notify_parsing_failure = os.getenv('LINE_NOTIFY_PARSING_FAILURE', 'true').lower() == 'true'
            self.notify_parsing_success = os.getenv('LINE_NOTIFY_PARSING_SUCCESS', 'false').lower() == 'true'
            
        except Exception as e:
            self.logger.error(f"載入 LINE 配置失敗: {e}")
            self.config_error = f"配置載入失敗: {e}"
    
    def _validate_config_safe(self):
        """
        安全的配置驗證，不會拋出異常
        """
        try:
            if not self.channel_access_token:
                self.config_error = "LINE_CHANNEL_ACCESS_TOKEN 環境變數未設定"
                return
            
            if not self.user_id:
                self.config_error = "LINE_USER_ID 環境變數未設定"
                return
            
            # 基本格式驗證
            if len(self.channel_access_token) < 10:
                self.config_error = "LINE_CHANNEL_ACCESS_TOKEN 格式不正確"
                return
            
            if not self.user_id.startswith('U'):
                self.config_error = "LINE_USER_ID 格式不正確（應以 'U' 開頭）"
                return
            
            self.is_enabled = True
            self.logger.info("LINE 設定驗證通過")
            
        except Exception as e:
            self.config_error = f"設定驗證失敗: {e}"
            self.logger.error(f"LINE 設定驗證失敗: {e}")
    
    def _validate_config(self):
        """
        嚴格的配置驗證（向後相容性）
        """
        if not self.is_enabled:
            raise ValueError(f"LINE 通知服務未啟用: {self.config_error}")
        
        if not self.channel_access_token:
            raise ValueError("LINE_CHANNEL_ACCESS_TOKEN 環境變數未設定")
        
        if not self.user_id:
            raise ValueError("LINE_USER_ID 環境變數未設定")
        
        self.logger.info("LINE 設定驗證通過")
    
    def send_parsing_failure_notification(self, email_data: Dict[str, Any]) -> bool:
        """
        發送解析失敗通知
        
        Args:
            email_data: 郵件資料
            
        Returns:
            bool: 是否成功發送
        """
        # 檢查服務是否啟用
        if not self.is_enabled:
            self.logger.debug(f"LINE 通知服務未啟用，跳過解析失敗通知: {self.config_error}")
            return True  # 返回 True 避免影響主流程
        
        try:
            # 檢查是否啟用解析失敗通知
            if not self.notify_parsing_failure:
                self.logger.info("解析失敗通知已停用，跳過通知")
                return True
            
            email_id = email_data.get('id')
            
            # 檢查是否已經通知過
            if self._is_already_notified(email_id, 'parsing_failure'):
                self.logger.info(f"郵件 {email_id} 解析失敗已通知過，跳過通知")
                return True
            
            # 構建通知訊息
            message = self._build_parsing_failure_message(email_data)
            
            # 發送通知
            success = self._send_message(message)
            
            if success:
                # 記錄通知
                self._record_notification(email_id, 'parsing_failure', email_data)
                self.logger.info(f"郵件 {email_id} 解析失敗通知已發送")
            else:
                self.logger.error(f"郵件 {email_id} 解析失敗通知發送失敗")
            
            return success
            
        except Exception as e:
            self.logger.error(f"發送解析失敗通知時發生錯誤: {e}")
            return False
    
    def send_parsing_success_notification(self, email_data: Dict[str, Any]) -> bool:
        """
        發送解析成功通知
        
        Args:
            email_data: 郵件資料
            
        Returns:
            bool: 是否成功發送
        """
        # 檢查服務是否啟用
        if not self.is_enabled:
            self.logger.debug(f"LINE 通知服務未啟用，跳過解析成功通知: {self.config_error}")
            return True  # 返回 True 避免影響主流程
        
        try:
            # 檢查是否啟用解析成功通知
            if not self.notify_parsing_success:
                self.logger.info("解析成功通知已停用，跳過通知")
                return True
            
            email_id = email_data.get('id')
            
            # 檢查是否已經通知過
            if self._is_already_notified(email_id, 'parsing_success'):
                self.logger.info(f"郵件 {email_id} 解析成功已通知過，跳過通知")
                return True
            
            # 構建通知訊息
            message = self._build_parsing_success_message(email_data)
            
            # 發送通知
            success = self._send_message(message)
            
            if success:
                # 記錄通知
                self._record_notification(email_id, 'parsing_success', email_data)
                self.logger.info(f"郵件 {email_id} 解析成功通知已發送")
            else:
                self.logger.error(f"郵件 {email_id} 解析成功通知發送失敗")
            
            return success
            
        except Exception as e:
            self.logger.error(f"發送解析成功通知時發生錯誤: {e}")
            return False
    
    def _build_parsing_failure_message(self, email_data: Dict[str, Any]) -> str:
        """
        構建解析失敗通知訊息
        
        Args:
            email_data: 郵件資料
            
        Returns:
            str: 通知訊息
        """
        email_id = email_data.get('id', 'N/A')
        subject = email_data.get('subject', 'N/A')
        sender = email_data.get('sender', 'N/A')
        received_time = email_data.get('received_time', 'N/A')
        error_message = email_data.get('error_message', 'N/A')
        vendor_code = email_data.get('vendor_code', 'N/A')
        
        # 解析接收時間
        try:
            if received_time != 'N/A':
                received_dt = datetime.fromisoformat(received_time.replace('Z', '+00:00'))
                received_time_str = received_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                received_time_str = 'N/A'
        except:
            received_time_str = str(received_time)
        
        # 簡化錯誤訊息
        simplified_error = self._simplify_error_message(error_message)
        
        message = f"""🚨 郵件解析失敗通知

📧 郵件資訊:
• ID: {email_id}
• 主旨: {subject}
• 寄件者: {sender}
• 接收時間: {received_time_str}
• 廠商代碼: {vendor_code}

❌ 失敗原因:
{simplified_error}

🔧 建議處理:
• 檢查郵件格式是否正確
• 確認廠商代碼是否支援
• 考慮手動處理或聯絡系統管理員

⏰ 通知時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return message
    
    def _build_parsing_success_message(self, email_data: Dict[str, Any]) -> str:
        """
        構建解析成功通知訊息
        
        Args:
            email_data: 郵件資料
            
        Returns:
            str: 通知訊息
        """
        email_id = email_data.get('id', 'N/A')
        subject = email_data.get('subject', 'N/A')
        sender = email_data.get('sender', 'N/A')
        received_time = email_data.get('received_time', 'N/A')
        vendor_code = email_data.get('vendor_code', 'N/A')
        extraction_method = email_data.get('extraction_method', 'N/A')
        mo_number = email_data.get('mo_number', 'N/A')
        pd = email_data.get('pd', 'N/A')
        lot = email_data.get('lot', 'N/A')
        yield_value = email_data.get('yield_value', 'N/A')
        
        # 解析接收時間
        try:
            if received_time != 'N/A':
                received_dt = datetime.fromisoformat(received_time.replace('Z', '+00:00'))
                received_time_str = received_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                received_time_str = 'N/A'
        except:
            received_time_str = str(received_time)
        
        # 解析方法顯示名稱
        method_display = {
            'traditional': '傳統解析',
            'llm': 'LLM解析',
            'hybrid': '混合解析'
        }.get(extraction_method, extraction_method)
        
        # 格式化MO編號顯示
        mo_display = self._format_mo_number(mo_number)
        
        message = f"""✅ 郵件解析成功通知

📧 郵件資訊:
• ID: {email_id}
• 主旨: {subject}
• 寄件者: {sender}
• 接收時間: {received_time_str}

📊 解析結果:
• 廠商代碼: {vendor_code}
• 產品代碼: {pd}
• 批次編號: {lot}
{mo_display}• 良率: {yield_value}
• 解析方法: {method_display}

⏰ 通知時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return message
    
    def _format_mo_number(self, mo_number: str) -> str:
        """
        格式化MO編號顯示
        
        Args:
            mo_number: MO編號字符串
            
        Returns:
            str: 格式化後的MO編號顯示文本
        """
        if not mo_number or mo_number == 'N/A' or mo_number.strip() == '':
            return ""  # 沒有MO編號時不顯示該行
        
        # 處理多個MO編號（逗號分隔）
        if ',' in mo_number:
            mo_list = [mo.strip() for mo in mo_number.split(',') if mo.strip()]
            if len(mo_list) > 3:
                # 超過3個MO時，顯示前3個加省略號
                display_mo = ', '.join(mo_list[:3]) + '...'
                return f"• MO編號: {display_mo}\n"
            else:
                display_mo = ', '.join(mo_list)
                return f"• MO編號: {display_mo}\n"
        else:
            # 單一MO編號
            return f"• MO編號: {mo_number}\n"
    
    def _simplify_error_message(self, error_message: str) -> str:
        """
        簡化錯誤訊息
        
        Args:
            error_message: 原始錯誤訊息
            
        Returns:
            str: 簡化後的錯誤訊息
        """
        if not error_message or error_message == 'N/A':
            return "無具體錯誤資訊"
        
        # 根據錯誤訊息類型提供友善的說明
        error_lower = error_message.lower()
        
        if 'jcet' in error_lower and 'validation' in error_lower:
            return "JCET 郵件格式驗證失敗"
        elif 'gtk' in error_lower and 'validation' in error_lower:
            return "GTK 郵件格式驗證失敗"
        elif 'llm' in error_lower:
            return "LLM 分析失敗"
        elif 'timeout' in error_lower:
            return "處理超時"
        elif 'parsing failed' in error_lower:
            return "解析引擎失敗"
        else:
            # 如果錯誤訊息太長，截取前100個字符
            if len(error_message) > 100:
                return error_message[:100] + "..."
            return error_message
    
    def _send_message(self, message: str) -> bool:
        """
        發送 LINE 訊息
        
        Args:
            message: 訊息內容
            
        Returns:
            bool: 是否成功發送
        """
        # 再次檢查服務狀態
        if not self.is_enabled:
            self.logger.debug("LINE 通知服務未啟用，無法發送訊息")
            return False
        
        try:
            headers = {
                'Authorization': f'Bearer {self.channel_access_token}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'to': self.user_id,
                'messages': [
                    {
                        'type': 'text',
                        'text': message
                    }
                ]
            }
            
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                self.logger.info("LINE 訊息發送成功")
                return True
            else:
                self.logger.error(f"LINE 訊息發送失敗: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"LINE 訊息發送網路錯誤: {e}")
            return False
        except Exception as e:
            self.logger.error(f"LINE 訊息發送時發生錯誤: {e}")
            return False
    
    def _is_already_notified(self, email_id: str, notification_type: str) -> bool:
        """
        檢查是否已經通知過
        
        Args:
            email_id: 郵件 ID
            notification_type: 通知類型
            
        Returns:
            bool: 是否已通知
        """
        try:
            if not os.path.exists(self.notification_log_file):
                return False
            
            with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)
            
            # 檢查是否存在相同的通知記錄
            for notification in notifications:
                if (notification.get('email_id') == str(email_id) and 
                    notification.get('type') == notification_type):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"檢查通知記錄時發生錯誤: {e}")
            return False
    
    def _record_notification(self, email_id: str, notification_type: str, email_data: Dict[str, Any]):
        """
        記錄通知
        
        Args:
            email_id: 郵件 ID
            notification_type: 通知類型
            email_data: 郵件資料
        """
        try:
            # 載入現有記錄
            notifications = []
            if os.path.exists(self.notification_log_file):
                with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                    notifications = json.load(f)
            
            # 新增通知記錄
            notification_record = {
                'email_id': str(email_id),
                'type': notification_type,
                'timestamp': datetime.now().isoformat(),
                'email_subject': email_data.get('subject', 'N/A'),
                'email_sender': email_data.get('sender', 'N/A'),
                'error_message': email_data.get('error_message', 'N/A')
            }
            
            notifications.append(notification_record)
            
            # 保留最近500筆記錄，避免檔案過大
            if len(notifications) > 500:
                notifications = notifications[-500:]
            
            # 儲存記錄
            with open(self.notification_log_file, 'w', encoding='utf-8') as f:
                json.dump(notifications, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"通知記錄已儲存: {email_id} - {notification_type}")
            
        except Exception as e:
            self.logger.error(f"記錄通知時發生錯誤: {e}")
    
    def get_notification_history(self, limit: int = 50) -> list:
        """
        獲取通知歷史記錄
        
        Args:
            limit: 返回記錄數量限制
            
        Returns:
            list: 通知歷史記錄
        """
        try:
            if not os.path.exists(self.notification_log_file):
                return []
            
            with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)
            
            # 按時間戳排序並返回指定數量
            notifications.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return notifications[:limit]
            
        except Exception as e:
            self.logger.error(f"獲取通知歷史記錄時發生錯誤: {e}")
            return []
    
    def clear_notification_history(self) -> bool:
        """
        清除通知歷史記錄
        
        Returns:
            bool: 是否成功清除
        """
        try:
            if os.path.exists(self.notification_log_file):
                os.remove(self.notification_log_file)
                self.logger.info("通知歷史記錄已清除")
                return True
            return True
            
        except Exception as e:
            self.logger.error(f"清除通知歷史記錄時發生錯誤: {e}")
            return False
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        獲取服務狀態資訊
        
        Returns:
            Dict: 服務狀態詳情
        """
        return {
            'enabled': self.is_enabled,
            'config_error': self.config_error,
            'has_token': bool(self.channel_access_token),
            'has_user_id': bool(self.user_id),
            'token_length': len(self.channel_access_token) if self.channel_access_token else 0,
            'notify_failure': self.notify_parsing_failure if self.is_enabled else False,
            'notify_success': self.notify_parsing_success if self.is_enabled else False,
            'notification_log_exists': os.path.exists(self.notification_log_file)
        }
    
    def test_service(self) -> Dict[str, Any]:
        """
        測試 LINE 通知服務
        
        Returns:
            Dict: 測試結果
        """
        try:
            if not self.is_enabled:
                return {
                    'success': False,
                    'message': f'LINE 通知服務未啟用: {self.config_error}',
                    'can_retry': True
                }
            
            # 發送測試訊息
            test_message = f"""🧪 LINE 通知服務測試

⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
✅ 服務狀態: 正常運作
🔧 配置狀態: 已正確載入

這是一條測試訊息，表示 LINE 通知功能正常運作。"""
            
            success = self._send_message(test_message)
            
            if success:
                return {
                    'success': True,
                    'message': 'LINE 通知服務測試成功',
                    'test_time': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'message': 'LINE 通知服務測試失敗，請檢查網路連接或 API 設定',
                    'can_retry': True
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'LINE 通知服務測試異常: {e}',
                'can_retry': False
            }