#!/usr/bin/env python3
"""
EQC 檔案掃描與識別模組
處理檔案掃描、EQC檔案識別、失敗資料生成等功能
遵循 CLAUDE.md 功能替換原則，從主處理器中獨立出來
"""

import os
import time
from typing import List, Tuple, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from ..utils.timestamp_extractor import TimestampExtractor
    from ..hyperlinks.hyperlink_processor import HyperlinkProcessor
    from ..monitoring.progress_monitor import ProgressMonitor


class EQCFileScanner:
    """
    EQC 檔案掃描與識別器
    負責檔案掃描、EQC檔案識別、失敗資料生成等功能
    """
    
    def __init__(self, timestamp_extractor: 'TimestampExtractor', 
                 hyperlink_processor: 'HyperlinkProcessor',
                 data_start_row: int = 12, max_scan_lines: int = 10000, timeout: int = 1200):
        """
        初始化檔案掃描器
        
        Args:
            timestamp_extractor: 時間戳提取器
            hyperlink_processor: 超連結處理器
            data_start_row: 資料起始行號
            max_scan_lines: 最大掃描行數
            timeout: 處理超時時間(秒)
        """
        self.timestamp_extractor = timestamp_extractor
        self.hyperlink_processor = hyperlink_processor
        self.data_start_row = data_start_row
        self.max_scan_lines = max_scan_lines
        self.timeout = timeout
        self.start_time = None
    
    def check_eqc_csv_file(self, file_path: str, allow_eqcfaildata: bool = True) -> bool:
        """
        檢查 CSV 檔案是否為 EQC 檔案 - 完全對應 VBA CheckEQCCSVFile 函數
        
        VBA 邏輯：
        1. 排[EXCEPT_CHAR]包含 eqctotaldata 的檔案路徑
        2. 可選擇性排[EXCEPT_CHAR] eqcfaildata 檔案（用於 BIN=1 搜尋時應允許）
        3. 檢查前兩行是否包含 (qc) 或檔案路徑包含 .qa
        4. 檢查第三行是否包含 qa
        
        Args:
            file_path: 要檢查的檔案路徑
            allow_eqcfaildata: 是否允許 EQCFAILDATA 檔案（用於 BIN=1 搜尋）
            
        Returns:
            bool: True 如果是 EQC 檔案，False 否則
        """
        if not file_path:
            return False
            
        # VBA: 排[EXCEPT_CHAR]包含 eqctotaldata 的檔案，可選擇性排[EXCEPT_CHAR] eqcfaildata
        file_path_lower = file_path.lower()
        if 'eqctotaldata' in file_path_lower:
            return False
        if not allow_eqcfaildata and 'eqcfaildata' in file_path_lower:
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # VBA: 檢查前兩行是否包含 (qc) 或檔案路徑包含 .qa
                # 嚴格按照 VBA 邏輯：只檢查 "(qc)"，不包含 "(auto_qc)" 等其他變體
                for i in range(2):
                    try:
                        line = f.readline()
                        if not line:  # 檔案行數不足
                            break
                        line_lower = line.lower()
                        if '(qc)' in line_lower or '.qa' in file_path_lower:
                            return True
                    except Exception:
                        continue
                
                # VBA: 檢查第三行是否包含 qa
                try:
                    third_line = f.readline()
                    if third_line and 'qa' in third_line.lower():
                        return True
                except Exception:
                    pass
                    
        except Exception as e:
            print(f"[WARNING] 檢查 EQC 檔案失敗 {os.path.basename(file_path)}: {e}")
            return False
            
        return False

    def find_online_eqc_bin1_datalog(self, eqc_files: List[str], progress_monitor: 'ProgressMonitor') -> str:
        """
        找出 EQC BIN=1 的 golden IC 資料
        對應 VBA FindOnlieEQCBin1datalog 函數
        現在使用 VBA 一致的檔案識別邏輯 (check_eqc_csv_file)
        [FIRE] 已修復無限迴圈問題：加入掃描限制和超時檢測
        [FIRE] 已移[EXCEPT_CHAR]檔案限制，加入智能進度監控
        
        Args:
            eqc_files: EQC檔案路徑列表
            progress_monitor: 進度監控器
            
        Returns:
            str: 找到的BIN=1內容，失敗返回空字串
        """
        total_files = len(eqc_files)
        print(f"[SEARCH] 開始搜尋 EQC BIN=1 資料")
        print(f"[CHART] 總檔案數: {total_files}, 最大掃描行數: {self.max_scan_lines}")
        
        for file_index, file_path in enumerate(eqc_files):
            # [FIRE] 檢查處理超時
            if progress_monitor.is_timeout():
                print(f"[TIME] 處理超時 ({self.timeout}秒)，已處理 {file_index + 1}/{total_files} 個檔案")
                break
                
            filename = os.path.basename(file_path)
            
            # 智能進度顯示
            progress_monitor.show_progress(file_index + 1, total_files, filename)
                
            # 使用 VBA 一致的檔案識別邏輯，允許 EQCFAILDATA 檔案（因為要搜尋 BIN=1）
            if self.check_eqc_csv_file(file_path, allow_eqcfaildata=True):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    print(f"[FILE] 掃描檔案: {filename} ({len(lines)} 行)")
                    
                    # 建立檔案前12行標頭
                    header_content = ""
                    for i in range(12):
                        if i < len(lines):
                            header_content += lines[i]
                    
                    # [FIRE] 從第13行開始檢查，但加入最大掃描限制
                    max_scan_to = min(len(lines), 12 + self.max_scan_lines)
                    scan_count = 0
                    
                    for i in range(12, max_scan_to):
                        scan_count += 1
                        
                        # [FIRE] 每1000行檢查一次超時
                        if scan_count % 1000 == 0:
                            if progress_monitor.is_timeout():
                                print(f"[TIME] 處理超時，停止掃描 {filename}")
                                break
                        
                        line = lines[i].strip()
                        if len(line) < 1:
                            break
                        
                        elements = line.split(',')
                        if len(elements) > 1:
                            try:
                                if int(elements[1]) == 1:  # 找到 BIN=1 (Bin# 列在第2欄)
                                    # 替換第1欄為 Golden IC 識別碼
                                    elements[0] = "9876543210"
                                    modified_line = ",".join(elements)
                                    golden_content = header_content + modified_line + "\n"
                                    print(f"[OK] 找到 EQC BIN=1 資料: {filename} (第{i+1}行，掃描了{scan_count}行)")
                                    progress_monitor.record_success()
                                    progress_monitor.show_summary()
                                    return golden_content
                            except (ValueError, IndexError):
                                continue
                    
                    print(f"[BOARD] {filename}: 已掃描 {scan_count} 行，未找到 BIN=1")
                    progress_monitor.record_failure()
                    
                except Exception as e:
                    print(f"[ERROR] 讀取檔案失敗 {filename}: {e}")
                    progress_monitor.record_failure()
                    continue
        
        print("[ERROR] 沒有找到 EQC BIN=1 資料")
        progress_monitor.show_summary()
        return ""

    def process_eqc_rt_files_sorted(self, eqc_rt_files: List[str], progress_monitor: 'ProgressMonitor') -> List[str]:
        """
        按時間排序處理 EQC RT 檔案
        遵循 CLAUDE.md 功能替換原則，替換原有的無序處理邏輯
        
        Args:
            eqc_rt_files: EQC RT檔案路徑列表
            progress_monitor: 進度監控器
            
        Returns:
            List[str]: 處理後的資料行列表
        """
        eqc_rt_data_lines = []
        
        if not eqc_rt_files:
            return eqc_rt_data_lines
        
        # [FIRE] 已移[EXCEPT_CHAR]檔案數量限制，加入智能進度監控
        total_files = len(eqc_rt_files)
        print(f"[CHART] EQC RT 檔案總數: {total_files}")
        
        # 步驟1: 為每個檔案提取時間戳
        files_with_timestamps = []
        for file_index, eqc_rt_file in enumerate(eqc_rt_files):
            # [FIRE] 檢查處理超時
            if progress_monitor.is_timeout():
                print(f"[TIME] 處理超時，停止 EQC RT 檔案處理")
                break
                
            timestamp = self.timestamp_extractor.extract_internal_timestamp(eqc_rt_file)
            if timestamp:
                files_with_timestamps.append((eqc_rt_file, timestamp))
                readable_time = self.timestamp_extractor.format_timestamp_readable(timestamp)
                print(f"   [FILE] {file_index+1}/{len(eqc_rt_files)}: {os.path.basename(eqc_rt_file)}: {readable_time}")
            else:
                # 如果無法提取內部時間戳，使用檔案修改時間作為備用
                try:
                    file_mtime = int(os.path.getmtime(eqc_rt_file))
                    files_with_timestamps.append((eqc_rt_file, file_mtime))
                    readable_time = self.timestamp_extractor.format_timestamp_readable(file_mtime)
                    print(f"   [FILE] {file_index+1}/{len(eqc_rt_files)}: {os.path.basename(eqc_rt_file)}: {readable_time} (檔案修改時間)")
                except Exception:
                    print(f"   [ERROR] {os.path.basename(eqc_rt_file)}: 無法提取時間戳")
                    continue
        
        # 步驟2: 按時間戳排序 (早→晚)
        files_with_timestamps.sort(key=lambda x: x[1])
        
        print(f"[REFRESH] EQC RT 檔案已按時間排序 (早→晚):")
        for i, (file_path, timestamp) in enumerate(files_with_timestamps, 1):
            readable_time = self.timestamp_extractor.format_timestamp_readable(timestamp)
            print(f"   {i}. {os.path.basename(file_path)} - {readable_time.split()[-1]}")  # 只顯示時間部分
        
        # 步驟3: 按排序後順序處理檔案
        for eqc_rt_file, _ in files_with_timestamps:
            try:
                with open(eqc_rt_file, 'r', encoding='utf-8') as f:
                    rt_lines = f.readlines()
                
                # 從第13行開始加入資料行
                for i in range(self.data_start_row, len(rt_lines)):
                    line = rt_lines[i].strip()
                    if not line:
                        break
                    
                    # 添加超連結
                    elements = line.split(',')
                    elements_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                        elements, eqc_rt_file, 2
                    )
                    eqc_rt_data_lines.append(','.join(elements_with_hyperlink))
                    
            except Exception as e:
                print(f"[ERROR] 處理 EQC RT 檔案失敗 {os.path.basename(eqc_rt_file)}: {e}")
        
        print(f"[BOARD] EQC RT 資料處理完成: 總共 {len(eqc_rt_data_lines)} 行資料")
        return eqc_rt_data_lines

    def _is_cta_csvfile(self, file_path: str) -> bool:
        """
        檢查 CSV 檔案是否為 CTA 檔案
        判斷依據：B1 欄位是否為 "CTA"（不區分大小寫）
        
        Args:
            file_path: CSV 檔案路徑
            
        Returns:
            bool: True 如果是 CTA 檔案，False 否則
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                
            if not first_line:
                return False
                
            # 分割 CSV 第一行
            elements = first_line.split(',')
            
            # 檢查 B1 欄位（索引 1）是否為 "CTA"
            if len(elements) >= 2:
                b1_value = elements[1].strip().lower()  # 不區分大小寫
                return b1_value == 'cta'
                
            return False
            
        except Exception as e:
            print(f"[WARNING] 檢查 CTA CSV 檔案失敗 {os.path.basename(file_path)}: {e}")
            return False
    
    def _get_cta_serial_column_index(self, file_path: str) -> int:
        """
        檢查 CTA 檔案第8行第5欄是否為 "Serial_No"
        
        Args:
            file_path: CTA CSV 檔案路徑
            
        Returns:
            int: 4 (第5欄索引) 如果是 Serial_No，-1 如果不是
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 檢查第8行（索引7）是否存在
            if len(lines) < 8:
                print(f"[WARNING] CTA檔案 {os.path.basename(file_path)} 行數不足8行")
                return -1
                
            # 取得第8行並分割
            line8 = lines[7].strip()
            elements = line8.split(',')
            
            # 檢查第5欄（索引4）是否為 "Serial_No"
            if len(elements) >= 5:
                col5_value = elements[4].strip()
                if col5_value.lower() == 'serial_no':
                    print(f"[OK] CTA檔案 {os.path.basename(file_path)} 第8行第5欄確認為 Serial_No")
                    return 4  # 第5欄的索引
                else:
                    print(f"[ERROR] CTA檔案 {os.path.basename(file_path)} 第8行第5欄不是 Serial_No，實際值: '{col5_value}'")
                    return -1
            else:
                print(f"[ERROR] CTA檔案 {os.path.basename(file_path)} 第8行欄位數不足5欄")
                return -1
                
        except Exception as e:
            print(f"[ERROR] 檢查 CTA Serial_No 欄位失敗 {os.path.basename(file_path)}: {e}")
            return -1

    def generate_ft_eqc_fail_data_with_hyperlinks(self, matched_pairs: List[Tuple[str, str]]) -> List[str]:
        """
        生成帶超連結的 FT-EQC 失敗配對資料
        結合 3.1 配對機制和超連結功能
        
        Args:
            matched_pairs: FT-EQC配對列表
            
        Returns:
            List[str]: 失敗配對資料行列表
        """
        fail_data_lines = []
        
        print("[REFRESH] 處理 FT-EQC 配對失敗資料 (含超連結):")
        
        # 為配對添加時間戳並排序
        pairs_with_timestamps = []
        for ft_file, eqc_file in matched_pairs:
            ft_timestamp = self.timestamp_extractor.extract_internal_timestamp(ft_file)
            eqc_timestamp = self.timestamp_extractor.extract_internal_timestamp(eqc_file)
            # 使用較早的時間戳作為配對的排序基準
            pair_timestamp = min(ft_timestamp or 0, eqc_timestamp or 0)
            pairs_with_timestamps.append((ft_file, eqc_file, pair_timestamp))
        
        # 按時間戳排序 (早→晚)
        pairs_with_timestamps.sort(key=lambda x: x[2])
        sorted_matched_pairs = [(ft, eqc) for ft, eqc, _ in pairs_with_timestamps]
        
        print(f"   [CHART] 已按時間戳排序 {len(sorted_matched_pairs)} 個配對")
        
        for ft_file, eqc_file in sorted_matched_pairs:
            try:
                # 檢查 EQC 檔案是否有失敗資料
                fail_rows = []
                with open(eqc_file, 'r', encoding='utf-8') as f:
                    eqc_lines = f.readlines()
                
                # 從第13行開始檢查失敗行
                for i in range(self.data_start_row, len(eqc_lines)):
                    line = eqc_lines[i].strip()
                    if not line:
                        break
                    
                    elements = line.split(',')
                    if len(elements) >= 2:
                        try:
                            if int(elements[1]) != 1:  # BIN != 1 表示失敗
                                fail_rows.append(elements)
                        except (ValueError, IndexError):
                            continue
                
                if fail_rows:
                    print(f"   [FILE] {os.path.basename(eqc_file)}: {len(fail_rows)} 個失敗行")
                    
                    # 如果有對應的 FT 檔案，嘗試找到匹配的 FT 資料
                    if ft_file and os.path.exists(ft_file):
                        with open(ft_file, 'r', encoding='utf-8') as f:
                            ft_lines = f.readlines()
                        
                        # 為每個失敗行嘗試找到對應的 FT 資料
                        for fail_row in fail_rows:
                            # [FIRE] 新增：CTA 檔案特殊處理
                            if self._is_cta_csvfile(eqc_file):
                                print(f"[TARGET] 偵測到 CTA 檔案: {os.path.basename(eqc_file)}")
                                
                                # 檢查第8行第5欄是否為 Serial_No
                                serial_column_index = self._get_cta_serial_column_index(eqc_file)
                                if serial_column_index == -1:
                                    # 返回錯誤記錄
                                    error_msg = f"CTA檔案 {os.path.basename(eqc_file)} 第8行第5欄不是Serial_No，跳過處理"
                                    print(f"[ERROR] {error_msg}")
                                    fail_data_lines.append(f"ERROR,{error_msg},,,")
                                    continue
                                
                                # 使用第5欄（索引4）進行配對
                                fail_serial = fail_row[serial_column_index] if len(fail_row) > serial_column_index else ""
                                print(f"[SEARCH] CTA配對序號: {fail_serial} (使用第5欄)")
                                
                                # 在 FT 檔案中也使用對應欄位配對
                                ft_matched = False
                                if self._is_cta_csvfile(ft_file):
                                    print(f"[TARGET] FT檔案也是 CTA 格式")
                                    ft_serial_column_index = self._get_cta_serial_column_index(ft_file)
                                    if ft_serial_column_index == -1:
                                        print(f"[WARNING] FT檔案 {os.path.basename(ft_file)} 不是有效的CTA格式")
                                    else:
                                        # CTA to CTA 配對（使用第5欄）
                                        print(f"[SEARCH] 開始搜尋 FT 檔案: {len(ft_lines)} 行，從第 {self.data_start_row + 1} 行開始")
                                        print(f"[TARGET] 目標 Serial_No: {fail_serial}")
                                        
                                        scanned_count = 0
                                        for i in range(self.data_start_row, len(ft_lines)):
                                            scanned_count += 1
                                            ft_line = ft_lines[i].strip()
                                            
                                            # 每1000行記錄一次進度
                                            if scanned_count % 1000 == 0:
                                                print(f"   [CHART] 已掃描 {scanned_count} 行...")
                                            
                                            if not ft_line:
                                                print(f"   [WARNING] 第 {i+1} 行為空行，跳過")
                                                continue  # 修復：跳過空行而不是中斷
                                            
                                            ft_elements = ft_line.split(',')
                                            if (len(ft_elements) > ft_serial_column_index and 
                                                ft_elements[ft_serial_column_index] == fail_serial):
                                                print(f"   [OK] 找到匹配！第 {i+1} 行，Serial_No: {fail_serial}")
                                                
                                                # 檢查 BIN1
                                                if len(ft_elements) > 1:
                                                    try:
                                                        ft_bin_value = int(ft_elements[1])  # BIN# 在第2欄
                                                        if ft_bin_value == 1:
                                                            # 只有 BIN=1 才加入
                                                            ft_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                                                                ft_elements, ft_file, 2
                                                            )
                                                            fail_data_lines.append(','.join(ft_with_hyperlink))
                                                            print(f"[OK] CTA配對成功 (BIN=1): {fail_serial}")
                                                            ft_matched = True
                                                        else:
                                                            print(f"[WARNING] FT資料BIN≠1 (BIN={ft_bin_value})，跳過: {fail_serial}")
                                                    except (ValueError, IndexError):
                                                        print(f"[ERROR] FT資料BIN值解析失敗，跳過: {fail_serial}")
                                                else:
                                                    print(f"[ERROR] FT資料欄位不足，跳過: {fail_serial}")
                                                
                                                break
                                        
                                        # 搜尋結束後記錄
                                        print(f"   [BOARD] 總共掃描了 {scanned_count} 行")
                                        if not ft_matched:
                                            print(f"   [ERROR] 未找到匹配的 Serial_No: {fail_serial}")
                                else:
                                    print(f"[CHART] FT檔案是普通格式，使用 A 欄配對")
                                    # CTA to 普通檔案配對（CTA用第5欄，普通檔案用第1欄）
                                    print(f"[SEARCH] 開始搜尋 FT 檔案(普通格式): {len(ft_lines)} 行，從第 {self.data_start_row + 1} 行開始")
                                    print(f"[TARGET] 目標 Serial_No: {fail_serial} (使用 A 欄配對)")
                                    
                                    scanned_count = 0
                                    for i in range(self.data_start_row, len(ft_lines)):
                                        scanned_count += 1
                                        ft_line = ft_lines[i].strip()
                                        
                                        # 每1000行記錄一次進度
                                        if scanned_count % 1000 == 0:
                                            print(f"   [CHART] 已掃描 {scanned_count} 行...")
                                        
                                        if not ft_line:
                                            print(f"   [WARNING] 第 {i+1} 行為空行，跳過")
                                            continue  # 修復：跳過空行而不是中斷
                                        
                                        ft_elements = ft_line.split(',')
                                        if len(ft_elements) > 0 and ft_elements[0] == fail_serial:
                                            print(f"   [OK] 找到匹配！第 {i+1} 行，Serial_No: {fail_serial}")
                                            
                                            # 檢查 BIN1
                                            if len(ft_elements) > 1:
                                                try:
                                                    ft_bin_value = int(ft_elements[1])  # BIN# 在第2欄
                                                    if ft_bin_value == 1:
                                                        # 只有 BIN=1 才加入
                                                        ft_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                                                            ft_elements, ft_file, 2
                                                        )
                                                        fail_data_lines.append(','.join(ft_with_hyperlink))
                                                        print(f"[OK] CTA-普通檔案配對成功 (BIN=1): {fail_serial}")
                                                        ft_matched = True
                                                    else:
                                                        print(f"[WARNING] FT資料BIN≠1 (BIN={ft_bin_value})，跳過: {fail_serial}")
                                                except (ValueError, IndexError):
                                                    print(f"[ERROR] FT資料BIN值解析失敗，跳過: {fail_serial}")
                                            else:
                                                print(f"[ERROR] FT資料欄位不足，跳過: {fail_serial}")
                                            
                                            break
                                    
                                    # 搜尋結束後記錄
                                    print(f"   [BOARD] 總共掃描了 {scanned_count} 行")
                                    if not ft_matched:
                                        print(f"   [ERROR] 未找到匹配的 Serial_No: {fail_serial}")
                                
                                # 加入 EQC 失敗行，添加超連結
                                eqc_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                                    fail_row, eqc_file, 2
                                )
                                fail_data_lines.append(','.join(eqc_with_hyperlink))
                                
                            else:
                                # 原有的 A 欄 (第1欄) 配對邏輯 - 非 CTA 檔案
                                fail_serial = fail_row[0] if fail_row else ""
                                
                                # 找對應的 FT 行 (非 CTA 檔案)
                                print(f"[SEARCH] 開始搜尋 FT 檔案(傳統格式): {len(ft_lines)} 行，從第 {self.data_start_row + 1} 行開始")
                                print(f"[TARGET] 目標 Serial_No: {fail_serial} (使用 A 欄配對)")
                                
                                scanned_count = 0
                                ft_matched = False
                                for i in range(self.data_start_row, len(ft_lines)):
                                    scanned_count += 1
                                    ft_line = ft_lines[i].strip()
                                    
                                    # 每1000行記錄一次進度
                                    if scanned_count % 1000 == 0:
                                        print(f"   [CHART] 已掃描 {scanned_count} 行...")
                                    
                                    if not ft_line:
                                        print(f"   [WARNING] 第 {i+1} 行為空行，跳過")
                                        continue  # 修復：跳過空行而不是中斷
                                    
                                    ft_elements = ft_line.split(',')
                                    if len(ft_elements) > 0 and ft_elements[0] == fail_serial:
                                        print(f"   [OK] 找到匹配！第 {i+1} 行，Serial_No: {fail_serial}")
                                        
                                        # 檢查 BIN1
                                        if len(ft_elements) > 1:
                                            try:
                                                ft_bin_value = int(ft_elements[1])  # BIN# 在第2欄
                                                if ft_bin_value == 1:
                                                    # 只有 BIN=1 才加入
                                                    ft_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                                                        ft_elements, ft_file, 2
                                                    )
                                                    fail_data_lines.append(','.join(ft_with_hyperlink))
                                                    print(f"[OK] 傳統格式配對成功 (BIN=1): {fail_serial}")
                                                    ft_matched = True
                                                else:
                                                    print(f"[WARNING] FT資料BIN≠1 (BIN={ft_bin_value})，跳過: {fail_serial}")
                                            except (ValueError, IndexError):
                                                print(f"[ERROR] FT資料BIN值解析失敗，跳過: {fail_serial}")
                                        else:
                                            print(f"[ERROR] FT資料欄位不足，跳過: {fail_serial}")
                                        
                                        break
                                
                                # 搜尋結束後記錄
                                print(f"   [BOARD] 總共掃描了 {scanned_count} 行")
                                if not ft_matched:
                                    print(f"   [ERROR] 未找到匹配的 Serial_No: {fail_serial}")
                                
                                # 加入 EQC 失敗行，添加超連結
                                eqc_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                                    fail_row, eqc_file, 2
                                )
                                fail_data_lines.append(','.join(eqc_with_hyperlink))
                
            except Exception as e:
                print(f"   [ERROR] 處理失敗 {os.path.basename(eqc_file)}: {e}")
        
        return fail_data_lines