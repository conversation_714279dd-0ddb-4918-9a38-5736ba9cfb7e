#!/usr/bin/env python3
'''
多人多工測試腳本 (生產模式)
'''

import os
import sys
import time
import threading
from pathlib import Path

# 設定生產模式
os.environ['USE_MEMORY_BROKER'] = 'false'

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def simulate_user_task(user_id, folder_path):
    '''模擬用戶任務'''
    try:
        from backend.tasks import process_complete_eqc_workflow_task
        
        print(f"👤 用戶 {user_id} 開始處理: {folder_path}")
        start_time = time.time()
        
        # 提交任務
        task = process_complete_eqc_workflow_task.delay(
            folder_path=folder_path,
            user_session_id=f"user_{user_id}_{int(time.time())}",
            options={}
        )
        
        print(f"📤 用戶 {user_id} 任務提交成功，ID: {task.id}")
        
        # 等待結果（設定較長的超時時間）
        try:
            result = task.get(timeout=300)  # 5分鐘超時
            elapsed = time.time() - start_time
            print(f"✅ 用戶 {user_id} 任務完成，耗時: {elapsed:.2f}秒")
            return True
        except Exception as e:
            print(f"❌ 用戶 {user_id} 任務失敗: {e}")
            return False
            
    except Exception as e:
        print(f"💥 用戶 {user_id} 任務異常: {e}")
        return False

def main():
    print("🧪 多人多工測試 (生產模式)")
    print("=" * 60)
    
    # 測試路徑
    test_paths = [
        "D:\\project\\python\\outlook_summary\\doc\\20250523",
        "D:\\project\\python\\outlook_summary\\doc\\202505231",
        "D:\\project\\python\\outlook_summary\\doc\\20250523"  # 重複路徑測試衝突
    ]
    
    # 創建線程模擬多用戶
    threads = []
    for i, path in enumerate(test_paths):
        thread = threading.Thread(
            target=simulate_user_task,
            args=(i+1, path)
        )
        threads.append(thread)
    
    # 啟動所有線程
    print("🚀 啟動多用戶並發測試...")
    start_time = time.time()
    
    for thread in threads:
        thread.start()
        time.sleep(1)  # 間隔1秒啟動
    
    # 等待所有線程完成
    for thread in threads:
        thread.join()
    
    total_time = time.time() - start_time
    print(f"\n📊 總測試時間: {total_time:.2f}秒")
    print("🎉 多人多工測試完成")

if __name__ == "__main__":
    main()
