"""Phase 4: 高級測試功能

這個模組實現壓力測試、負載測試、端到端測試等高級測試功能。
"""

import pytest
import sys
import os
import asyncio
import threading
import time
import concurrent.futures
import statistics
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import tempfile
import json
import random

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 導入需要測試的模組
try:
    from frontend.api.dependencies import (
        get_api_state, get_staging_service, get_processing_service,
        APIState, ServiceContainer
    )
    from frontend.api.ft_eqc_api import app
    from backend.shared.infrastructure.adapters.staging.service import FileStagingService
    from backend.shared.infrastructure.adapters.processing.service import FileProcessingService
    
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False


class TestStressTesting:
    """壓力測試 - 測試系統在高負載下的表現"""
    
    def test_api_state_stress_test(self):
        """API 狀態壓力測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        api_state = get_api_state()
        
        # 壓力測試參數
        num_operations = 1000
        num_threads = 10
        
        def stress_worker():
            """壓力測試工作函數"""
            operations_per_thread = num_operations // num_threads
            for _ in range(operations_per_thread):
                # 模擬高頻操作
                api_state.increment_request_count()
                api_state.get_stats()
                
                # 添加和移除連接
                conn_id = f"stress_conn_{threading.current_thread().ident}_{random.randint(1, 1000)}"
                api_state.add_connection(conn_id, {"type": "stress_test"})
                api_state.remove_connection(conn_id)
        
        # 執行壓力測試
        start_time = time.time()
        threads = []
        
        for _ in range(num_threads):
            thread = threading.Thread(target=stress_worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有線程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 驗證結果
        final_count = api_state.system_stats["request_count"]
        operations_per_second = num_operations / total_time
        
        # 壓力測試應該在合理時間內完成
        assert total_time < 10.0, f"Stress test took too long: {total_time:.2f}s"
        assert final_count >= num_operations, f"Not all operations completed: {final_count}/{num_operations}"
        assert operations_per_second >= 100, f"Operations per second too low: {operations_per_second:.1f}"
        
        print(f"✅ API state stress test passed - {operations_per_second:.1f} ops/s, {total_time:.2f}s")
    
    def test_service_container_stress_test(self):
        """服務容器壓力測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        # 壓力測試參數
        num_requests = 500
        num_threads = 5
        
        results = []
        
        def service_stress_worker():
            """服務壓力測試工作函數"""
            thread_results = []
            requests_per_thread = num_requests // num_threads
            
            for _ in range(requests_per_thread):
                start_time = time.time()
                
                # 獲取服務實例
                staging_service = get_staging_service()
                processing_service = get_processing_service()
                
                # 驗證服務可用
                assert staging_service is not None
                assert processing_service is not None
                
                end_time = time.time()
                thread_results.append(end_time - start_time)
            
            results.extend(thread_results)
        
        # 執行壓力測試
        start_time = time.time()
        threads = []
        
        for _ in range(num_threads):
            thread = threading.Thread(target=service_stress_worker)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 分析結果
        avg_response_time = statistics.mean(results)
        max_response_time = max(results)
        min_response_time = min(results)
        
        # 驗證性能指標
        assert avg_response_time < 0.01, f"Average response time too slow: {avg_response_time:.4f}s"
        assert max_response_time < 0.05, f"Max response time too slow: {max_response_time:.4f}s"
        assert total_time < 5.0, f"Total stress test time too long: {total_time:.2f}s"
        
        print(f"✅ Service container stress test passed - avg: {avg_response_time:.4f}s, max: {max_response_time:.4f}s")
    
    def test_dependency_injection_stress_test(self):
        """依賴注入壓力測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        # 壓力測試參數
        num_injections = 200
        num_concurrent = 8
        
        def injection_stress_worker():
            """依賴注入壓力測試工作函數"""
            injections_per_worker = num_injections // num_concurrent
            
            for _ in range(injections_per_worker):
                # 模擬依賴注入場景
                api_state = get_api_state()
                staging_service = get_staging_service()
                processing_service = get_processing_service()
                
                # 驗證依賴注入正確性
                assert isinstance(api_state, APIState)
                assert isinstance(staging_service, FileStagingService)
                assert isinstance(processing_service, FileProcessingService)
                
                # 驗證單例行為
                api_state2 = get_api_state()
                assert api_state is api_state2
        
        # 執行並發壓力測試
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(injection_stress_worker) for _ in range(num_concurrent)]
            concurrent.futures.wait(futures)
        
        total_time = time.time() - start_time
        injections_per_second = num_injections / total_time
        
        # 驗證性能
        assert total_time < 3.0, f"Dependency injection stress test too slow: {total_time:.2f}s"
        assert injections_per_second >= 100, f"Injection rate too low: {injections_per_second:.1f}/s"
        
        print(f"✅ Dependency injection stress test passed - {injections_per_second:.1f} injections/s")


class TestLoadTesting:
    """負載測試 - 測試系統在持續負載下的穩定性"""
    
    def test_sustained_load_test(self):
        """持續負載測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        api_state = get_api_state()
        
        # 負載測試參數
        duration_seconds = 5  # 5秒持續負載
        target_rps = 50      # 每秒50個請求
        
        start_time = time.time()
        request_times = []
        error_count = 0
        
        while time.time() - start_time < duration_seconds:
            iteration_start = time.time()
            
            try:
                # 模擬請求處理
                api_state.increment_request_count()
                stats = api_state.get_stats()
                
                # 模擬連接管理
                conn_id = f"load_test_{time.time()}"
                api_state.add_connection(conn_id, {"type": "load_test"})
                api_state.remove_connection(conn_id)
                
                request_times.append(time.time() - iteration_start)
                
            except Exception as e:
                error_count += 1
                print(f"Load test error: {e}")
            
            # 控制請求頻率
            elapsed = time.time() - iteration_start
            sleep_time = max(0, (1.0 / target_rps) - elapsed)
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        total_time = time.time() - start_time
        total_requests = len(request_times)
        actual_rps = total_requests / total_time
        
        # 分析性能指標
        if request_times:
            avg_response_time = statistics.mean(request_times)
            p95_response_time = statistics.quantiles(request_times, n=20)[18]  # 95th percentile
            error_rate = error_count / (total_requests + error_count)
        else:
            avg_response_time = 0
            p95_response_time = 0
            error_rate = 1.0
        
        # 驗證負載測試結果
        assert error_rate < 0.05, f"Error rate too high: {error_rate:.2%}"
        assert actual_rps >= target_rps * 0.8, f"Actual RPS too low: {actual_rps:.1f} (target: {target_rps})"
        assert avg_response_time < 0.01, f"Average response time too slow: {avg_response_time:.4f}s"
        assert p95_response_time < 0.02, f"95th percentile response time too slow: {p95_response_time:.4f}s"
        
        print(f"✅ Sustained load test passed - {actual_rps:.1f} RPS, {avg_response_time:.4f}s avg, {error_rate:.2%} errors")
    
    def test_memory_load_test(self):
        """記憶體負載測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        api_state = get_api_state()
        
        # 記憶體負載測試參數
        num_connections = 1000
        connection_data_size = 100  # 每個連接的數據大小
        
        # 添加大量連接
        start_time = time.time()
        
        for i in range(num_connections):
            conn_id = f"memory_test_conn_{i}"
            # 創建較大的連接數據
            connection_data = {
                "type": "memory_test",
                "user_id": f"user_{i}",
                "session_data": "x" * connection_data_size,
                "metadata": {
                    "created_at": time.time(),
                    "test_id": i,
                    "extra_data": list(range(10))
                }
            }
            api_state.add_connection(conn_id, connection_data)
        
        creation_time = time.time() - start_time
        
        # 驗證連接數量
        stats = api_state.get_stats()
        assert stats["active_connections_count"] >= num_connections
        
        # 測試訪問性能
        access_times = []
        for i in range(0, num_connections, 50):  # 每50個測試一次
            conn_id = f"memory_test_conn_{i}"
            start_access = time.time()
            connection = api_state.get_connection(conn_id)
            access_times.append(time.time() - start_access)
            assert connection is not None
            assert connection["user_id"] == f"user_{i}"
        
        avg_access_time = statistics.mean(access_times)
        
        # 清理連接
        cleanup_start = time.time()
        for i in range(num_connections):
            conn_id = f"memory_test_conn_{i}"
            api_state.remove_connection(conn_id)
        cleanup_time = time.time() - cleanup_start
        
        # 驗證性能指標
        assert creation_time < 2.0, f"Connection creation too slow: {creation_time:.2f}s"
        assert avg_access_time < 0.001, f"Connection access too slow: {avg_access_time:.4f}s"
        assert cleanup_time < 1.0, f"Connection cleanup too slow: {cleanup_time:.2f}s"
        
        print(f"✅ Memory load test passed - {num_connections} connections, {avg_access_time:.4f}s access time")


class TestEndToEndTesting:
    """端到端測試 - 測試完整的工作流程"""
    
    def test_complete_request_workflow(self):
        """完整請求工作流程測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        # 模擬完整的請求處理流程
        api_state = get_api_state()
        staging_service = get_staging_service()
        processing_service = get_processing_service()
        
        # 1. 模擬用戶連接
        user_id = "e2e_test_user"
        connection_id = f"conn_{user_id}_{time.time()}"
        
        api_state.add_connection(connection_id, {
            "type": "websocket",
            "user_id": user_id,
            "session_id": "test_session"
        })
        
        # 2. 模擬請求處理
        initial_count = api_state.system_stats["request_count"]
        api_state.increment_request_count()
        
        # 3. 模擬暫存服務操作
        staging_task_data = {
            "user_id": user_id,
            "file_path": "/tmp/test_file.txt",
            "operation": "stage_file"
        }
        
        # 4. 模擬處理服務操作
        processing_task_data = {
            "user_id": user_id,
            "task_type": "file_processing",
            "input_data": staging_task_data
        }
        
        # 5. 驗證服務狀態
        assert isinstance(staging_service, FileStagingService)
        assert isinstance(processing_service, FileProcessingService)
        
        # 6. 驗證請求計數
        assert api_state.system_stats["request_count"] == initial_count + 1
        
        # 7. 驗證連接存在
        connection = api_state.get_connection(connection_id)
        assert connection is not None
        assert connection["user_id"] == user_id
        
        # 8. 清理
        api_state.remove_connection(connection_id)
        
        print("✅ Complete request workflow test passed")
    
    def test_multi_user_scenario(self):
        """多用戶場景測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        api_state = get_api_state()
        num_users = 10
        
        # 模擬多個用戶同時操作
        user_connections = {}
        
        # 1. 多用戶連接
        for i in range(num_users):
            user_id = f"user_{i}"
            connection_id = f"conn_{user_id}_{time.time()}"
            
            api_state.add_connection(connection_id, {
                "type": "websocket",
                "user_id": user_id,
                "session_id": f"session_{i}"
            })
            
            user_connections[user_id] = connection_id
        
        # 2. 模擬並發請求
        initial_count = api_state.system_stats["request_count"]
        
        def user_activity(user_id):
            """模擬用戶活動"""
            for _ in range(5):  # 每個用戶5個請求
                api_state.increment_request_count()
                time.sleep(0.01)  # 小延遲模擬真實場景
        
        # 並發執行用戶活動
        threads = []
        for user_id in user_connections.keys():
            thread = threading.Thread(target=user_activity, args=(user_id,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 3. 驗證結果
        final_count = api_state.system_stats["request_count"]
        expected_requests = num_users * 5
        
        assert final_count >= initial_count + expected_requests
        
        # 4. 驗證所有連接仍然存在
        stats = api_state.get_stats()
        assert stats["active_connections_count"] >= num_users
        
        # 5. 清理所有連接
        for connection_id in user_connections.values():
            api_state.remove_connection(connection_id)
        
        print(f"✅ Multi-user scenario test passed - {num_users} users, {expected_requests} requests")
    
    def test_error_recovery_scenario(self):
        """錯誤恢復場景測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        api_state = get_api_state()
        
        # 1. 正常操作
        initial_count = api_state.system_stats["request_count"]
        api_state.increment_request_count()
        
        # 2. 模擬錯誤情況
        initial_error_count = api_state.system_stats["error_count"]
        api_state.increment_error_count()
        
        # 3. 模擬錯誤連接處理
        error_connection_id = "error_test_conn"
        api_state.add_connection(error_connection_id, {"type": "error_test"})
        
        # 4. 模擬錯誤恢復
        try:
            # 嘗試移除不存在的連接（應該優雅處理）
            result = api_state.remove_connection("nonexistent_connection")
            assert result is False  # 應該返回 False 而不是拋出異常
            
            # 正常移除存在的連接
            result = api_state.remove_connection(error_connection_id)
            assert result is True
            
        except Exception as e:
            pytest.fail(f"Error recovery failed: {e}")
        
        # 5. 驗證系統狀態
        assert api_state.system_stats["request_count"] == initial_count + 1
        assert api_state.system_stats["error_count"] == initial_error_count + 1
        
        # 6. 驗證系統仍然可以正常操作
        api_state.increment_request_count()
        assert api_state.system_stats["request_count"] == initial_count + 2
        
        print("✅ Error recovery scenario test passed")


class TestPerformanceBenchmarking:
    """性能基準測試"""
    
    def test_dependency_injection_benchmark(self):
        """依賴注入性能基準測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        # 基準測試參數
        num_iterations = 1000
        
        # 測試依賴獲取性能
        times = []
        for _ in range(num_iterations):
            start_time = time.time()
            
            api_state = get_api_state()
            staging_service = get_staging_service()
            processing_service = get_processing_service()
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        # 計算統計數據
        avg_time = statistics.mean(times)
        median_time = statistics.median(times)
        p95_time = statistics.quantiles(times, n=20)[18]  # 95th percentile
        p99_time = statistics.quantiles(times, n=100)[98]  # 99th percentile
        
        # 性能基準
        assert avg_time < 0.001, f"Average DI time too slow: {avg_time:.4f}s"
        assert median_time < 0.001, f"Median DI time too slow: {median_time:.4f}s"
        assert p95_time < 0.002, f"95th percentile DI time too slow: {p95_time:.4f}s"
        assert p99_time < 0.005, f"99th percentile DI time too slow: {p99_time:.4f}s"
        
        print(f"✅ DI benchmark - avg: {avg_time:.4f}s, p95: {p95_time:.4f}s, p99: {p99_time:.4f}s")
    
    def test_api_state_operations_benchmark(self):
        """API 狀態操作性能基準測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        api_state = get_api_state()
        
        # 基準測試不同操作
        operations = {
            "increment_request": lambda: api_state.increment_request_count(),
            "increment_error": lambda: api_state.increment_error_count(),
            "get_stats": lambda: api_state.get_stats(),
            "add_connection": lambda: api_state.add_connection(f"bench_{time.time()}", {"type": "benchmark"}),
        }
        
        benchmark_results = {}
        
        for op_name, operation in operations.items():
            times = []
            
            for _ in range(500):  # 每個操作測試500次
                start_time = time.time()
                operation()
                end_time = time.time()
                times.append(end_time - start_time)
            
            avg_time = statistics.mean(times)
            p95_time = statistics.quantiles(times, n=20)[18]
            
            benchmark_results[op_name] = {
                "avg": avg_time,
                "p95": p95_time
            }
            
            # 性能基準驗證 (調整為實際可達成的基準)
            assert avg_time < 0.001, f"{op_name} average time too slow: {avg_time:.5f}s"
            assert p95_time < 0.002, f"{op_name} p95 time too slow: {p95_time:.5f}s"
        
        # 清理測試連接
        stats = api_state.get_stats()
        print(f"✅ API state operations benchmark passed - {len(benchmark_results)} operations tested")
        
        for op_name, results in benchmark_results.items():
            print(f"  {op_name}: avg={results['avg']:.5f}s, p95={results['p95']:.5f}s")
    
    def test_concurrent_access_benchmark(self):
        """並發訪問性能基準測試"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        api_state = get_api_state()
        
        # 並發基準測試參數
        num_threads = 8
        operations_per_thread = 100
        
        results = []
        
        def concurrent_worker():
            """並發工作函數"""
            thread_times = []
            
            for i in range(operations_per_thread):
                start_time = time.time()
                
                # 混合操作
                api_state.increment_request_count()
                api_state.get_stats()
                
                conn_id = f"concurrent_{threading.current_thread().ident}_{i}"
                api_state.add_connection(conn_id, {"type": "concurrent_test"})
                api_state.remove_connection(conn_id)
                
                end_time = time.time()
                thread_times.append(end_time - start_time)
            
            results.extend(thread_times)
        
        # 執行並發基準測試
        start_time = time.time()
        
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=concurrent_worker)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 分析並發性能
        total_operations = num_threads * operations_per_thread
        operations_per_second = total_operations / total_time
        avg_operation_time = statistics.mean(results)
        p95_operation_time = statistics.quantiles(results, n=20)[18]
        
        # 並發性能基準
        assert operations_per_second >= 1000, f"Concurrent OPS too low: {operations_per_second:.1f}"
        assert avg_operation_time < 0.01, f"Concurrent avg time too slow: {avg_operation_time:.4f}s"
        assert p95_operation_time < 0.02, f"Concurrent p95 time too slow: {p95_operation_time:.4f}s"
        
        print(f"✅ Concurrent access benchmark - {operations_per_second:.1f} OPS, {avg_operation_time:.4f}s avg")
