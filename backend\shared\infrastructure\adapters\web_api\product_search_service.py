"""產品搜尋服務
實作智慧產品搜尋功能，支援並行搜尋和時間範圍篩選
"""

import asyncio
import os
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from loguru import logger

from ..domain.entities.file_search import (
    FileInfo, ProductSearchResult, SearchFilters, SearchStatus, 
    SearchTask, TimeRange
)
from ..data_models.search_models import TimeRangeType


class ProductSearchService:
    """產品搜尋服務
    
    提供基於產品名稱的智慧搜尋功能，支援：
    - 產品資料夾定位
    - 時間範圍篩選
    - 並行搜尋
    - 進度追蹤
    """
    
    def __init__(self, max_workers: int = 4, search_timeout: int = 300):
        """初始化搜尋服務
        
        Args:
            max_workers: 最大並行工作執行緒數
            search_timeout: 搜尋超時時間（秒）
        """
        self.max_workers = max_workers
        self.search_timeout = search_timeout
        self.active_tasks: Dict[str, SearchTask] = {}
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def search_product_folder(
        self, 
        product_name: str, 
        base_path: Path,
        time_range: TimeRange,
        filters: Optional[SearchFilters] = None
    ) -> ProductSearchResult:
        """搜尋產品資料夾
        
        Args:
            product_name: 產品名稱
            base_path: 基礎搜尋路徑
            time_range: 時間範圍
            filters: 額外的搜尋篩選條件
            
        Returns:
            ProductSearchResult: 搜尋結果
        """
        start_time = time.time()
        
        try:
            logger.info(f"開始搜尋產品: {product_name} 在路徑: {base_path}")
            
            # 設定預設篩選條件
            if filters is None:
                filters = SearchFilters(time_range=time_range)
            else:
                filters.time_range = time_range
            
            # 1. 定位產品資料夾
            if filters.search_directory != "auto":
                product_folder = await self._locate_product_folder_by_directory(base_path, product_name, filters.search_directory)
            else:
                product_folder = await self._locate_product_folder(base_path, product_name, filters.search_directory)
            if not product_folder:
                return ProductSearchResult(
                    product_name=product_name,
                    product_folder=base_path,
                    matched_files=[],
                    total_files=0,
                    total_files_in_directory=0,
                    search_duration=time.time() - start_time,
                    filters_applied=filters,
                    status=SearchStatus.FAILED,
                    error_message=f"找不到產品資料夾: {product_name}"
                )
            
            logger.info(f"找到產品資料夾: {product_folder}")
            
            # 2. 並行搜尋檔案
            matched_files = await self._parallel_search_files(product_folder, filters)

            # 如果沒有找到符合條件的檔案，統計目錄總檔案數
            total_files_in_directory = 0
            if not matched_files:
                total_files_in_directory = await self._count_total_files(product_folder)

            # 如果沒有找到符合時間範圍的檔案，嘗試放寬時間限制
            if not matched_files and filters.time_range:
                logger.info("🔄 沒有找到符合時間範圍的檔案，嘗試放寬時間限制...")
                relaxed_filters = SearchFilters(
                    time_range=None,  # 移除時間限制
                    file_types=filters.file_types,
                    min_size=filters.min_size,
                    max_size=filters.max_size,
                    include_directories=filters.include_directories,
                    search_directory=filters.search_directory
                )
                matched_files = await self._parallel_search_files(product_folder, relaxed_filters)
                logger.info(f"🔄 放寬時間限制後找到 {len(matched_files)} 個檔案")
            
            # 3. 建立搜尋結果
            result = ProductSearchResult(
                product_name=product_name,
                product_folder=product_folder,
                matched_files=matched_files,
                total_files=len(matched_files),
                total_files_in_directory=total_files_in_directory,  # 新增總檔案數
                search_duration=time.time() - start_time,
                filters_applied=filters,
                status=SearchStatus.COMPLETED
            )
            
            logger.info(f"搜尋完成: 找到 {len(matched_files)} 個檔案，耗時 {result.search_duration:.2f} 秒")
            return result
            
        except Exception as e:
            logger.error(f"搜尋產品資料夾時發生錯誤: {e}")
            return ProductSearchResult(
                product_name=product_name,
                product_folder=base_path,
                matched_files=[],
                total_files=0,
                total_files_in_directory=0,
                search_duration=time.time() - start_time,
                filters_applied=filters or SearchFilters(),
                status=SearchStatus.FAILED,
                error_message=str(e)
            )

    async def _count_total_files(self, folder_path: Path) -> int:
        """統計目錄中的總檔案數（不包含篩選條件）

        Args:
            folder_path: 要統計的資料夾路徑

        Returns:
            int: 總檔案數
        """
        try:
            accessible_path = self._convert_to_accessible_path(folder_path)
            total_count = 0

            for item in accessible_path.rglob('*'):
                if item.is_file():
                    total_count += 1

            logger.info(f"📊 目錄 {folder_path} 總共包含 {total_count} 個檔案")
            return total_count

        except Exception as e:
            logger.warning(f"統計檔案數時發生錯誤: {e}")
            return 0

    async def _locate_product_folder_by_directory(self, base_path: Path, product_name: str, search_directory: str) -> Optional[Path]:
        """根據指定目錄搜尋產品資料夾

        Args:
            base_path: 基礎搜尋路徑
            product_name: 產品名稱
            search_directory: 指定搜尋目錄

        Returns:
            Optional[Path]: 找到的產品資料夾路徑，如果沒找到則返回 None
        """
        try:
            # 將 UNC 路徑轉換為可存取的路徑
            search_path = self._convert_to_accessible_path(base_path)
            logger.info(f"🔍 開始在指定目錄搜尋產品: {product_name} (目錄: {search_directory})")

            if not search_path.exists():
                logger.warning(f"基礎路徑不存在: {search_path}")
                return None

            found_folders = []

            if search_directory == "all":
                # 搜尋所有目錄
                search_dirs = ["", "ETD", "FT", "GTK", "JCAP", "JCET", "JSSI", "JCAP_JCET", "AMAT", "ASML", "LAM", "TEL", "KLA"]
            else:
                # 搜尋指定目錄
                search_dirs = [search_directory]

            for dir_name in search_dirs:
                try:
                    if dir_name == "":
                        # 搜尋根目錄
                        current_path = search_path
                        logger.info("📁 搜尋根目錄")
                    else:
                        # 搜尋子目錄
                        current_path = search_path / dir_name
                        logger.info(f"📁 搜尋 {dir_name} 目錄")

                    if not current_path.exists():
                        logger.debug(f"ℹ️ {dir_name or '根'} 目錄不存在")
                        continue

                    items = list(current_path.iterdir())
                    logger.info(f"📊 {dir_name or '根'} 目錄包含 {len(items)} 個項目")

                    for item in items:
                        if item.is_dir():
                            logger.debug(f"🔍 檢查目錄: {item.name}")
                            if product_name.lower() in item.name.lower():
                                found_folders.append(item)
                                logger.info(f"✅ 在 {dir_name or '根目錄'} 找到匹配: {item.name}")

                except Exception as e:
                    logger.warning(f"❌ 搜尋 {dir_name or '根目錄'} 失敗: {e}")

            logger.info(f"🎯 指定目錄搜尋完成，總共找到 {len(found_folders)} 個匹配資料夾")

            if found_folders:
                # 選擇最佳匹配的資料夾
                selected_folder = self._select_best_folder(found_folders, product_name)
                logger.info(f"✓ 選擇產品資料夾: {selected_folder}")
                return selected_folder
            else:
                logger.warning(f"在指定目錄 {search_directory} 中找不到產品: {product_name}")
                return None

        except Exception as e:
            logger.error(f"指定目錄搜尋時發生錯誤: {e}")
            return None

    async def _locate_product_folder(self, base_path: Path, product_name: str, search_directory: str = "auto") -> Optional[Path]:
        """定位產品資料夾
        
        Args:
            base_path: 基礎搜尋路徑
            product_name: 產品名稱
            
        Returns:
            Optional[Path]: 產品資料夾路徑，如果找不到則返回 None
        """
        try:
            # 將 UNC 路徑轉換為可存取的路徑
            search_path = self._convert_to_accessible_path(base_path)
            logger.info(f"[DEBUG] 搜尋路徑: {search_path}")
            logger.info(f"[DEBUG] 路徑類型: {type(search_path)}")

            # 簡化路徑檢查
            if not search_path.exists():
                logger.warning(f"基礎路徑不存在: {search_path}")
                return None
            
            def search_directories():
                """基於測試腳本的有效搜尋邏輯"""
                found_folders = []

                try:
                    logger.info(f"🔍 開始搜尋產品: {product_name}")

                    # 階段1: 搜尋根目錄
                    logger.info("📁 階段1: 搜尋根目錄")
                    try:
                        items = list(search_path.iterdir())
                        logger.info(f"📊 根目錄包含 {len(items)} 個項目")

                        for item in items:
                            if item.is_dir():
                                logger.debug(f"🔍 檢查目錄: {item.name}")
                                if product_name.lower() in item.name.lower():
                                    found_folders.append(item)
                                    logger.info(f"✅ 在根目錄找到匹配: {item.name}")

                    except Exception as e:
                        logger.warning(f"❌ 搜尋根目錄失敗: {e}")

                    # 階段2: 如果根目錄沒找到，搜尋 ETD/FT 目錄
                    if not found_folders:
                        logger.info("📁 階段2: 搜尋 ETD/FT 目錄")
                        etd_path = search_path / "ETD" / "FT"

                        try:
                            if etd_path.exists():
                                ft_items = list(etd_path.iterdir())
                                logger.info(f"📊 ETD/FT 目錄包含 {len(ft_items)} 個項目")

                                for item in ft_items:
                                    if item.is_dir():
                                        logger.debug(f"🔍 檢查 ETD/FT 目錄: {item.name}")
                                        if product_name.lower() in item.name.lower():
                                            found_folders.append(item)
                                            logger.info(f"✅ 在 ETD/FT 找到匹配: {item.name}")

                            else:
                                logger.info("ℹ️ ETD/FT 目錄不存在")

                        except Exception as e:
                            logger.warning(f"❌ 搜尋 ETD/FT 目錄失敗: {e}")

                    # 階段3: 搜尋所有重要目錄的子目錄（不管前面是否找到）
                    logger.info("📁 階段3: 搜尋所有重要目錄")
                    important_dirs = ["GTK", "JCAP", "JCET", "JSSI", "JCAP_JCET", "AMAT", "ASML", "LAM", "TEL", "KLA"]

                    for dir_name in important_dirs:
                        try:
                            dir_path = search_path / dir_name
                            if dir_path.exists():
                                sub_items = list(dir_path.iterdir())
                                logger.debug(f"📊 {dir_name} 目錄包含 {len(sub_items)} 個項目")

                                for item in sub_items:
                                    if item.is_dir():
                                        logger.debug(f"🔍 檢查 {dir_name} 子目錄: {item.name}")
                                        if product_name.lower() in item.name.lower():
                                            found_folders.append(item)
                                            logger.info(f"✅ 在 {dir_name} 找到匹配: {item.name}")
                            else:
                                logger.debug(f"ℹ️ {dir_name} 目錄不存在")

                        except Exception as e:
                            logger.warning(f"❌ 搜尋 {dir_name} 目錄失敗: {e}")

                    # 移除深度遞迴搜尋，專注於產品資料夾搜尋

                    logger.info(f"🎯 搜尋完成，總共找到 {len(found_folders)} 個匹配資料夾")
                    return found_folders

                except Exception as e:
                    logger.error(f"搜尋過程發生錯誤: {e}")
                    return []
            
            # 直接執行搜尋（避免異步複雜性）
            found_folders = search_directories()

            if found_folders:
                # 返回第一個找到的資料夾
                selected_folder = found_folders[0]
                logger.info(f"✓ 選擇產品資料夾: {selected_folder}")
                return selected_folder
            else:
                logger.info(f"✗ 未找到產品資料夾: {product_name}")
                return None
            
        except Exception as e:
            logger.error(f"定位產品資料夾時發生錯誤: {e}")
            return None
    
    async def _parallel_search_files(
        self, 
        product_folder: Path, 
        filters: SearchFilters
    ) -> List[FileInfo]:
        """並行搜尋檔案
        
        Args:
            product_folder: 產品資料夾路徑
            filters: 搜尋篩選條件
            
        Returns:
            List[FileInfo]: 符合條件的檔案列表
        """
        try:
            logger.info(f"🔍 開始搜尋檔案在: {product_folder}")

            # 獲取所有子目錄
            subdirectories = []
            try:
                for item in product_folder.iterdir():
                    if item.is_dir():
                        subdirectories.append(item)
            except (PermissionError, OSError) as e:
                logger.warning(f"❌ 無法讀取產品資料夾: {e}")
                return []

            # 如果沒有子目錄，直接搜尋當前目錄
            if not subdirectories:
                subdirectories = [product_folder]

            logger.info(f"📊 準備搜尋 {len(subdirectories)} 個目錄")
            
            # 簡化：直接同步搜尋，避免複雜的異步問題
            all_files = []
            for subdir in subdirectories:
                try:
                    files = self._search_directory_sync(subdir, filters)
                    all_files.extend(files)
                    logger.debug(f"📁 搜尋 {subdir.name}: 找到 {len(files)} 個檔案")
                except Exception as e:
                    logger.warning(f"❌ 搜尋 {subdir} 失敗: {e}")

            logger.info(f"✅ 檔案搜尋完成，共找到 {len(all_files)} 個檔案")
            return all_files

        except Exception as e:
            logger.error(f"❌ 搜尋檔案時發生錯誤: {e}")
            return []
    
    def _search_directory_sync(self, directory: Path, filters: SearchFilters) -> List[FileInfo]:
        """同步搜尋目錄（在執行緒中執行）
        
        Args:
            directory: 要搜尋的目錄
            filters: 搜尋篩選條件
            
        Returns:
            List[FileInfo]: 符合條件的檔案列表
        """
        files = []
        
        try:
            # 使用 os.walk 進行遞迴搜尋
            for root, dirs, filenames in os.walk(directory):
                root_path = Path(root)
                
                # 處理目錄
                if filters.include_directories:
                    for dirname in dirs:
                        dir_path = root_path / dirname
                        try:
                            stat_info = dir_path.stat()
                            modified_time = datetime.fromtimestamp(stat_info.st_mtime)
                            
                            if filters.time_range and not filters.time_range.contains(modified_time):
                                continue
                            
                            file_info = FileInfo(
                                path=dir_path,
                                name=dirname,
                                size=0,
                                modified_time=modified_time,
                                file_type="目錄",
                                is_directory=True
                            )
                            files.append(file_info)
                            
                        except (OSError, PermissionError):
                            continue
                
                # 處理檔案
                for filename in filenames:
                    file_path = root_path / filename
                    
                    try:
                        stat_info = file_path.stat()
                        modified_time = datetime.fromtimestamp(stat_info.st_mtime)
                        
                        # 應用篩選條件
                        if not filters.matches_file(file_path, stat_info.st_size, modified_time):
                            continue
                        
                        # 判斷檔案類型
                        file_type = self._get_file_type(file_path)
                        
                        file_info = FileInfo(
                            path=file_path,
                            name=filename,
                            size=stat_info.st_size,
                            modified_time=modified_time,
                            file_type=file_type,
                            is_directory=False
                        )
                        files.append(file_info)
                        
                    except (OSError, PermissionError):
                        continue
            
        except Exception as e:
            logger.warning(f"搜尋目錄 {directory} 時發生錯誤: {e}")
        
        return files
    
    def _get_file_type(self, file_path: Path) -> str:
        """判斷檔案類型
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            str: 檔案類型描述
        """
        ext = file_path.suffix.lower()
        
        type_map = {
            ('.txt', '.log', '.csv'): "文字檔案",
            ('.xlsx', '.xls'): "Excel 檔案",
            ('.zip', '.7z', '.rar'): "壓縮檔案",
            ('.pdf',): "PDF 檔案",
            ('.doc', '.docx'): "Word 檔案",
            ('.jpg', '.jpeg', '.png', '.gif'): "圖片檔案",
            ('.mp4', '.avi', '.mov'): "影片檔案",
            ('.py', '.js', '.html', '.css'): "程式碼檔案"
        }
        
        for extensions, file_type in type_map.items():
            if ext in extensions:
                return file_type
        
        return "其他檔案"
    
    def _convert_to_accessible_path(self, path: Path) -> Path:
        """將路徑轉換為可存取的格式
        
        Args:
            path: 原始路徑
            
        Returns:
            Path: 可存取的路徑
        """
        path_str = str(path)
        
        # 如果是 UNC 路徑，在 Windows 上直接使用
        if path_str.startswith("\\\\"):
            return Path(path_str)
        
        # 其他情況直接返回
        return path
    
    def create_time_range(self, time_range_type: TimeRangeType, 
                         custom_start: Optional[datetime] = None,
                         custom_end: Optional[datetime] = None) -> TimeRange:
        """建立時間範圍
        
        Args:
            time_range_type: 時間範圍類型
            custom_start: 自訂開始時間
            custom_end: 自訂結束時間
            
        Returns:
            TimeRange: 時間範圍物件
        """
        if time_range_type == TimeRangeType.LAST_WEEK:
            return TimeRange.last_days(7)
        elif time_range_type == TimeRangeType.LAST_MONTH:
            return TimeRange.last_months(1)
        elif time_range_type == TimeRangeType.LAST_3_MONTHS:
            return TimeRange.last_months(3)
        elif time_range_type == TimeRangeType.LAST_6_MONTHS:
            return TimeRange.last_months(6)
        elif time_range_type == TimeRangeType.CURRENT_QUARTER:
            return TimeRange.current_quarter()
        elif time_range_type == TimeRangeType.CUSTOM:
            if custom_start and custom_end:
                return TimeRange(custom_start, custom_end)
            else:
                # 預設為最近 6 個月
                return TimeRange.last_months(6)
        else:
            # 預設為最近 6 個月
            return TimeRange.last_months(6)
    
    async def create_search_task(
        self,
        product_name: str,
        base_path: Path,
        filters: SearchFilters
    ) -> str:
        """建立搜尋任務
        
        Args:
            product_name: 產品名稱
            base_path: 基礎搜尋路徑
            filters: 搜尋篩選條件
            
        Returns:
            str: 任務 ID
        """
        task_id = str(uuid.uuid4())
        
        task = SearchTask(
            task_id=task_id,
            product_name=product_name,
            base_path=base_path,
            filters=filters,
            created_at=datetime.now(),
            status=SearchStatus.PENDING
        )
        
        self.active_tasks[task_id] = task
        logger.info(f"建立搜尋任務: {task_id} for product: {product_name}")
        
        return task_id
    
    async def execute_search_task(self, task_id: str) -> Optional[ProductSearchResult]:
        """執行搜尋任務
        
        Args:
            task_id: 任務 ID
            
        Returns:
            Optional[ProductSearchResult]: 搜尋結果，如果任務不存在則返回 None
        """
        if task_id not in self.active_tasks:
            logger.warning(f"搜尋任務不存在: {task_id}")
            return None
        
        task = self.active_tasks[task_id]
        task.status = SearchStatus.IN_PROGRESS
        
        try:
            result = await self.search_product_folder(
                task.product_name,
                task.base_path,
                task.filters.time_range,
                task.filters
            )
            
            task.result = result
            task.status = SearchStatus.COMPLETED if result.success else SearchStatus.FAILED
            
            return result
            
        except Exception as e:
            logger.error(f"執行搜尋任務 {task_id} 時發生錯誤: {e}")
            task.status = SearchStatus.FAILED
            return None
    
    def get_task_status(self, task_id: str) -> Optional[SearchTask]:
        """獲取任務狀態
        
        Args:
            task_id: 任務 ID
            
        Returns:
            Optional[SearchTask]: 任務物件，如果不存在則返回 None
        """
        return self.active_tasks.get(task_id)
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的任務
        
        Args:
            max_age_hours: 任務最大保留時間（小時）
        """
        current_time = datetime.now()
        tasks_to_remove = []
        
        for task_id, task in self.active_tasks.items():
            if task.is_completed():
                age_hours = (current_time - task.created_at).total_seconds() / 3600
                if age_hours > max_age_hours:
                    tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.active_tasks[task_id]
            logger.debug(f"清理已完成任務: {task_id}")
        
        if tasks_to_remove:
            logger.info(f"清理了 {len(tasks_to_remove)} 個已完成任務")