"""
跨平台路徑管理器
自動檢測 Windows/Linux 環境並進行路徑轉換
"""

import os
import platform
import re
from typing import Tuple, Union, Optional
from pathlib import Path


class PathManager:
    """智能跨平台路徑管理器"""
    
    _current_os = None
    _is_wsl = None
    
    @classmethod
    def detect_environment(cls) -> <PERSON><PERSON>[str, bool]:
        """
        檢測當前執行環境
        
        Returns:
            Tuple[str, bool]: (作業系統, 是否為WSL)
        """
        if cls._current_os is not None and cls._is_wsl is not None:
            return cls._current_os, cls._is_wsl
        
        system = platform.system().lower()
        is_wsl = False
        
        if system == 'linux':
            # 檢測是否在 WSL 環境中
            try:
                with open('/proc/version', 'r') as f:
                    version_info = f.read().lower()
                    is_wsl = 'microsoft' in version_info or 'wsl' in version_info
            except (FileNotFoundError, PermissionError):
                is_wsl = False
        
        cls._current_os = system
        cls._is_wsl = is_wsl
        
        return system, is_wsl
    
    @classmethod
    def is_windows_path(cls, path: str) -> bool:
        """檢查是否為 Windows 路徑格式"""
        return bool(re.match(r'^[A-Za-z]:\\', path))
    
    @classmethod
    def is_wsl_path(cls, path: str) -> bool:
        """檢查是否為 WSL 路徑格式"""
        return path.startswith('/mnt/') and bool(re.match(r'^/mnt/[a-z]/', path))
    
    @classmethod
    def is_unix_path(cls, path: str) -> bool:
        """檢查是否為 Unix 路徑格式"""
        return path.startswith('/') and not cls.is_wsl_path(path)
    
    @classmethod
    def windows_to_wsl(cls, windows_path: str) -> str:
        """Windows 路徑轉 WSL 路徑"""
        if not cls.is_windows_path(windows_path):
            return windows_path
        
        match = re.match(r'^([A-Za-z]):\\(.*)$', windows_path)
        if match:
            drive = match.group(1).lower()
            path = match.group(2).replace('\\', '/')
            return f"/mnt/{drive}/{path}"
        
        return windows_path
    
    @classmethod
    def wsl_to_windows(cls, wsl_path: str) -> str:
        """WSL 路徑轉 Windows 路徑"""
        if not cls.is_wsl_path(wsl_path):
            return wsl_path
        
        match = re.match(r'^/mnt/([a-z])/(.*)$', wsl_path)
        if match:
            drive = match.group(1).upper()
            path = match.group(2).replace('/', '\\')
            return f"{drive}:\\{path}"
        
        return wsl_path
    
    @classmethod
    def normalize_path(cls, path: str) -> str:
        """
        根據當前環境正規化路徑
        
        Args:
            path: 輸入路徑
            
        Returns:
            str: 適合當前環境的路徑
        """
        if not path:
            return path
        
        current_os, is_wsl = cls.detect_environment()
        
        # Windows 環境
        if current_os == 'windows':
            if cls.is_wsl_path(path):
                # WSL 路徑轉 Windows 路徑
                normalized = cls.wsl_to_windows(path)
                print(f"路徑轉換 (WSL→Windows): {path} → {normalized}")
                return normalized
            return path
        
        # Linux/WSL 環境
        elif current_os == 'linux':
            if is_wsl:
                # WSL 環境：Windows 路徑轉 WSL 路徑
                if cls.is_windows_path(path):
                    normalized = cls.windows_to_wsl(path)
                    print(f"路徑轉換 (Windows→WSL): {path} → {normalized}")
                    return normalized
            else:
                # 純 Linux 環境：將 WSL 路徑轉為相對路徑或處理
                if cls.is_wsl_path(path):
                    # 嘗試轉換為本地路徑
                    normalized = cls._wsl_to_linux(path)
                    print(f"路徑轉換 (WSL→Linux): {path} → {normalized}")
                    return normalized
                elif cls.is_windows_path(path):
                    # Windows 路徑在純 Linux 環境中需要特殊處理
                    normalized = cls._windows_to_linux(path)
                    print(f"路徑轉換 (Windows→Linux): {path} → {normalized}")
                    return normalized
            return path
        
        # macOS 或其他系統
        else:
            # 類似 Linux 處理
            if cls.is_windows_path(path):
                normalized = cls._windows_to_linux(path)
                print(f"路徑轉換 (Windows→{current_os}): {path} → {normalized}")
                return normalized
            elif cls.is_wsl_path(path):
                normalized = cls._wsl_to_linux(path)
                print(f"路徑轉換 (WSL→{current_os}): {path} → {normalized}")
                return normalized
            return path
    
    @classmethod
    def _wsl_to_linux(cls, wsl_path: str) -> str:
        """WSL 路徑轉純 Linux 路徑（移[EXCEPT_CHAR] /mnt 前綴）"""
        match = re.match(r'^/mnt/([a-z])/(.*)$', wsl_path)
        if match:
            path = match.group(2)
            # 嘗試找到對應的本地路徑
            return f"/{path}"
        return wsl_path
    
    @classmethod
    def _windows_to_linux(cls, windows_path: str) -> str:
        """Windows 路徑轉純 Linux 路徑"""
        match = re.match(r'^([A-Za-z]):\\(.*)$', windows_path)
        if match:
            path = match.group(2).replace('\\', '/')
            # 嘗試相對路徑或當前目錄下的路徑
            return path
        return windows_path
    
    @classmethod
    def find_existing_path(cls, *path_candidates: str) -> Optional[str]:
        """
        從多個路徑候選中找到第一個存在的路徑
        
        Args:
            *path_candidates: 路徑候選列表
            
        Returns:
            str | None: 第一個存在的路徑，如果都不存在則返回 None
        """
        for path in path_candidates:
            if not path:
                continue
            
            normalized = cls.normalize_path(path)
            if os.path.exists(normalized):
                return normalized
        
        return None
    
    @classmethod
    def smart_path_resolution(cls, original_path: str) -> Tuple[str, str, bool]:
        """
        智能路徑解析：嘗試多種路徑格式
        
        Args:
            original_path: 原始路徑
            
        Returns:
            Tuple[str, str, bool]: (原始路徑, 可用路徑, 是否存在)
        """
        if not original_path:
            return original_path, original_path, False
        
        # 生成所有可能的路徑格式
        candidates = [
            original_path,  # 原始路徑
            cls.normalize_path(original_path),  # 正規化路徑
        ]
        
        # 如果是 Windows 路徑，添加 WSL 格式
        if cls.is_windows_path(original_path):
            candidates.append(cls.windows_to_wsl(original_path))
        
        # 如果是 WSL 路徑，添加 Windows 格式
        elif cls.is_wsl_path(original_path):
            candidates.append(cls.wsl_to_windows(original_path))
        
        # 嘗試相對路徑
        if os.path.isabs(original_path):
            relative_path = os.path.basename(original_path)
            candidates.append(relative_path)
            candidates.append(f"./{relative_path}")
            candidates.append(f"doc/{relative_path}")
        
        # 找到第一個存在的路徑
        for path in candidates:
            if path and os.path.exists(path):
                if path != original_path:
                    print(f"路徑解析成功: {original_path} → {path}")
                return original_path, path, True
        
        # 如果都不存在，返回正規化後的路徑
        normalized = cls.normalize_path(original_path)
        print(f"路徑不存在，使用正規化路徑: {original_path} → {normalized}")
        return original_path, normalized, False
    
    @classmethod
    def get_environment_info(cls) -> dict:
        """獲取環境信息"""
        current_os, is_wsl = cls.detect_environment()
        
        return {
            'os': current_os,
            'is_wsl': is_wsl,
            'platform': platform.platform(),
            'python_platform': platform.system(),
            'cwd': os.getcwd(),
            'supports_wsl_conversion': current_os in ['windows', 'linux'],
            'path_separator': os.path.sep,
            'environment_type': cls._get_environment_type()
        }
    
    @classmethod
    def _get_environment_type(cls) -> str:
        """獲取環境類型描述"""
        current_os, is_wsl = cls.detect_environment()
        
        if current_os == 'windows':
            return 'Windows (Native)'
        elif current_os == 'linux' and is_wsl:
            return 'Linux (WSL)'
        elif current_os == 'linux':
            return 'Linux (Native)'
        elif current_os == 'darwin':
            return 'macOS'
        else:
            return f'{current_os.title()} (Unknown)'


# 向下相容的函數介面
def normalize_path(path: str) -> str:
    """向下相容的路徑正規化函數"""
    return PathManager.normalize_path(path)


def smart_path_resolution(path: str) -> Tuple[str, str, bool]:
    """向下相容的智能路徑解析函數"""
    return PathManager.smart_path_resolution(path)


def get_environment_info() -> dict:
    """向下相容的環境信息函數"""
    return PathManager.get_environment_info()# 測試更新
