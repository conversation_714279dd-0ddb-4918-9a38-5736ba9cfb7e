"""
FT-EQC 分組處理器測試 - TDD 實作
測試檔案發現、分類、配對和分組功能
"""

import pytest
import tempfile
import os
from datetime import datetime
from pathlib import Path
from unittest.mock import patch

# 導入待測試的類別
from backend.shared.infrastructure.adapters.excel.ft_eqc_grouping_processor import FTEQCGroupingProcessor


class TestFTEQCGroupingProcessor:
    """FT-EQC 分組處理器測試類別"""
    
    def setup_method(self):
        """每個測試前的設置"""
        # 創建臨時目錄結構
        self.temp_dir = tempfile.mkdtemp()
        self.test_data_dir = Path(self.temp_dir)
        
        # 創建子資料夾結構
        (self.test_data_dir / "subfolder1").mkdir()
        (self.test_data_dir / "subfolder2" / "nested").mkdir(parents=True)
        
        # 創建測試用 CSV 檔案
        self._create_test_csv_files()
    
    def teardown_method(self):
        """每個測試後的清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_test_csv_files(self):
        """創建測試用的 CSV 檔案"""
        # FT 檔案 (包含 (ft) 標記)
        ft_content = """Device,Bin,Test_Time,Site
Header Line 1,(ft),TestData,Site1
Header Line 2,Additional,Info,Data
1,1,100.5,1
2,1,101.2,2
3,2,105.8,3"""
        
        # EQC 檔案 (包含 (qc) 標記)
        eqc_content = """Device,Bin,Test_Time,Site
Header Line 1,(qc),TestData,Site1
Header Line 2,Additional,Info,Data
1,1,100.5,1
2,2,101.2,2  
3,1,105.8,3"""
        
        # Online EQC 檔案 (包含 (qc) 且路徑含 onlineeqc)
        online_eqc_content = """Device,Bin,Test_Time,Site
Header Line 1,(qc),TestData,Site1
Header Line 2,Additional,Info,Data
1,1,100.5,1
2,2,101.2,2"""
        
        # CTA 格式檔案 (第7行含 Serial_No)
        cta_content = """Device,Bin,Test_Time,Site
Header Line 1,(ft),TestData,Site1
Line 3,Info,Data,More
Line 4,Info,Data,More
Line 5,Info,Data,More
Line 6,Info,Data,More
Line 7,Device,Serial_No,Site,Part_No,Dut_No,Site_No
Line 8,cta,test,data,more
Line 9,Info,Data,More
Line 10,Info,Data,More
Line 11,Info,Time,More
1,1,100.5,1,PN001,DUT001,SITE1
2,1,101.2,2,PN001,DUT002,SITE2"""
        
        # 創建檔案 (使用時間戳命名以測試配對)
        timestamp = "20250607"
        
        # 主資料夾檔案
        self._write_csv_file(f"FT_test_{timestamp}01.csv", ft_content)
        self._write_csv_file(f"EQC_test_{timestamp}02.csv", eqc_content)
        self._write_csv_file(f"onlineeqc_test_{timestamp}03.csv", online_eqc_content)
        
        # 子資料夾檔案
        subfolder1 = self.test_data_dir / "subfolder1"
        self._write_csv_file(f"FT_sub1_{timestamp}10.csv", ft_content, subfolder1)
        self._write_csv_file(f"EQC_sub1_{timestamp}11.csv", eqc_content, subfolder1)
        
        # 嵌套子資料夾檔案  
        nested = self.test_data_dir / "subfolder2" / "nested"
        self._write_csv_file(f"CTA_nested_{timestamp}20.csv", cta_content, nested)
        
        # 無關檔案 (應被排[EXCEPT_CHAR])
        self._write_csv_file("other_file.txt", "not a csv", self.test_data_dir)
        self._write_csv_file("EQCTOTALDATA.csv", "processed file", self.test_data_dir)
    
    def _write_csv_file(self, filename: str, content: str, directory: Path = None):
        """寫入 CSV 檔案"""
        if directory is None:
            directory = self.test_data_dir
        
        file_path = directory / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def test_init_processor(self):
        """測試處理器初始化"""
        processor = FTEQCGroupingProcessor()
        assert processor is not None
        assert hasattr(processor, 'file_discovery')
        assert hasattr(processor, 'time_matcher')
        assert hasattr(processor, 'cta_handler')
    
    def test_discover_all_csv_files_recursive(self):
        """測試遞迴發現所有 CSV 檔案"""
        processor = FTEQCGroupingProcessor()
        csv_files = processor.discover_csv_files(str(self.test_data_dir))
        
        # 預期找到的檔案數量 (排[EXCEPT_CHAR] EQCTOTALDATA.csv 和 .txt 檔案)
        assert len(csv_files) == 6  # 6個有效的 CSV 檔案
        
        # 檢查是否包含子資料夾的檔案
        file_names = [os.path.basename(f) for f in csv_files]
        assert any("sub1" in name for name in file_names)
        assert any("nested" in name for name in file_names)
    
    def test_classify_ft_files(self):
        """測試 FT 檔案分類"""
        processor = FTEQCGroupingProcessor()
        csv_files = processor.discover_csv_files(str(self.test_data_dir))
        ft_files = processor.classify_ft_files(csv_files)
        
        # 預期找到 3 個 FT 檔案 (包含 CTA 檔案)
        assert len(ft_files) == 3
        
        # 檢查 FT 檔案是否正確識別
        ft_names = [os.path.basename(f) for f in ft_files]
        assert any("FT_test" in name for name in ft_names)
        assert any("CTA_nested" in name for name in ft_names)
    
    def test_classify_eqc_files(self):
        """測試 EQC 檔案分類"""
        processor = FTEQCGroupingProcessor()
        csv_files = processor.discover_csv_files(str(self.test_data_dir))
        eqc_files = processor.classify_eqc_files(csv_files)
        
        # 預期找到 3 個 EQC 檔案 (包含 Online EQC)
        assert len(eqc_files) == 3
        
        # 檢查檔案類型正確
        eqc_names = [os.path.basename(f) for f in eqc_files]
        assert any("EQC_test" in name for name in eqc_names)
        assert any("onlineeqc" in name for name in eqc_names)
    
    def test_time_based_matching_by_filename(self):
        """測試基於檔案名稱時間戳的配對"""
        # processor = FTEQCGroupingProcessor()
        
        # 模擬檔案列表
        # ft_files = ["FT_test_20250607001.csv", "FT_test_20250607010.csv"]
        # eqc_files = ["EQC_test_20250607002.csv", "EQC_test_20250607012.csv"]
        
        # 測試時間差 < 400 的配對
        # matches = processor.match_by_filename_timestamp(ft_files, eqc_files)
        # assert len(matches) == 2  # 應該有 2 對成功匹配
        
        # 測試配對正確性
        # assert ("FT_test_20250607001.csv", "EQC_test_20250607002.csv") in matches
        # assert ("FT_test_20250607010.csv", "EQC_test_20250607012.csv") in matches
        pass
    
    def test_time_based_matching_by_modification_time(self):
        """測試基於檔案修改時間的配對"""
        # processor = FTEQCGroupingProcessor()
        
        # 創建同時間修改的檔案進行測試
        # with patch('os.path.getmtime') as mock_mtime:
        #     # 模擬檔案修改時間
        #     mock_mtime.side_effect = lambda f: 1000 if "FT" in f else 1050
        #     
        #     ft_files = ["FT_test.csv"]
        #     eqc_files = ["EQC_test.csv"]
        #     
        #     matches = processor.match_by_modification_time(ft_files, eqc_files)
        #     assert len(matches) == 1  # 時間差 50秒 < 60秒閾值
        pass
    
    def test_cta_format_detection_and_matching(self):
        """測試 CTA 格式檢測和匹配"""
        # processor = FTEQCGroupingProcessor()
        
        # cta_file = str(self.test_data_dir / "subfolder2" / "nested" / "CTA_nested_2025060720.csv")
        # cta_info = processor.detect_cta_format(cta_file)
        
        # 驗證 CTA 格式檢測
        # assert cta_info['format'] == 8290  # Serial_No 格式
        # assert cta_info['match_mode'] == 'three_column'
        pass
    
    def test_complete_grouping_process(self):
        """測試完整的分組處理流程"""
        processor = FTEQCGroupingProcessor()
        result = processor.process_folder(str(self.test_data_dir))
        
        # 驗證返回結果結構
        assert hasattr(result, 'matched_pairs')
        assert hasattr(result, 'unmatched_eqc') 
        assert hasattr(result, 'statistics')
        
        # 驗證統計資料
        stats = result.statistics
        assert stats['total_csv_files'] > 0
        assert stats['ft_files_count'] > 0
        assert stats['eqc_files_count'] > 0
    
    def test_exclude_processed_files(self):
        """測試排[EXCEPT_CHAR]已處理的檔案"""
        # processor = FTEQCGroupingProcessor()
        # csv_files = processor.discover_csv_files(str(self.test_data_dir))
        
        # 確認排[EXCEPT_CHAR]了 EQCTOTALDATA.csv 和 EQCFAILDATA.csv
        # file_names = [os.path.basename(f).lower() for f in csv_files]
        # assert not any("eqctotaldata" in name for name in file_names)
        # assert not any("eqcfaildata" in name for name in file_names)
        pass
    
    def test_handle_empty_folder(self):
        """測試處理空資料夾"""
        # processor = FTEQCGroupingProcessor()
        # empty_dir = tempfile.mkdtemp()
        
        # result = processor.process_folder(empty_dir)
        
        # 驗證空資料夾處理
        # assert result['matched_pairs'] == []
        # assert result['unmatched_eqc'] == []
        # assert result['statistics']['total_csv_files'] == 0
        
        # shutil.rmtree(empty_dir)
        pass
    
    def test_error_handling_invalid_path(self):
        """測試錯誤處理 - 無效路徑"""
        # processor = FTEQCGroupingProcessor()
        
        # with pytest.raises(FileNotFoundError):
        #     processor.process_folder("/invalid/path/does/not/exist")
        pass


if __name__ == "__main__":
    # 執行測試 - 應該全部失敗 (TDD Red 階段)
    pytest.main([__file__, "-v"])