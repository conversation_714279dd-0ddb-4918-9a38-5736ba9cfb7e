"""統一日誌系統
提供企業級多服務日誌聚合、監控和分析功能
"""

import sys
import os
import json
import traceback
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Callable
from enum import Enum
from dataclasses import dataclass, field
from contextlib import contextmanager

from loguru import logger
import structlog

from .config_manager import get_config_manager


class LogLevel(Enum):
    """日誌級別"""
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogFormat(Enum):
    """日誌格式"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    JSON = "json"
    STRUCTURED = "structured"


@dataclass
class ServiceLogContext:
    """服務日誌上下文"""
    service_name: str
    service_type: str
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    extra_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LogEntry:
    """日誌條目"""
    timestamp: datetime
    level: LogLevel
    service_name: str
    message: str
    context: Optional[ServiceLogContext] = None
    exception: Optional[Exception] = None
    stack_trace: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class LogFilter:
    """日誌過濾器"""
    
    def __init__(self, 
                 min_level: LogLevel = LogLevel.INFO,
                 include_services: Optional[List[str]] = None,
                 exclude_services: Optional[List[str]] = None,
                 include_patterns: Optional[List[str]] = None,
                 exclude_patterns: Optional[List[str]] = None):
        self.min_level = min_level
        self.include_services = include_services or []
        self.exclude_services = exclude_services or []
        self.include_patterns = [re.compile(p) for p in (include_patterns or [])]
        self.exclude_patterns = [re.compile(p) for p in (exclude_patterns or [])]
    
    def should_log(self, entry: LogEntry) -> bool:
        """判斷是否應該記錄此日誌"""
        # 級別過濾
        level_values = {
            LogLevel.TRACE: 0,
            LogLevel.DEBUG: 1,
            LogLevel.INFO: 2,
            LogLevel.SUCCESS: 2,
            LogLevel.WARNING: 3,
            LogLevel.ERROR: 4,
            LogLevel.CRITICAL: 5
        }
        
        if level_values.get(entry.level, 0) < level_values.get(self.min_level, 0):
            return False
        
        # 服務過濾
        if self.include_services and entry.service_name not in self.include_services:
            return False
        
        if self.exclude_services and entry.service_name in self.exclude_services:
            return False
        
        # 模式過濾
        if self.include_patterns:
            if not any(pattern.search(entry.message) for pattern in self.include_patterns):
                return False
        
        if self.exclude_patterns:
            if any(pattern.search(entry.message) for pattern in self.exclude_patterns):
                return False
        
        return True


class LogFormatter:
    """日誌格式化器"""
    
    def __init__(self, format_type: LogFormat = LogFormat.DETAILED):
        self.format_type = format_type
    
    def format(self, entry: LogEntry) -> str:
        """格式化日誌條目"""
        if self.format_type == LogFormat.SIMPLE:
            return self._format_simple(entry)
        elif self.format_type == LogFormat.DETAILED:
            return self._format_detailed(entry)
        elif self.format_type == LogFormat.JSON:
            return self._format_json(entry)
        elif self.format_type == LogFormat.STRUCTURED:
            return self._format_structured(entry)
        else:
            return self._format_detailed(entry)
    
    def _format_simple(self, entry: LogEntry) -> str:
        """簡單格式"""
        timestamp = entry.timestamp.strftime("%H:%M:%S")
        return f"[{timestamp}] {entry.level.value} - {entry.service_name} - {entry.message}"
    
    def _format_detailed(self, entry: LogEntry) -> str:
        """詳細格式"""
        timestamp = entry.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        
        parts = [
            f"[{timestamp}]",
            f"[{entry.level.value}]",
            f"[{entry.service_name}]",
            f"{entry.message}"
        ]
        
        if entry.context and entry.context.request_id:
            parts.insert(-1, f"[REQ:{entry.context.request_id[:8]}]")
        
        if entry.exception:
            parts.append(f"\n異常: {str(entry.exception)}")
        
        if entry.stack_trace:
            parts.append(f"\n堆棧追蹤:\n{entry.stack_trace}")
        
        return " ".join(parts)
    
    def _format_json(self, entry: LogEntry) -> str:
        """
JSON 格式"""
        data = {
            "timestamp": entry.timestamp.isoformat(),
            "level": entry.level.value,
            "service": entry.service_name,
            "message": entry.message,
            "metadata": entry.metadata
        }
        
        if entry.context:
            data["context"] = {
                "request_id": entry.context.request_id,
                "user_id": entry.context.user_id,
                "session_id": entry.context.session_id,
                "extra_data": entry.context.extra_data
            }
        
        if entry.exception:
            data["exception"] = {
                "type": type(entry.exception).__name__,
                "message": str(entry.exception),
                "stack_trace": entry.stack_trace
            }
        
        return json.dumps(data, ensure_ascii=False, default=str)
    
    def _format_structured(self, entry: LogEntry) -> str:
        """結構化格式"""
        # 使用 structlog 風格
        data = {
            "event": entry.message,
            "level": entry.level.value,
            "service": entry.service_name,
            "timestamp": entry.timestamp.isoformat()
        }
        
        if entry.context:
            data.update({
                "request_id": entry.context.request_id,
                "user_id": entry.context.user_id,
                "session_id": entry.context.session_id
            })
            data.update(entry.context.extra_data)
        
        data.update(entry.metadata)
        
        # 將數據格式化為 key=value 形式
        formatted_parts = []
        for key, value in data.items():
            if value is not None:
                formatted_parts.append(f"{key}={value}")
        
        return " ".join(formatted_parts)


class UnifiedLogger:
    """統一日誌系統
    
    整合所有服務的日誌輸出，提供統一的日誌管理和監控
    """
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.filters: List[LogFilter] = []
        self.formatters: Dict[str, LogFormatter] = {
            "console": LogFormatter(LogFormat.DETAILED),
            "file": LogFormatter(LogFormat.JSON),
            "error": LogFormatter(LogFormat.DETAILED)
        }
        self.log_handlers: List[Callable] = []
        self.service_contexts: Dict[str, ServiceLogContext] = {}
        
        # 初始化日誌系統
        self._setup_logging()
    
    def _setup_logging(self):
        """設定日誌系統"""
        # 清除現有的 handlers
        logger.remove()
        
        # 獲取配置
        log_level = getattr(self.config_manager.service_config, 'logging_level', 'INFO')
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 控制台輸出
        logger.add(
            sys.stdout,
            level=log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            filter=self._console_filter,
            colorize=True
        )
        
        # 文件輸出
        logger.add(
            log_dir / "integrated_services.log",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 錯誤日誌
        logger.add(
            log_dir / "errors.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message} | {exception}",
            rotation="5 MB",
            retention="60 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 服務特定日誌
        for service_name in self.config_manager.get_enabled_services().keys():
            logger.add(
                log_dir / f"{service_name}.log",
                level="INFO",
                format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {message}",
                filter=lambda record, svc=service_name: record["extra"].get("service") == svc,
                rotation="5 MB",
                retention="15 days",
                encoding="utf-8"
            )
    
    def _console_filter(self, record) -> bool:
        """控制台輸出過濾器"""
        # 過濾掉太冗長的調試訊息
        message = record["message"].lower()
        
        # 過濾關鍵字
        noise_keywords = [
            "favicon.ico",
            "health check",
            "ping",
            "heartbeat"
        ]
        
        for keyword in noise_keywords:
            if keyword in message:
                return False
        
        return True
    
    def create_service_context(self, 
                              service_name: str, 
                              service_type: str,
                              request_id: Optional[str] = None,
                              **kwargs) -> ServiceLogContext:
        """建立服務日誌上下文"""
        context = ServiceLogContext(
            service_name=service_name,
            service_type=service_type,
            request_id=request_id,
            extra_data=kwargs
        )
        
        self.service_contexts[service_name] = context
        return context
    
    def get_service_logger(self, service_name: str):
        """獲取服務專用日誌器"""
        return logger.bind(service=service_name)
    
    @contextmanager
    def service_context(self, service_name: str, **kwargs):
        """服務日誌上下文管理器"""
        service_logger = self.get_service_logger(service_name)
        
        # 記錄服務進入
        service_logger.info(f"服務 {service_name} 開始處理", **kwargs)
        
        try:
            yield service_logger
        except Exception as e:
            # 記錄異常
            service_logger.error(
                f"服務 {service_name} 發生異常: {str(e)}",
                exception=traceback.format_exc(),
                **kwargs
            )
            raise
        finally:
            # 記錄服務離開
            service_logger.info(f"服務 {service_name} 處理完成", **kwargs)
    
    def log_request(self, 
                   service_name: str,
                   method: str,
                   path: str,
                   status_code: Optional[int] = None,
                   duration: Optional[float] = None,
                   **kwargs):
        """記錄 HTTP 請求"""
        service_logger = self.get_service_logger(service_name)
        
        message_parts = [f"{method} {path}"]
        
        if status_code:
            message_parts.append(f"[{status_code}]")
        
        if duration:
            message_parts.append(f"({duration:.3f}s)")
        
        level = "INFO"
        if status_code and status_code >= 400:
            level = "WARNING" if status_code < 500 else "ERROR"
        
        getattr(service_logger, level.lower())(
            " ".join(message_parts),
            method=method,
            path=path,
            status_code=status_code,
            duration=duration,
            **kwargs
        )
    
    def log_performance(self, 
                       service_name: str,
                       operation: str,
                       duration: float,
                       **metrics):
        """記錄效能指標"""
        service_logger = self.get_service_logger(service_name)
        
        service_logger.info(
            f"效能指標: {operation} 耗時 {duration:.3f}s",
            operation=operation,
            duration=duration,
            **metrics
        )
    
    def log_business_event(self, 
                          service_name: str,
                          event_type: str,
                          description: str,
                          **data):
        """記錄業務事件"""
        service_logger = self.get_service_logger(service_name)
        
        service_logger.info(
            f"業務事件: {event_type} - {description}",
            event_type=event_type,
            description=description,
            **data
        )
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """獲取日誌統計資訊"""
        log_dir = Path("logs")
        
        stats = {
            "log_directory": str(log_dir),
            "log_files": [],
            "total_size_mb": 0,
            "services": list(self.service_contexts.keys())
        }
        
        if log_dir.exists():
            for log_file in log_dir.glob("*.log"):
                file_size = log_file.stat().st_size / (1024 * 1024)  # MB
                stats["log_files"].append({
                    "name": log_file.name,
                    "size_mb": round(file_size, 2),
                    "modified": datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
                stats["total_size_mb"] += file_size
            
            stats["total_size_mb"] = round(stats["total_size_mb"], 2)
        
        return stats
    
    def cleanup_old_logs(self, days: int = 30):
        """清理舊日誌文件"""
        log_dir = Path("logs")
        
        if not log_dir.exists():
            return
        
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
        cleaned_files = []
        
        for log_file in log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    cleaned_files.append(log_file.name)
                except Exception as e:
                    logger.warning(f"無法刪除日誌文件 {log_file}: {e}")
        
        if cleaned_files:
            logger.info(f"清理了 {len(cleaned_files)} 個舊日誌文件")


# 全域日誌管理器實例
_unified_logger: Optional[UnifiedLogger] = None


def get_unified_logger() -> UnifiedLogger:
    """獲取全域統一日誌管理器實例"""
    global _unified_logger
    
    if _unified_logger is None:
        _unified_logger = UnifiedLogger()
    
    return _unified_logger


def init_unified_logger() -> UnifiedLogger:
    """初始化統一日誌管理器"""
    global _unified_logger
    _unified_logger = UnifiedLogger()
    return _unified_logger
