"""
策略 B 處理器：先處理 BIN → 再找 SITE → site 分佈 + 超連結
優化版本：短路邏輯 + 記憶體效率 + 詳細 LOG
"""

import pandas as pd
import os
from typing import Dict, List, Tuple, Any
from datetime import datetime
import openpyxl
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter
try:
    from .site_column_finder import SiteColumnFinder
except ImportError:
    from site_column_finder import SiteColumnFinder


class StrategyBProcessor:
    """
    策略 B 處理器：BIN-first 完整檢查策略
    
    流程：
    1. BIN 處理 + 記錄第一個 fail 位置（完整檢查模式）
    2. Site 欄位查找
    3. Site 統計計算
    4. 超連結建立
    """
    
    def __init__(self, enable_logging: bool = True):
        self.enable_logging = enable_logging
        self.site_finder = SiteColumnFinder(enable_logging=enable_logging)
        self.spec_check_count = 0  # 效能統計
        self.short_circuit_count = 0  # 短路次數統計
        
    def process_bins_with_fail_tracking(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[int, int]]:
        """
        步驟 1: BIN 處理 + 記錄第一個 fail 位置（完整檢查模式）
        
        Returns:
            (processed_df, fail_positions) 
            fail_positions: {device_row: first_fail_col}
        """
        print("[SEARCH] 策略 B - 步驟 1: BIN 處理 + fail 位置追蹤（完整檢查模式）")
        
        fail_positions = {}
        devices_processed = 0
        total_spec_checks = 0
        short_circuits = 0
        
        if df.empty:
            print("DataFrame 為空，跳過 BIN 處理")
            return df, fail_positions
        
        # 設定範圍
        device_start_row = 12  # 第13行開始
        max_limit_row = 9      # 第10行 Max 限值
        min_limit_row = 10     # 第11行 Min 限值
        bin_assignment_row = 5 # 第6行 BIN 分配
        test_start_col = 2     # 從 C 欄開始測試項目
        
        # 對每個設備進行 BIN 分配（關鍵：短路優化）
        for device_row in range(device_start_row, len(df)):
            if len(df.columns) > 1:
                assigned_bin = 1  # 預設 BIN 1 (PASS)
                first_fail_found = False
                
                # [SEARCH] 完整檢查：從頭檢查到尾，記錄第一個 FAIL 的 BIN
                for test_col in range(test_start_col, len(df.columns)):
                    total_spec_checks += 1
                    
                    # 獲取測試值、上限、下限
                    test_value = df.iloc[device_row, test_col]
                    max_limit = df.iloc[max_limit_row, test_col] if max_limit_row < len(df) else 'none'
                    min_limit = df.iloc[min_limit_row, test_col] if min_limit_row < len(df) else 'none'
                    
                    # 檢查是否 FAIL
                    if not self._is_test_pass(test_value, max_limit, min_limit):
                        # 只記錄第一個 FAIL 位置，但繼續檢查所有項目
                        if not first_fail_found:
                            assigned_bin = df.iloc[bin_assignment_row, test_col]
                            try:
                                assigned_bin = int(assigned_bin)
                            except (ValueError, TypeError):
                                assigned_bin = test_col + 3  # 預設 BIN 號碼
                            
                            fail_positions[device_row] = test_col
                            first_fail_found = True
                            short_circuits += 1
                            
                            # [SEARCH] 修正：記錄第一個 FAIL 但繼續檢查完所有項目
                            # 不使用 break，繼續檢查後續項目
                
                # 更新設備的 BIN 欄位
                df.iloc[device_row, 1] = assigned_bin
                devices_processed += 1
        
        # 統計資訊
        self.spec_check_count = total_spec_checks
        self.short_circuit_count = short_circuits
        
        print(f"[OK] 完成 {devices_processed} 個設備的 BIN 處理")
        print(f"[FAST] 效能統計: {total_spec_checks:,} 次 spec 檢查（完整檢查模式）")
        print(f"[SEARCH] 第一個 FAIL 記錄: {short_circuits} 個設備有 FAIL")
        
        return df, fail_positions
    
    def scan_bin_column_and_calculate_site_stats(self, df: pd.DataFrame, site_column: int) -> Dict[str, Dict[str, int]]:
        """
        步驟 3: 掃描 B 欄所有 BIN，讀取對應 Site，統計各 Site fail 分佈
        
        功能替換原則：直接取代舊版本，正確實作 BIN 欄掃描
        """
        print("[CHART] 策略 B - 步驟 3: 掃描 B 欄 BIN + Site 統計")
        
        site_stats = {}
        device_start_row = 12
        devices_scanned = 0
        pass_count = 0
        fail_count = 0
        
        print(f"[SEARCH] 開始掃描 B 欄 BIN 號碼...")
        
        for device_row in range(device_start_row, len(df)):
            if site_column < len(df.columns) and len(df.columns) > 1:
                # 讀取 B 欄的 BIN 號碼
                bin_no = str(df.iloc[device_row, 1]).strip()  # B 欄 (index 1)
                
                # 讀取對應的 Site 值
                site = str(df.iloc[device_row, site_column]).strip()  # Site 欄
                
                # 初始化 site 統計
                if site not in site_stats:
                    site_stats[site] = {
                        'total': 0,
                        'pass': 0,
                        'fail': 0,
                        'bins': {}
                    }
                
                # 記錄這個 BIN
                if bin_no not in site_stats[site]['bins']:
                    site_stats[site]['bins'][bin_no] = 0
                site_stats[site]['bins'][bin_no] += 1
                
                # 統計總數
                site_stats[site]['total'] += 1
                
                # 判斷 PASS/FAIL (BIN 1 = PASS, 其他 = FAIL)
                if bin_no == '1':
                    site_stats[site]['pass'] += 1
                    pass_count += 1
                else:
                    site_stats[site]['fail'] += 1
                    fail_count += 1
                
                devices_scanned += 1
                
                # 顯示前 5 個設備的掃描結果
                if devices_scanned <= 5:
                    excel_row = device_row + 1
                    print(f"  設備 {excel_row}: Site {site}, BIN {bin_no}")
        
        print(f"[OK] 完成 {devices_scanned} 個設備的 B 欄掃描")
        print(f"[CHART] 發現 {len(site_stats)} 個不同的 Site")
        print(f"[TARGET] 總計: PASS {pass_count} 個, FAIL {fail_count} 個")
        
        # 顯示各 Site 統計摘要
        print(f"\n[BOARD] 各 Site 統計摘要:")
        for site, stats in site_stats.items():
            print(f"  Site {site}: 總計 {stats['total']}, PASS {stats['pass']}, FAIL {stats['fail']}")
        
        return site_stats
    
    def create_hyperlinks(self, workbook: openpyxl.Workbook, fail_positions: Dict[int, int]) -> int:
        """
        步驟 4: 建立超連結（FAIL BIN → 第一個 fail 位置）
        """
        print("[LINK] 策略 B - 步驟 4: 建立超連結")
        
        worksheet = workbook.active
        hyperlinks_created = 0
        
        for device_row, fail_col in fail_positions.items():
            try:
                # BIN 欄位位置 (B欄)
                bin_cell = worksheet.cell(row=device_row + 1, column=2)
                
                # 第一個 fail 位置
                fail_col_letter = get_column_letter(fail_col + 1)
                fail_cell_address = f"{fail_col_letter}{device_row + 1}"
                
                # 建立超連結
                bin_cell.hyperlink = f"#{fail_cell_address}"
                bin_cell.font = Font(color="FF0000", underline='single')  # 紅色 + 底線
                
                hyperlinks_created += 1
                
            except Exception as e:
                print(f"[WARNING] 建立超連結失敗 - 設備行 {device_row}: {str(e)}")
        
        print(f"[OK] 完成 {hyperlinks_created} 個超連結建立")
        return hyperlinks_created
    
    def execute_strategy_b(self, df: pd.DataFrame, csv_file_path: str = "") -> Dict[str, Any]:
        """
        執行完整的策略 B 工作流程
        """
        print(f"[TARGET] 開始執行策略 B - 優化版 BIN-first 處理")
        print(f"[FOLDER] 檔案: {os.path.basename(csv_file_path)}")
        
        start_time = datetime.now()
        
        # 步驟 1: BIN 處理 + fail 位置追蹤
        df_processed, fail_positions = self.process_bins_with_fail_tracking(df)
        
        # 步驟 2: Site 欄位查找
        print("[SEARCH] 策略 B - 步驟 2: Site 欄位查找")
        site_column, site_log = self.site_finder.find_site_column_with_log(
            df_processed, csv_file_path, header_row=7, start_col=2
        )
        
        # 步驟 3: 掃描 B 欄 BIN + Site 統計
        site_statistics = {}
        if site_column is not None:
            site_statistics = self.scan_bin_column_and_calculate_site_stats(df_processed, site_column)
        else:
            print("[ERROR] 未找到 Site 欄位，跳過 Site 統計")
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 生成詳細 LOG
        log_content = self._generate_strategy_b_log(
            csv_file_path, df_processed, fail_positions, site_statistics, 
            site_column, processing_time, start_time
        )
        
        if self.enable_logging:
            self._write_strategy_b_log(log_content)
        
        result = {
            'processed_df': df_processed,
            'fail_positions': fail_positions,
            'site_column': site_column,
            'site_statistics': site_statistics,
            'processing_time': processing_time,
            'spec_check_count': self.spec_check_count,
            'short_circuit_count': self.short_circuit_count,
            'log_content': log_content
        }
        
        print(f"[PARTY] 策略 B 完成！總處理時間: {processing_time:.2f} 秒")
        return result
    
    def execute_site_statistics_only(self, df: pd.DataFrame, csv_file_path: str = "", site_column: int = None) -> Dict[str, Any]:
        """
        執行純 Site 統計 (不重新做 BIN 分配)
        用於 csv_to_excel_converter.py 存檔前的統計分析
        """
        print(f"[CHART] 執行 Site 統計分析 - 純統計模式")
        print(f"[FOLDER] 檔案: {os.path.basename(csv_file_path)}")
        
        start_time = datetime.now()
        
        # 使用傳入的 site_column 或自動查找
        if site_column is None:
            print("[SEARCH] 自動查找 Site 欄位...")
            site_column, site_log = self.site_finder.find_site_column_with_log(
                df, csv_file_path, header_row=7, start_col=2
            )
        else:
            print(f"[SEARCH] 使用指定的 Site 欄位: 第{site_column+1}欄")
        
        # 執行 Site 統計 (掃描已分配的 BIN)
        site_statistics = {}
        if site_column is not None:
            site_statistics = self.scan_bin_column_and_calculate_site_stats(df, site_column)
        else:
            print("[ERROR] 未找到 Site 欄位，跳過 Site 統計")
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 生成統計 LOG
        log_content = self._generate_site_statistics_log(
            csv_file_path, df, site_statistics, site_column, processing_time, start_time
        )
        
        if self.enable_logging:
            self._write_site_statistics_log(log_content)
        
        result = {
            'site_column': site_column,
            'site_statistics': site_statistics,
            'processing_time': processing_time,
            'log_content': log_content
        }
        
        print(f"[OK] Site 統計分析完成！處理時間: {processing_time:.2f} 秒")
        return result
    
    def _is_test_pass(self, test_value, max_limit, min_limit) -> bool:
        """檢查測試項目是否 PASS"""
        try:
            test_val = float(str(test_value))
            
            # 檢查上限
            if str(max_limit).lower() not in ['none', 'null', '', 'nan']:
                try:
                    max_val = float(str(max_limit))
                    if test_val > max_val:
                        return False
                except (ValueError, TypeError):
                    pass
            
            # 檢查下限
            if str(min_limit).lower() not in ['none', 'null', '', 'nan']:
                try:
                    min_val = float(str(min_limit))
                    if test_val < min_val:
                        return False
                except (ValueError, TypeError):
                    pass
            
            return True
            
        except (ValueError, TypeError):
            return False
    
    def _generate_strategy_b_log(self, csv_file_path: str, df: pd.DataFrame, 
                                fail_positions: Dict[int, int], site_statistics: Dict[str, Dict[str, int]], 
                                site_column: int, processing_time: float, start_time: datetime) -> str:
        """生成策略 B 詳細 LOG"""
        
        log_lines = []
        log_lines.append("[TARGET] 策略 B 處理記錄 - BIN-first 優化版")
        log_lines.append("=" * 80)
        log_lines.append(f"檔案: {os.path.basename(csv_file_path)}")
        log_lines.append(f"處理時間: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        log_lines.append(f"資料規模: {len(df)} 行 x {len(df.columns)} 欄")
        log_lines.append("")
        
        # 效能統計
        log_lines.append("[FAST] 效能統計:")
        log_lines.append(f"  總處理時間: {processing_time:.2f} 秒")
        log_lines.append(f"  Spec 檢查次數: {self.spec_check_count:,} (完整檢查模式)")
        log_lines.append(f"  第一個 FAIL 記錄數: {self.short_circuit_count}")
        log_lines.append("")
        
        # Fail 位置統計
        log_lines.append("[START] BIN 處理結果:")
        log_lines.append(f"  發現 FAIL 設備: {len(fail_positions)} 個")
        log_lines.append(f"  PASS 設備: {len(df) - 12 - len(fail_positions)} 個")
        
        if fail_positions:
            # 分析 fail 位置分布
            fail_cols = list(fail_positions.values())
            min_fail_col = min(fail_cols)
            max_fail_col = max(fail_cols)
            avg_fail_col = sum(fail_cols) / len(fail_cols)
            
            log_lines.append(f"  第一個 Fail 位置範圍: 第{min_fail_col+1}欄 到 第{max_fail_col+1}欄")
            log_lines.append(f"  平均 Fail 位置: 第{avg_fail_col:.1f}欄")
            
            # 顯示前10個 fail 位置範例
            log_lines.append("")
            log_lines.append("[BOARD] Fail 位置範例 (前10個):")
            log_lines.append("| 設備行 | Excel行 | 第一個Fail欄 | Excel欄 | BIN號 |")
            log_lines.append("|--------|----------|-------------|----------|-------|")
            
            count = 0
            for device_row, fail_col in fail_positions.items():
                if count >= 10:
                    break
                    
                excel_row = device_row + 1
                fail_col_letter = get_column_letter(fail_col + 1)
                bin_no = df.iloc[device_row, 1] if device_row < len(df) else "N/A"
                
                log_lines.append(f"| {device_row:6d} | {excel_row:8d} | {fail_col:11d} | {fail_col_letter:8s} | {str(bin_no):5s} |")
                count += 1
            
            if len(fail_positions) > 10:
                log_lines.append(f"| ...    | ...      | ...         | ...      | ...   |")
                log_lines.append(f"總計 {len(fail_positions)} 個 FAIL 設備")
        
        log_lines.append("")
        
        # Site 統計 Summary
        if site_column is not None:
            col_letter = get_column_letter(site_column + 1)
            log_lines.append(f"[SEARCH] Site 欄位: 第{site_column+1}欄 ({col_letter})")
            log_lines.append("")
            log_lines.append("[CHART] 各 Site Fail 分佈 Summary:")
            log_lines.append("=" * 50)
            
            for site, stats in site_statistics.items():
                log_lines.append(f"\n[FACTORY] Site {site} 統計:")
                log_lines.append(f"  總設備數: {stats['total']}")
                log_lines.append(f"  PASS 設備: {stats['pass']} ({stats['pass']/stats['total']*100:.1f}%)")
                log_lines.append(f"  FAIL 設備: {stats['fail']} ({stats['fail']/stats['total']*100:.1f}%)")
                
                log_lines.append("  BIN 分佈:")
                # 按 BIN 號碼排序
                sorted_bins = sorted(stats['bins'].items(), key=lambda x: int(x[0]) if x[0].isdigit() else 9999)
                for bin_no, count in sorted_bins:
                    percentage = (count / stats['total'] * 100) if stats['total'] > 0 else 0
                    status = "PASS" if bin_no == '1' else "FAIL"
                    log_lines.append(f"    BIN {bin_no:3s}: {count:2d} 個 ({percentage:5.1f}%) - {status}")
            
            # 整體統計對比
            log_lines.append(f"\n[STATS] Site 對比 Summary:")
            log_lines.append("| Site | 總計 | PASS | FAIL | Pass率 | 主要Fail BIN |")
            log_lines.append("|------|------|------|------|--------|--------------|")
            
            for site, stats in site_statistics.items():
                pass_rate = (stats['pass'] / stats['total'] * 100) if stats['total'] > 0 else 0
                
                # 找出最多的 FAIL BIN
                fail_bins = {k: v for k, v in stats['bins'].items() if k != '1'}
                main_fail_bin = "N/A"
                if fail_bins:
                    main_fail_bin = max(fail_bins.items(), key=lambda x: x[1])[0]
                
                log_lines.append(f"| {site:4s} | {stats['total']:4d} | {stats['pass']:4d} | {stats['fail']:4d} | {pass_rate:5.1f}% | BIN {main_fail_bin:3s} |")
        else:
            log_lines.append("[ERROR] Site 欄位: 未找到")
        
        log_lines.append("")
        log_lines.append("[TARGET] 策略 B 優勢:")
        log_lines.append("  [OK] 完整檢查: 從頭檢查到尾，確保準確性")
        log_lines.append("  [OK] 第一個 FAIL 記錄: 準確記錄第一個 FAIL 的 BIN")
        log_lines.append("  [OK] 記憶體效率: 只記錄必要的 fail 位置")
        log_lines.append("  [OK] 超連結就緒: fail 位置可直接建立超連結")
        log_lines.append("  [OK] Site 統計: 基於 BIN 結果快速計算")
        log_lines.append("")
        log_lines.append(f"策略 B 完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return "\n".join(log_lines)
    
    def _write_strategy_b_log(self, log_content: str):
        """寫入策略 B LOG 到檔案"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            # 輸出到專案根目錄的 logs 資料夾
            project_root = "/mnt/d/project/python/outlook_summary"
            logs_dir = os.path.join(project_root, "logs")
            log_filename = os.path.join(logs_dir, f"STRATEGY_B_LOG_{timestamp}.txt")
            
            os.makedirs(logs_dir, exist_ok=True)
            
            with open(log_filename, 'w', encoding='utf-8') as f:
                f.write(log_content)
            
            print(f"[EDIT] 策略 B 記錄已保存: {log_filename}")
            
        except Exception as e:
            print(f"[WARNING] 無法保存策略 B 記錄: {str(e)}")
    
    def _generate_site_statistics_log(self, csv_file_path: str, df: pd.DataFrame, 
                                    site_statistics: Dict[str, Dict[str, int]], 
                                    site_column: int, processing_time: float, start_time: datetime) -> str:
        """生成 Site 統計分析 LOG"""
        
        log_lines = []
        log_lines.append("[CHART] Site 統計分析記錄 - 純統計模式")
        log_lines.append("=" * 80)
        log_lines.append(f"檔案: {os.path.basename(csv_file_path)}")
        log_lines.append(f"處理時間: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        log_lines.append(f"資料規模: {len(df)} 行 x {len(df.columns)} 欄")
        log_lines.append("")
        
        # 效能統計
        log_lines.append("[FAST] 效能統計:")
        log_lines.append(f"  總處理時間: {processing_time:.2f} 秒")
        log_lines.append(f"  處理模式: 純 Site 統計 (不重新分配 BIN)")
        log_lines.append("")
        
        # Site 統計詳情
        if site_column is not None:
            from openpyxl.utils import get_column_letter
            col_letter = get_column_letter(site_column + 1)
            log_lines.append(f"[SEARCH] Site 欄位: 第{site_column+1}欄 ({col_letter})")
            log_lines.append("")
            log_lines.append("[CHART] 各 Site BIN 分佈 Summary:")
            log_lines.append("=" * 50)
            
            total_devices = 0
            total_pass = 0
            total_fail = 0
            
            for site, stats in site_statistics.items():
                total_devices += stats['total']
                total_pass += stats['pass']
                total_fail += stats['fail']
                
                log_lines.append(f"\n[FACTORY] Site {site} 統計:")
                log_lines.append(f"  總設備數: {stats['total']}")
                log_lines.append(f"  PASS 設備: {stats['pass']} ({stats['pass']/stats['total']*100:.1f}%)")
                log_lines.append(f"  FAIL 設備: {stats['fail']} ({stats['fail']/stats['total']*100:.1f}%)")
                
                log_lines.append("  BIN 分佈:")
                # 按 BIN 號碼排序
                sorted_bins = sorted(stats['bins'].items(), key=lambda x: int(x[0]) if x[0].isdigit() else 9999)
                for bin_no, count in sorted_bins:
                    percentage = (count / stats['total'] * 100) if stats['total'] > 0 else 0
                    status = "PASS" if bin_no == '1' else "FAIL"
                    log_lines.append(f"    BIN {bin_no:3s}: {count:2d} 個 ({percentage:5.1f}%) - {status}")
            
            # 整體統計對比
            log_lines.append(f"\n[STATS] 整體統計 Summary:")
            log_lines.append(f"  總設備數: {total_devices}")
            log_lines.append(f"  總 PASS: {total_pass} ({total_pass/total_devices*100:.1f}%)")
            log_lines.append(f"  總 FAIL: {total_fail} ({total_fail/total_devices*100:.1f}%)")
            log_lines.append(f"  Site 數量: {len(site_statistics)} 個")
            
            # Site 對比表格
            log_lines.append(f"\n[STATS] Site 對比 Summary:")
            log_lines.append("| Site | 總計 | PASS | FAIL | Pass率 | 主要Fail BIN |")
            log_lines.append("|------|------|------|------|--------|--------------|")
            
            for site, stats in site_statistics.items():
                pass_rate = (stats['pass'] / stats['total'] * 100) if stats['total'] > 0 else 0
                
                # 找出最多的 FAIL BIN
                fail_bins = {k: v for k, v in stats['bins'].items() if k != '1'}
                main_fail_bin = "N/A"
                if fail_bins:
                    main_fail_bin = max(fail_bins.items(), key=lambda x: x[1])[0]
                
                log_lines.append(f"| {site:4s} | {stats['total']:4d} | {stats['pass']:4d} | {stats['fail']:4d} | {pass_rate:5.1f}% | BIN {main_fail_bin:3s} |")
        else:
            log_lines.append("[ERROR] Site 欄位: 未找到")
        
        log_lines.append("")
        log_lines.append("[TARGET] 純統計模式優勢:")
        log_lines.append("  [OK] 高效率: 不重新計算 BIN，直接讀取已分配結果")
        log_lines.append("  [OK] 準確性: 基於最終 BIN 分配結果統計")
        log_lines.append("  [OK] 整合性: 與 csv_to_excel_converter.py 完美整合")
        log_lines.append("")
        log_lines.append(f"Site 統計分析完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return "\n".join(log_lines)
    
    def _write_site_statistics_log(self, log_content: str):
        """寫入 Site 統計 LOG 到 datalog.txt"""
        try:
            # 統一輸出到 logs 資料夾的 datalog.txt
            project_root = "/mnt/d/project/python/outlook_summary"
            logs_dir = os.path.join(project_root, "logs")
            os.makedirs(logs_dir, exist_ok=True)
            log_filename = os.path.join(logs_dir, "datalog.txt")
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 附加模式寫入，保留之前的記錄
            with open(log_filename, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*80}\n")
                f.write(f"SITE 統計分析記錄 - {timestamp}\n")
                f.write(f"{'='*80}\n")
                f.write(log_content)
                f.write(f"\n{'='*80}\n\n")
            
            print(f"[EDIT] Site 統計記錄已附加到: datalog.txt")
            
        except Exception as e:
            print(f"[WARNING] 無法保存 Site 統計記錄: {str(e)}")


def test_strategy_b_with_real_csv():
    """使用實際 CSV 檔案測試策略 B"""
    csv_file_path = "/mnt/d/project/python/outlook_summary/doc/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv"
    
    print(f"[START] 測試策略 B - 使用實際 CSV")
    print(f"[FOLDER] 檔案: {os.path.basename(csv_file_path)}")
    
    # 讀取 CSV
    try:
        lines = []
        with open(csv_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                parts = line.strip().split(',')
                lines.append(parts)
        
        max_cols = max(len(parts) for parts in lines)
        for parts in lines:
            while len(parts) < max_cols:
                parts.append('')
        
        df = pd.DataFrame(lines)
        print(f"[OK] CSV 讀取成功: {len(df)} 行 x {len(df.columns)} 欄")
        
        # 執行策略 B
        processor = StrategyBProcessor(enable_logging=True)
        result = processor.execute_strategy_b(df, csv_file_path)
        
        print(f"[PARTY] 策略 B 測試完成!")
        print(f"[CHART] 結果摘要:")
        print(f"  - FAIL 設備: {len(result['fail_positions'])} 個")
        print(f"  - Site 數量: {len(result['site_statistics'])} 個")
        print(f"  - 處理時間: {result['processing_time']:.2f} 秒")
        print(f"  - Spec 檢查: {result['spec_check_count']:,} 次")
        print(f"  - 短路優化: {result['short_circuit_count']} 次")
        
        return result
        
    except Exception as e:
        print(f"[ERROR] 策略 B 測試失敗: {str(e)}")
        return None


if __name__ == "__main__":
    test_strategy_b_with_real_csv()