"""Step 1.1: 依賴函數測試
測試 dependencies.py 中的核心依賴函數實現

這個文件包含了 Step 1.1.1 創建的所有測試，用於驗證依賴函數的正確行為。
"""

import pytest
from unittest.mock import Mock, patch
from fastapi import HTTPException


class MockFileStagingService:
    """Mock 檔案暫存服務"""
    def __init__(self):
        self.service_id = "mock_staging_service"
        self.is_healthy = True
    
    def health_check(self):
        return {"status": "healthy", "service_id": self.service_id}
    
    def get_service_statistics(self):
        return {
            "total_tasks": 10,
            "active_tasks": 2,
            "completed_tasks": 8
        }


class MockFileProcessingService:
    """Mock 檔案處理服務"""
    def __init__(self):
        self.service_id = "mock_processing_service"
        self.is_healthy = True
    
    def health_check(self):
        return {"status": "healthy", "service_id": self.service_id}
    
    def get_service_statistics(self):
        return {
            "total_tasks": 5,
            "active_tasks": 1,
            "completed_tasks": 4
        }


class TestRequireStagingService:
    """測試 require_staging_service() 函數"""
    
    def test_require_staging_service_when_available(self):
        """測試：當暫存服務可用時，應該正確返回服務實例"""
        
        with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
            # 安排：模擬服務可用
            mock_service = MockFileStagingService()
            mock_get.return_value = mock_service
            
            # 導入要測試的函數
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from frontend.api.dependencies import require_staging_service
            
            # 執行：調用依賴函數
            result = require_staging_service()
            
            # 驗證：應該返回服務實例
            assert result is mock_service
            assert result.service_id == "mock_staging_service"
            mock_get.assert_called_once()
    
    def test_require_staging_service_when_unavailable(self):
        """測試：當暫存服務不可用時，應該拋出 HTTPException"""
        
        with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
            # 安排：模擬服務不可用
            mock_get.return_value = None
            
            # 導入要測試的函數
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from frontend.api.dependencies import require_staging_service
            
            # 執行和驗證：應該拋出 HTTPException
            with pytest.raises(HTTPException) as exc_info:
                require_staging_service()
            
            # 驗證錯誤詳情
            assert exc_info.value.status_code == 503
            assert "檔案暫存服務不可用" in str(exc_info.value.detail)
            mock_get.assert_called_once()
    
    def test_require_staging_service_with_initialization_error(self):
        """測試：當服務初始化有錯誤時，應該包含錯誤訊息"""
        
        with patch('src.presentation.api.dependencies.get_staging_service') as mock_get, \
             patch('src.presentation.api.dependencies.get_service_container') as mock_container:
            
            # 安排：模擬服務不可用且有初始化錯誤
            mock_get.return_value = None
            mock_container_instance = Mock()
            mock_container_instance.get_initialization_errors.return_value = {
                'staging': '連接資料庫失敗'
            }
            mock_container.return_value = mock_container_instance
            
            # 導入要測試的函數
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from frontend.api.dependencies import require_staging_service
            
            # 執行和驗證：應該拋出包含詳細錯誤的 HTTPException
            with pytest.raises(HTTPException) as exc_info:
                require_staging_service()
            
            # 驗證錯誤詳情包含初始化錯誤
            assert exc_info.value.status_code == 503
            assert "連接資料庫失敗" in str(exc_info.value.detail)


class TestRequireProcessingService:
    """測試 require_processing_service() 函數"""
    
    def test_require_processing_service_when_available(self):
        """測試：當處理服務可用時，應該正確返回服務實例"""
        
        with patch('src.presentation.api.dependencies.get_processing_service') as mock_get:
            # 安排：模擬服務可用
            mock_service = MockFileProcessingService()
            mock_get.return_value = mock_service
            
            # 導入要測試的函數
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from frontend.api.dependencies import require_processing_service
            
            # 執行：調用依賴函數
            result = require_processing_service()
            
            # 驗證：應該返回服務實例
            assert result is mock_service
            assert result.service_id == "mock_processing_service"
            mock_get.assert_called_once()
    
    def test_require_processing_service_when_unavailable(self):
        """測試：當處理服務不可用時，應該拋出 HTTPException"""
        
        with patch('src.presentation.api.dependencies.get_processing_service') as mock_get:
            # 安排：模擬服務不可用
            mock_get.return_value = None
            
            # 導入要測試的函數
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from frontend.api.dependencies import require_processing_service
            
            # 執行和驗證：應該拋出 HTTPException
            with pytest.raises(HTTPException) as exc_info:
                require_processing_service()
            
            # 驗證錯誤詳情
            assert exc_info.value.status_code == 503
            assert "檔案處理服務不可用" in str(exc_info.value.detail)
            mock_get.assert_called_once()


class TestDependencyFunctionBehavior:
    """測試依賴函數的整體行為"""
    
    def test_dependency_functions_are_callable(self):
        """測試：依賴函數應該是可調用的，而不是返回函數的函數"""
        
        # 導入要測試的函數
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from frontend.api.dependencies import require_staging_service, require_processing_service
        
        # 驗證：這些應該是函數，不是返回函數的函數
        assert callable(require_staging_service)
        assert callable(require_processing_service)
        
        # 驗證：調用時不應該返回另一個函數
        with patch('src.presentation.api.dependencies.get_staging_service', return_value=None):
            try:
                result = require_staging_service()
                # 如果沒有拋出異常，結果不應該是函數
                assert not callable(result), "require_staging_service() 不應該返回函數"
            except HTTPException:
                # 拋出 HTTPException 是正確的行為
                pass


if __name__ == "__main__":
    # 運行測試
    pytest.main([__file__, "-v"])
