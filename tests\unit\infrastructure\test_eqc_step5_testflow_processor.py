#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EQC Step 5 測試流程生成處理器 - 單元測試
按照 TDD 原則：先寫失敗測試，再實作功能
"""

import os
import pytest
import tempfile
from unittest.mock import Mock, patch
from datetime import datetime

# 導入待測試模組（會失敗，因為還沒實作）
from backend.shared.infrastructure.adapters.excel.eqc.eqc_step5_testflow_processor import EQCStep5TestFlowProcessor


class TestEQCStep5TestFlowProcessor:
    """EQC Step 5 測試流程生成處理器單元測試
    
    測試重點：
    1. 原格式保留驗證 - 確保輸出CSV與輸入CSV格式完全相同
    2. 數據行重排正確性 - 驗證線性測試流程排列正確
    3. 繁體中文訊息輸出
    4. 錯誤處理機制
    """

    def setup_method(self):
        """測試前設置"""
        self.processor = EQCStep5TestFlowProcessor()
        
        # 建立測試用的 CSV 資料（模擬 EQCTOTALDATA.csv）
        self.test_csv_content = [
            "WAFER_ID,BIN1,BIN2,BIN3,BIN4,BIN5,BIN6,BIN7,BIN8,BIN9,BIN10,BIN11,BIN12,BIN13,BIN14,BIN15,BIN16,BIN17,BIN18,BIN19,BIN20,BIN21,BIN22,BIN23,BIN24,BIN25,BIN26,BIN27,BIN28,BIN29,BIN30,BIN31,BIN32,BIN33,BIN34,BIN35,BIN36,BIN37,BIN38,BIN39,BIN40\n",
            "DIE_X,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "DIE_Y,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "PART_ID,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "SOFT_BIN,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "RESULT,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "X,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "Y,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "SITE_NUM,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "OnlineEQC_Fail,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0\n",
            "A,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "B,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "C,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            "D,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40\n",
            # Online EQC 區域（第14-33行）
            "OnlineEQC_01,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 第14行
            "OnlineEQC_02,15,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,37,0,1,0\n",  # 第15行 - FAIL: 15(OnlineEQC) <-> 37(RT)
            "OnlineEQC_03,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 第16行
            # ... 省略中間行
            "OnlineEQC_20,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 第33行
            # RT 區域（第34行之後）
            "RT_01,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 第34行
            "RT_02,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 第35行
            "RT_03,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,37,0,1,0\n",  # 第37行
        ]
        
        # 模擬 Step4 DEBUG LOG 內容
        self.test_debug_log_content = [
            "EQC Step 4 - CODE區間匹配搜尋 DEBUG LOG",
            "=" * 60,
            "開始時間: 2025-06-11 14:36:33",
            "",
            "FAIL 行檢測結果:",
            "第15行: FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)",
            "",
            "CODE 匹配搜尋結果:",
            "OnlineEQC第15行 → RT第37行: 匹配成功",
            "總計：1 個匹配"
        ]

    def test_processor_initialization(self):
        """測試處理器初始化"""
        assert self.processor is not None
        assert hasattr(self.processor, 'generate_test_flow_csv')

    def test_generate_test_flow_csv_success(self):
        """測試成功生成測試流程 CSV"""
        # 建立臨時檔案
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as temp_csv:
            temp_csv.writelines(self.test_csv_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(self.test_debug_log_content))
            temp_log_path = temp_log.name
        
        try:
            # 測試核心功能
            result = self.processor.generate_test_flow_csv(
                eqctotaldata_path=temp_csv_path,
                step4_debug_log_path=temp_log_path
            )
            
            # 驗證結果結構
            assert result['status'] == 'success'
            assert 'output_file' in result
            assert 'total_rows' in result
            assert 'reordered_rows' in result
            assert result['total_rows'] == len(self.test_csv_content)
            
            # 驗證輸出檔案存在
            assert os.path.exists(result['output_file'])
            
            # 驗證檔案內容（保持原格式）
            with open(result['output_file'], 'r', encoding='utf-8') as f:
                output_content = f.readlines()
            
            # 前13行應該完全相同
            for i in range(13):
                assert output_content[i] == self.test_csv_content[i]
            
            # 資料行數量應該相同（只是順序不同）
            assert len(output_content) == len(self.test_csv_content)
            
        finally:
            # 清理臨時檔案
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])

    def test_generate_test_flow_csv_file_not_found(self):
        """測試檔案不存在的錯誤處理"""
        result = self.processor.generate_test_flow_csv(
            eqctotaldata_path="/nonexistent/file.csv",
            step4_debug_log_path="/nonexistent/log.txt"
        )
        
        assert result['status'] == 'error'
        assert 'error_message' in result
        assert '檔案不存在' in result['error_message'] or '不存在' in result['error_message']

    def test_parse_fail_mapping_from_debug_log(self):
        """測試從 DEBUG LOG 解析 FAIL 對應關係"""
        # 建立臨時 DEBUG LOG 檔案
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(self.test_debug_log_content))
            temp_log_path = temp_log.name
        
        try:
            # 測試解析功能
            result = self.processor._parse_fail_mapping_from_debug_log(temp_log_path)
            
            # 驗證解析結果
            assert result['status'] == 'success'
            assert 'mappings' in result
            assert len(result['mappings']) > 0
            assert result['mappings'][0]['online_eqc_row'] == 15
            assert result['mappings'][0]['rt_row'] == 37
            
        finally:
            os.unlink(temp_log_path)

    def test_reorder_data_rows_for_test_flow(self):
        """測試資料行重新排列邏輯"""
        # 測試輸入：模擬 CSV 資料行（資料區從第14行開始，對應索引0）
        test_data_rows = [
            "OnlineEQC_01,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引0=行14
            "OnlineEQC_02,15,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,37,0,1,0\n",  # 索引1=行15 - FAIL
            # 跳過中間的行...
        ] + ["OnlineEQC_{i:02d},1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n".format(i=i) for i in range(3, 21)] + [
            # RT 區域開始（約第34行，索引20）
            "RT_01,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引20=行34
            "RT_02,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引21=行35
            "RT_03,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,37,0,1,0\n",  # 索引22=行36
            "RT_04,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,37,0,1,0\n",  # 索引23=行37 - RT匹配行
        ]
        
        # 測試 FAIL 對應關係：OnlineEQC行15 [LEFT_RIGHT_ARROW] RT行37
        fail_mappings = [{'online_eqc_row': 15, 'rt_row': 37, 'ft_row': 14}]
        
        # 測試重新排列功能
        result = self.processor._reorder_data_rows_for_test_flow(test_data_rows, fail_mappings)
        
        # 驗證結果
        assert result['status'] == 'success'
        assert 'reordered_rows' in result
        assert len(result['reordered_rows']) == len(test_data_rows)
        
        # 驗證至少有行被重新排列
        assert result['reordered_rows_count'] >= 0

    def test_csv_format_preservation(self):
        """測試原格式保留驗證 - 核心測試案例"""
        # 建立臨時檔案
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as temp_csv:
            temp_csv.writelines(self.test_csv_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(self.test_debug_log_content))
            temp_log_path = temp_log.name
        
        try:
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            # 讀取輸出檔案
            with open(result['output_file'], 'r', encoding='utf-8') as f:
                output_lines = f.readlines()
            
            # 驗證格式完全保留
            # 1. 前13行標頭完全相同
            for i in range(13):
                assert output_lines[i] == self.test_csv_content[i], f"標頭第{i+1}行格式不一致"
            
            # 2. 總行數相同（無新增、無刪[EXCEPT_CHAR]）
            assert len(output_lines) == len(self.test_csv_content), "總行數改變，違反格式保留原則"
            
            # 3. 每行欄位數量相同
            for i, (original, output) in enumerate(zip(self.test_csv_content, output_lines)):
                original_fields = original.count(',')
                output_fields = output.count(',')
                assert original_fields == output_fields, f"第{i+1}行欄位數量改變"
            
            # 4. 字符編碼保持一致
            assert all(isinstance(line, str) for line in output_lines), "字符編碼格式改變"
            
        finally:
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])
    
    def test_data_row_reordering_correctness(self):
        """測試數據行重排正確性 - 核心測試案例"""
        # 建立更精確的測試資料，模擬真實 FAIL 對應
        test_data_rows = [
            # OnlineEQC 區域 (索引0-19 = 行14-33)
            "OnlineEQC_01,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引0=行14 - FT行
            "OnlineEQC_02,15,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引1=行15 - OnlineEQC FAIL行
            "OnlineEQC_03,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引2=行16
        ] + [f"OnlineEQC_{i:02d},1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n" for i in range(4, 21)] + [
            # RT 區域開始
            "RT_01,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引20=行34
        ] + [f"RT_{i:02d},1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n" for i in range(2, 24)] + [
            "RT_24,37,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0\n",  # 索引43=行37 - RT匹配行
        ]
        
        # FAIL 對應: 14(FT) → 15(OnlineEQC) → 37(RT)
        fail_mappings = [{'ft_row': 14, 'online_eqc_row': 15, 'rt_row': 37}]
        
        result = self.processor._reorder_data_rows_for_test_flow(test_data_rows, fail_mappings)
        
        # 驗證重排結果
        assert result['status'] == 'success'
        reordered_rows = result['reordered_rows']
        
        # 驗證線性測試流程：FT(14) → OnlineEQC(15) → RT(37)
        # 前3行應該是按照線性順序排列的對應行
        ft_row_content = "OnlineEQC_01"  # 第14行內容（索引0）
        onlineeqc_row_content = "OnlineEQC_02,15"  # 第15行內容（索引1）
        rt_row_content = "RT_24,37"  # 第37行內容（索引23）
        
        # 檢查重排後的前幾行是否包含正確的線性流程
        reordered_content = ''.join(reordered_rows)
        assert ft_row_content in reordered_content, "FT行未正確包含在重排結果中"
        assert onlineeqc_row_content in reordered_content, "OnlineEQC FAIL行未正確包含在重排結果中"
        assert rt_row_content in reordered_content, "RT匹配行未正確包含在重排結果中"
        
        # 驗證所有行都被保留（無遺失）
        assert len(reordered_rows) == len(test_data_rows), "重排過程中遺失了資料行"
    
    def test_header_preservation(self):
        """測試前13行標頭完全複製 - 核心測試案例"""
        # 建立有特殊字符的標頭測試
        special_header_content = [
            "# 特殊註解行，包含中文和符號!@#$%^&*()\n",
            "WAFER_ID,測試欄位,αβγδε,日期,時間\n",
            "座標_X,座標_Y,溫度°C,濕度%,電壓V\n",
        ] + self.test_csv_content[3:13] + self.test_csv_content[13:]
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_csv:
            temp_csv.writelines(special_header_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(self.test_debug_log_content))
            temp_log_path = temp_log.name
        
        try:
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            with open(result['output_file'], 'r', encoding='utf-8') as f:
                output_lines = f.readlines()
            
            # 驗證前13行標頭絕對不變
            for i in range(13):
                assert output_lines[i] == special_header_content[i], f"標頭第{i+1}行被意外修改"
                
            # 特別驗證特殊字符保留
            assert "特殊註解行" in output_lines[0], "中文特殊字符未保留"
            assert "αβγδε" in output_lines[1], "希臘字母未保留"
            assert "°C" in output_lines[2], "特殊符號未保留"
            
        finally:
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])
    
    def test_chinese_output_message(self):
        """測試繁體中文輸出訊息"""
        # 測試中文錯誤訊息
        result = self.processor.generate_test_flow_csv(
            eqctotaldata_path="/nonexistent/file.csv",
            step4_debug_log_path="/nonexistent/log.txt"
        )
        
        # 驗證中文錯誤訊息
        assert '檔案' in result['error_message'] or '失敗' in result['error_message']
        # 檔案路徑可能包含英文，但主要訊息應該是中文
        # 檢查主要錯誤訊息部分
        main_message = result['error_message'].split(':')[0]  # 取冒號前的主要訊息
        assert '檔案不存在' in main_message or '不存在' in main_message
        
    def test_empty_fail_mappings_handling(self):
        """測試無 FAIL 對應關係的處理"""
        # 建立無 FAIL 資訊的 DEBUG LOG
        empty_debug_log = [
            "EQC Step 4 - CODE區間匹配搜尋 DEBUG LOG",
            "=" * 60,
            "開始時間: 2025-06-11 14:36:33",
            "",
            "無發現 FAIL 行",
            "處理完成"
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as temp_csv:
            temp_csv.writelines(self.test_csv_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(empty_debug_log))
            temp_log_path = temp_log.name
        
        try:
            result = self.processor.generate_test_flow_csv(temp_csv_path, temp_log_path)
            
            # 應該成功但保持原始順序
            assert result['status'] == 'success'
            assert result['fail_mappings_count'] == 0
            
            # 驗證檔案內容與原始完全相同
            with open(result['output_file'], 'r', encoding='utf-8') as f:
                output_content = f.readlines()
            
            assert output_content == self.test_csv_content
            
        finally:
            os.unlink(temp_csv_path)
            os.unlink(temp_log_path)
            if 'result' in locals() and os.path.exists(result.get('output_file', '')):
                os.unlink(result['output_file'])
    
    def test_multiple_fail_mappings(self):
        """測試多個 FAIL 對應關係處理"""
        # 建立多個 FAIL 的 DEBUG LOG
        multi_fail_debug_log = [
            "EQC Step 4 - CODE區間匹配搜尋 DEBUG LOG",
            "=" * 60,
            "開始時間: 2025-06-11 14:36:33",
            "",
            "FAIL 行檢測結果:",
            "第15行: FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)",
            "第17行: FAIL #2: 17(OnlineEQC) [LEFT_RIGHT_ARROW] 39(RT)",
            "第19行: FAIL #3: 19(OnlineEQC) [LEFT_RIGHT_ARROW] 41(RT)",
            "",
            "CODE 匹配搜尋結果:",
            "總計：3 個匹配"
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(multi_fail_debug_log))
            temp_log_path = temp_log.name
        
        try:
            result = self.processor._parse_fail_mapping_from_debug_log(temp_log_path)
            
            # 驗證解析到多個對應關係
            assert result['status'] == 'success'
            assert len(result['mappings']) == 3
            
            # 驗證每個對應關係的正確性
            expected_mappings = [
                {'online_eqc_row': 15, 'rt_row': 37, 'ft_row': 14},
                {'online_eqc_row': 17, 'rt_row': 39, 'ft_row': 16},
                {'online_eqc_row': 19, 'rt_row': 41, 'ft_row': 18}
            ]
            
            for i, mapping in enumerate(result['mappings']):
                assert mapping == expected_mappings[i], f"第{i+1}個對應關係解析錯誤"
                
        finally:
            os.unlink(temp_log_path)
    
    def test_error_handling_robustness(self):
        """測試錯誤處理健壯性"""
        # 測試各種錯誤情況
        
        # 1. 空檔案
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as empty_csv:
            empty_csv_path = empty_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_log:
            temp_log.writelines('\n'.join(self.test_debug_log_content))
            temp_log_path = temp_log.name
        
        try:
            result = self.processor.generate_test_flow_csv(empty_csv_path, temp_log_path)
            # 應該能處理但可能產生警告
            assert 'status' in result
            
        finally:
            os.unlink(empty_csv_path)
            os.unlink(temp_log_path)
        
        # 2. 格式錯誤的 DEBUG LOG
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as temp_csv:
            temp_csv.writelines(self.test_csv_content)
            temp_csv_path = temp_csv.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as bad_log:
            bad_log.write("這是一個格式錯誤的DEBUG LOG檔案\n包含中文但沒有FAIL資訊")
            bad_log_path = bad_log.name
        
        try:
            result = self.processor.generate_test_flow_csv(temp_csv_path, bad_log_path)
            # 應該能處理並給出適當回應
            assert 'status' in result
            
        finally:
            os.unlink(temp_csv_path)
            os.unlink(bad_log_path)


class TestEQCStep5Integration:
    """EQC Step 5 整合測試"""
    
    @pytest.mark.skip(reason="標準處理器整合尚未實作，預留給整合階段")
    def test_integration_with_standard_processor(self):
        """測試與標準處理器的整合"""
        # 這個測試確保 Step 5 可以正確整合到主流程
        # 測試應該失敗，因為標準處理器還沒有整合 Step 5
        
        from eqc_standard_processor import StandardEQCProcessor
        
        processor = StandardEQCProcessor()
        
        # 檢查是否有 Step 5 相關方法（應該失敗）
        assert hasattr(processor, '_execute_step5_testflow_generation'), "標準處理器應該有 Step 5 方法"

    @pytest.mark.skip(reason="API 端點整合尚未實作，預留給整合階段")
    def test_api_endpoint_integration(self):
        """測試 API 端點整合"""
        # 這個測試確保 API 端點正確暴露 Step 5 功能
        # 測試應該失敗，因為 API 端點還沒有實作
        
        from frontend.api.ft_eqc_api import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # 測試 Step 5 API 端點（應該失敗）
        response = client.post("/api/eqc/generate_test_flow", json={
            "doc_directory": "test_directory"
        })
        
        assert response.status_code == 200, "Step 5 API 端點應該存在"
        assert response.json()['status'] == 'success', "Step 5 API 應該正常運作"