#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EQC Step 5 測試流程生成處理器

核心功能：
- 解析 Step 4 DEBUG LOG 中的 FAIL 對應關係
- 重新排列 CSV 資料行，生成線性測試流程
- 保持完全原始 CSV 格式，只調整行順序

實作原則：
- 純數據行重新排列：只重組行的順序
- 零格式變更：輸入與輸出檔案格式完全相同
- 保留完全原始CSV格式：不新增任何欄位
"""

import os
import re
import logging
from typing import List, Dict, Any, Tuple
from datetime import datetime


class EQCStep5TestFlowProcessor:
    """EQC Step 5 測試流程生成處理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_test_flow_csv(self, eqctotaldata_path: str, step4_debug_log_path: str) -> Dict[str, Any]:
        """
        生成測試流程 CSV 檔案
        
        Args:
            eqctotaldata_path: EQCTOTALDATA.csv 檔案路徑
            step4_debug_log_path: Step 4 DEBUG LOG 檔案路徑
        
        Returns:
            處理結果字典
        """
        try:
            self.logger.info("[ROCKET] Step 5: 開始生成測試流程 CSV...")
            
            # 檢查檔案存在性
            if not os.path.exists(eqctotaldata_path):
                return {
                    'status': 'error',
                    'error_message': f'EQCTOTALDATA.csv 檔案不存在: {eqctotaldata_path}'
                }
            
            if not os.path.exists(step4_debug_log_path):
                return {
                    'status': 'error',
                    'error_message': f'Step 4 DEBUG LOG 檔案不存在: {step4_debug_log_path}'
                }
            
            # 讀取原始 CSV 檔案
            with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                rows = f.readlines()
            
            total_rows = len(rows)
            self.logger.info(f"   原始 CSV 總行數: {total_rows}")
            
            # 解析 Step 4 DEBUG LOG 中的 FAIL 對應關係
            mapping_result = self._parse_fail_mapping_from_debug_log(step4_debug_log_path)
            if mapping_result['status'] != 'success':
                return mapping_result
            
            fail_mappings = mapping_result['mappings']
            self.logger.info(f"   解析到 {len(fail_mappings)} 個 FAIL 對應關係")
            
            # 分離前13行標頭和資料行
            header_rows = rows[:13]  # 前13行完全保持不變
            data_rows = rows[13:]    # 第14行之後是資料區
            
            self.logger.info(f"   標頭行數: {len(header_rows)}")
            self.logger.info(f"   資料行數: {len(data_rows)}")
            
            # 重新排列資料行生成測試流程
            reorder_result = self._reorder_data_rows_for_test_flow(data_rows, fail_mappings, eqctotaldata_path)
            if reorder_result['status'] != 'success':
                return reorder_result
            
            # 組合最終結果
            final_rows = header_rows + reorder_result['reordered_rows']
            
            # 生成輸出檔案
            output_filename = f"EQCTOTALDATA_Step5_TestFlow_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            output_path = os.path.join(os.path.dirname(eqctotaldata_path), output_filename)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.writelines(final_rows)
            
            self.logger.info(f"   [OK] 測試流程 CSV 已生成: {output_filename}")
            self.logger.info(f"   輸出總行數: {len(final_rows)}")
            self.logger.info(f"   重新排列行數: {reorder_result['reordered_rows_count']}")
            
            # 檔案重命名邏輯：將 Step5 檔案重命名為 EQCTOTALDATA.csv
            final_output_path = self._rename_to_main_file(output_path, eqctotaldata_path)
            
            return {
                'status': 'success',
                'output_file': final_output_path,
                'original_step5_file': output_path,
                'total_rows': len(final_rows),
                'reordered_rows': reorder_result['reordered_rows_count'],
                'fail_mappings_count': len(fail_mappings),
                'new_online_eqc_rows': reorder_result.get('new_online_eqc_rows', []),
                'new_eqc_rt_rows': reorder_result.get('new_eqc_rt_rows', []),
                'final_online_eqc_rt_rows': reorder_result.get('final_online_eqc_rt_rows', []),
                'processing_stage': 'step5_testflow_generation_complete'
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Step 5 測試流程生成失敗: {e}")
            return {
                'status': 'error',
                'error_message': f'Step 5 處理失敗: {str(e)}'
            }
    
    def _parse_fail_mapping_from_debug_log(self, debug_log_path: str) -> Dict[str, Any]:
        """
        從 Step 4 DEBUG LOG 解析 FAIL 對應關係
        
        實際格式：
        [TARGET] 【FAIL #1】匹配搜尋:
        目標: 第15行, BIN=31
        [OK] 匹配 #1: 第37行, Serial=4, BIN=1
        """
        try:
            self.logger.info("[SEARCH] 解析 Step 4 DEBUG LOG 中的 FAIL 對應關係...")
            
            with open(debug_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析每個 FAIL 區塊
            mappings = []
            fail_blocks = re.findall(r'\[TARGET\] 【FAIL #\d+】匹配搜尋:(.*?)(?=\[TARGET\] 【FAIL #\d+】|$)', content, re.DOTALL)
            
            for i, block in enumerate(fail_blocks, 1):
                # 解析目標行（OnlineEQC 行）
                target_match = re.search(r'目標:\s*第(\d+)行', block)
                if not target_match:
                    continue
                
                online_eqc_row = int(target_match.group(1))
                
                # 解析匹配行（RT 行）- 找到所有匹配
                rt_matches = re.findall(r'\[OK\] 匹配 #\d+:\s*第(\d+)行', block)
                if not rt_matches:
                    continue
                
                ft_row = online_eqc_row - 1  # FT 行通常在 OnlineEQC 前一行
                
                # 為每個 RT 匹配創建一個測試流程
                for rt_match_num, rt_row_str in enumerate(rt_matches, 1):
                    rt_row = int(rt_row_str)
                    
                    mapping = {
                        'online_eqc_row': online_eqc_row,
                        'rt_row': rt_row,
                        'ft_row': ft_row,
                        'rt_stage': rt_match_num  # RT1, RT2, RT3...
                    }
                    mappings.append(mapping)
                    
                    self.logger.info(f"   找到對應 #{i}-RT{rt_match_num}: FT行{ft_row} → OnlineEQC行{online_eqc_row} → RT{rt_match_num}行{rt_row}")
            
            if not mappings:
                self.logger.warning("   [WARNING] 未找到任何 FAIL 對應關係")
                return {
                    'status': 'warning',
                    'mappings': [],
                    'message': '未找到 FAIL 對應關係，將保持原始順序'
                }
            
            return {
                'status': 'success',
                'mappings': mappings,
                'total_mappings': len(mappings)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': f'解析 DEBUG LOG 失敗: {str(e)}'
            }
    
    def _reorder_data_rows_for_test_flow(self, data_rows: List[str], fail_mappings: List[Dict], eqctotaldata_path: str = None) -> Dict[str, Any]:
        """
        重新排列資料行生成線性測試流程
        
        邏輯：
        1. 識別 FT、OnlineEQC、RT 區域
        2. 根據 FAIL 對應關係創建線性流程：FT → OnlineEQC → RT
        3. 保持其他行的相對位置
        """
        try:
            self.logger.info("[REFRESH] 重新排列資料行生成測試流程...")
            
            if not fail_mappings:
                self.logger.info("   無 FAIL 對應關係，保持原始順序")
                return {
                    'status': 'success',
                    'reordered_rows': data_rows,
                    'reordered_rows_count': 0
                }
            
            # 分析行類型和位置
            ft_rows = []
            online_eqc_rows = []
            rt_rows = []
            other_rows = []
            
            # 動態計算各區域範圍（基於 B9 欄位 FAIL 數量）
            try:
                # 讀取完整檔案以獲取 B9 欄位
                if eqctotaldata_path and os.path.exists(eqctotaldata_path):
                    with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                        all_rows = f.readlines()
                    fail_count = int(all_rows[8].split(',')[1].strip())  # B9 欄位 FAIL 數量
                else:
                    # 備用：從 fail_mappings 數量估算
                    fail_count = len(set(mapping['online_eqc_row'] for mapping in fail_mappings))
                
                # 動態計算邊界（轉換為 data_rows 相對索引）
                online_eqc_start = 0    # 第14行開始（相對於 data_rows 是索引0）
                online_eqc_end = (fail_count * 2) - 1  # 動態計算結束位置
                rt_start = online_eqc_end + 1          # RT 區域開始
                
                self.logger.info(f"   [TARGET] 動態邊界計算: B9 FAIL數={fail_count}, Online EQC索引範圍={online_eqc_start}-{online_eqc_end}, RT開始索引={rt_start}")
            except Exception as e:
                # 出錯時使用原硬編碼值
                self.logger.warning(f"   [WARNING] 動態邊界計算失敗，使用預設值: {e}")
                online_eqc_start = 0    # 第14行開始（相對於 data_rows 是索引0）
                online_eqc_end = 19     # 大約第33行結束
                rt_start = 20           # RT 區域開始
            
            for i, row in enumerate(data_rows):
                absolute_row_num = i + 14  # 轉換為絕對行號（第14行開始）
                
                if i < online_eqc_start:
                    # FT 區域（實際上第14行之前的行在資料區很少，但為了完整性）
                    ft_rows.append((i, row, absolute_row_num))
                elif i <= online_eqc_end:
                    # OnlineEQC 區域
                    online_eqc_rows.append((i, row, absolute_row_num))
                else:
                    # RT 區域
                    rt_rows.append((i, row, absolute_row_num))
            
            self.logger.info(f"   分析結果: FT區域 {len(ft_rows)} 行, OnlineEQC區域 {len(online_eqc_rows)} 行, RT區域 {len(rt_rows)} 行")
            
            # 生成線性測試流程
            reordered_rows = []
            used_indices = set()
            
            # 追蹤 Online EQC 和 EQC RT 在新 CSV 中的行數位置
            new_online_eqc_rows = []
            new_eqc_rt_rows = []
            
            # 將 FAIL 對應關係按群組歸類
            fail_groups = {}
            for mapping in fail_mappings:
                online_eqc_row = mapping['online_eqc_row']
                if online_eqc_row not in fail_groups:
                    fail_groups[online_eqc_row] = {
                        'ft_row': mapping['ft_row'],
                        'online_eqc_row': online_eqc_row,
                        'rt_rows': []
                    }
                fail_groups[online_eqc_row]['rt_rows'].append(mapping['rt_row'])
            
            # 處理每個 FAIL 群組
            for online_eqc_row, group in fail_groups.items():
                ft_row_num = group['ft_row']
                online_eqc_row_num = group['online_eqc_row']
                rt_rows = sorted(group['rt_rows'])  # 確保 RT 按順序排列
                
                # 找對應的行索引
                ft_index = ft_row_num - 14  # 轉換為 data_rows 索引
                online_eqc_index = online_eqc_row_num - 14
                
                # 按順序添加：FT → OnlineEQC → RT1 → RT2 → RT3...
                if 0 <= ft_index < len(data_rows) and ft_index not in used_indices:
                    reordered_rows.append(data_rows[ft_index])
                    used_indices.add(ft_index)
                
                if 0 <= online_eqc_index < len(data_rows) and online_eqc_index not in used_indices:
                    reordered_rows.append(data_rows[online_eqc_index])
                    used_indices.add(online_eqc_index)
                    # 記錄 Online EQC 在新 CSV 中的行數（+14 因為前面有13行標頭）
                    new_online_eqc_row_num = len(reordered_rows) + 13
                    new_online_eqc_rows.append(new_online_eqc_row_num)
                
                # 添加所有 RT 行
                rt_flow = []
                for rt_row_num in rt_rows:
                    rt_index = rt_row_num - 14
                    if 0 <= rt_index < len(data_rows) and rt_index not in used_indices:
                        reordered_rows.append(data_rows[rt_index])
                        used_indices.add(rt_index)
                        # 記錄 EQC RT 在新 CSV 中的行數
                        new_eqc_rt_row_num = len(reordered_rows) + 13
                        new_eqc_rt_rows.append(new_eqc_rt_row_num)
                        rt_flow.append(f"RT{len(rt_flow)+1}行{rt_row_num}")
                
                rt_flow_str = " → ".join(rt_flow) if rt_flow else "無RT"
                self.logger.info(f"   線性流程: 第{ft_row_num}行 → 第{online_eqc_row_num}行 → {rt_flow_str}")
            
            # 添加剩餘未使用的行（保持原始順序）
            for i, row in enumerate(data_rows):
                if i not in used_indices:
                    reordered_rows.append(row)
            
            # 生成最終 Online EQC RT 行數列表（合併並排序）
            final_online_eqc_rt_rows = sorted(new_online_eqc_rows + new_eqc_rt_rows)
            
            reordered_count = len(used_indices)
            self.logger.info(f"   [OK] 重新排列完成: {reordered_count} 行參與重排, 總計 {len(reordered_rows)} 行")
            self.logger.info(f"   [CHART] 新位置統計: Online EQC {len(new_online_eqc_rows)} 行, EQC RT {len(new_eqc_rt_rows)} 行")
            self.logger.info(f"   [TARGET] final_online_eqc_rt_rows: {final_online_eqc_rt_rows}")
            
            return {
                'status': 'success',
                'reordered_rows': reordered_rows,
                'reordered_rows_count': reordered_count,
                'linear_flow_count': len(fail_mappings),
                'new_online_eqc_rows': new_online_eqc_rows,
                'new_eqc_rt_rows': new_eqc_rt_rows,
                'final_online_eqc_rt_rows': final_online_eqc_rt_rows
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': f'重新排列資料行失敗: {str(e)}'
            }
    
    def _rename_to_main_file(self, step5_file_path: str, original_eqctotaldata_path: str) -> str:
        """
        將 Step5 測試流程檔案重命名為 EQCTOTALDATA.csv
        
        Args:
            step5_file_path: Step5 測試流程檔案路徑
            original_eqctotaldata_path: 原始 EQCTOTALDATA.csv 路徑
        
        Returns:
            最終的 EQCTOTALDATA.csv 路徑
        """
        try:
            # 刪[EXCEPT_CHAR]舊的 EQCTOTALDATA.csv（如果存在）
            if os.path.exists(original_eqctotaldata_path):
                os.remove(original_eqctotaldata_path)
                self.logger.info(f"   [DELETE] 已刪[EXCEPT_CHAR]舊的 EQCTOTALDATA.csv")
            
            # 重命名 Step5 檔案為 EQCTOTALDATA.csv
            os.rename(step5_file_path, original_eqctotaldata_path)
            self.logger.info(f"   [EDIT] 已重命名: {os.path.basename(step5_file_path)} → EQCTOTALDATA.csv")
            
            return original_eqctotaldata_path
            
        except Exception as e:
            self.logger.error(f"[ERROR] 檔案重命名失敗: {e}")
            return step5_file_path  # 如果失敗，返回原始檔案路徑