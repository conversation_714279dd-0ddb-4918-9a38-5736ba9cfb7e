# Execution Plan - 20250818_050337

## 執行摘要
- 任務類型: 後端錯誤修復 (Bug Fix)
- 使用的 Agents: [BMAD-AGENT: pm]
- 調用專業支援: [SPECIALIST-AGENT: python-pro], [SPECIALIST-AGENT: backend-architect]
- 執行時間: 2025-08-18 05:03:37

## 詳細執行步驟

### 修復任務 #1: 500 Internal Server Error

#### 子任務 1.1: 修復方法名不匹配
- **檔案**: frontend/email/routes/email_routes.py
- **位置**: 第 73 行
- **修改**: `database.mark_email_read(email_id)` → `database.mark_email_as_read(email_id)`
- **驗證**: 確認 EmailDatabase 類中存在 mark_email_as_read 方法

#### 子任務 1.2: 修復模組導入錯誤  
- **檔案**: frontend/email/routes/email_routes.py
- **位置**: 第 342 行
- **修改**: `from backend.shared.models.email_models import EmailData` → `from backend.email.models.email_models import EmailData`
- **驗證**: 確認正確的模組路徑存在

### 修復任務 #2: 重複處理錯誤優化

#### 子任務 2.1: 添加強制重新處理選項
- **檔案**: frontend/email/routes/email_routes.py  
- **位置**: 第 356-360 行
- **修改邏輯**:
  ```python
  data = request.get_json() or {}
  force_reprocess = data.get('force', False)
  
  if email.is_processed and not force_reprocess:
      return jsonify({
          'success': False,
          'message': '此郵件已經處理過',
          'suggestion': '如需重新處理，請添加參數 "force": true'
      })
  ```

#### 子任務 2.2: 添加重置處理狀態 API
- **新增端點**: `/api/<int:email_id>/reset-process-status`
- **方法**: POST
- **功能**: 重置郵件的 is_processed 和 parse_status 狀態

## 實施順序

### Phase 3A: 核心錯誤修復 (高優先級)
1. **修復方法名** - 立即解決 500 錯誤
2. **修復導入路徑** - 確保模組正確載入
3. **基本測試** - 驗證郵件詳情頁面可正常載入

### Phase 3B: 功能增強 (中優先級)  
1. **添加強制重新處理邏輯** - 改善用戶體驗
2. **創建重置狀態 API** - 提供管理功能
3. **前端按鈕測試** - 驗證處理流程完整性

## 驗收標準

### 功能驗收標準
- [ACCEPT] 訪問 http://localhost:5000/email/ 不再出現 500 錯誤
- [ACCEPT] 點擊郵件詳情不再產生伺服器錯誤
- [ACCEPT] 處理按鈕可正常點擊，不再出現「已經處理過」錯誤
- [ACCEPT] 支援強制重新處理已處理的郵件
- [ACCEPT] 提供重置處理狀態的管理功能

### 技術驗收標準
- [ACCEPT] 所有修改的程式碼語法正確
- [ACCEPT] 導入路徑指向存在的模組
- [ACCEPT] 方法調用匹配實際可用的方法
- [ACCEPT] API 回應格式一致且有意義
- [ACCEPT] 錯誤處理機制完善

### 測試要求
- [TEST] 郵件詳情頁面載入測試
- [TEST] 郵件處理 API 功能測試  
- [TEST] 重複處理邏輯測試
- [TEST] 強制重新處理功能測試
- [TEST] 錯誤場景處理測試

## 風險評估與緩解

### 高風險項目
1. **方法不存在風險**
   - 風險: 修改後的方法名可能不存在
   - 緩解: 在修改前檢查 EmailDatabase 類的可用方法

2. **導入路徑錯誤風險**
   - 風險: 新的導入路徑可能仍然不正確
   - 緩解: 驗證模組結構和檔案存在性

3. **向後兼容性風險**
   - 風險: 修改可能影響其他依賴的功能
   - 緩解: 進行完整的回歸測試

### 中風險項目
1. **API 格式變更影響**
   - 風險: 新的 API 回應格式可能影響前端
   - 緩解: 保持現有 API 格式，只添加新欄位

## 所需資源

### 技術專家
- [SPECIALIST-AGENT: python-pro] - Python 程式碼修復
- [SPECIALIST-AGENT: backend-architect] - 架構一致性檢查
- [SPECIALIST-AGENT: frontend-playwright-validator] - 前端功能驗證

### 測試資源
- 開發環境存取權限
- 測試郵件資料
- 瀏覽器測試環境 (Chrome, Firefox, Safari)

## 完成時間估算
- **Phase 3A (核心修復)**: 30-45 分鐘
- **Phase 3B (功能增強)**: 15-30 分鐘  
- **測試驗證**: 15-20 分鐘
- **總計**: 60-95 分鐘

## 下一階段輸入
- 讀取檔案: .bmad/flow-results/execution-plan-20250818_050337.md
- 執行要求: 按照計劃順序執行所有修復任務
- 專家調用: python-pro + backend-architect + frontend-playwright-validator

## Agent 交接資訊  
- 前階段 Agent: [BMAD-AGENT: analyst] + [SPECIALIST-AGENT: error-detective]
- 當前階段 Agent: [BMAD-AGENT: pm]
- 下階段 Agent: [BMAD-AGENT: dev] + [SPECIALIST-AGENT: python-pro] + [SPECIALIST-AGENT: backend-architect]
- 上下文傳遞: 詳細修復步驟、驗收標準、風險評估