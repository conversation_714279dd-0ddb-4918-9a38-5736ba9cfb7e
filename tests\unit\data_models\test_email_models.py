"""
郵件數據模型測試
TASK_004: 建立郵件數據模型
遵循 TDD 原則 - 先寫失敗的測試
"""

import pytest
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

from backend.email.models.email_models import (
    EmailData,
    EmailAttachment, 
    VendorIdentificationResult,
    EmailParsingResult,
    ProcessingStatus,
    EmailMetadata,
    TaskData,
    FileProcessingInfo,
    EmailProcessingContext
)


class TestEmailAttachment:
    """測試郵件附件模型"""

    def test_attachment_creation_valid_data(self):
        """測試建立有效的附件"""
        attachment = EmailAttachment(
            filename="test_file.csv",
            content_type="text/csv",
            size_bytes=1024,
            file_path=Path("/tmp/test_file.csv")
        )
        
        assert attachment.filename == "test_file.csv"
        assert attachment.content_type == "text/csv"
        assert attachment.size_bytes == 1024
        assert attachment.file_path == Path("/tmp/test_file.csv")
        assert attachment.is_processed is False

    def test_attachment_invalid_size(self):
        """測試附件大小驗證"""
        with pytest.raises(ValueError):
            EmailAttachment(
                filename="test.csv",
                content_type="text/csv",
                size_bytes=-1,  # 無效大小
                file_path=Path("/tmp/test.csv")
            )

    def test_attachment_invalid_extension(self):
        """測試不允許的副檔名"""
        with pytest.raises(ValueError):
            EmailAttachment(
                filename="malicious.exe",  # 不允許的副檔名
                content_type="application/octet-stream",
                size_bytes=1024,
                file_path=Path("/tmp/malicious.exe")
            )

    def test_attachment_size_limit(self):
        """測試附件大小限制"""
        with pytest.raises(ValueError):
            EmailAttachment(
                filename="large_file.csv",
                content_type="text/csv",
                size_bytes=100 * 1024 * 1024 + 1,  # 超過 100MB
                file_path=Path("/tmp/large_file.csv")
            )

    def test_attachment_chinese_filename(self):
        """測試中文檔名支援"""
        attachment = EmailAttachment(
            filename="測試檔案.csv",
            content_type="text/csv",
            size_bytes=1024,
            file_path=Path("/tmp/測試檔案.csv")
        )
        
        assert attachment.filename == "測試檔案.csv"


class TestEmailData:
    """測試郵件數據模型"""

    def test_email_data_creation_minimal(self):
        """測試建立最小必要的郵件數據"""
        email = EmailData(
            message_id="<EMAIL>",
            subject="Test Email",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        
        assert email.message_id == "<EMAIL>"
        assert email.subject == "Test Email"
        assert email.sender == "<EMAIL>"
        assert len(email.attachments) == 0

    def test_email_data_with_attachments(self):
        """測試包含附件的郵件"""
        attachment = EmailAttachment(
            filename="data.csv",
            content_type="text/csv",
            size_bytes=1024,
            file_path=Path("/tmp/data.csv")
        )
        
        email = EmailData(
            message_id="<EMAIL>",
            subject="GTK FT HOLD MO:F123456",
            sender="<EMAIL>",
            received_time=datetime.now(),
            attachments=[attachment]
        )
        
        assert len(email.attachments) == 1
        assert email.attachments[0].filename == "data.csv"

    def test_email_subject_validation(self):
        """測試郵件主旨驗證"""
        with pytest.raises(ValueError):
            EmailData(
                message_id="<EMAIL>",
                subject="",  # 空主旨
                sender="<EMAIL>",
                received_time=datetime.now()
            )

    def test_email_sender_validation(self):
        """測試寄件者驗證"""
        with pytest.raises(ValueError):
            EmailData(
                message_id="<EMAIL>",
                subject="Test Subject",
                sender="invalid-email",  # 無效 email 格式
                received_time=datetime.now()
            )

    def test_email_chinese_content(self):
        """測試中文內容支援"""
        email = EmailData(
            message_id="<EMAIL>",
            subject="GTK FT HOLD MO:F123456 批次測試",
            sender="測試者@gtk.com",
            received_time=datetime.now(),
            body="郵件內容包含中文字元"
        )
        
        assert "批次測試" in email.subject
        assert email.body == "郵件內容包含中文字元"


class TestVendorIdentificationResult:
    """測試廠商識別結果模型"""

    def test_vendor_identification_success(self):
        """測試成功的廠商識別"""
        result = VendorIdentificationResult(
            vendor_code="GTK",
            vendor_name="Greatek Technology",
            confidence_score=0.95,
            matching_patterns=["@gtk.com", "ft hold"],
            is_identified=True
        )
        
        assert result.vendor_code == "GTK"
        assert result.confidence_score == 0.95
        assert result.is_identified is True
        assert "ft hold" in result.matching_patterns

    def test_vendor_identification_failed(self):
        """測試失敗的廠商識別"""
        result = VendorIdentificationResult(
            vendor_code=None,
            vendor_name=None,
            confidence_score=0.1,
            matching_patterns=[],
            is_identified=False
        )
        
        assert result.vendor_code is None
        assert result.is_identified is False
        assert result.confidence_score == 0.1

    def test_confidence_score_validation(self):
        """測試信心分數驗證"""
        with pytest.raises(ValueError):
            VendorIdentificationResult(
                vendor_code="GTK",
                vendor_name="Greatek",
                confidence_score=1.5,  # 超過 1.0
                matching_patterns=[],
                is_identified=True
            )


class TestEmailParsingResult:
    """測試郵件解析結果模型"""

    def test_parsing_result_success(self):
        """測試成功的解析結果"""
        result = EmailParsingResult(
            is_success=True,
            vendor_code="GTK",
            mo_number="F123456",
            lot_number="ABC.1",
            extracted_data={
                "test_type": "FT",
                "hold_reason": "Quality Issue"
            }
        )
        
        assert result.is_success is True
        assert result.mo_number == "F123456"
        assert result.extracted_data["test_type"] == "FT"

    def test_parsing_result_failure(self):
        """測試失敗的解析結果"""
        result = EmailParsingResult(
            is_success=False,
            error_message="無法解析主旨格式",
            validation_errors=["主旨格式不正確", "缺少 MO 編號"]
        )
        
        assert result.is_success is False
        assert "無法解析主旨格式" in result.error_message
        assert len(result.validation_errors) == 2

    def test_parsing_result_mo_number_validation(self):
        """測試 MO 編號格式驗證"""
        with pytest.raises(ValueError):
            EmailParsingResult(
                is_success=True,
                mo_number="123",  # 格式不正確
                vendor_code="GTK"
            )


class TestProcessingStatus:
    """測試處理狀態枚舉"""

    def test_processing_status_values(self):
        """測試處理狀態值"""
        assert ProcessingStatus.PENDING.value == "pending"
        assert ProcessingStatus.PROCESSING.value == "processing"
        assert ProcessingStatus.COMPLETED.value == "completed"
        assert ProcessingStatus.FAILED.value == "failed"


class TestEmailMetadata:
    """測試郵件元數據模型"""

    def test_metadata_creation(self):
        """測試元數據建立"""
        metadata = EmailMetadata(
            message_size_bytes=2048,
            attachment_count=3,
            processing_priority=1,
            source_folder="Inbox",
            backup_location=Path("/backup/email_123")
        )
        
        assert metadata.message_size_bytes == 2048
        assert metadata.attachment_count == 3
        assert metadata.processing_priority == 1

    def test_metadata_priority_validation(self):
        """測試優先級驗證"""
        with pytest.raises(ValueError):
            EmailMetadata(
                processing_priority=0  # 優先級必須 >= 1
            )


class TestTaskData:
    """測試任務數據模型"""

    def test_task_data_creation(self):
        """測試任務數據建立"""
        task = TaskData(
            task_id="task_123",
            email_id="email_456",
            vendor_code="GTK",
            mo_number="F123456",
            status=ProcessingStatus.PENDING,
            created_time=datetime.now()
        )
        
        assert task.task_id == "task_123"
        assert task.status == ProcessingStatus.PENDING
        assert task.vendor_code == "GTK"

    def test_task_data_status_update(self):
        """測試任務狀態更新"""
        task = TaskData(
            task_id="task_123",
            email_id="email_456",
            vendor_code="GTK",
            mo_number="F123456",
            status=ProcessingStatus.PENDING,
            created_time=datetime.now()
        )
        
        # 模擬狀態更新
        task.status = ProcessingStatus.PROCESSING
        task.started_time = datetime.now()
        
        assert task.status == ProcessingStatus.PROCESSING
        assert task.started_time is not None


class TestFileProcessingInfo:
    """測試檔案處理資訊模型"""

    def test_file_processing_info_creation(self):
        """測試檔案處理資訊建立"""
        info = FileProcessingInfo(
            source_file=Path("/tmp/source.csv"),
            processed_file=Path("/tmp/processed.xlsx"),
            file_type="CSV",
            processing_steps=["unzip", "convert", "validate"],
            file_size_bytes=1024
        )
        
        assert info.file_type == "CSV"
        assert "convert" in info.processing_steps
        assert info.file_size_bytes == 1024

    def test_file_processing_steps_validation(self):
        """測試處理步驟驗證"""
        with pytest.raises(ValueError):
            FileProcessingInfo(
                source_file=Path("/tmp/source.csv"),
                file_type="CSV",
                processing_steps=[],  # 空的處理步驟
                file_size_bytes=1024
            )


class TestEmailProcessingContext:
    """測試郵件處理上下文模型"""

    def test_processing_context_complete(self):
        """測試完整的處理上下文"""
        email = EmailData(
            message_id="<EMAIL>",
            subject="GTK FT HOLD MO:F123456",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        
        vendor_result = VendorIdentificationResult(
            vendor_code="GTK",
            vendor_name="Greatek Technology",
            confidence_score=0.95,
            matching_patterns=["gtk.com"],
            is_identified=True
        )
        
        parsing_result = EmailParsingResult(
            is_success=True,
            vendor_code="GTK",
            mo_number="F123456"
        )
        
        context = EmailProcessingContext(
            email_data=email,
            vendor_identification=vendor_result,
            parsing_result=parsing_result,
            processing_start_time=datetime.now()
        )
        
        assert context.email_data.subject == "GTK FT HOLD MO:F123456"
        assert context.vendor_identification.vendor_code == "GTK"
        assert context.parsing_result.mo_number == "F123456"

    def test_processing_context_validation(self):
        """測試處理上下文驗證"""
        email = EmailData(
            message_id="<EMAIL>",
            subject="Test",
            sender="<EMAIL>",
            received_time=datetime.now()
        )
        
        # 廠商識別成功但解析失敗的情況
        vendor_result = VendorIdentificationResult(
            vendor_code="GTK",
            vendor_name="Greatek",
            confidence_score=0.95,
            matching_patterns=["gtk.com"],
            is_identified=True
        )
        
        parsing_result = EmailParsingResult(
            is_success=False,
            error_message="解析失敗"
        )
        
        context = EmailProcessingContext(
            email_data=email,
            vendor_identification=vendor_result,
            parsing_result=parsing_result,
            processing_start_time=datetime.now()
        )
        
        assert context.vendor_identification.is_identified is True
        assert context.parsing_result.is_success is False