#!/usr/bin/env python3
"""
CTA to CSV 處理器
專門處理 CTA 檔案轉換為 CSV 格式的所有邏輯
支援 [Data] → _FT_ 和 [QAData] → _QC_/_eqc_ 的檔案輸出
"""

import os
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import re
from .cta_column_aligner import CTAColumnAligner


class CTAToCSVProcessor:
    """CTA to CSV 專用處理器"""
    
    def __init__(self):
        self.supported_sections = ['[Data]', '[QAData]']
        self.column_aligner = CTAColumnAligner()  # 新增對齊器
    
    def detect_cta_sections(self, file_path: str) -> Dict[str, int]:
        """
        檢測 CTA 檔案中的區段位置
        
        Args:
            file_path: CTA 檔案路徑
            
        Returns:
            Dict[str, int]: 區段名稱和行號的對應字典
        """
        sections = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line_clean = line.strip()
                    
                    # 檢測支援的區段
                    for section in self.supported_sections:
                        if section in line_clean:
                            sections[section] = line_num
                            print(f"[OK] 發現 {section} 區段在第 {line_num} 行")
                            
        except Exception as e:
            print(f"[ERROR] 檢測區段失敗: {e}")
            
        return sections
    
    def parse_cta_source_data(self, file_path: str) -> Dict[str, str]:
        """
        解析 CTA 檔案的關鍵欄位 - 使用動態電腦名稱提取
        
        Args:
            file_path: CTA 檔案路徑
            
        Returns:
            Dict[str, str]: 關鍵欄位的內容
        """
        source_data = {
            'icname': '',      # B15: ICName
            'endtime': '',     # B25: EndTime  
            'computer': '',    # 動態提取: Computer 或 Tester_Name
            'testtype': '',    # B16: TestType (用於判斷是否含qc)
            'b9_field': '',    # B9: 用於判斷是否含_qc
            'b11_field': ''    # B11: 用於判斷是否含_qc
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 使用動態電腦名稱提取
            computer_name = self.column_aligner.extract_computer_name_dynamic(file_path)
            source_data['computer'] = computer_name
            
            # 根據行號解析其他關鍵欄位
            key_mappings = {
                9: 'b9_field',     # B9: 用於判斷是否含_qc
                11: 'b11_field',   # B11: 用於判斷是否含_qc
                15: 'icname',      # B15: ICName
                16: 'testtype',    # B16: TestType
                # 移[EXCEPT_CHAR]固定的 endtime，改為動態尋找
            }
            
            for line_num, key in key_mappings.items():
                if line_num <= len(lines):
                    line = lines[line_num - 1].strip()  # 轉為0-based索引
                    parts = line.split(',', 1)
                    if len(parts) >= 2:
                        # 移[EXCEPT_CHAR]尾部的逗號和空白
                        value = parts[1].rstrip(',').strip()
                        source_data[key] = value
                        print(f"[BOARD] 解析 {key}: {value}")
            
            # 動態尋找 EndTime
            endtime_found = False
            
            # 1. 檢查 A25 是否為 EndTime
            if 25 <= len(lines):
                line = lines[24].strip()  # 0-based index
                if line.startswith('EndTime,'):
                    parts = line.split(',', 1)
                    if len(parts) >= 2:
                        source_data['endtime'] = parts[1].rstrip(',').strip()
                        endtime_found = True
                        print(f"[BOARD] 找到 EndTime 在第 25 行: {source_data['endtime']}")
            
            # 2. 如果 A25 不是，檢查 A49
            if not endtime_found and 49 <= len(lines):
                line = lines[48].strip()  # 0-based index
                if line.startswith('EndTime,'):
                    parts = line.split(',', 1)
                    if len(parts) >= 2:
                        source_data['endtime'] = parts[1].rstrip(',').strip()
                        endtime_found = True
                        print(f"[BOARD] 找到 EndTime 在第 49 行: {source_data['endtime']}")
            
            # 3. 如果都不是，動態搜尋 A1-A80
            if not endtime_found:
                for i in range(min(80, len(lines))):
                    line = lines[i].strip()
                    if line.startswith('EndTime,'):
                        parts = line.split(',', 1)
                        if len(parts) >= 2:
                            source_data['endtime'] = parts[1].rstrip(',').strip()
                            endtime_found = True
                            print(f"[BOARD] 找到 EndTime 在第 {i+1} 行: {source_data['endtime']}")
                            break
            
            if not endtime_found:
                print("[WARNING] 未找到 EndTime，使用空字串")
                source_data['endtime'] = ''
                        
        except Exception as e:
            print(f"[ERROR] 解析來源資料失敗: {e}")
            
        return source_data
    
    def format_time_string(self, time_str: str) -> str:
        """
        轉換時間格式：2025-06-27 14:02:32  UTC+8 → 06/27/25 14:02:32
        
        Args:
            time_str: 原始時間字串
            
        Returns:
            str: 格式化後的時間字串
        """
        try:
            # 移[EXCEPT_CHAR] UTC+8 後綴
            clean_time = re.sub(r'\s+UTC\+8.*$', '', time_str.strip())
            
            # 解析時間
            dt = datetime.strptime(clean_time, '%Y-%m-%d %H:%M:%S')
            
            # 轉換為目標格式
            formatted = dt.strftime('%m/%d/%y %H:%M:%S')
            
            print(f"[THREE_OCLOCK] 時間轉換: {time_str} → {formatted}")
            return formatted
            
        except Exception as e:
            print(f"[ERROR] 時間格式轉換失敗: {e}")
            return time_str
    
    def generate_b2_content(self, icname: str, section_type: str, source_data: Dict[str, str]) -> str:
        """
        產生 B2 欄位內容
        
        Args:
            icname: 原檔的 ICName (B15)
            section_type: 區段類型 ('[Data]' 或 '[QAData]')
            source_data: 解析的來源資料，包含所有欄位
            
        Returns:
            str: B2 欄位內容
        """
        # 檢查 B9、B11、B15 任一欄位是否包含 QC 相關字樣
        has_qc_in_original = (
            'qc' in icname.lower() or
            '_qc' in source_data.get('b9_field', '').lower() or
            '_qc' in source_data.get('b11_field', '').lower()
        )
        
        if has_qc_in_original:
            # 原檔 B15 有含 qc → 一律加 (qc)
            result = f"{icname}(qc)"
            print(f"[LABEL] 原檔含QC，產生 B2: {section_type} → {result}")
        else:
            # 原檔 B15 沒有含 qc → 根據區段決定
            if section_type == '[Data]':
                result = f"{icname}(auto_qc)"
            elif section_type == '[QAData]':
                result = f"{icname}(qc)"
            else:
                result = icname
            print(f"[LABEL] 原檔無QC，產生 B2: {section_type} → {result}")
            
        return result
    
    def find_data_columns_position(self, test_names: List[str]) -> int:
        """
        在測試項目名稱中尋找 Data_Num/Data_Cnt 欄位的位置
        
        Args:
            test_names: 測試項目名稱列表
            
        Returns:
            int: Data_Num/Data_Cnt 欄位的索引位置，如果未找到則返回 len(test_names)
        """
        # 搜尋可能的 Data_Num/Data_Cnt 欄位名稱模式
        data_patterns = ['Data_Num', 'Data_Cnt', 'TEST_NUM', 'data_num', 'data_cnt', 'test_num']
        
        for i, test_name in enumerate(test_names):
            test_name_clean = test_name.strip()
            for pattern in data_patterns:
                if pattern in test_name_clean:
                    print(f"[TARGET] 找到 Data 欄位: '{test_name_clean}' 在位置 {i}")
                    return i
                    
        # 如果沒有找到，返回測試項目總數（表示所有欄位都需要填 "none"）
        print(f"[WARNING] 未找到 Data_Num/Data_Cnt 欄位，使用預設位置: {len(test_names)}")
        return len(test_names)
    
    def generate_output_filename(self, original_name: str, section_type: str, source_data: Dict[str, str]) -> str:
        """
        產生輸出檔名
        
        Args:
            original_name: 原始檔名 (不含副檔名)
            section_type: 區段類型
            source_data: 解析的來源資料，包含所有欄位
            
        Returns:
            str: 輸出檔名
        """
        icname = source_data.get('icname', '')
        # 檢查 B9、B11、B15 任一欄位是否包含 QC 相關字樣
        has_qc = (
            'qc' in icname.lower() or
            '_qc' in source_data.get('b9_field', '').lower() or
            '_qc' in source_data.get('b11_field', '').lower()
        )
        
        if has_qc:
            # 原檔 B15 有含 qc → 判斷是 "(qc)" 要加 eqc
            if section_type == '[Data]':
                suffix = '_eqc_'  # 因為會產生 (qc)
            elif section_type == '[QAData]':
                suffix = '_eqc_'  # 因為會產生 (qc)
            else:
                suffix = '_eqc_'
        else:
            # 原檔 B15 沒有含 qc → 根據區段決定
            if section_type == '[Data]':
                suffix = '_FT_'   # 因為會產生 (auto_qc)
            elif section_type == '[QAData]':
                suffix = '_online_eqc_'  # 因為會產生 (qc)
            else:
                suffix = '_processed_'
            
        output_name = f"{original_name}{suffix}.csv"
        print(f"[EDIT] 產生檔名: {section_type} + 原檔含qc={has_qc} → {output_name}")
        
        return output_name
    
    def extract_data11_from_section(self, file_path: str, section_start: int, source_data: Dict[str, str], section_type: str) -> pd.DataFrame:
        """
        從指定區段提取 Data11 格式資料
        
        Args:
            file_path: CTA 檔案路徑
            section_start: 區段開始行號
            source_data: 解析的來源資料
            section_type: 區段類型
            
        Returns:
            pd.DataFrame: Data11 格式的資料框
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 讀取區段資料
            section_lines = []
            start_idx = section_start  # section_start 已經是實際行號
            
            # 從區段標記後開始讀取資料
            for i in range(start_idx, len(lines)):
                line = lines[i].strip()
                if line and not line.startswith('['):  # 讀到下一個區段為止
                    section_lines.append(line.split(','))
                elif line.startswith('['):
                    break  # 遇到下一個區段停止
                    
            if len(section_lines) < 5:
                raise ValueError(f"區段資料不足，僅有 {len(section_lines)} 行")
                
            # 解析測試結構
            test_names = [name.strip() for name in section_lines[0] if name.strip()]
            unit_values = [unit.strip() for unit in section_lines[1] if unit.strip()]
            
            # 處理 min/max 值，保留空值為 "none" 字串
            min_values = []
            max_values = []
            for min_val in section_lines[2]:
                cleaned = min_val.strip()
                min_values.append("none" if not cleaned else cleaned)
            for max_val in section_lines[3]:
                cleaned = max_val.strip()
                max_values.append("none" if not cleaned else cleaned)
            
            # 測試資料從第5行開始
            test_data = []
            for line_data in section_lines[4:]:
                if line_data and len(line_data) > 0:
                    row = [cell.strip() for cell in line_data]
                    test_data.append(row)
                    
            print(f"[CHART] 解析 {section_type}: {len(test_names)} 個測試項目, {len(test_data)} 筆測試資料")
            
            # 建立 Data11 格式的標頭
            target_cols = 2 + len(test_names)  # A、B欄 + 測試項目
            
            # 處理時間格式
            formatted_time = self.format_time_string(source_data['endtime'])
            
            # 產生 B2 內容
            b2_content = self.generate_b2_content(source_data['icname'], section_type, source_data)
            
            # 建立 Min/Max 行 - 動態定位 Data_Num/Data_Cnt 欄位
            max_row = ['', '']  # A、B欄為空字串
            min_row = ['', '']  # A、B欄為空字串
            
            # 尋找 Data_Num/Data_Cnt 欄位位置
            data_column_position = self.find_data_columns_position(test_names)
            
            # 在 Data_Num/Data_Cnt 及其前面的欄位都填入 "none" 字串
            none_columns_count = data_column_position + 1  # 包含 Data_Num/Data_Cnt 本身
            max_row.extend(["none"] * none_columns_count)  # Data 欄位及之前都填入 "none"
            min_row.extend(["none"] * none_columns_count)  # Data 欄位及之前都填入 "none"
            
            # 從 Data_Num/Data_Cnt 欄位之後開始放置實際的 max/min 值
            if len(max_values) > none_columns_count:
                max_row.extend(max_values[none_columns_count:])  # 從 Data 欄位之後開始
            if len(min_values) > none_columns_count:
                min_row.extend(min_values[none_columns_count:])  # 從 Data 欄位之後開始
                
            # 確保行長度一致 - 剩餘欄位也填入 "none" 字串
            while len(max_row) < target_cols:
                max_row.append("none")
            while len(min_row) < target_cols:
                min_row.append("none")
                
            # 建立標頭行
            header_rows = [
                ['Spreadsheet', 'CTA'] + [''] * (target_cols - 2),              # 行1: CTA 格式標記
                ['Test Program:', b2_content] + [''] * (target_cols - 2),       # 行2: 動態B2內容
                ['Lot ID:', 'DNHL6.1W_RT1_1'] + [''] * (target_cols - 2),      # 行3
                ['Operator:', 'ft'] + [''] * (target_cols - 2),                 # 行4
                ['Computer:', source_data['computer']] + [''] * (target_cols - 2), # 行5: 動態電腦名
                ['Date:', formatted_time] + [''] * (target_cols - 2),           # 行6: 動態時間
                ['', ''] + [f'0.00.{i:02d}' for i in range(1, target_cols - 1)], # 行7: 版本號
                ['', ''] + test_names,                                           # 行8: 測試項目名稱
                ['CTA'] + [''] * (target_cols - 1),                            # 行9: CTA標記
                max_row[:target_cols],                                          # 行10: 最大值（前16欄為None）
                min_row[:target_cols],                                          # 行11: 最小值（前16欄為None）
                ['Serial#', 'Bin#'] + unit_values,                             # 行12: 欄位標頭
            ]
            
            # 處理測試資料
            data_rows = []
            for row_idx, row_data in enumerate(test_data):
                if len(row_data) == 0:
                    continue
                    
                # A欄: Serial#, B欄: Bin#, C欄開始: 測試資料
                processed_row = []
                
                # Serial# (通常是第一個欄位)
                serial_no = row_data[0] if len(row_data) > 0 else str(row_idx + 1)
                processed_row.append(serial_no)
                
                # Bin# (通常是第6個欄位，索引5)
                bin_no = row_data[5] if len(row_data) > 5 else '1'
                processed_row.append(bin_no)
                
                # 測試資料 - 保持原始空值，不轉換為 "none"
                for cell in row_data:
                    cleaned_cell = cell.strip() if cell else ''
                    processed_row.append(cleaned_cell)  # 直接使用清理後的值，空格保持為空字串
                
                # 確保行長度一致 - 補空字串而非 "none"
                while len(processed_row) < target_cols:
                    processed_row.append("")  # 使用空字串而不是 "none"
                    
                data_rows.append(processed_row[:target_cols])
                
            # 合併所有資料
            all_rows = header_rows + data_rows
            
            # 創建 DataFrame 並設定正確的欄位名稱（使用第8行的測試項目名稱）
            column_names = ['Column_A', 'Column_B'] + test_names
            data11_df = pd.DataFrame(all_rows, columns=column_names)
            
            print(f"[OK] 建立 {section_type} Data11: {len(data11_df)} 行 x {len(data11_df.columns)} 列")
            
            return data11_df
            
        except Exception as e:
            print(f"[ERROR] 提取 {section_type} 資料失敗: {e}")
            return pd.DataFrame()
    
    def process_cta_to_csv(self, file_path: str, output_dir: str = None) -> List[str]:
        """
        主要處理函數：將 CTA 檔案轉換為 CSV 檔案
        已整合智能欄位對齊和動態電腦名稱提取功能
        
        Args:
            file_path: CTA 檔案路徑
            output_dir: 輸出目錄 (如果為 None，則輸出到檔案同目錄)
            
        Returns:
            List[str]: 輸出的 CSV 檔案路徑列表
        """
        output_files = []
        
        try:
            print(f"[ROCKET] 開始增強版 CTA to CSV 處理: {file_path}")
            
            # 1. 檢測區段
            sections = self.detect_cta_sections(file_path)
            if not sections:
                print("[WARNING] 未發現支援的區段")
                return output_files
            
            # 2. 解析來源資料 (使用動態電腦名稱提取)
            source_data = self.parse_cta_source_data(file_path)
            
            # 3. 根據區段情況選擇處理方式
            if '[Data]' in sections and '[QAData]' in sections:
                # 配對處理：使用欄位對齊
                print("[REFRESH] 檢測到配對區段，啟用智能欄位對齊")
                output_files = self.process_paired_sections_with_alignment(
                    file_path, sections, source_data, output_dir
                )
            else:
                # 單獨處理：使用標準邏輯
                print("[REFRESH] 使用標準處理邏輯")
                
                # 設定輸出目錄：如果未指定，則使用檔案所在目錄
                if output_dir is None:
                    output_path = Path(file_path).parent
                    print(f"[FOLDER] 輸出到檔案同目錄: {output_path}")
                else:
                    output_path = Path(output_dir)
                    output_path.mkdir(exist_ok=True)
                    print(f"[FOLDER] 輸出到指定目錄: {output_path}")
                
                # 解析檔名
                file_stem = Path(file_path).stem
                
                # 處理每個區段
                for section_type, section_line in sections.items():
                    print(f"\n[BOARD] 處理 {section_type} 區段...")
                    
                    # 提取 Data11 資料
                    data11_df = self.extract_data11_from_section(
                        file_path, section_line, source_data, section_type
                    )
                    
                    if data11_df.empty:
                        print(f"[WARNING] {section_type} 區段無有效資料")
                        continue
                        
                    # 產生輸出檔名
                    output_filename = self.generate_output_filename(
                        file_stem, section_type, source_data
                    )
                    output_file_path = output_path / output_filename
                    
                    # 輸出 CSV - 直接輸出，已使用 "none" 字串無需額外處理
                    data11_df.to_csv(output_file_path, index=False, header=False, encoding='utf-8')
                    output_files.append(str(output_file_path))
                    
                    print(f"[OK] 輸出完成: {output_filename}")
                    print(f"   [CHART] 資料大小: {len(data11_df)} 行 x {len(data11_df.columns)} 列")
                    
                print(f"\n[PARTY] CTA to CSV 處理完成，共輸出 {len(output_files)} 個檔案")
            
        except Exception as e:
            print(f"[ERROR] CTA to CSV 處理失敗: {e}")
            import traceback
            traceback.print_exc()
            
        return output_files
    
    def process_paired_sections_with_alignment(self, file_path: str, sections: Dict[str, int], source_data: Dict[str, str], output_dir: str = None) -> List[str]:
        """
        處理配對的 [Data] 和 [QAData] 區段，進行欄位對齊
        
        Args:
            file_path: CTA 檔案路徑
            sections: 區段資訊 {區段名: 行號}
            source_data: 解析的來源資料
            output_dir: 輸出目錄
            
        Returns:
            List[str]: 輸出的 CSV 檔案路徑列表
        """
        output_files = []
        
        try:
            print("[TOOL] 處理配對區段 (啟用欄位對齊)")
            
            # 設定輸出目錄
            if output_dir is None:
                output_path = Path(file_path).parent
            else:
                output_path = Path(output_dir)
                output_path.mkdir(exist_ok=True)
            
            file_stem = Path(file_path).stem
            
            # 處理 [Data] 區段 (FT 檔案)
            if '[Data]' in sections:
                print("\n[BOARD] 處理 [Data] 區段 (FT 檔案)...")
                ft_data = self.extract_data11_from_section(
                    file_path, sections['[Data]'], source_data, '[Data]'
                )
                
                if not ft_data.empty:
                    ft_filename = self.generate_output_filename(file_stem, '[Data]', source_data)
                    ft_file_path = output_path / ft_filename
                    ft_data.to_csv(ft_file_path, index=False, header=False, encoding='utf-8')
                    output_files.append(str(ft_file_path))
                    print(f"[OK] 輸出完成: {ft_filename}")
                    print(f"   [CHART] 資料大小: {len(ft_data)} 行 x {len(ft_data.columns)} 列")
            
            # 處理 [QAData] 區段 (Online EQC 檔案) 並進行欄位對齊
            if '[QAData]' in sections:
                print("\n[BOARD] 處理 [QAData] 區段 (Online EQC 檔案)...")
                eqc_data = self.extract_data11_from_section(
                    file_path, sections['[QAData]'], source_data, '[QAData]'
                )
                
                if not eqc_data.empty:
                    # 如果同時有 FT 資料，進行欄位對齊
                    if '[Data]' in sections and not ft_data.empty:
                        print("[TOOL] 開始智能欄位對齊...")
                        aligned_eqc_data = self.column_aligner.smart_align_eqc_columns(eqc_data, ft_data)
                        
                        # 驗證對齊結果
                        if self.column_aligner.validate_column_consistency(ft_data, aligned_eqc_data):
                            eqc_data = aligned_eqc_data
                            print("[OK] 欄位對齊成功")
                        else:
                            print("[WARNING] 欄位對齊驗證失敗，使用原始資料")
                    
                    eqc_filename = self.generate_output_filename(file_stem, '[QAData]', source_data)
                    eqc_file_path = output_path / eqc_filename
                    eqc_data.to_csv(eqc_file_path, index=False, header=False, encoding='utf-8')
                    output_files.append(str(eqc_file_path))
                    print(f"[OK] 輸出完成: {eqc_filename}")
                    print(f"   [CHART] 資料大小: {len(eqc_data)} 行 x {len(eqc_data.columns)} 列")
            
            print(f"\n[PARTY] 配對處理完成，共輸出 {len(output_files)} 個檔案")
            
        except Exception as e:
            print(f"[ERROR] 配對處理失敗: {e}")
            import traceback
            traceback.print_exc()
            
        return output_files
    

def is_cta_csv_enhanced(file_path: str) -> Dict[str, any]:
    """
    增強版 CTA 檢測函數
    
    Args:
        file_path: 檔案路徑
        
    Returns:
        Dict: CTA 檢測結果和區段資訊
    """
    result = {
        'is_cta': False,
        'sections': {},
        'has_data': False,
        'has_qadata': False
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line_clean = line.strip()
                
                if '[Data]' in line_clean:
                    result['sections']['[Data]'] = line_num
                    result['has_data'] = True
                    result['is_cta'] = True
                    
                elif '[QAData]' in line_clean:
                    result['sections']['[QAData]'] = line_num
                    result['has_qadata'] = True
                    result['is_cta'] = True
                    
        print(f"[SEARCH] CTA 檢測結果: {result}")
        
    except Exception as e:
        print(f"[ERROR] CTA 檢測失敗: {e}")
        
    return result


def archive_cta_csv_file(file_path: str, archive_dir: str) -> bool:
    """
    將 CTA CSV 檔案歸檔到指定目錄
    
    Args:
        file_path: 要歸檔的檔案路徑
        archive_dir: 歸檔目錄路徑
        
    Returns:
        bool: 歸檔是否成功
    """
    try:
        import shutil
        
        archive_path = Path(archive_dir)
        archive_path.mkdir(exist_ok=True)
        
        file_name = Path(file_path).name
        dest_path = archive_path / file_name
        
        shutil.move(file_path, str(dest_path))
        print(f"[FOLDER] 檔案已歸檔: {file_name} → {archive_dir}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 檔案歸檔失敗: {e}")
        return False