"""
Dramatiq 管道工具 - 管道管理和狀態工具
"""

import os
import json
import redis
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass, field
from loguru import logger

from backend.shared.infrastructure.adapters.vendor_file_monitor import get_vendor_file_monitor, FileProcessingStatus


class PipelineStatus(Enum):
    """管道狀態枚舉"""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIALLY_FAILED = "partially_failed"


@dataclass
class PipelineContext:
    """管道上下文"""
    pipeline_id: str
    pipeline_type: str = "vendor_processing"
    created_at: datetime = field(default_factory=datetime.now)
    status: PipelineStatus = PipelineStatus.PENDING
    task_count: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            'pipeline_id': self.pipeline_id,
            'pipeline_type': self.pipeline_type,
            'created_at': self.created_at.isoformat(),
            'status': self.status.value,
            'task_count': self.task_count,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'metadata': self.metadata
        }


class PipelineManager:
    """管道管理器"""
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379, redis_db: int = 0):
        """初始化管道管理器"""
        try:
            self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db)
            self.redis_client.ping()
            logger.info("✅ PipelineManager Redis 連接成功")
        except Exception as e:
            logger.warning(f"⚠️ PipelineManager Redis 連接失敗: {e}")
            self.redis_client = None
        
        self.monitor = get_vendor_file_monitor()
    
    def create_pipeline_context(
        self,
        pipeline_type: str = "vendor_processing",
        metadata: Dict[str, Any] = None
    ) -> str:
        """創建管道上下文"""
        pipeline_id = str(uuid.uuid4())
        
        context = PipelineContext(
            pipeline_id=pipeline_id,
            pipeline_type=pipeline_type,
            metadata=metadata or {}
        )
        
        # 保存到 Redis
        if self.redis_client:
            try:
                context_key = f"pipeline:context:{pipeline_id}"
                self.redis_client.setex(
                    context_key,
                    timedelta(days=1),  # 保存1天
                    json.dumps(context.to_dict())
                )
                logger.debug(f"✅ 管道上下文已保存: {pipeline_id}")
            except Exception as e:
                logger.warning(f"⚠️ 保存管道上下文失敗: {e}")
        
        return pipeline_id
    
    def get_pipeline_context(self, pipeline_id: str) -> Optional[PipelineContext]:
        """獲取管道上下文"""
        if not self.redis_client:
            return None
        
        try:
            context_key = f"pipeline:context:{pipeline_id}"
            data = self.redis_client.get(context_key)
            
            if data:
                context_dict = json.loads(data.decode('utf-8'))
                context = PipelineContext(
                    pipeline_id=context_dict['pipeline_id'],
                    pipeline_type=context_dict['pipeline_type'],
                    created_at=datetime.fromisoformat(context_dict['created_at']),
                    status=PipelineStatus(context_dict['status']),
                    task_count=context_dict['task_count'],
                    completed_tasks=context_dict['completed_tasks'],
                    failed_tasks=context_dict['failed_tasks'],
                    metadata=context_dict['metadata']
                )
                return context
        except Exception as e:
            logger.warning(f"⚠️ 獲取管道上下文失敗: {e}")
        
        return None
    
    def update_pipeline_context(
        self,
        pipeline_id: str,
        status: PipelineStatus = None,
        task_count: int = None,
        completed_tasks: int = None,
        failed_tasks: int = None,
        metadata_update: Dict[str, Any] = None
    ) -> bool:
        """更新管道上下文"""
        context = self.get_pipeline_context(pipeline_id)
        if not context:
            logger.warning(f"⚠️ 管道上下文不存在: {pipeline_id}")
            return False
        
        # 更新字段
        if status is not None:
            context.status = status
        if task_count is not None:
            context.task_count = task_count
        if completed_tasks is not None:
            context.completed_tasks = completed_tasks
        if failed_tasks is not None:
            context.failed_tasks = failed_tasks
        if metadata_update:
            context.metadata.update(metadata_update)
        
        # 保存回 Redis
        if self.redis_client:
            try:
                context_key = f"pipeline:context:{pipeline_id}"
                self.redis_client.setex(
                    context_key,
                    timedelta(days=1),
                    json.dumps(context.to_dict())
                )
                logger.debug(f"✅ 管道上下文已更新: {pipeline_id}")
                return True
            except Exception as e:
                logger.warning(f"⚠️ 更新管道上下文失敗: {e}")
        
        return False
    
    def get_pipeline_status(self, pipeline_id: str) -> Dict[str, Any]:
        """獲取管道狀態"""
        context = self.get_pipeline_context(pipeline_id)
        if not context:
            return {
                'pipeline_id': pipeline_id,
                'status': 'not_found',
                'message': '管道不存在'
            }
        
        # 獲取相關的監控數據
        active_trackings = self.monitor.get_active_trackings()
        pipeline_trackings = [
            t for t in active_trackings 
            if pipeline_id in str(t.tracking_id)  # 簡化的匹配邏輯
        ]
        
        return {
            'pipeline_id': pipeline_id,
            'pipeline_type': context.pipeline_type,
            'status': context.status.value,
            'created_at': context.created_at.isoformat(),
            'task_count': context.task_count,
            'completed_tasks': context.completed_tasks,
            'failed_tasks': context.failed_tasks,
            'active_tasks': len(pipeline_trackings),
            'progress_percentage': (
                (context.completed_tasks + context.failed_tasks) / max(1, context.task_count) * 100
                if context.task_count > 0 else 0
            ),
            'metadata': context.metadata,
            'task_details': [
                {
                    'tracking_id': t.tracking_id,
                    'status': t.status.value,
                    'progress': t.progress_percentage,
                    'file_name': t.file_name,
                    'vendor_name': t.vendor_name,
                    'current_step': t.current_step
                }
                for t in pipeline_trackings
            ]
        }
    
    def list_active_pipelines(self) -> List[Dict[str, Any]]:
        """列出所有活躍的管道"""
        if not self.redis_client:
            return []
        
        try:
            # 掃描所有管道上下文
            pattern = "pipeline:context:*"
            pipeline_keys = self.redis_client.keys(pattern)
            
            active_pipelines = []
            for key in pipeline_keys:
                try:
                    data = self.redis_client.get(key)
                    if data:
                        context_dict = json.loads(data.decode('utf-8'))
                        
                        # 只返回活躍的管道
                        status = context_dict.get('status')
                        if status in ['pending', 'running']:
                            active_pipelines.append({
                                'pipeline_id': context_dict['pipeline_id'],
                                'pipeline_type': context_dict['pipeline_type'],
                                'status': status,
                                'created_at': context_dict['created_at'],
                                'task_count': context_dict['task_count'],
                                'completed_tasks': context_dict['completed_tasks'],
                                'failed_tasks': context_dict['failed_tasks']
                            })
                except Exception as e:
                    logger.debug(f"處理管道鍵 {key} 時發生錯誤: {e}")
            
            return active_pipelines
            
        except Exception as e:
            logger.warning(f"⚠️ 列出活躍管道失敗: {e}")
            return []
    
    def cancel_pipeline(self, pipeline_id: str, reason: str = "用戶取消") -> Dict[str, Any]:
        """取消管道執行"""
        logger.warning(f"🛑 嘗試取消管道: {pipeline_id} - {reason}")
        
        # 更新管道狀態
        success = self.update_pipeline_context(
            pipeline_id,
            status=PipelineStatus.CANCELLED,
            metadata_update={'cancel_reason': reason, 'cancelled_at': datetime.now().isoformat()}
        )
        
        if success:
            # 記錄取消日誌
            try:
                if self.redis_client:
                    cancel_log_key = f"pipeline:cancel_log:{pipeline_id}"
                    cancel_log = {
                        'pipeline_id': pipeline_id,
                        'reason': reason,
                        'cancelled_at': datetime.now().isoformat()
                    }
                    self.redis_client.setex(
                        cancel_log_key,
                        timedelta(days=7),  # 保存7天
                        json.dumps(cancel_log)
                    )
            except Exception as e:
                logger.debug(f"記錄取消日誌失敗: {e}")
            
            return {
                'pipeline_id': pipeline_id,
                'status': 'cancelled',
                'message': f'管道已標記為取消: {reason}',
                'timestamp': datetime.now().isoformat(),
                'note': '注意：已在執行的任務可能仍會繼續執行'
            }
        else:
            return {
                'pipeline_id': pipeline_id,
                'status': 'error',
                'message': '取消管道失敗',
                'timestamp': datetime.now().isoformat()
            }
    
    def cleanup_completed_pipelines(self, older_than_hours: int = 24) -> Dict[str, Any]:
        """清理已完成的管道"""
        if not self.redis_client:
            return {'cleaned_count': 0, 'message': 'Redis 不可用'}
        
        try:
            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            pattern = "pipeline:context:*"
            pipeline_keys = self.redis_client.keys(pattern)
            
            cleaned_count = 0
            for key in pipeline_keys:
                try:
                    data = self.redis_client.get(key)
                    if data:
                        context_dict = json.loads(data.decode('utf-8'))
                        created_at = datetime.fromisoformat(context_dict['created_at'])
                        status = context_dict.get('status')
                        
                        # 清理舊的已完成或失敗的管道
                        if (created_at < cutoff_time and 
                            status in ['completed', 'failed', 'cancelled']):
                            self.redis_client.delete(key)
                            cleaned_count += 1
                            logger.debug(f"🗑️ 清理舊管道: {context_dict['pipeline_id']}")
                            
                except Exception as e:
                    logger.debug(f"處理管道鍵 {key} 時發生錯誤: {e}")
            
            logger.info(f"🗑️ 清理已完成管道: {cleaned_count} 個")
            return {
                'cleaned_count': cleaned_count,
                'cutoff_time': cutoff_time.isoformat(),
                'message': f'已清理 {cleaned_count} 個舊管道'
            }
            
        except Exception as e:
            logger.error(f"❌ 清理管道失敗: {e}")
            return {
                'cleaned_count': 0,
                'error': str(e),
                'message': '清理管道時發生錯誤'
            }


# 全局管道管理器實例
_pipeline_manager: Optional[PipelineManager] = None


def get_pipeline_manager() -> PipelineManager:
    """獲取全局管道管理器實例"""
    global _pipeline_manager
    if _pipeline_manager is None:
        _pipeline_manager = PipelineManager()
    return _pipeline_manager


def create_pipeline_with_context(
    vendor_files: List[Dict[str, Any]],
    pipeline_type: str = "vendor_processing",
    metadata: Dict[str, Any] = None
) -> tuple[str, PipelineContext]:
    """
    創建帶上下文的管道
    
    Returns:
        tuple: (pipeline_id, context)
    """
    manager = get_pipeline_manager()
    
    # 創建管道上下文
    pipeline_id = manager.create_pipeline_context(
        pipeline_type=pipeline_type,
        metadata={
            'vendor_files_count': len(vendor_files),
            'vendor_codes': list(set(vf.get('vendor_code') for vf in vendor_files if vf.get('vendor_code'))),
            'created_by': 'unified_email_processor',
            **(metadata or {})
        }
    )
    
    # 更新任務計數
    manager.update_pipeline_context(
        pipeline_id,
        status=PipelineStatus.PENDING,
        task_count=len(vendor_files)
    )
    
    context = manager.get_pipeline_context(pipeline_id)
    return pipeline_id, context