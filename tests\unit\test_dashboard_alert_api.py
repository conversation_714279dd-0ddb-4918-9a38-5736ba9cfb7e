"""
統一監控儀表板 - 告警管理 API 單元測試

Task 19: 測試告警管理 API 的所有端點
- 告警查詢 API 測試
- 告警確認 API 測試
- 告警統計 API 測試
- 告警規則管理 API 測試
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from fastapi import FastAPI

from backend.monitoring.api.dashboard_alert_api import router
from backend.monitoring.models.dashboard_alert_models import (
    DashboardAlert,
    AlertLevel,
    AlertStatus,
    AlertType,
    AlertRule,
    AlertSummary,
    NotificationChannel
)


@pytest.fixture
def app():
    """創建測試應用"""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
def client(app):
    """創建測試客戶端"""
    return TestClient(app)


@pytest.fixture
def mock_alert_service():
    """模擬告警服務"""
    service = Mock()
    service.get_alerts_by_criteria = AsyncMock()
    service.count_alerts_by_criteria = AsyncMock()
    service.get_active_alerts = Mock()
    service.get_alert_by_id = AsyncMock()
    service.get_related_alerts = AsyncMock()
    service.get_alert_timeline = AsyncMock()
    service.acknowledge_alert = AsyncMock()
    service.resolve_alert = AsyncMock()
    service.add_alert_note = AsyncMock()
    service.get_alert_summary = Mock()
    service.get_alert_history = Mock()
    service.get_top_alert_types = AsyncMock()
    service.generate_trend_data = AsyncMock()
    service.get_peak_alert_hour = AsyncMock()
    service.get_level_distribution = AsyncMock()
    service.export_alerts = AsyncMock()
    service.cleanup_old_alerts = Mock()
    service.get_service_statistics = Mock()
    service.get_alert_rules = AsyncMock()
    service.create_alert_rule = AsyncMock()
    service.get_alert_rule_by_id = AsyncMock()
    service.update_alert_rule = AsyncMock()
    service.delete_alert_rule = AsyncMock()
    service.get_rule_trigger_history = AsyncMock()
    return service


@pytest.fixture
def sample_alert():
    """創建測試告警"""
    return DashboardAlert(
        id="test-alert-1",
        alert_type=AlertType.QUEUE_OVERFLOW,
        level=AlertLevel.WARNING,
        title="測試告警",
        message="這是一個測試告警",
        source="test_monitor",
        triggered_at=datetime.now(),
        status=AlertStatus.ACTIVE,
        threshold_value=10.0,
        current_value=15.0,
        rule_id="test-rule-1"
    )


@pytest.fixture
def sample_alert_rule():
    """創建測試告警規則"""
    return AlertRule(
        id="test-rule-1",
        name="測試規則",
        description="測試告警規則",
        metric_type="email_queue",
        metric_name="pending_count",
        threshold_value=10.0,
        threshold_operator=">",
        alert_type=AlertType.QUEUE_OVERFLOW,
        alert_level=AlertLevel.WARNING,
        alert_title_template="佇列過載: {current_value}",
        alert_message_template="當前佇列數量 {current_value} 超過閾值 {threshold_value}",
        enabled=True,
        notification_channels=[NotificationChannel.EMAIL]
    )


class TestAlertQueryAPI:
    """告警查詢 API 測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alerts_success(self, mock_service_dep, client, mock_alert_service, sample_alert):
        """測試獲取告警列表成功"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alerts_by_criteria.return_value = [sample_alert]
        mock_alert_service.count_alerts_by_criteria.return_value = 1
        
        # 執行請求
        response = client.get("/api/alerts/")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert len(data["data"]["alerts"]) == 1
        assert data["data"]["pagination"]["total"] == 1
        
        # 驗證服務調用
        mock_alert_service.get_alerts_by_criteria.assert_called_once()
        mock_alert_service.count_alerts_by_criteria.assert_called_once()
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alerts_with_filters(self, mock_service_dep, client, mock_alert_service):
        """測試帶篩選條件的告警查詢"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alerts_by_criteria.return_value = []
        mock_alert_service.count_alerts_by_criteria.return_value = 0
        
        # 執行請求
        response = client.get("/api/alerts/?level=critical&status=active&limit=50")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["data"]["filters"]["level"] == "critical"
        assert data["data"]["filters"]["status"] == "active"
        assert data["data"]["pagination"]["limit"] == 50
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alerts_invalid_level(self, mock_service_dep, client, mock_alert_service):
        """測試無效告警級別"""
        mock_service_dep.return_value = mock_alert_service
        
        response = client.get("/api/alerts/?level=invalid")
        
        assert response.status_code == 400
        assert "無效的告警級別" in response.json()["detail"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_active_alerts(self, mock_service_dep, client, mock_alert_service, sample_alert):
        """測試獲取活躍告警"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_active_alerts.return_value = [sample_alert]
        
        # 執行請求
        response = client.get("/api/alerts/active")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["total_count"] == 1
        assert data["data"]["priority_sorted"] == True
        assert "level_counts" in data["data"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_details(self, mock_service_dep, client, mock_alert_service, sample_alert):
        """測試獲取告警詳情"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_by_id.return_value = sample_alert
        mock_alert_service.get_related_alerts.return_value = []
        mock_alert_service.get_alert_timeline.return_value = []
        
        # 執行請求
        response = client.get("/api/alerts/test-alert-1")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "alert" in data["data"]
        assert "related_alerts" in data["data"]
        assert "timeline" in data["data"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_details_not_found(self, mock_service_dep, client, mock_alert_service):
        """測試獲取不存在的告警詳情"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_by_id.return_value = None
        
        # 執行請求
        response = client.get("/api/alerts/nonexistent")
        
        # 驗證結果
        assert response.status_code == 404
        assert "找不到告警" in response.json()["detail"]


class TestAlertAcknowledgeAPI:
    """告警確認 API 測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_acknowledge_alert_success(self, mock_service_dep, client, mock_alert_service, sample_alert):
        """測試確認告警成功"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.acknowledge_alert.return_value = True
        mock_alert_service.add_alert_note.return_value = True
        mock_alert_service.get_alert_by_id.return_value = sample_alert
        
        # 執行請求
        request_data = {
            "acknowledged_by": "test_user",
            "note": "已確認此告警"
        }
        response = client.post("/api/alerts/test-alert-1/acknowledge", json=request_data)
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "已確認" in data["message"]
        assert data["data"]["acknowledged_by"] == "test_user"
        
        # 驗證服務調用
        mock_alert_service.acknowledge_alert.assert_called_once_with(
            alert_id="test-alert-1",
            acknowledged_by="test_user"
        )
        mock_alert_service.add_alert_note.assert_called_once()
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_acknowledge_alert_not_found(self, mock_service_dep, client, mock_alert_service):
        """測試確認不存在的告警"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.acknowledge_alert.return_value = False
        
        # 執行請求
        request_data = {"acknowledged_by": "test_user"}
        response = client.post("/api/alerts/nonexistent/acknowledge", json=request_data)
        
        # 驗證結果
        assert response.status_code == 404
        assert "告警不存在或已處理" in response.json()["detail"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_resolve_alert_success(self, mock_service_dep, client, mock_alert_service, sample_alert):
        """測試解決告警成功"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.resolve_alert.return_value = True
        mock_alert_service.get_alert_by_id.return_value = sample_alert
        
        # 執行請求
        request_data = {
            "resolved_by": "test_user",
            "resolution_note": "問題已解決"
        }
        response = client.post("/api/alerts/test-alert-1/resolve", json=request_data)
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "已解決" in data["message"]
        assert data["data"]["resolved_by"] == "test_user"
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_batch_acknowledge_alerts(self, mock_service_dep, client, mock_alert_service):
        """測試批量確認告警"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.acknowledge_alert.return_value = True
        mock_alert_service.add_alert_note.return_value = True
        
        # 執行請求
        request_data = ["alert-1", "alert-2", "alert-3"]
        response = client.post(
            "/api/alerts/batch/acknowledge",
            json={
                "alert_ids": request_data,
                "acknowledged_by": "test_user",
                "note": "批量確認"
            }
        )
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["success_count"] == 3
        assert data["data"]["total_requested"] == 3
        
        # 驗證服務調用次數
        assert mock_alert_service.acknowledge_alert.call_count == 3


class TestAlertStatisticsAPI:
    """告警統計 API 測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_statistics_summary(self, mock_service_dep, client, mock_alert_service):
        """測試獲取告警統計摘要"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_summary = AlertSummary(
            total_alerts=10,
            active_alerts=5,
            critical_alerts=2,
            error_alerts=1,
            warning_alerts=2,
            info_alerts=0
        )
        mock_alert_service.get_alert_summary.return_value = mock_summary
        mock_alert_service.get_alert_history.return_value = []
        mock_alert_service.get_top_alert_types.return_value = []
        
        # 執行請求
        response = client.get("/api/alerts/statistics/summary")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "summary" in data["data"]
        assert "trends" in data["data"]
        assert "source_statistics" in data["data"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_trends(self, mock_service_dep, client, mock_alert_service):
        """測試獲取告警趨勢"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_history.return_value = []
        mock_alert_service.generate_trend_data.return_value = {"data": {}}
        mock_alert_service.get_peak_alert_hour.return_value = {"hour": 14, "count": 5}
        mock_alert_service.get_level_distribution.return_value = {"counts": {}}
        
        # 執行請求
        response = client.get("/api/alerts/statistics/trends?time_range=24h&group_by=hour")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["time_range"] == "24h"
        assert data["data"]["group_by"] == "hour"
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_trends_invalid_range(self, mock_service_dep, client, mock_alert_service):
        """測試無效時間範圍"""
        mock_service_dep.return_value = mock_alert_service
        
        response = client.get("/api/alerts/statistics/trends?time_range=invalid")
        
        assert response.status_code == 400
        assert "無效的時間範圍" in response.json()["detail"]


class TestAlertRulesAPI:
    """告警規則管理 API 測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_rules(self, mock_service_dep, client, mock_alert_service, sample_alert_rule):
        """測試獲取告警規則列表"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_rules.return_value = [sample_alert_rule]
        
        # 執行請求
        response = client.get("/api/alerts/rules")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert len(data["data"]["rules"]) == 1
        assert data["data"]["total_count"] == 1
        assert data["data"]["enabled_count"] == 1
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_create_alert_rule_success(self, mock_service_dep, client, mock_alert_service, sample_alert_rule):
        """測試創建告警規則成功"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.create_alert_rule.return_value = sample_alert_rule
        
        # 執行請求
        request_data = {
            "name": "測試規則",
            "description": "測試告警規則",
            "metric_type": "email_queue",
            "metric_name": "pending_count",
            "threshold_value": 10.0,
            "threshold_operator": ">",
            "alert_type": "queue_overflow",
            "alert_level": "warning",
            "alert_title_template": "佇列過載: {current_value}",
            "alert_message_template": "當前佇列數量 {current_value} 超過閾值 {threshold_value}",
            "enabled": True,
            "cooldown_seconds": 900,
            "notification_channels": ["email"]
        }
        response = client.post("/api/alerts/rules", json=request_data)
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "創建成功" in data["message"]
        assert "rule" in data["data"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_create_alert_rule_invalid_operator(self, mock_service_dep, client, mock_alert_service):
        """測試創建告警規則 - 無效運算符"""
        mock_service_dep.return_value = mock_alert_service
        
        request_data = {
            "name": "測試規則",
            "metric_type": "email_queue",
            "metric_name": "pending_count",
            "threshold_value": 10.0,
            "threshold_operator": "invalid",  # 無效運算符
            "alert_type": "queue_overflow",
            "alert_level": "warning",
            "alert_title_template": "測試",
            "alert_message_template": "測試"
        }
        response = client.post("/api/alerts/rules", json=request_data)
        
        assert response.status_code == 400
        assert "無效的比較運算符" in response.json()["detail"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_rule_details(self, mock_service_dep, client, mock_alert_service, sample_alert_rule):
        """測試獲取告警規則詳情"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_rule_by_id.return_value = sample_alert_rule
        mock_alert_service.get_rule_trigger_history.return_value = []
        
        # 執行請求
        response = client.get("/api/alerts/rules/test-rule-1")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "rule" in data["data"]
        assert "trigger_history" in data["data"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_update_alert_rule(self, mock_service_dep, client, mock_alert_service, sample_alert_rule):
        """測試更新告警規則"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_rule_by_id.return_value = sample_alert_rule
        mock_alert_service.update_alert_rule.return_value = sample_alert_rule
        
        # 執行請求
        request_data = {
            "threshold_value": 20.0,
            "enabled": False
        }
        response = client.put("/api/alerts/rules/test-rule-1", json=request_data)
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "更新成功" in data["message"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_delete_alert_rule(self, mock_service_dep, client, mock_alert_service):
        """測試刪除告警規則"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.delete_alert_rule.return_value = True
        
        # 執行請求
        response = client.delete("/api/alerts/rules/test-rule-1")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "已刪除" in data["message"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_toggle_alert_rule(self, mock_service_dep, client, mock_alert_service, sample_alert_rule):
        """測試切換告警規則狀態"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_rule_by_id.return_value = sample_alert_rule
        
        # 創建停用的規則用於測試
        disabled_rule = sample_alert_rule
        disabled_rule.enabled = False
        mock_alert_service.update_alert_rule.return_value = disabled_rule
        
        # 執行請求
        response = client.post("/api/alerts/rules/test-rule-1/toggle")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "停用" in data["message"]


class TestAlertHistoryAPI:
    """告警歷史 API 測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_history(self, mock_service_dep, client, mock_alert_service, sample_alert):
        """測試獲取告警歷史"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alert_history.return_value = [sample_alert]
        
        # 執行請求
        response = client.get("/api/alerts/history?hours=24&limit=100")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert len(data["data"]["alerts"]) == 1
        assert data["data"]["time_range_hours"] == 24
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_export_alerts_json(self, mock_service_dep, client, mock_alert_service):
        """測試匯出告警資料 - JSON 格式"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.export_alerts.return_value = [{"id": "test", "level": "warning"}]
        
        # 執行請求
        response = client.get("/api/alerts/export?format=json")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["export_format"] == "json"
        assert isinstance(data["data"]["export_data"], list)
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_export_alerts_csv(self, mock_service_dep, client, mock_alert_service):
        """測試匯出告警資料 - CSV 格式"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.export_alerts.return_value = "ID,Level,Title\ntest,warning,Test Alert"
        
        # 執行請求
        response = client.get("/api/alerts/export?format=csv")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["export_format"] == "csv"
        assert isinstance(data["data"]["export_data"], str)
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_export_alerts_invalid_format(self, mock_service_dep, client, mock_alert_service):
        """測試匯出告警資料 - 無效格式"""
        mock_service_dep.return_value = mock_alert_service
        
        response = client.get("/api/alerts/export?format=xml")
        
        assert response.status_code == 400
        assert "無效的匯出格式" in response.json()["detail"]


class TestAlertMaintenanceAPI:
    """告警維護 API 測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_cleanup_old_alerts(self, mock_service_dep, client, mock_alert_service):
        """測試清理舊告警記錄"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.cleanup_old_alerts.return_value = 25
        
        # 執行請求
        response = client.post("/api/alerts/maintenance/cleanup?days=30")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "已清理 25 個" in data["message"]
        assert data["data"]["cleaned_count"] == 25
        assert data["data"]["retention_days"] == 30
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_get_alert_service_statistics(self, mock_service_dep, client, mock_alert_service):
        """測試獲取告警服務統計"""
        # 設定模擬
        mock_service_dep.return_value = mock_alert_service
        mock_stats = {
            "active_alerts_count": 5,
            "total_alerts_in_history": 100,
            "notification_channels": 3,
            "alert_stats": {
                "total_generated": 150,
                "total_sent": 140,
                "total_merged": 10
            }
        }
        mock_alert_service.get_service_statistics.return_value = mock_stats
        
        # 執行請求
        response = client.get("/api/alerts/service/statistics")
        
        # 驗證結果
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["active_alerts_count"] == 5
        assert data["data"]["total_alerts_in_history"] == 100


class TestAlertAPIErrorHandling:
    """告警 API 錯誤處理測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_service_exception_handling(self, mock_service_dep, client, mock_alert_service):
        """測試服務異常處理"""
        # 設定模擬拋出異常
        mock_service_dep.return_value = mock_alert_service
        mock_alert_service.get_alerts_by_criteria.side_effect = Exception("Service error")
        
        # 執行請求
        response = client.get("/api/alerts/")
        
        # 驗證結果
        assert response.status_code == 500
        assert "獲取告警列表失敗" in response.json()["detail"]
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_batch_acknowledge_too_many_alerts(self, mock_service_dep, client, mock_alert_service):
        """測試批量確認過多告警"""
        mock_service_dep.return_value = mock_alert_service
        
        # 創建超過限制的告警 ID 列表
        alert_ids = [f"alert-{i}" for i in range(101)]
        
        response = client.post(
            "/api/alerts/batch/acknowledge",
            json={
                "alert_ids": alert_ids,
                "acknowledged_by": "test_user"
            }
        )
        
        assert response.status_code == 400
        assert "批量操作最多支援 100 個告警" in response.json()["detail"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])