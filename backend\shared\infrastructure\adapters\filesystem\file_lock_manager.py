#!/usr/bin/env python3
"""
檔案鎖定管理器
防止多人並發處理時的檔案系統競爭條件
"""

import os
import time
import hashlib
from pathlib import Path
from typing import Dict, Set, Optional
from threading import Lock
from datetime import datetime, timedelta
from loguru import logger

class FileLockManager:
    """檔案鎖定管理器 - 防止並發檔案衝突"""
    
    def __init__(self):
        self._locks: Dict[str, Dict] = {}  # path_hash -> lock_info
        self._lock = Lock()  # 保護 _locks 字典的線程鎖
        
    def _get_path_hash(self, folder_path: str) -> str:
        """獲取路徑的唯一哈希值"""
        normalized_path = os.path.normpath(folder_path).lower()
        return hashlib.md5(normalized_path.encode()).hexdigest()
    
    def _is_path_conflict(self, folder_path: str) -> Optional[str]:
        """檢查路徑是否與現有鎖定路徑衝突"""
        normalized_path = os.path.normpath(folder_path).lower()
        
        for path_hash, lock_info in self._locks.items():
            locked_path = os.path.normpath(lock_info['path']).lower()
            
            # 檢查是否為相同路徑或父子路徑關係
            if (normalized_path == locked_path or 
                normalized_path.startswith(locked_path + os.sep) or
                locked_path.startswith(normalized_path + os.sep)):
                return lock_info['session_id']
        
        return None
    
    def acquire_lock(self, folder_path: str, session_id: str, user_id: str) -> Dict[str, any]:
        """
        獲取路徑鎖定
        
        Returns:
            Dict with 'success', 'message', 'conflict_session' keys
        """
        with self._lock:
            # 檢查路徑衝突
            conflict_session = self._is_path_conflict(folder_path)
            if conflict_session:
                return {
                    'success': False,
                    'message': f'路徑衝突：該路徑或相關路徑正在被其他會話處理',
                    'conflict_session': conflict_session
                }
            
            # 獲取鎖定
            path_hash = self._get_path_hash(folder_path)
            self._locks[path_hash] = {
                'path': folder_path,
                'session_id': session_id,
                'user_id': user_id,
                'locked_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(hours=2)  # 2小時超時
            }
            
            logger.info(f"🔒 路徑鎖定成功: {folder_path} (會話: {session_id})")
            return {
                'success': True,
                'message': '路徑鎖定成功',
                'lock_info': self._locks[path_hash]
            }
    
    def release_lock(self, folder_path: str, session_id: str) -> bool:
        """釋放路徑鎖定"""
        with self._lock:
            path_hash = self._get_path_hash(folder_path)
            
            if path_hash in self._locks:
                lock_info = self._locks[path_hash]
                if lock_info['session_id'] == session_id:
                    del self._locks[path_hash]
                    logger.info(f"🔓 路徑鎖定釋放: {folder_path} (會話: {session_id})")
                    return True
                else:
                    logger.warning(f"⚠️ 無權釋放鎖定: {folder_path} (會話: {session_id})")
                    return False
            
            return True  # 鎖定不存在，視為成功
    
    def cleanup_expired_locks(self):
        """清理過期的鎖定"""
        try:
            with self._lock:
                now = datetime.now()
                expired_hashes = []

                for path_hash, lock_info in self._locks.items():
                    if now > lock_info['expires_at']:
                        expired_hashes.append(path_hash)

                for path_hash in expired_hashes:
                    lock_info = self._locks[path_hash]
                    del self._locks[path_hash]
                    logger.info(f"🧹 清理過期鎖定: {lock_info['path']} (會話: {lock_info['session_id']})")
        except Exception as e:
            logger.error(f"清理過期鎖定失敗: {e}")
    
    def get_active_locks(self) -> Dict[str, Dict]:
        """獲取所有活躍的鎖定"""
        try:
            with self._lock:
                # 先清理過期鎖定
                self.cleanup_expired_locks()
                return self._locks.copy()
        except Exception as e:
            logger.error(f"獲取活躍鎖定失敗: {e}")
            return {}
    
    def is_path_locked(self, folder_path: str) -> Optional[Dict]:
        """檢查路徑是否被鎖定"""
        with self._lock:
            conflict_session = self._is_path_conflict(folder_path)
            if conflict_session:
                # 找到衝突的鎖定信息
                for lock_info in self._locks.values():
                    if lock_info['session_id'] == conflict_session:
                        return lock_info
            return None

# 全局實例
_file_lock_manager = None

def get_file_lock_manager() -> FileLockManager:
    """獲取檔案鎖定管理器實例"""
    global _file_lock_manager
    if _file_lock_manager is None:
        _file_lock_manager = FileLockManager()
    return _file_lock_manager
