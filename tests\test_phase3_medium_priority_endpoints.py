"""
測試 Phase 3 中優先級端點重構
測試重構後的搜尋和 UI 端點是否正確使用依賴注入
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from frontend.api.search_routes import router as search_router
from frontend.api.ui_routes import router as ui_router
from frontend.api.dependencies import (
    get_api_state, get_product_search_service, get_llm_search_service,
    get_staging_service, get_processing_service,
    require_product_search_service, require_llm_search_service
)


@pytest.fixture
def app():
    """創建測試應用"""
    app = FastAPI()
    app.include_router(search_router)
    app.include_router(ui_router)
    return app


@pytest.fixture
def mock_api_state():
    """模擬 API 狀態"""
    mock_state = Mock()
    mock_state.increment_request_count = Mock()
    mock_state.increment_error_count = Mock()
    return mock_state


@pytest.fixture
def mock_product_search_service():
    """模擬產品搜尋服務"""
    mock_service = Mock()
    mock_service.get_task_status = Mock(return_value={
        "status": "completed",
        "progress": 100.0,
        "message": "搜尋完成",
        "results_count": 5
    })
    return mock_service


@pytest.fixture
def mock_llm_search_service():
    """模擬 LLM 搜尋服務"""
    mock_service = Mock()
    mock_service.smart_search = AsyncMock(return_value={
        "results": [{"file": "test.txt", "score": 0.9}],
        "processing_time": 1.5
    })
    mock_service.smart_search_advanced = AsyncMock(return_value={
        "results": [{"file": "test.txt", "score": 0.9}],
        "processing_time": 1.5,
        "search_strategy": "semantic",
        "confidence_score": 0.85
    })
    return mock_service


@pytest.fixture
def mock_staging_service():
    """模擬暫存服務"""
    mock_service = Mock()
    mock_service.get_service_statistics = Mock(return_value={
        "total_tasks": 10,
        "pending_tasks": 2,
        "active_tasks": 1,
        "completed_tasks": 6,
        "failed_tasks": 1
    })
    return mock_service


@pytest.fixture
def mock_processing_service():
    """模擬處理服務"""
    mock_service = Mock()
    mock_service.list_tasks = Mock(return_value=[
        {"id": "task1", "status": "completed"},
        {"id": "task2", "status": "running"}
    ])
    return mock_service


class TestSearchRoutesRefactoring:
    """測試搜尋路由重構"""

    def test_search_task_status_uses_dependency_injection(
        self, app, mock_api_state, mock_product_search_service
    ):
        """測試搜尋任務狀態端點使用依賴注入"""
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service

        client = TestClient(app)
        
        # 調用端點
        response = client.get("/api/search/task/test-task-id")
        
        # 驗證響應
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == "test-task-id"
        assert data["status"] == "completed"
        assert data["progress"] == 100.0
        
        # 驗證依賴注入被調用
        mock_api_state.increment_request_count.assert_called_once()
        mock_product_search_service.get_task_status.assert_called_once_with("test-task-id")

    def test_smart_search_get_uses_dependency_injection(
        self, app, mock_api_state, mock_llm_search_service
    ):
        """測試智慧搜尋 GET 端點使用依賴注入"""
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        app.dependency_overrides[require_llm_search_service] = lambda: mock_llm_search_service

        client = TestClient(app)
        
        # 調用端點
        response = client.get("/api/smart-search?query=test query")
        
        # 驗證響應
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["query"] == "test query"
        assert len(data["results"]) == 1
        
        # 驗證依賴注入被調用
        mock_api_state.increment_request_count.assert_called_once()
        mock_llm_search_service.smart_search.assert_called_once()

    @pytest.mark.asyncio
    async def test_smart_search_post_uses_dependency_injection(
        self, app, mock_api_state, mock_llm_search_service
    ):
        """測試智慧搜尋 POST 端點使用依賴注入"""
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        app.dependency_overrides[require_llm_search_service] = lambda: mock_llm_search_service

        client = TestClient(app)
        
        # 準備請求數據
        request_data = {
            "query": "test query",
            "search_paths": ["/test/path"],
            "max_results": 10
        }
        
        # 調用端點
        response = client.post("/api/smart-search", json=request_data)
        
        # 驗證響應
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["query"] == "test query"
        
        # 驗證依賴注入被調用
        mock_api_state.increment_request_count.assert_called_once()
        mock_llm_search_service.smart_search_advanced.assert_called_once()

    def test_task_status_uses_dependency_injection(
        self, app, mock_api_state
    ):
        """測試任務狀態端點使用依賴注入"""
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        
        client = TestClient(app)
        
        # 模擬 AsyncResult
        with patch('src.presentation.api.search_routes.AsyncResult') as mock_async_result:
            mock_result = Mock()
            mock_result.status = "SUCCESS"
            mock_result.result = {"data": "test"}
            mock_result.ready.return_value = True
            mock_result.successful.return_value = True
            mock_result.failed.return_value = False
            mock_async_result.return_value = mock_result
            
            # 調用端點
            response = client.get("/api/task/status/test-task-id")
            
            # 驗證響應
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "SUCCESS"
            
            # 驗證依賴注入被調用
            mock_api_state.increment_request_count.assert_called_once()


class TestUIRoutesRefactoring:
    """測試 UI 路由重構"""

    def test_dashboard_uses_dependency_injection(
        self, app, mock_api_state, mock_staging_service, mock_processing_service
    ):
        """測試儀表板端點使用依賴注入"""
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        app.dependency_overrides[get_staging_service] = lambda: mock_staging_service
        app.dependency_overrides[get_processing_service] = lambda: mock_processing_service
        
        client = TestClient(app)
        
        # 調用端點
        response = client.get("/dashboard")
        
        # 驗證響應
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # 驗證依賴注入被調用
        mock_api_state.increment_request_count.assert_called_once()
        mock_staging_service.get_service_statistics.assert_called_once()
        mock_processing_service.list_tasks.assert_called_once()

    def test_dashboard_handles_service_unavailable(
        self, app, mock_api_state
    ):
        """測試儀表板處理服務不可用的情況"""
        # 設置依賴覆蓋（服務返回 None）
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        app.dependency_overrides[get_staging_service] = lambda: None
        app.dependency_overrides[get_processing_service] = lambda: None
        
        client = TestClient(app)
        
        # 調用端點
        response = client.get("/dashboard")
        
        # 驗證響應
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # 驗證依賴注入被調用
        mock_api_state.increment_request_count.assert_called_once()

    def test_simple_test_endpoint_uses_dependency_injection(
        self, app, mock_api_state
    ):
        """測試簡單測試端點使用依賴注入"""
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        
        client = TestClient(app)
        
        # 調用端點
        response = client.post("/api/test/simple")
        
        # 驗證響應
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["message"] == "測試端點正常"
        
        # 驗證依賴注入被調用
        mock_api_state.increment_request_count.assert_called_once()


class TestErrorHandling:
    """測試錯誤處理"""

    def test_search_endpoint_error_handling(
        self, app, mock_api_state, mock_product_search_service
    ):
        """測試搜尋端點錯誤處理"""
        # 設置服務拋出異常
        mock_product_search_service.get_task_status.side_effect = Exception("Service error")
        
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service
        
        client = TestClient(app)
        
        # 調用端點
        response = client.get("/api/search/task/test-task-id")
        
        # 驗證錯誤響應
        assert response.status_code == 500
        
        # 驗證錯誤計數被調用
        mock_api_state.increment_error_count.assert_called_once()

    def test_dashboard_error_handling(
        self, app, mock_api_state
    ):
        """測試儀表板錯誤處理"""
        # 設置 API 狀態拋出異常
        mock_api_state.increment_request_count.side_effect = Exception("State error")
        
        # 設置依賴覆蓋
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
        app.dependency_overrides[get_staging_service] = lambda: None
        app.dependency_overrides[get_processing_service] = lambda: None
        
        client = TestClient(app)
        
        # 調用端點
        response = client.get("/dashboard")
        
        # 驗證錯誤響應
        assert response.status_code == 500
        assert "text/html" in response.headers["content-type"]
