"""
GTK 解析器簡化測試
專注於核心功能測試，遵循 TDD
"""

import pytest
from datetime import datetime

from backend.email.models.email_models import EmailData
from backend.email.parsers.gtk_parser import GTKParser
from backend.email.parsers.base_parser import ParsingContext


class TestGTKParserSimple:
    """GTK 解析器簡化測試"""

    def setup_method(self):
        """每個測試方法前的設置"""
        self.parser = GTKParser()

    def test_gtk_parser_initialization(self):
        """測試 GTK 解析器初始化"""
        assert self.parser.vendor_name == "GTK"
        assert self.parser.vendor_code == "GTK"
        assert self.parser.get_confidence_threshold() == 0.8
        assert "ft hold" in self.parser.supported_patterns
        assert "ft lot" in self.parser.supported_patterns

    def test_identify_vendor_ft_hold(self):
        """測試識別包含 'ft hold' 的郵件"""
        email = EmailData(
            message_id="test-001",
            subject="GTK FT HOLD MO:F123456 LOT:ABC123 YIELD:95.5%",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "ft hold" in result.matching_patterns
        assert result.vendor_code == "GTK"

    def test_identify_vendor_ft_lot(self):
        """測試識別包含 'ft lot' 的郵件"""
        email = EmailData(
            message_id="test-msg",
            subject="GTK FT LOT processing MO:F123456 LOT:XYZ789",
            sender="<EMAIL>", 
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "ft lot" in result.matching_patterns

    def test_cannot_identify_non_gtk_email(self):
        """測試不能識別非 GTK 格式的郵件"""
        email = EmailData(
            message_id="test-msg",
            subject="ETD ANF processing data",
            sender="<EMAIL>",
            body="Test email body", 
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is False
        assert result.confidence_score < 0.8

    def test_extract_keyword_value_basic(self):
        """測試基本關鍵字提取功能"""
        subject = "GTK FT HOLD MO:F123456 LOT:ABC123 YIELD:95.5%"
        
        mo_value = self.parser.extract_keyword_value("MO", subject)
        assert mo_value == "F123456"
        
        lot_value = self.parser.extract_keyword_value("LOT", subject)
        assert lot_value == "ABC123"
        
        yield_value = self.parser.extract_keyword_value("YIELD", subject)
        assert yield_value == "95.5%"

    def test_extract_keyword_value_not_found(self):
        """測試找不到關鍵字時的處理"""
        subject = "GTK FT HOLD processing data"
        
        missing_value = self.parser.extract_keyword_value("MISSING", subject)
        assert missing_value == "?"

    def test_parse_email_complete(self):
        """測試完整的郵件解析"""
        email = EmailData(
            message_id="test-msg",
            subject="GTK FT HOLD MO:F123456 LOT:ABC123 YIELD:95.5%",
            sender="<EMAIL>",
            body="BIN1: 1000 units\nTotal processed: 1050 units",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="GTK"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "GTK"
        assert result.mo_number == "F123456"
        assert result.lot_number == "ABC123"
        assert result.extracted_data["yield_value"] == "95.5%"
        assert result.is_success is True

    def test_find_bin1_line(self):
        """測試 BIN1 資料提取"""
        email = EmailData(
            message_id="test-msg",
            subject="GTK FT HOLD MO:F123456",
            sender="<EMAIL>",
            body="Processing status:\nBIN1: 950 passed units\nBIN2: 50 failed units",
            received_time=datetime.now()
        )
        
        bin1_line = self.parser.find_bin1_line(email)
        assert bin1_line == "BIN1: 950 passed units"

    def test_case_insensitive_parsing(self):
        """測試大小寫不敏感的解析"""
        email = EmailData(
            message_id="test-msg",
            subject="gtk Ft Hold mo:f123456 lot:abc123",  # 小寫
            sender="<EMAIL>",
            body="Test body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8