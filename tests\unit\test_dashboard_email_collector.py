"""
郵件監控收集器單元測試

測試 DashboardEmailCollector 的各項功能：
1. 佇列狀態指標收集
2. 處理效能指標收集
3. 廠商分組指標收集
4. code_comparison 任務監控
5. 健康檢查功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from backend.monitoring.collectors.dashboard_email_collector import (
    DashboardEmailCollector,
    get_email_collector,
    collect_email_metrics
)
from backend.monitoring.models.dashboard_metrics_models import EmailMetrics


class TestDashboardEmailCollector:
    """郵件監控收集器測試類"""
    
    def setup_method(self):
        """測試前設置"""
        self.collector = DashboardEmailCollector()
    
    @pytest.mark.asyncio
    async def test_collect_metrics_success(self):
        """測試成功收集指標"""
        # Mock 各個收集方法
        with patch.object(self.collector, '_collect_queue_metrics') as mock_queue, \
             patch.object(self.collector, '_collect_performance_metrics') as mock_perf, \
             patch.object(self.collector, '_collect_vendor_metrics') as mock_vendor, \
             patch.object(self.collector, '_collect_code_comparison_metrics') as mock_code:
            
            # 設置 mock 返回值
            mock_queue.return_value = {
                'pending_count': 10,
                'processing_count': 5,
                'completed_count': 100,
                'failed_count': 2
            }
            
            mock_perf.return_value = {
                'avg_processing_time_seconds': 45.5,
                'throughput_per_hour': 25.0
            }
            
            mock_vendor.return_value = {
                'vendor_queue_counts': {'GTK': 3, 'JCET': 2},
                'vendor_success_rates': {'GTK': 0.95, 'JCET': 0.88}
            }
            
            mock_code.return_value = {
                'code_comparison_active': 2,
                'code_comparison_pending': 3,
                'code_comparison_avg_duration': 120.5
            }
            
            # 執行測試
            metrics = await self.collector.collect_metrics()
            
            # 驗證結果
            assert isinstance(metrics, EmailMetrics)
            assert metrics.pending_count == 10
            assert metrics.processing_count == 5
            assert metrics.completed_count == 100
            assert metrics.failed_count == 2
            assert metrics.avg_processing_time_seconds == 45.5
            assert metrics.throughput_per_hour == 25.0
            assert metrics.vendor_queue_counts == {'GTK': 3, 'JCET': 2}
            assert metrics.vendor_success_rates == {'GTK': 0.95, 'JCET': 0.88}
            assert metrics.code_comparison_active == 2
            assert metrics.code_comparison_pending == 3
            assert metrics.code_comparison_avg_duration == 120.5
    
    @pytest.mark.asyncio
    async def test_collect_metrics_error_handling(self):
        """測試錯誤處理"""
        # Mock 拋出異常
        with patch.object(self.collector, '_collect_queue_metrics', side_effect=Exception("Database error")):
            metrics = await self.collector.collect_metrics()
            
            # 應該返回預設值
            assert isinstance(metrics, EmailMetrics)
            assert metrics.pending_count == 0
            assert metrics.processing_count == 0
            assert metrics.completed_count == 0
            assert metrics.failed_count == 0
    
    @pytest.mark.asyncio
    async def test_collect_queue_metrics(self):
        """測試佇列狀態指標收集"""
        # Mock 資料庫會話
        mock_session = MagicMock()
        
        # 創建更詳細的 mock 設置來處理複雜的查詢
        # 為不同的查詢設置不同的返回值
        def mock_query_side_effect(*args, **kwargs):
            mock_query = MagicMock()
            mock_query.filter.return_value = mock_query
            mock_query.join.return_value = mock_query
            mock_query.distinct.return_value = mock_query
            mock_query.exists.return_value = MagicMock()
            return mock_query
        
        mock_session.query.side_effect = mock_query_side_effect
        
        # 設置不同查詢的計數結果
        count_results = [10, 5, 100, 2]  # pending, processing, completed, failed
        count_index = 0
        
        def mock_count():
            nonlocal count_index
            if count_index < len(count_results):
                result = count_results[count_index]
                count_index += 1
                return result
            return 0
        
        # 為所有可能的查詢鏈設置 count 方法
        mock_session.query.return_value.filter.return_value.count = mock_count
        mock_session.query.return_value.join.return_value.filter.return_value.distinct.return_value.count = mock_count
        mock_session.query.return_value.filter.return_value.filter.return_value.count = mock_count
        
        with patch.object(self.collector.email_db, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = await self.collector._collect_queue_metrics()
            
            # 由於 mock 的複雜性，我們只驗證結果的結構和類型
            assert 'pending_count' in result
            assert 'processing_count' in result
            assert 'completed_count' in result
            assert 'failed_count' in result
            assert isinstance(result['pending_count'], int)
            assert isinstance(result['processing_count'], int)
            assert isinstance(result['completed_count'], int)
            assert isinstance(result['failed_count'], int)
    
    @pytest.mark.asyncio
    async def test_collect_performance_metrics(self):
        """測試處理效能指標收集"""
        # Mock 資料庫會話和查詢結果
        mock_session = MagicMock()
        
        # 模擬已完成的處理記錄
        mock_process1 = MagicMock()
        mock_process1.started_at = datetime.now() - timedelta(minutes=5)
        mock_process1.completed_at = datetime.now()
        
        mock_process2 = MagicMock()
        mock_process2.started_at = datetime.now() - timedelta(minutes=3)
        mock_process2.completed_at = datetime.now()
        
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_process1, mock_process2]
        mock_session.query.return_value.filter.return_value.count.return_value = 15
        
        with patch.object(self.collector.email_db, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = await self.collector._collect_performance_metrics()
            
            assert 'avg_processing_time_seconds' in result
            assert 'throughput_per_hour' in result
            assert result['throughput_per_hour'] == 15.0
    
    @pytest.mark.asyncio
    async def test_collect_vendor_metrics(self):
        """測試廠商分組指標收集"""
        mock_session = MagicMock()
        
        # Mock 不同的查詢結果
        mock_session.query.return_value.filter.return_value.count.side_effect = [
            5,   # GTK vendor_code 郵件數
            0,   # GTK 關鍵字郵件數（不會被調用）
            3,   # GTK 待處理郵件數
            15,  # GTK 已完成郵件數
            2,   # JCET vendor_code 郵件數
            0,   # JCET 關鍵字郵件數（不會被調用）
            1,   # JCET 待處理郵件數
            8,   # JCET 已完成郵件數
        ]
        
        with patch.object(self.collector.email_db, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = await self.collector._collect_vendor_metrics()
            
            assert 'vendor_queue_counts' in result
            assert 'vendor_success_rates' in result
            
            # 檢查是否有廠商資料（具體值可能因 mock 設置而異）
            assert isinstance(result['vendor_queue_counts'], dict)
            assert isinstance(result['vendor_success_rates'], dict)
    
    @pytest.mark.asyncio
    async def test_collect_code_comparison_metrics(self):
        """測試 code_comparison 任務監控"""
        # Mock 任務管理器
        mock_tasks = [
            {'task_id': '1', 'task_type': 'code_comparison', 'status': 'running'},
            {'task_id': '2', 'task_type': 'code_comparison', 'status': 'pending'},
            {'task_id': '3', 'task_type': 'code_comparison', 'status': 'completed'},
            {'task_id': '4', 'task_type': 'email_processing', 'status': 'running'},  # 不相關任務
        ]
        
        mock_task_detail = {
            'task_id': '3',
            'actual_duration': 150.5
        }
        
        with patch.object(self.collector.task_manager, 'list_tasks', return_value=mock_tasks), \
             patch.object(self.collector.task_manager, 'get_task_status', return_value=mock_task_detail):
            
            result = await self.collector._collect_code_comparison_metrics()
            
            assert result['code_comparison_active'] == 1
            assert result['code_comparison_pending'] == 1
            assert result['code_comparison_avg_duration'] == 150.5
    
    def test_get_health_status_healthy(self):
        """測試健康狀態檢查 - 健康狀態"""
        mock_session = MagicMock()
        mock_session.query.return_value.scalar.return_value = 100
        
        mock_task_stats = {'total_tasks': 50}
        
        with patch.object(self.collector.email_db, 'get_session') as mock_get_session, \
             patch.object(self.collector.task_manager, 'get_statistics', return_value=mock_task_stats):
            
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = self.collector.get_health_status()
            
            assert result['status'] == 'healthy'
            assert result['database_connection'] == 'ok'
            assert result['task_manager_connection'] == 'ok'
            assert result['total_emails'] == 100
            assert result['total_tasks'] == 50
    
    def test_get_health_status_unhealthy(self):
        """測試健康狀態檢查 - 不健康狀態"""
        with patch.object(self.collector.email_db, 'get_session', side_effect=Exception("Database connection failed")):
            result = self.collector.get_health_status()
            
            assert result['status'] == 'unhealthy'
            assert 'error' in result
    
    def test_get_email_collector_singleton(self):
        """測試單例模式"""
        collector1 = get_email_collector()
        collector2 = get_email_collector()
        
        assert collector1 is collector2
        assert isinstance(collector1, DashboardEmailCollector)
    
    @pytest.mark.asyncio
    async def test_collect_email_metrics_convenience_function(self):
        """測試便利函數"""
        with patch('src.dashboard_monitoring.collectors.dashboard_email_collector.get_email_collector') as mock_get:
            mock_collector = MagicMock()
            mock_metrics = EmailMetrics(
                pending_count=5,
                processing_count=2,
                completed_count=50,
                failed_count=1,
                avg_processing_time_seconds=30.0,
                throughput_per_hour=20.0,
                vendor_queue_counts={'GTK': 2},
                vendor_success_rates={'GTK': 0.9},
                code_comparison_active=1,
                code_comparison_pending=2,
                code_comparison_avg_duration=90.0
            )
            
            # 由於 collect_metrics 是 async 方法，需要使用 AsyncMock
            from unittest.mock import AsyncMock
            mock_collector.collect_metrics = AsyncMock(return_value=mock_metrics)
            mock_get.return_value = mock_collector
            
            result = await collect_email_metrics()
            
            assert isinstance(result, EmailMetrics)
            assert result.pending_count == 5
            assert result.processing_count == 2
    
    def test_vendor_keywords_mapping(self):
        """測試廠商關鍵字映射"""
        assert 'ETD' in self.collector.vendor_keywords
        assert 'anf' in self.collector.vendor_keywords['ETD']
        
        assert 'GTK' in self.collector.vendor_keywords
        assert 'ft hold' in self.collector.vendor_keywords['GTK']
        assert 'ft lot' in self.collector.vendor_keywords['GTK']
        
        assert 'JCET' in self.collector.vendor_keywords
        assert 'jcet' in self.collector.vendor_keywords['JCET']
    
    @pytest.mark.asyncio
    async def test_error_handling_in_vendor_metrics(self):
        """測試廠商指標收集中的錯誤處理"""
        mock_session = MagicMock()
        mock_session.query.side_effect = Exception("Query failed")
        
        with patch.object(self.collector.email_db, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = await self.collector._collect_vendor_metrics()
            
            # 應該返回空的字典而不是拋出異常
            assert result['vendor_queue_counts'] == {}
            assert result['vendor_success_rates'] == {}
    
    @pytest.mark.asyncio
    async def test_error_handling_in_code_comparison_metrics(self):
        """測試 code_comparison 指標收集中的錯誤處理"""
        with patch.object(self.collector.task_manager, 'list_tasks', side_effect=Exception("Task manager error")):
            result = await self.collector._collect_code_comparison_metrics()
            
            # 應該返回預設值而不是拋出異常
            assert result['code_comparison_active'] == 0
            assert result['code_comparison_pending'] == 0
            assert result['code_comparison_avg_duration'] == 0.0


class TestEmailCollectorIntegration:
    """郵件收集器整合測試"""
    
    @pytest.mark.asyncio
    async def test_full_metrics_collection_flow(self):
        """測試完整的指標收集流程"""
        collector = DashboardEmailCollector()
        
        # 這個測試需要真實的資料庫連接，所以我們跳過或使用 mock
        with patch.object(collector, '_collect_queue_metrics') as mock_queue, \
             patch.object(collector, '_collect_performance_metrics') as mock_perf, \
             patch.object(collector, '_collect_vendor_metrics') as mock_vendor, \
             patch.object(collector, '_collect_code_comparison_metrics') as mock_code:
            
            # 設置真實的返回值
            mock_queue.return_value = {
                'pending_count': 15,
                'processing_count': 8,
                'completed_count': 200,
                'failed_count': 5
            }
            
            mock_perf.return_value = {
                'avg_processing_time_seconds': 60.0,
                'throughput_per_hour': 30.0
            }
            
            mock_vendor.return_value = {
                'vendor_queue_counts': {'GTK': 5, 'JCET': 3, 'ETD': 2},
                'vendor_success_rates': {'GTK': 0.92, 'JCET': 0.85, 'ETD': 0.90}
            }
            
            mock_code.return_value = {
                'code_comparison_active': 3,
                'code_comparison_pending': 5,
                'code_comparison_avg_duration': 180.0
            }
            
            # 執行完整的收集流程
            metrics = await collector.collect_metrics()
            
            # 驗證所有指標都被正確收集
            assert metrics.pending_count == 15
            assert metrics.processing_count == 8
            assert metrics.completed_count == 200
            assert metrics.failed_count == 5
            assert metrics.avg_processing_time_seconds == 60.0
            assert metrics.throughput_per_hour == 30.0
            assert len(metrics.vendor_queue_counts) == 3
            assert len(metrics.vendor_success_rates) == 3
            assert metrics.code_comparison_active == 3
            assert metrics.code_comparison_pending == 5
            assert metrics.code_comparison_avg_duration == 180.0
            
            # 驗證所有方法都被調用
            mock_queue.assert_called_once()
            mock_perf.assert_called_once()
            mock_vendor.assert_called_once()
            mock_code.assert_called_once()


if __name__ == "__main__":
    # 運行測試
    pytest.main([__file__, "-v"])