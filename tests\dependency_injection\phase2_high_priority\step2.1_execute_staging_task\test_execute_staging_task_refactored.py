"""測試重構後的 execute_staging_task 端點

測試 /api/staging/execute/{task_id} 端點從直接調用改為依賴注入模式後的行為
"""

import pytest
import uuid
from unittest.mock import Mock, AsyncMock
from fastapi import FastAPI, HTTPException
from fastapi.testclient import TestClient

# 導入測試基礎設施
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from tests.dependency_injection.shared.test_infrastructure import (
    TestClientFactory, MockServiceFactory, DependencyOverrideManager, TestDataFactory
)
from tests.dependency_injection.shared.mock_services import MockFileStagingService, MockAPIState
from tests.dependency_injection.shared.constants import TEST_TASK_IDS, TEST_PRODUCT_NAMES

# 導入依賴注入函數
from frontend.shared.dependencies.dependencies import require_staging_service, get_api_state


class TestExecuteStagingTaskRefactored:
    """測試重構後的 execute_staging_task 端點"""
    
    def setup_method(self):
        """設置測試環境"""
        # 創建 Mock 服務
        self.mock_staging_service = MockServiceFactory.create_staging_service(
            service_id="test_staging_service",
            is_healthy=True
        )
        self.mock_api_state = MockAPIState()
        
        # 創建測試應用
        self.app = FastAPI()
        
        # 設置依賴注入覆蓋
        self.dependency_manager = DependencyOverrideManager(self.app)
        self.dependency_manager.override_staging_service(self.mock_staging_service)
        self.dependency_manager.override_api_state(self.mock_api_state)
        
        # 添加重構後的路由
        self._add_refactored_route()
        
        # 創建測試客戶端
        self.client = TestClientFactory.create_client(self.app)
    
    def _add_refactored_route(self):
        """添加重構後的路由定義"""
        from fastapi import APIRouter, Depends
        from backend.services.staging import FileStagingService
        from frontend.shared.dependencies.dependencies import APIState
        
        router = APIRouter()
        
        @router.post("/api/staging/execute/{task_id}")
        async def execute_staging_task_refactored(
            task_id: str,
            staging_service: FileStagingService = Depends(require_staging_service),
            api_state: APIState = Depends(get_api_state)
        ):
            """重構後的執行檔案暫存任務端點（使用依賴注入）"""
            try:
                # 驗證 task_id 格式
                try:
                    uuid.UUID(task_id)
                except ValueError:
                    raise HTTPException(status_code=400, detail="無效的任務ID格式")
                
                # 檢查任務是否存在
                task = staging_service.get_task_status(task_id)
                if task is None:
                    raise HTTPException(status_code=404, detail=f"找不到任務: {task_id}")
                
                # 執行暫存任務
                result = await staging_service.execute_staging_task(task_id)
                
                if result.success:
                    return {
                        "success": True,
                        "task_id": task_id,
                        "message": "暫存任務執行成功",
                        "staging_directory": str(result.staging_directory),
                        "staged_files": [str(f) for f in result.staged_files],
                        "total_files": result.total_files,
                        "total_size": result.total_size,
                        "staging_duration": result.staging_duration,
                        "average_speed": result.average_speed,
                        "integrity_check_passed": result.integrity_check_passed
                    }
                else:
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        status_code=422,
                        content={
                            "success": False,
                            "task_id": task_id,
                            "message": "暫存任務執行失敗",
                            "error_message": result.error_message,
                            "failed_files": result.failed_files
                        }
                    )
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"執行暫存任務時發生錯誤: {str(e)}")
        
        self.app.include_router(router)
    
    def test_execute_staging_task_success(self):
        """測試成功執行暫存任務"""
        # 準備測試數據
        task_id = "550e8400-e29b-41d4-a716-************"  # 有效的 UUID 格式
        
        # 設置 Mock 行為 - 任務存在
        # 先創建任務，然後手動設置任務ID
        self.mock_staging_service.create_staging_task(
            product_name="TestProduct",
            source_files=["/test/file1.txt", "/test/file2.csv"]
        )
        # 手動設置指定的任務ID
        self.mock_staging_service.tasks[task_id] = {
            "id": task_id,
            "product_name": "TestProduct",
            "source_files": ["/test/file1.txt", "/test/file2.csv"],
            "status": "pending"
        }
        
        # 設置執行結果
        mock_result = Mock()
        mock_result.success = True
        mock_result.staging_directory = "/tmp/staging/test_product"
        mock_result.staged_files = ["/tmp/staging/test_product/file1.txt", "/tmp/staging/test_product/file2.csv"]
        mock_result.total_files = 2
        mock_result.total_size = 1024
        mock_result.staging_duration = 5.2
        mock_result.average_speed = 196.9
        mock_result.integrity_check_passed = True
        
        self.mock_staging_service.execute_staging_task = AsyncMock(return_value=mock_result)
        
        # 執行請求
        response = self.client.post(f"/api/staging/execute/{task_id}")

        # 驗證響應
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content}")
        print(f"Response text: {response.text}")

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert data["task_id"] == task_id
        assert data["message"] == "暫存任務執行成功"
        assert data["staging_directory"] == "/tmp/staging/test_product"
        assert len(data["staged_files"]) == 2
        assert data["total_files"] == 2
        assert data["total_size"] == 1024
        assert data["staging_duration"] == 5.2
        assert data["average_speed"] == 196.9
        assert data["integrity_check_passed"] is True
        
        # 驗證服務方法被調用
        self.mock_staging_service.execute_staging_task.assert_called_once_with(task_id)
    
    def test_execute_staging_task_invalid_uuid(self):
        """測試無效的任務ID格式"""
        invalid_task_id = "invalid-uuid-format"

        # 執行請求
        response = self.client.post(f"/api/staging/execute/{invalid_task_id}")

        # 驗證響應
        assert response.status_code == 400
        data = response.json()
        assert data["detail"] == "無效的任務ID格式"

    def test_execute_staging_task_not_found(self):
        """測試任務不存在的情況"""
        task_id = "550e8400-e29b-41d4-a716-************"
        
        # 設置 Mock 行為 - 任務不存在
        self.mock_staging_service.get_task_status = Mock(return_value=None)
        
        # 執行請求
        response = self.client.post(f"/api/staging/execute/{task_id}")
        
        # 驗證響應
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == f"找不到任務: {task_id}"
    
    def test_execute_staging_task_execution_failure(self):
        """測試暫存任務執行失敗"""
        task_id = "550e8400-e29b-41d4-a716-************"

        # 設置 Mock 行為 - 任務存在
        self.mock_staging_service.tasks[task_id] = {
            "id": task_id,
            "product_name": "TestProduct",
            "source_files": ["/test/file1.txt"],
            "status": "pending"
        }
        
        # 設置執行失敗結果
        mock_result = Mock()
        mock_result.success = False
        mock_result.error_message = "磁碟空間不足"
        mock_result.failed_files = ["/test/file1.txt"]
        
        self.mock_staging_service.execute_staging_task = AsyncMock(return_value=mock_result)
        
        # 執行請求
        response = self.client.post(f"/api/staging/execute/{task_id}")
        
        # 驗證響應
        assert response.status_code == 422
        data = response.json()
        
        assert data["success"] is False
        assert data["task_id"] == task_id
        assert data["message"] == "暫存任務執行失敗"
        assert data["error_message"] == "磁碟空間不足"
        assert data["failed_files"] == ["/test/file1.txt"]
    
    def test_execute_staging_task_service_exception(self):
        """測試服務拋出異常的情況"""
        task_id = "550e8400-e29b-41d4-a716-446655440003"

        # 設置 Mock 行為 - 任務存在
        self.mock_staging_service.tasks[task_id] = {
            "id": task_id,
            "product_name": "TestProduct",
            "source_files": ["/test/file1.txt"],
            "status": "pending"
        }
        
        # 設置服務拋出異常
        self.mock_staging_service.execute_staging_task = AsyncMock(
            side_effect=Exception("網路連接失敗")
        )
        
        # 執行請求
        response = self.client.post(f"/api/staging/execute/{task_id}")
        
        # 驗證響應
        assert response.status_code == 500
        data = response.json()
        assert "執行暫存任務時發生錯誤: 網路連接失敗" in data["detail"]
    
    def test_dependency_injection_working(self):
        """測試依賴注入是否正常工作"""
        task_id = "550e8400-e29b-41d4-a716-446655440004"

        # 設置 Mock 行為
        self.mock_staging_service.tasks[task_id] = {
            "id": task_id,
            "product_name": "TestProduct",
            "source_files": ["/test/file1.txt"],
            "status": "pending"
        }
        
        mock_result = Mock()
        mock_result.success = True
        mock_result.staging_directory = "/tmp/staging/test"
        mock_result.staged_files = ["/tmp/staging/test/file1.txt"]
        mock_result.total_files = 1
        mock_result.total_size = 512
        mock_result.staging_duration = 2.1
        mock_result.average_speed = 243.8
        mock_result.integrity_check_passed = True
        
        self.mock_staging_service.execute_staging_task = AsyncMock(return_value=mock_result)
        
        # 執行請求
        response = self.client.post(f"/api/staging/execute/{task_id}")
        
        # 驗證響應成功
        assert response.status_code == 200
        
        # 驗證依賴注入的服務被正確使用
        assert self.mock_staging_service.get_task_status.called
        self.mock_staging_service.execute_staging_task.assert_called_once_with(task_id)
        
        # 驗證沒有直接調用 get_file_staging_service()
        # 這證明我們使用的是依賴注入而不是直接服務調用
    
    def teardown_method(self):
        """清理測試環境"""
        self.dependency_manager.reset_all_overrides()
