"""錯誤恢復機制測試
測試重試、熔斷器等錯誤恢復策略
"""

import pytest
import sys
import os
import time
from unittest.mock import Mock, patch, call

# 設置導入路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))


class TestRetryMechanism:
    """測試重試機制"""
    
    def test_successful_retry_after_failures(self):
        """測試在失敗後成功重試"""
        print("🧪 測試失敗後成功重試...")
        
        from frontend.api.error_handling import with_retry
        
        call_count = 0
        def flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception(f"暫時失敗 #{call_count}")
            return f"成功，共嘗試 {call_count} 次"
        
        result = with_retry(
            func=flaky_function,
            max_retries=3,
            backoff_factor=0.01  # 快速重試用於測試
        )
        
        assert "成功" in result
        assert call_count == 3
        
        print("✅ 失敗後成功重試測試通過")
    
    def test_max_retries_exceeded(self):
        """測試超過最大重試次數"""
        print("🧪 測試超過最大重試次數...")
        
        from frontend.api.error_handling import with_retry
        
        def always_fail():
            raise Exception("總是失敗")
        
        with pytest.raises(Exception) as exc_info:
            with_retry(
                func=always_fail,
                max_retries=2,
                backoff_factor=0.01
            )
        
        assert "總是失敗" in str(exc_info.value)
        
        print("✅ 超過最大重試次數測試通過")
    
    def test_no_retry_needed(self):
        """測試不需要重試的成功情況"""
        print("🧪 測試不需要重試的成功情況...")
        
        from frontend.api.error_handling import with_retry
        
        call_count = 0
        def immediate_success():
            nonlocal call_count
            call_count += 1
            return "立即成功"
        
        result = with_retry(
            func=immediate_success,
            max_retries=3,
            backoff_factor=0.01
        )
        
        assert result == "立即成功"
        assert call_count == 1
        
        print("✅ 不需要重試的成功情況測試通過")


class TestCircuitBreaker:
    """測試熔斷器模式"""
    
    def test_circuit_breaker_closed_state(self):
        """測試熔斷器關閉狀態"""
        print("🧪 測試熔斷器關閉狀態...")
        
        from frontend.api.error_handling import CircuitBreaker
        
        circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=1.0)
        
        def successful_function():
            return "成功"
        
        # 在關閉狀態下，應該正常調用函數
        result = circuit_breaker.call(successful_function)
        assert result == "成功"
        assert circuit_breaker.state == "CLOSED"
        
        print("✅ 熔斷器關閉狀態測試通過")
    
    def test_circuit_breaker_open_state(self):
        """測試熔斷器開啟狀態"""
        print("🧪 測試熔斷器開啟狀態...")
        
        from frontend.api.error_handling import CircuitBreaker
        
        circuit_breaker = CircuitBreaker(failure_threshold=2, recovery_timeout=60.0)
        
        def failing_function():
            raise Exception("服務失敗")
        
        # 造成足夠的失敗來開啟熔斷器
        for i in range(2):
            with pytest.raises(Exception):
                circuit_breaker.call(failing_function)
        
        # 確認熔斷器已開啟
        assert circuit_breaker.state == "OPEN"
        
        # 下一次調用應該直接失敗，不調用函數
        with pytest.raises(Exception) as exc_info:
            circuit_breaker.call(failing_function)
        
        assert "熔斷器開啟" in str(exc_info.value) or "circuit breaker" in str(exc_info.value).lower()
        
        print("✅ 熔斷器開啟狀態測試通過")
    
    def test_circuit_breaker_half_open_state(self):
        """測試熔斷器半開狀態"""
        print("🧪 測試熔斷器半開狀態...")
        
        from frontend.api.error_handling import CircuitBreaker
        
        circuit_breaker = CircuitBreaker(failure_threshold=1, recovery_timeout=0.1)
        
        def failing_function():
            raise Exception("服務失敗")
        
        def successful_function():
            return "恢復成功"
        
        # 造成失敗開啟熔斷器
        with pytest.raises(Exception):
            circuit_breaker.call(failing_function)
        
        assert circuit_breaker.state == "OPEN"
        
        # 等待恢復超時
        time.sleep(0.2)
        
        # 下一次調用應該進入半開狀態並成功
        result = circuit_breaker.call(successful_function)
        assert result == "恢復成功"
        assert circuit_breaker.state == "CLOSED"
        
        print("✅ 熔斷器半開狀態測試通過")


class TestErrorRecoveryStrategies:
    """測試錯誤恢復策略"""
    
    def test_retry_strategy(self):
        """測試重試策略"""
        print("🧪 測試重試策略...")
        
        from frontend.api.error_handling.errors import OperationTimeoutError, ErrorRecoveryStrategy
        
        error = OperationTimeoutError(operation="數據處理", timeout_seconds=30)
        
        assert error.recovery_strategy == ErrorRecoveryStrategy.RETRY
        
        print("✅ 重試策略測試通過")
    
    def test_circuit_breaker_strategy(self):
        """測試熔斷器策略"""
        print("🧪 測試熔斷器策略...")
        
        from frontend.api.error_handling.errors import ExternalServiceError, ErrorRecoveryStrategy
        
        error = ExternalServiceError(
            service_name="第三方API",
            service_error="連接超時"
        )
        
        assert error.recovery_strategy == ErrorRecoveryStrategy.CIRCUIT_BREAKER
        
        print("✅ 熔斷器策略測試通過")
    
    def test_fail_fast_strategy(self):
        """測試快速失敗策略"""
        print("🧪 測試快速失敗策略...")
        
        from frontend.api.error_handling.errors import ServiceInitializationError, ErrorRecoveryStrategy
        
        error = ServiceInitializationError(
            service_name="配置服務",
            initialization_error="配置檔案格式錯誤"
        )
        
        assert error.recovery_strategy == ErrorRecoveryStrategy.FAIL_FAST
        
        print("✅ 快速失敗策略測試通過")


def run_error_recovery_tests():
    """運行所有錯誤恢復測試"""
    print("🎯 錯誤恢復機制測試")
    print("=" * 50)
    
    test_classes = [
        TestRetryMechanism,
        TestCircuitBreaker,
        TestErrorRecoveryStrategies
    ]
    
    passed = 0
    failed = 0
    
    for test_class in test_classes:
        print(f"\n📋 運行 {test_class.__name__} 測試...")
        
        instance = test_class()
        methods = [method for method in dir(instance) if method.startswith('test_')]
        
        for method_name in methods:
            try:
                method = getattr(instance, method_name)
                method()
                passed += 1
            except Exception as e:
                print(f"❌ {method_name} 失敗: {e}")
                failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed} 通過, {failed} 失敗")
    
    return failed == 0


if __name__ == "__main__":
    success = run_error_recovery_tests()
    exit(0 if success else 1)