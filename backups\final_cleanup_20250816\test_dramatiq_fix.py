#!/usr/bin/env python3
"""
Dramatiq 修復驗證測試

🎯 功能：
  - 驗證 Dramatiq 任務模組可以正確導入
  - 測試健康檢查任務的提交和執行
  - 確認 Redis 連接和任務隊列正常工作

🔧 使用方式：
  python test_dramatiq_fix.py
"""

import os
import sys
import time
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_dramatiq_import():
    """測試 Dramatiq 任務模組導入"""
    print("🔍 測試 1: Dramatiq 任務模組導入")
    
    try:
        # 測試根目錄 dramatiq_tasks 模組導入
        import dramatiq_tasks
        print("✅ dramatiq_tasks 模組導入成功")
        
        # 檢查 broker
        broker = dramatiq_tasks.broker
        print(f"✅ Broker 類型: {type(broker).__name__}")
        
        # 檢查任務
        tasks = [
            'process_complete_eqc_workflow_task',
            'search_product_task',
            'run_csv_summary_task', 
            'run_code_comparison_task',
            'health_check_task',
            'process_vendor_files_task',
            'pipeline_completion_task',
            'extract_archive_task'
        ]
        
        for task_name in tasks:
            if hasattr(dramatiq_tasks, task_name):
                task = getattr(dramatiq_tasks, task_name)
                print(f"✅ 任務 {task_name}: {task.actor_name} (隊列: {task.queue_name})")
            else:
                print(f"❌ 任務 {task_name} 未找到")
                
        return True
        
    except Exception as e:
        print(f"❌ Dramatiq 任務模組導入失敗: {e}")
        return False

def test_redis_connection():
    """測試 Redis 連接"""
    print("\n🔍 測試 2: Redis 連接")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 連接正常")
        
        # 檢查 Redis 信息
        info = r.info()
        print(f"✅ Redis 版本: {info.get('redis_version', 'Unknown')}")
        print(f"✅ 已連接客戶端: {info.get('connected_clients', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 連接失敗: {e}")
        return False

def test_task_submission():
    """測試任務提交（不需要 Worker 運行）"""
    print("\n🔍 測試 3: 任務提交")
    
    try:
        # 導入任務
        from dramatiq_tasks import health_check_task
        
        # 提交健康檢查任務
        print("📤 提交健康檢查任務...")
        message = health_check_task.send()
        
        print(f"✅ 任務提交成功")
        print(f"   - 消息 ID: {message.message_id}")
        print(f"   - 隊列: {message.queue_name}")
        print(f"   - Actor: {message.actor_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任務提交失敗: {e}")
        return False

def test_broker_configuration():
    """測試 Broker 配置"""
    print("\n🔍 測試 4: Broker 配置")
    
    try:
        from dramatiq_config import broker, get_broker, QUEUE_CONFIG
        
        print(f"✅ Broker 實例: {type(broker).__name__}")
        print(f"✅ get_broker() 函數: {type(get_broker()).__name__}")
        
        # 檢查隊列配置
        print("✅ 隊列配置:")
        for queue_name, config in QUEUE_CONFIG.items():
            print(f"   - {queue_name}: 重試{config['max_retries']}次, 時限{config['time_limit']}ms")
            
        return True
        
    except Exception as e:
        print(f"❌ Broker 配置測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 Dramatiq 修復驗證測試開始")
    print("=" * 50)
    
    tests = [
        test_dramatiq_import,
        test_redis_connection, 
        test_task_submission,
        test_broker_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 測試 {test_func.__name__} 發生異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！Dramatiq 修復成功！")
        print("\n📋 下一步:")
        print("1. 啟動 Dramatiq Worker: .\\start_dramatiq.bat")
        print("2. 在另一個終端測試任務執行")
        print("3. 檢查任務執行日誌和結果")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
