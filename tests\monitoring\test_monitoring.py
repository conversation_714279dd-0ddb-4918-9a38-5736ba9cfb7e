#!/usr/bin/env python3
"""
測試監控系統功能
"""

import sys
import asyncio
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.monitoring.collectors.eqc_task_collector import get_eqc_task_collector

async def test_monitoring():
    """測試監控系統"""
    print("🧪 開始監控系統測試")
    
    try:
        collector = get_eqc_task_collector()
        print('✅ EQC 任務收集器初始化成功')
        
        # 測試收集指標
        await collector.collect_eqc_metrics()
        print('✅ 指標收集成功')
        
        # 測試獲取儀表板數據
        dashboard_data = await collector.get_eqc_dashboard_data()
        print(f'✅ 儀表板數據獲取成功，包含 {len(dashboard_data)} 個項目')
        
        # 顯示概覽數據
        overview = dashboard_data.get('overview', {})
        print(f'📊 概覽數據: {overview}')
        
        # 顯示任務數據
        tasks_info = dashboard_data.get('tasks', {})
        print(f'📋 任務數據: 總數 {tasks_info.get("task_count", 0)}, 活躍 {tasks_info.get("active_count", 0)}')
        
        return True
        
    except Exception as e:
        print(f'❌ 監控系統測試失敗: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_monitoring())
    print(f"\n{'✅ 監控系統測試通過' if success else '❌ 監控系統測試失敗'}")
