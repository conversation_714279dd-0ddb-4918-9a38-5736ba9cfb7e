"""
Dramatiq 整合模組單元測試

🎯 測試目標：
  - 驗證 Dramatiq 任務註冊功能
  - 測試任務提交和狀態查詢
  - 確認錯誤處理機制
  - 驗證健康檢查功能

🔧 測試範圍：
  - DramatiqTaskRegistry 類
  - 任務提交函數
  - 狀態檢查功能
  - 異常處理
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

# 測試目標模組
from backend.tasks.dramatiq_integration import (
    DramatiqTaskRegistry,
    get_dramatiq_tasks,
    get_dramatiq_task,
    is_dramatiq_available,
    submit_eqc_workflow_task,
    submit_product_search_task,
    submit_csv_summary_task,
    submit_code_comparison_task,
    health_check,
    get_task_status,
    DramatiqTaskStatus
)


class TestDramatiqTaskRegistry:
    """測試 Dramatiq 任務註冊器"""
    
    def test_registry_initialization(self):
        """測試註冊器初始化"""
        registry = DramatiqTaskRegistry()
        
        assert registry.tasks == {}
        assert registry.initialized == False
    
    @patch('src.tasks.dramatiq_integration.dramatiq_tasks')
    @patch('src.tasks.dramatiq_integration.dramatiq_config')
    def test_registry_initialize_success(self, mock_config, mock_tasks):
        """測試成功初始化"""
        # 模擬任務
        mock_task = Mock()
        mock_tasks.process_complete_eqc_workflow_task = mock_task
        mock_tasks.search_product_task = mock_task
        mock_tasks.run_csv_summary_task = mock_task
        mock_tasks.run_code_comparison_task = mock_task
        mock_tasks.health_check_task = mock_task
        
        registry = DramatiqTaskRegistry()
        registry.initialize()
        
        assert registry.initialized == True
        assert len(registry.tasks) == 5
        assert 'process_complete_eqc_workflow' in registry.tasks
        assert 'search_product' in registry.tasks
        assert 'run_csv_summary' in registry.tasks
        assert 'run_code_comparison' in registry.tasks
        assert 'health_check' in registry.tasks
    
    @patch('src.tasks.dramatiq_integration.dramatiq_tasks')
    def test_registry_initialize_failure(self, mock_tasks):
        """測試初始化失敗"""
        # 模擬導入錯誤
        mock_tasks.side_effect = ImportError("Module not found")
        
        registry = DramatiqTaskRegistry()
        
        with pytest.raises(ImportError):
            registry.initialize()
        
        assert registry.initialized == False
    
    def test_get_task_before_initialization(self):
        """測試初始化前獲取任務"""
        registry = DramatiqTaskRegistry()
        
        with patch.object(registry, 'initialize') as mock_init:
            task = registry.get_task('test_task')
            mock_init.assert_called_once()
    
    def test_get_all_tasks_before_initialization(self):
        """測試初始化前獲取所有任務"""
        registry = DramatiqTaskRegistry()
        
        with patch.object(registry, 'initialize') as mock_init:
            tasks = registry.get_all_tasks()
            mock_init.assert_called_once()
    
    @patch('redis.Redis')
    @patch('dramatiq')
    def test_is_available_success(self, mock_dramatiq, mock_redis):
        """測試可用性檢查成功"""
        mock_redis_instance = Mock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.ping.return_value = True
        
        registry = DramatiqTaskRegistry()
        result = registry.is_available()
        
        assert result == True
        mock_redis_instance.ping.assert_called_once()
    
    @patch('redis.Redis')
    def test_is_available_failure(self, mock_redis):
        """測試可用性檢查失敗"""
        mock_redis_instance = Mock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.ping.side_effect = Exception("Connection failed")
        
        registry = DramatiqTaskRegistry()
        result = registry.is_available()
        
        assert result == False


class TestDramatiqTaskStatus:
    """測試 Dramatiq 任務狀態檢查器"""
    
    @patch('redis.Redis')
    def test_check_task_status_processing(self, mock_redis):
        """測試處理中任務狀態"""
        mock_redis_instance = Mock()
        mock_redis.return_value = mock_redis_instance
        
        # 模擬任務在活躍隊列中
        mock_redis_instance.llen.return_value = 0
        mock_redis_instance.sismember.return_value = True
        
        status = DramatiqTaskStatus.check_task_status("test_task_id")
        
        assert status['status'] == 'PROCESSING'
        assert 'queue' in status
        assert status['message'] == '任務處理中'
    
    @patch('redis.Redis')
    def test_check_task_status_success(self, mock_redis):
        """測試成功完成任務狀態"""
        mock_redis_instance = Mock()
        mock_redis.return_value = mock_redis_instance
        
        # 模擬任務不在活躍隊列但有結果
        mock_redis_instance.llen.return_value = 0
        mock_redis_instance.sismember.return_value = False
        mock_redis_instance.exists.return_value = True
        mock_redis_instance.get.return_value = "task_result"
        
        status = DramatiqTaskStatus.check_task_status("test_task_id")
        
        assert status['status'] == 'SUCCESS'
        assert status['result'] == "task_result"
        assert status['message'] == '任務完成'
    
    @patch('redis.Redis')
    def test_check_task_status_pending(self, mock_redis):
        """測試待處理任務狀態"""
        mock_redis_instance = Mock()
        mock_redis.return_value = mock_redis_instance
        
        # 模擬任務不在任何隊列中
        mock_redis_instance.llen.return_value = 0
        mock_redis_instance.sismember.return_value = False
        mock_redis_instance.exists.return_value = False
        
        status = DramatiqTaskStatus.check_task_status("test_task_id")
        
        assert status['status'] == 'PENDING'
        assert status['message'] == '任務等待中或狀態未知'
    
    @patch('redis.Redis')
    def test_check_task_status_error(self, mock_redis):
        """測試狀態檢查錯誤"""
        mock_redis.side_effect = Exception("Redis connection failed")
        
        status = DramatiqTaskStatus.check_task_status("test_task_id")
        
        assert status['status'] == 'ERROR'
        assert 'error' in status
        assert status['message'] == '無法檢查任務狀態'


class TestDramatiqIntegrationFunctions:
    """測試 Dramatiq 整合函數"""
    
    @patch('src.tasks.dramatiq_integration._task_registry')
    def test_get_dramatiq_tasks(self, mock_registry):
        """測試獲取 Dramatiq 任務"""
        mock_tasks = {'task1': Mock(), 'task2': Mock()}
        mock_registry.get_all_tasks.return_value = mock_tasks
        
        result = get_dramatiq_tasks()
        
        assert result == mock_tasks
        mock_registry.get_all_tasks.assert_called_once()
    
    @patch('src.tasks.dramatiq_integration._task_registry')
    def test_get_dramatiq_task(self, mock_registry):
        """測試獲取指定 Dramatiq 任務"""
        mock_task = Mock()
        mock_registry.get_task.return_value = mock_task
        
        result = get_dramatiq_task('test_task')
        
        assert result == mock_task
        mock_registry.get_task.assert_called_once_with('test_task')
    
    @patch('src.tasks.dramatiq_integration._task_registry')
    def test_is_dramatiq_available(self, mock_registry):
        """測試 Dramatiq 可用性檢查"""
        mock_registry.is_available.return_value = True
        
        result = is_dramatiq_available()
        
        assert result == True
        mock_registry.is_available.assert_called_once()
    
    @patch('src.tasks.dramatiq_integration.get_dramatiq_task')
    def test_submit_eqc_workflow_task(self, mock_get_task):
        """測試提交 EQC 工作流程任務"""
        mock_task = Mock()
        mock_result = Mock()
        mock_task.send.return_value = mock_result
        mock_get_task.return_value = mock_task
        
        result = submit_eqc_workflow_task("test_folder", "test_session", {"option": "value"})
        
        assert result == mock_result
        mock_task.send.assert_called_once_with("test_folder", "test_session", {"option": "value"})
    
    @patch('src.tasks.dramatiq_integration.get_dramatiq_task')
    def test_submit_eqc_workflow_task_not_available(self, mock_get_task):
        """測試 EQC 任務不可用時的錯誤處理"""
        mock_get_task.return_value = None
        
        with pytest.raises(RuntimeError, match="Dramatiq EQC 工作流程任務不可用"):
            submit_eqc_workflow_task("test_folder", "test_session")
    
    @patch('src.tasks.dramatiq_integration.get_dramatiq_task')
    def test_submit_product_search_task(self, mock_get_task):
        """測試提交產品搜索任務"""
        mock_task = Mock()
        mock_result = Mock()
        mock_task.send.return_value = mock_result
        mock_get_task.return_value = mock_task
        
        result = submit_product_search_task("test_product", ["path1"], 100, {"filter": "value"})
        
        assert result == mock_result
        mock_task.send.assert_called_once_with("test_product", ["path1"], 100, {"filter": "value"})
    
    @patch('src.tasks.dramatiq_integration.get_dramatiq_task')
    def test_submit_csv_summary_task(self, mock_get_task):
        """測試提交 CSV 摘要任務"""
        mock_task = Mock()
        mock_result = Mock()
        mock_task.send.return_value = mock_result
        mock_get_task.return_value = mock_task
        
        result = submit_csv_summary_task("test_path")
        
        assert result == mock_result
        mock_task.send.assert_called_once_with("test_path")
    
    @patch('src.tasks.dramatiq_integration.get_dramatiq_task')
    def test_submit_code_comparison_task(self, mock_get_task):
        """測試提交代碼比較任務"""
        mock_task = Mock()
        mock_result = Mock()
        mock_task.send.return_value = mock_result
        mock_get_task.return_value = mock_task
        
        result = submit_code_comparison_task("test_path")
        
        assert result == mock_result
        mock_task.send.assert_called_once_with("test_path")
    
    @patch('src.tasks.dramatiq_integration.get_dramatiq_task')
    def test_health_check(self, mock_get_task):
        """測試健康檢查"""
        mock_task = Mock()
        mock_result = Mock()
        mock_task.send.return_value = mock_result
        mock_get_task.return_value = mock_task
        
        result = health_check()
        
        assert result == mock_result
        mock_task.send.assert_called_once()
    
    @patch('src.tasks.dramatiq_integration.DramatiqTaskStatus.check_task_status')
    def test_get_task_status(self, mock_check_status):
        """測試獲取任務狀態"""
        mock_status = {'status': 'SUCCESS', 'message': 'Task completed'}
        mock_check_status.return_value = mock_status
        
        result = get_task_status("test_task_id")
        
        assert result == mock_status
        mock_check_status.assert_called_once_with("test_task_id")


@pytest.mark.asyncio
class TestDramatiqIntegrationAsync:
    """測試 Dramatiq 整合的異步功能"""
    
    async def test_async_task_submission(self):
        """測試異步任務提交"""
        with patch('src.tasks.dramatiq_integration.get_dramatiq_task') as mock_get_task:
            mock_task = Mock()
            mock_result = Mock()
            mock_task.send.return_value = mock_result
            mock_get_task.return_value = mock_task
            
            # 模擬異步任務提交
            result = submit_eqc_workflow_task("test_folder")
            
            assert result == mock_result
            mock_task.send.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
