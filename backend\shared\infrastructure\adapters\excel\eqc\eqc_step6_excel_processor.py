#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EQC Step 6 Excel 處理器

核心功能：
- 將 Step 5 測試流程 CSV 轉換為 Excel 格式
- 標記最終 Online EQC RT 行數為黃色（從第1欄到主 CODE 區間結束）
- 保持完整的資料格式和結構

實作原則：
- 精確行數標記：只標記 Step 5 生成的最終 Online EQC RT 行
- 靈活欄位範圍：從第1欄到主 CODE 區間動態計算結束欄位
- 高品質 Excel 輸出：保持原始資料完整性
"""

import os
import pandas as pd
import logging
from typing import List, Dict, Any, Tuple
from datetime import datetime
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill, Font, Alignment
from openpyxl.utils import get_column_letter


class EQCStep6ExcelProcessor:
    """EQC Step 6 Excel 處理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 黃色填充樣式
        self.yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
        # 標準字體樣式
        self.standard_font = Font(name="Calibri", size=11)
        # 粗體字體樣式
        self.bold_font = Font(name="Calibri", size=11, bold=True)
        # 標準對齊樣式
        self.standard_alignment = Alignment(horizontal="left", vertical="center")
    
    def generate_excel_with_highlighted_rows(
        self, 
        csv_file_path: str, 
        final_online_eqc_rt_rows: List[int],
        code_region_start_column: int,
        code_region_end_column: int,
        output_dir: str = None
    ) -> Dict[str, Any]:
        """
        生成 Excel 檔案並標記最終 Online EQC RT 行數為黃色，並將第8行主 CODE 欄位設為粗體
        
        Args:
            csv_file_path: Step 5 生成的 CSV 檔案路徑
            final_online_eqc_rt_rows: 最終 Online EQC RT 行數列表
            code_region_start_column: 主 CODE 區間開始欄位號（如 298）
            code_region_end_column: 主 CODE 區間結束欄位號（如 335）
            output_dir: 輸出目錄（可選，默認與 CSV 相同目錄）
        
        Returns:
            處理結果字典
        """
        try:
            self.logger.info("[ROCKET] Step 6: 開始生成 Excel 檔案並標記 Online EQC RT 行...")
            
            # 檢查輸入檔案
            if not os.path.exists(csv_file_path):
                return {
                    'status': 'error',
                    'error_message': f'CSV 檔案不存在: {csv_file_path}'
                }
            
            # 設定輸出目錄
            if output_dir is None:
                output_dir = os.path.dirname(csv_file_path)
            
            # 讀取 CSV 檔案（處理欄位數不一致的問題）
            self.logger.info(f"   讀取 CSV 檔案: {os.path.basename(csv_file_path)}")
            
            # 使用更強健的方法讀取 CSV
            try:
                # 首先嘗試標準讀取
                # 嘗試多種編碼讀取 CSV
                try:
                    df = pd.read_csv(csv_file_path, header=None, encoding='utf-8')
                except UnicodeDecodeError:
                    try:
                        df = pd.read_csv(csv_file_path, header=None, encoding='big5')
                    except UnicodeDecodeError:
                        df = pd.read_csv(csv_file_path, header=None, encoding='cp950')
                self.logger.info(f"   標準讀取成功: {len(df)} 行 x {len(df.columns)} 欄")
            except Exception as e:
                self.logger.warning(f"   標準讀取失敗，直接使用手動讀取: {e}")
                # 直接使用手動讀取，避免跳過資料行
                df = self._manual_csv_read(csv_file_path)
            
            total_rows = len(df)
            total_columns = len(df.columns)
            
            self.logger.info(f"   CSV 檔案結構: {total_rows} 行 x {total_columns} 欄")
            self.logger.info(f"   主 CODE 區間: 第{code_region_start_column}-{code_region_end_column}欄")
            self.logger.info(f"   要標記的行數: {len(final_online_eqc_rt_rows)} 行")
            
            # 生成 Excel 檔案
            excel_result = self._create_excel_with_highlighting(
                df, 
                final_online_eqc_rt_rows, 
                code_region_start_column,
                code_region_end_column,
                output_dir
            )
            
            if excel_result['status'] != 'success':
                return excel_result
            
            self.logger.info(f"   [OK] Excel 檔案已生成: {excel_result['excel_filename']}")
            self.logger.info(f"   標記行數: {len(final_online_eqc_rt_rows)} 行（黃色標記）")
            self.logger.info(f"   標記範圍: 第1欄 到 第{code_region_end_column}欄")
            
            # 檔案重命名邏輯：將 Step6 檔案重命名為 EQCTOTALDATA.xlsx
            final_excel_path = self._rename_to_main_excel_file(excel_result['excel_file_path'], output_dir)
            
            return {
                'status': 'success',
                'excel_file_path': final_excel_path,
                'original_step6_file': excel_result['excel_file_path'],
                'excel_filename': "EQCTOTALDATA.xlsx",
                'total_rows': total_rows,
                'total_columns': total_columns,
                'highlighted_rows_count': len(final_online_eqc_rt_rows),
                'highlight_column_range': f"A-{get_column_letter(code_region_end_column)}",
                'processing_stage': 'step6_excel_generation_complete'
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Step 6 Excel 生成失敗: {e}")
            return {
                'status': 'error',
                'error_message': f'Step 6 處理失敗: {str(e)}'
            }
    
    def _create_excel_with_highlighting(
        self, 
        df: pd.DataFrame, 
        final_online_eqc_rt_rows: List[int],
        code_region_start_column: int,
        code_region_end_column: int,
        output_dir: str
    ) -> Dict[str, Any]:
        """
        建立 Excel 檔案並套用黃色標記，並將第8行主 CODE 欄位設為粗體
        """
        try:
            # 生成輸出檔案名稱
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            excel_filename = f"EQCTOTALDATA_Step6_HighlightedEQCRT_{timestamp}.xlsx"
            excel_file_path = os.path.join(output_dir, excel_filename)
            
            # 建立 Excel Workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "EQC_TestFlow_Highlighted"
            
            self.logger.info("   [CHART] 寫入資料到 Excel...")
            
            # 寫入資料到 Excel（從第1行開始）
            for row_idx, row_data in df.iterrows():
                excel_row = row_idx + 1  # Excel 行號從1開始
                for col_idx, value in enumerate(row_data):
                    excel_col = col_idx + 1  # Excel 欄號從1開始
                    
                    # 設定儲存格值
                    cell = ws.cell(row=excel_row, column=excel_col)
                    cell.value = value
                    
                    # 設定樣式：第8行主 CODE 欄位使用粗體
                    if excel_row == 8 and code_region_start_column <= excel_col <= code_region_end_column:
                        cell.font = self.bold_font
                    else:
                        cell.font = self.standard_font
                    
                    cell.alignment = self.standard_alignment
            
            # 套用黃色標記到指定的行
            self.logger.info("   [ART] 套用黃色標記到 Online EQC RT 行...")
            highlighted_count = self._apply_yellow_highlighting(
                ws, 
                final_online_eqc_rt_rows, 
                code_region_end_column
            )
            
            # 計算第8行粗體欄位數量
            bold_columns_count = code_region_end_column - code_region_start_column + 1
            self.logger.info(f"   [FIRE] 第8行主 CODE 欄位已設為粗體: 第{code_region_start_column}-{code_region_end_column}欄 ({bold_columns_count} 個欄位)")
            
            # 調整欄寬（只調整前幾欄以提升效能）
            self._adjust_column_widths(ws, min(20, code_region_end_column))
            
            # 儲存 Excel 檔案
            wb.save(excel_file_path)
            
            return {
                'status': 'success',
                'excel_file_path': excel_file_path,
                'excel_filename': excel_filename,
                'highlighted_count': highlighted_count
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': f'建立 Excel 檔案失敗: {str(e)}'
            }
    
    def _apply_yellow_highlighting(
        self, 
        worksheet, 
        final_online_eqc_rt_rows: List[int],
        code_region_end_column: int
    ) -> int:
        """
        套用黃色標記到指定的行（從第1欄到主 CODE 區間結束欄）
        
        Args:
            worksheet: Excel 工作表
            final_online_eqc_rt_rows: 要標記的行數列表
            code_region_end_column: CODE 區間結束欄位號
        
        Returns:
            實際標記的儲存格數量
        """
        highlighted_count = 0
        
        for row_num in final_online_eqc_rt_rows:
            # 標記從第1欄到 CODE 區間結束欄
            for col_num in range(1, code_region_end_column + 1):
                try:
                    cell = worksheet.cell(row=row_num, column=col_num)
                    cell.fill = self.yellow_fill
                    highlighted_count += 1
                except Exception as e:
                    self.logger.warning(f"   [WARNING] 無法標記第{row_num}行第{col_num}欄: {e}")
                    continue
        
        self.logger.info(f"   [OK] 完成標記: {highlighted_count} 個儲存格已標記為黃色")
        return highlighted_count
    
    def _adjust_column_widths(self, worksheet, max_columns: int = 20):
        """
        調整欄寬以提升可讀性（僅調整前幾欄以提升效能）
        """
        try:
            for col_num in range(1, min(max_columns + 1, worksheet.max_column + 1)):
                column_letter = get_column_letter(col_num)
                # 設定適中的欄寬
                if col_num <= 2:
                    worksheet.column_dimensions[column_letter].width = 15  # Serial# 和 Bin# 欄位較寬
                else:
                    worksheet.column_dimensions[column_letter].width = 10  # 其他欄位標準寬度
        except Exception as e:
            self.logger.warning(f"   [WARNING] 調整欄寬時發生錯誤: {e}")
    
    def generate_summary_report(
        self, 
        processing_results: Dict[str, Any],
        output_dir: str
    ) -> Dict[str, Any]:
        """
        生成 Step 6 處理摘要報告
        
        Args:
            processing_results: Step 6 處理結果
            output_dir: 輸出目錄
        
        Returns:
            報告生成結果
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"EQC_Step6_Excel處理報告_{timestamp}.txt"
            report_file_path = os.path.join(output_dir, report_filename)
            
            # 生成報告內容
            content = []
            content.append("EQC Step 6 - Excel 檔案生成與行數標記報告")
            content.append("=" * 60)
            content.append("")
            content.append(f"處理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append(f"輸出目錄: {output_dir}")
            content.append("")
            
            if processing_results.get('status') == 'success':
                content.append("[OK] Step 6 處理成功")
                content.append("")
                content.append("[CHART] 檔案資訊:")
                content.append(f"  Excel 檔案: {processing_results.get('excel_filename', 'N/A')}")
                content.append(f"  總行數: {processing_results.get('total_rows', 0)}")
                content.append(f"  總欄數: {processing_results.get('total_columns', 0)}")
                content.append("")
                content.append("[ART] 標記資訊:")
                content.append(f"  標記行數: {processing_results.get('highlighted_rows_count', 0)} 行")
                content.append(f"  標記範圍: {processing_results.get('highlight_column_range', 'N/A')}")
                content.append(f"  標記顏色: 黃色 (Online EQC RT 行)")
                content.append("")
            else:
                content.append("[ERROR] Step 6 處理失敗")
                content.append(f"  失敗原因: {processing_results.get('error_message', '未知錯誤')}")
                content.append("")
            
            content.append("處理階段:")
            content.append("  1. [OK] CSV 檔案讀取")
            content.append("  2. [OK] Excel 檔案建立")
            content.append("  3. [OK] 資料轉換與寫入")
            content.append("  4. [OK] Online EQC RT 行黃色標記")
            content.append("  5. [OK] 檔案儲存")
            content.append("")
            content.append("處理完成時間: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # 寫入報告檔案
            with open(report_file_path, 'w', encoding='utf-8') as f:
                f.write("\n".join(content))
            
            return {
                'status': 'success',
                'report_file_path': report_file_path,
                'report_filename': report_filename
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': f'生成摘要報告失敗: {str(e)}'
            }
    
    def _manual_csv_read(self, csv_file_path: str) -> pd.DataFrame:
        """
        手動讀取 CSV 檔案，處理欄位數不一致的問題
        """
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.logger.info(f"   讀取到的原始行數: {len(lines)}")
            
            # 找出最大欄位數
            max_columns = 0
            parsed_rows = []
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if line:  # 只處理非空行
                    try:
                        # 分割並計算欄位數
                        row_data = line.split(',')
                        max_columns = max(max_columns, len(row_data))
                        parsed_rows.append(row_data)
                    except Exception as e:
                        self.logger.warning(f"   第{line_num}行解析失敗: {e}")
                        continue
                else:
                    # 保留空行作為空的 row_data
                    parsed_rows.append([])
            
            self.logger.info(f"   成功解析的行數: {len(parsed_rows)}")
            self.logger.info(f"   最大欄位數: {max_columns}")
            
            # 統一所有行的欄位數（補齊短的行）
            normalized_rows = []
            for row in parsed_rows:
                # 補齊欄位數到最大值
                while len(row) < max_columns:
                    row.append('')
                # 截斷超出的欄位
                row = row[:max_columns]
                normalized_rows.append(row)
            
            # 建立 DataFrame
            df = pd.DataFrame(normalized_rows)
            
            self.logger.info(f"   手動讀取成功: {len(df)} 行 x {len(df.columns)} 欄")
            return df
            
        except Exception as e:
            self.logger.error(f"   手動讀取失敗: {e}")
            # 返回空的 DataFrame
            return pd.DataFrame()
    
    def _rename_to_main_excel_file(self, step6_file_path: str, output_dir: str) -> str:
        """
        將 Step6 Excel 檔案重命名為 EQCTOTALDATA.xlsx
        
        Args:
            step6_file_path: Step6 Excel 檔案路徑
            output_dir: 輸出目錄
        
        Returns:
            最終的 EQCTOTALDATA.xlsx 路徑
        """
        try:
            # 目標檔案路徑
            target_excel_path = os.path.join(output_dir, "EQCTOTALDATA.xlsx")
            
            # 刪[EXCEPT_CHAR]舊的 EQCTOTALDATA.xlsx（如果存在）
            if os.path.exists(target_excel_path):
                os.remove(target_excel_path)
                self.logger.info(f"   [DELETE] 已刪[EXCEPT_CHAR]舊的 EQCTOTALDATA.xlsx")
            
            # 重命名 Step6 檔案為 EQCTOTALDATA.xlsx
            os.rename(step6_file_path, target_excel_path)
            self.logger.info(f"   [EDIT] 已重命名: {os.path.basename(step6_file_path)} → EQCTOTALDATA.xlsx")
            
            return target_excel_path
            
        except Exception as e:
            self.logger.error(f"[ERROR] Excel 檔案重命名失敗: {e}")
            return step6_file_path  # 如果失敗，返回原始檔案路徑