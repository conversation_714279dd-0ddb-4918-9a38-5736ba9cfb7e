"""Phase 4: 修復 TestClient 相容性問題的測試

這個模組解決了 TestClient 相容性問題，提供可運行的真實測試。
"""

import pytest
import sys
import os
from pathlib import Path
from typing import Dict, Any

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用正確的 TestClient 導入方式
try:
    # 嘗試使用 httpx 作為 TestClient 的替代方案
    import httpx
    from fastapi import FastAPI, Depends, HTTPException
    from unittest.mock import Mock, patch

    # 導入實際的依賴
    from frontend.api.dependencies import (
        get_api_state, get_staging_service, get_processing_service
    )

    # 創建一個相容的 TestClient 類
    class CompatibleTestClient:
        def __init__(self, app):
            self.app = app
            # 使用 httpx 創建同步客戶端
            import uvicorn
            import threading
            import time
            import socket

            # 找一個可用的端口
            sock = socket.socket()
            sock.bind(('', 0))
            port = sock.getsockname()[1]
            sock.close()

            self.base_url = f"http://127.0.0.1:{port}"
            self.server_thread = None
            self.server = None
            self._start_server(port)

        def _start_server(self, port):
            """啟動測試服務器"""
            import uvicorn
            import threading
            import time

            def run_server():
                config = uvicorn.Config(self.app, host="127.0.0.1", port=port, log_level="error")
                self.server = uvicorn.Server(config)
                self.server.run()

            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()

            # 等待服務器啟動
            time.sleep(0.5)

        def get(self, path, **kwargs):
            """發送 GET 請求"""
            with httpx.Client() as client:
                return client.get(f"{self.base_url}{path}", **kwargs)

        def post(self, path, **kwargs):
            """發送 POST 請求"""
            with httpx.Client() as client:
                return client.post(f"{self.base_url}{path}", **kwargs)

    # 使用我們的相容客戶端
    TestClient = CompatibleTestClient
    DEPENDENCIES_AVAILABLE = True

except ImportError as e:
    print(f"Warning: Dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False
    TestClient = None
    FastAPI = None


class TestFixedTestClient:
    """測試修復後的 TestClient 功能"""
    
    @pytest.fixture
    def simple_app(self):
        """創建一個簡單的測試應用"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        app = FastAPI(title="Test App")
        
        @app.get("/")
        def root():
            return {"message": "Hello World", "status": "ok"}
        
        @app.get("/health")
        def health():
            return {"status": "healthy", "timestamp": "2025-08-02"}
        
        @app.post("/echo")
        def echo(data: Dict[str, Any]):
            return {"received": data, "status": "success"}
        
        return app
    
    @pytest.fixture
    def dependency_app(self):
        """創建使用依賴注入的測試應用"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        app = FastAPI(title="Dependency Test App")
        
        # Mock 依賴
        def mock_api_state():
            mock = Mock()
            mock.increment_request_count = Mock()
            mock.get_request_count = Mock(return_value=42)
            mock.is_healthy = Mock(return_value=True)
            return mock
        
        def mock_staging_service():
            mock = Mock()
            mock.service_id = "test_staging"
            mock.is_healthy = Mock(return_value=True)
            mock.get_status = Mock(return_value={"status": "ready"})
            return mock
        
        # 覆蓋依賴
        app.dependency_overrides[get_api_state] = mock_api_state
        app.dependency_overrides[get_staging_service] = mock_staging_service
        
        @app.get("/api/state")
        def get_state(api_state=Depends(get_api_state)):
            api_state.increment_request_count()
            return {
                "request_count": api_state.get_request_count(),
                "healthy": api_state.is_healthy()
            }
        
        @app.get("/api/staging/status")
        def get_staging_status(staging_service=Depends(get_staging_service)):
            return {
                "service_id": staging_service.service_id,
                "status": staging_service.get_status(),
                "healthy": staging_service.is_healthy()
            }
        
        return app
    
    def test_simple_get_request(self, simple_app):
        """測試簡單的 GET 請求"""
        client = TestClient(simple_app)
        
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Hello World"
        assert data["status"] == "ok"
    
    def test_simple_post_request(self, simple_app):
        """測試簡單的 POST 請求"""
        client = TestClient(simple_app)
        
        test_data = {"name": "test", "value": 123}
        response = client.post("/echo", json=test_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["received"] == test_data
        assert data["status"] == "success"
    
    def test_health_endpoint(self, simple_app):
        """測試健康檢查端點"""
        client = TestClient(simple_app)
        
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
    
    def test_dependency_injection_api_state(self, dependency_app):
        """測試 API 狀態依賴注入"""
        client = TestClient(dependency_app)
        
        response = client.get("/api/state")
        
        assert response.status_code == 200
        data = response.json()
        assert data["request_count"] == 42
        assert data["healthy"] is True
    
    def test_dependency_injection_staging_service(self, dependency_app):
        """測試暫存服務依賴注入"""
        client = TestClient(dependency_app)
        
        response = client.get("/api/staging/status")
        
        assert response.status_code == 200
        data = response.json()
        assert data["service_id"] == "test_staging"
        assert data["status"] == {"status": "ready"}
        assert data["healthy"] is True
    
    def test_multiple_requests_consistency(self, simple_app):
        """測試多個請求的一致性"""
        client = TestClient(simple_app)
        
        # 發送多個請求
        responses = []
        for i in range(5):
            response = client.get("/health")
            responses.append(response)
        
        # 驗證所有請求都成功
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
    
    def test_error_handling(self, simple_app):
        """測試錯誤處理"""
        client = TestClient(simple_app)
        
        # 請求不存在的端點
        response = client.get("/nonexistent")
        
        assert response.status_code == 404
    
    def test_invalid_json_handling(self, simple_app):
        """測試無效 JSON 處理"""
        client = TestClient(simple_app)
        
        # 發送無效的 JSON
        response = client.post(
            "/echo",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        # 應該返回 422 (驗證錯誤) 或 400 (壞請求)
        assert response.status_code in [400, 422]


class TestRealDependencyInjection:
    """測試真實的依賴注入機制"""
    
    def test_dependency_override_mechanism(self):
        """測試依賴覆蓋機制"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        app = FastAPI()
        
        # 原始依賴
        def original_dependency():
            return "original"
        
        # Mock 依賴
        def mock_dependency():
            return "mocked"
        
        @app.get("/test")
        def test_endpoint(dep=Depends(original_dependency)):
            return {"value": dep}
        
        # 測試原始依賴
        client = TestClient(app)
        response = client.get("/test")
        assert response.json()["value"] == "original"
        
        # 覆蓋依賴
        app.dependency_overrides[original_dependency] = mock_dependency
        
        # 測試覆蓋後的依賴
        response = client.get("/test")
        assert response.json()["value"] == "mocked"
        
        # 清理覆蓋
        app.dependency_overrides.clear()
        
        # 測試恢復原始依賴
        response = client.get("/test")
        assert response.json()["value"] == "original"
    
    def test_nested_dependencies(self):
        """測試嵌套依賴"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        app = FastAPI()
        
        def base_dependency():
            return "base"
        
        def nested_dependency(base=Depends(base_dependency)):
            return f"nested-{base}"
        
        @app.get("/nested")
        def nested_endpoint(nested=Depends(nested_dependency)):
            return {"value": nested}
        
        client = TestClient(app)
        response = client.get("/nested")
        assert response.json()["value"] == "nested-base"
        
        # 覆蓋基礎依賴
        app.dependency_overrides[base_dependency] = lambda: "mocked-base"
        
        response = client.get("/nested")
        assert response.json()["value"] == "nested-mocked-base"
    
    def test_dependency_with_exceptions(self):
        """測試依賴中的異常處理"""
        if not DEPENDENCIES_AVAILABLE:
            pytest.skip("Dependencies not available")
        
        app = FastAPI()
        
        def failing_dependency():
            raise HTTPException(status_code=503, detail="Service unavailable")
        
        @app.get("/failing")
        def failing_endpoint(dep=Depends(failing_dependency)):
            return {"value": dep}
        
        client = TestClient(app)
        response = client.get("/failing")
        
        assert response.status_code == 503
        assert "Service unavailable" in response.text


class TestPerformanceBaseline:
    """建立真實的性能基準"""
    
    def test_simple_endpoint_response_time(self, simple_app):
        """測試簡單端點的響應時間"""
        import time
        
        client = TestClient(simple_app)
        
        # 預熱
        client.get("/")
        
        # 測量響應時間
        start_time = time.time()
        response = client.get("/")
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # 真實的基準：本地測試應該在 100ms 內
        assert response_time < 0.1, f"Response time too slow: {response_time:.3f}s"
        assert response.status_code == 200
        
        print(f"✅ Simple endpoint response time: {response_time:.3f}s")
    
    def test_dependency_injection_overhead(self, dependency_app):
        """測試依賴注入的開銷"""
        import time
        
        client = TestClient(dependency_app)
        
        # 預熱
        client.get("/api/state")
        
        # 測量多個請求的平均時間
        times = []
        for _ in range(10):
            start_time = time.time()
            response = client.get("/api/state")
            end_time = time.time()
            
            if response.status_code == 200:
                times.append(end_time - start_time)
        
        avg_time = sum(times) / len(times)
        max_time = max(times)
        
        # 依賴注入應該不會增加太多開銷
        assert avg_time < 0.05, f"Average DI overhead too high: {avg_time:.3f}s"
        assert max_time < 0.1, f"Max DI overhead too high: {max_time:.3f}s"
        
        print(f"✅ DI average overhead: {avg_time:.3f}s, max: {max_time:.3f}s")
    
    def test_concurrent_requests_baseline(self, simple_app):
        """測試並發請求基準"""
        import threading
        import time
        
        client = TestClient(simple_app)
        results = []
        
        def make_request():
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            results.append({
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code == 200
            })
        
        # 創建 5 個並發請求
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # 啟動所有線程
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # 等待所有線程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 驗證結果
        successful_requests = [r for r in results if r['success']]
        assert len(successful_requests) >= 4, "At least 80% requests should succeed"
        
        avg_response_time = sum(r['response_time'] for r in successful_requests) / len(successful_requests)
        assert avg_response_time < 0.2, f"Concurrent avg response time too slow: {avg_response_time:.3f}s"
        assert total_time < 1.0, f"Total concurrent execution time too slow: {total_time:.3f}s"
        
        print(f"✅ Concurrent test: {len(successful_requests)}/5 success, avg: {avg_response_time:.3f}s")
