"""
告警資料存取層單元測試

測試 DashboardAlertRepository 的所有功能：
- 資料表初始化
- 告警記錄儲存
- 告警狀態管理
- 告警規則管理
- 告警統計分析
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

from backend.monitoring.repositories.dashboard_alert_repository import DashboardAlertRepository
from backend.monitoring.models.dashboard_alert_models import DashboardAlert, AlertRule


class TestDashboardAlertRepository:
    """告警資料存取層測試類"""
    
    @pytest.fixture
    def temp_db_path(self):
        """創建臨時資料庫檔案"""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
            
    @pytest.fixture
    def repository(self, temp_db_path):
        """創建測試用的告警資料庫存取層實例"""
        return DashboardAlertRepository(temp_db_path)
        
    @pytest.fixture
    def sample_alert(self):
        """創建測試用的告警資料"""
        return DashboardAlert(
            id="alert_123",
            alert_type="queue_overflow",
            level="warning",
            title="郵件佇列告警",
            message="郵件佇列待處理數量過多 (15 > 10)",
            source="email_monitor",
            triggered_at=datetime.now(),
            threshold_value=10.0,
            current_value=15.0,
            metadata={"queue_name": "email_processing"}
        )
        
    @pytest.fixture
    def sample_alert_rule(self):
        """創建測試用的告警規則"""
        return AlertRule(
            id=None,
            rule_name="test_email_queue_warning",
            rule_category="email",
            metric_path="email.pending_count",
            condition_type="greater_than",
            threshold_value=10.0,
            alert_level="warning",
            alert_message="郵件佇列待處理數量過多 ({current_value} > {threshold_value})",
            is_enabled=True,
            cooldown_minutes=5,
            max_occurrences=5
        )
        
    def test_init_tables(self, repository):
        """測試資料表初始化"""
        # 檢查資料表是否已創建
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            
            # 檢查所有必要的表格是否存在
            tables = [
                'alert_history',
                'dashboard_alert_rules',
                'alert_notifications',
                'alert_suppression_rules'
            ]
            
            for table in tables:
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table,))
                result = cursor.fetchone()
                assert result is not None, f"表格 {table} 未創建"
                
        # 檢查是否插入了預設告警規則
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) as count FROM dashboard_alert_rules")
            result = cursor.fetchone()
            assert result['count'] > 0, "未插入預設告警規則"
            
    @pytest.mark.asyncio
    async def test_store_alert(self, repository, sample_alert):
        """測試儲存告警記錄"""
        # 儲存告警
        result = await repository.store_alert(sample_alert)
        assert result is True
        
        # 驗證資料是否正確儲存
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM alert_history WHERE alert_id = ?
            """, (sample_alert.id,))
            result = cursor.fetchone()
            
            assert result is not None
            assert result['alert_type'] == sample_alert.alert_type
            assert result['alert_level'] == sample_alert.level
            assert result['title'] == sample_alert.title
            assert result['status'] == 'active'
            
    @pytest.mark.asyncio
    async def test_store_duplicate_alert(self, repository, sample_alert):
        """測試儲存重複告警（應該更新發生次數）"""
        # 儲存第一個告警
        await repository.store_alert(sample_alert)
        
        # 儲存相同類型的告警
        duplicate_alert = DashboardAlert(
            id="alert_456",
            alert_type=sample_alert.alert_type,
            level=sample_alert.level,
            title="重複告警",
            message="另一個相同類型的告警",
            source=sample_alert.source,
            triggered_at=datetime.now()
        )
        
        result = await repository.store_alert(duplicate_alert)
        assert result is True
        
        # 檢查是否更新了發生次數而不是創建新記錄
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) as count, MAX(occurrence_count) as max_count
                FROM alert_history 
                WHERE alert_type = ? AND source_system = ? AND status = 'active'
            """, (sample_alert.alert_type, sample_alert.source))
            result = cursor.fetchone()
            
            # 應該只有一條記錄，但發生次數應該增加
            assert result['count'] == 1
            assert result['max_count'] > 1
            
    @pytest.mark.asyncio
    async def test_get_active_alerts(self, repository, sample_alert):
        """測試獲取活躍告警"""
        # 儲存多個不同狀態的告警
        alerts = []
        for i in range(3):
            alert = DashboardAlert(
                id=f"alert_{i}",
                alert_type=f"test_type_{i}",
                level="warning" if i % 2 == 0 else "critical",
                title=f"測試告警 {i}",
                message=f"測試訊息 {i}",
                source="test_monitor",
                triggered_at=datetime.now() - timedelta(minutes=i*10),
                status="active" if i < 2 else "resolved"
            )
            alerts.append(alert)
            await repository.store_alert(alert)
            
        # 獲取活躍告警
        active_alerts = await repository.get_active_alerts()
        
        # 應該只返回活躍狀態的告警
        assert len(active_alerts) == 2
        for alert in active_alerts:
            assert alert.status == "active"
            
        # 檢查排序（應該按級別和時間排序）
        assert active_alerts[0].level in ["critical", "warning"]
        
    @pytest.mark.asyncio
    async def test_acknowledge_alert(self, repository, sample_alert):
        """測試確認告警"""
        # 儲存告警
        await repository.store_alert(sample_alert)
        
        # 確認告警
        result = await repository.acknowledge_alert(sample_alert.id)
        assert result is True
        
        # 驗證狀態是否已更新
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT status, acknowledged_at FROM alert_history 
                WHERE alert_id = ?
            """, (sample_alert.id,))
            result = cursor.fetchone()
            
            assert result['status'] == 'acknowledged'
            assert result['acknowledged_at'] is not None
            
    @pytest.mark.asyncio
    async def test_resolve_alert(self, repository, sample_alert):
        """測試解決告警"""
        # 儲存並確認告警
        await repository.store_alert(sample_alert)
        await repository.acknowledge_alert(sample_alert.id)
        
        # 解決告警
        result = await repository.resolve_alert(sample_alert.id)
        assert result is True
        
        # 驗證狀態是否已更新
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT status, resolved_at FROM alert_history 
                WHERE alert_id = ?
            """, (sample_alert.id,))
            result = cursor.fetchone()
            
            assert result['status'] == 'resolved'
            assert result['resolved_at'] is not None
            
    @pytest.mark.asyncio
    async def test_get_alert_history(self, repository):
        """測試獲取告警歷史"""
        # 儲存多個不同時間的告警
        for i in range(5):
            alert = DashboardAlert(
                id=f"history_alert_{i}",
                alert_type="test_type",
                level="warning" if i % 2 == 0 else "error",
                title=f"歷史告警 {i}",
                message=f"歷史訊息 {i}",
                source="test_monitor",
                triggered_at=datetime.now() - timedelta(hours=i*2)
            )
            await repository.store_alert(alert)
            
        # 獲取24小時內的告警歷史
        history = await repository.get_alert_history(24)
        assert len(history) == 5
        
        # 獲取12小時內的告警歷史
        history_12h = await repository.get_alert_history(12)
        assert len(history_12h) < 5
        
        # 按級別篩選
        error_history = await repository.get_alert_history(24, alert_level="error")
        assert len(error_history) > 0
        for alert in error_history:
            assert alert['alert_level'] == "error"
            
    @pytest.mark.asyncio
    async def test_get_alert_statistics(self, repository):
        """測試獲取告警統計資料"""
        # 儲存多個不同類型和狀態的告警
        alert_data = [
            ("critical", "queue_overflow", "active"),
            ("warning", "high_cpu", "acknowledged"),
            ("error", "disk_full", "resolved"),
            ("critical", "queue_overflow", "resolved"),
            ("warning", "memory_high", "active")
        ]
        
        for i, (level, alert_type, status) in enumerate(alert_data):
            alert = DashboardAlert(
                id=f"stats_alert_{i}",
                alert_type=alert_type,
                level=level,
                title=f"統計告警 {i}",
                message=f"統計訊息 {i}",
                source="test_monitor",
                triggered_at=datetime.now() - timedelta(hours=i),
                status=status
            )
            await repository.store_alert(alert)
            
        # 獲取統計資料
        stats = await repository.get_alert_statistics(7)
        
        assert isinstance(stats, dict)
        assert 'by_level' in stats
        assert 'by_type' in stats
        assert 'by_source' in stats
        assert 'by_status' in stats
        assert 'summary' in stats
        
        # 檢查統計資料的正確性
        assert stats['by_level']['critical'] == 2
        assert stats['by_level']['warning'] == 2
        assert stats['by_level']['error'] == 1
        assert stats['summary']['total_alerts'] == 5
        
    @pytest.mark.asyncio
    async def test_get_alert_rules(self, repository):
        """測試獲取告警規則"""
        # 獲取所有啟用的規則
        rules = await repository.get_alert_rules()
        assert len(rules) > 0
        
        # 檢查預設規則是否存在
        rule_names = [rule['rule_name'] for rule in rules]
        assert 'email_queue_warning' in rule_names
        assert 'system_cpu_warning' in rule_names
        
        # 按類別篩選
        email_rules = await repository.get_alert_rules(category='email')
        assert len(email_rules) > 0
        for rule in email_rules:
            assert rule['rule_category'] == 'email'
            
    @pytest.mark.asyncio
    async def test_create_alert_rule(self, repository, sample_alert_rule):
        """測試創建告警規則"""
        rule_data = {
            'rule_name': sample_alert_rule.rule_name,
            'rule_category': sample_alert_rule.rule_category,
            'metric_path': sample_alert_rule.metric_path,
            'condition_type': sample_alert_rule.condition_type,
            'threshold_value': sample_alert_rule.threshold_value,
            'alert_level': sample_alert_rule.alert_level,
            'alert_message': sample_alert_rule.alert_message,
            'is_enabled': sample_alert_rule.is_enabled,
            'cooldown_minutes': sample_alert_rule.cooldown_minutes,
            'max_occurrences': sample_alert_rule.max_occurrences
        }
        
        result = await repository.create_alert_rule(rule_data)
        assert result is True
        
        # 驗證規則是否正確創建
        rules = await repository.get_alert_rules()
        rule_names = [rule['rule_name'] for rule in rules]
        assert sample_alert_rule.rule_name in rule_names
        
    @pytest.mark.asyncio
    async def test_update_alert_rule(self, repository, sample_alert_rule):
        """測試更新告警規則"""
        # 先創建規則
        rule_data = {
            'rule_name': sample_alert_rule.rule_name,
            'rule_category': sample_alert_rule.rule_category,
            'metric_path': sample_alert_rule.metric_path,
            'condition_type': sample_alert_rule.condition_type,
            'threshold_value': sample_alert_rule.threshold_value,
            'alert_level': sample_alert_rule.alert_level,
            'alert_message': sample_alert_rule.alert_message
        }
        await repository.create_alert_rule(rule_data)
        
        # 獲取創建的規則ID
        rules = await repository.get_alert_rules()
        rule_id = None
        for rule in rules:
            if rule['rule_name'] == sample_alert_rule.rule_name:
                rule_id = rule['id']
                break
                
        assert rule_id is not None
        
        # 更新規則
        update_data = {
            'threshold_value': 20.0,
            'alert_level': 'critical',
            'is_enabled': False
        }
        
        result = await repository.update_alert_rule(rule_id, update_data)
        assert result is True
        
        # 驗證更新是否成功
        updated_rules = await repository.get_alert_rules(enabled_only=False)
        updated_rule = None
        for rule in updated_rules:
            if rule['id'] == rule_id:
                updated_rule = rule
                break
                
        assert updated_rule is not None
        assert updated_rule['threshold_value'] == 20.0
        assert updated_rule['alert_level'] == 'critical'
        assert updated_rule['is_enabled'] is False
        
    @pytest.mark.asyncio
    async def test_delete_alert_rule(self, repository, sample_alert_rule):
        """測試刪除告警規則"""
        # 先創建規則
        rule_data = {
            'rule_name': sample_alert_rule.rule_name,
            'rule_category': sample_alert_rule.rule_category,
            'metric_path': sample_alert_rule.metric_path,
            'condition_type': sample_alert_rule.condition_type,
            'threshold_value': sample_alert_rule.threshold_value,
            'alert_level': sample_alert_rule.alert_level,
            'alert_message': sample_alert_rule.alert_message
        }
        await repository.create_alert_rule(rule_data)
        
        # 獲取創建的規則ID
        rules = await repository.get_alert_rules()
        rule_id = None
        for rule in rules:
            if rule['rule_name'] == sample_alert_rule.rule_name:
                rule_id = rule['id']
                break
                
        assert rule_id is not None
        
        # 刪除規則
        result = await repository.delete_alert_rule(rule_id)
        assert result is True
        
        # 驗證規則是否已刪除
        remaining_rules = await repository.get_alert_rules()
        rule_names = [rule['rule_name'] for rule in remaining_rules]
        assert sample_alert_rule.rule_name not in rule_names
        
    @pytest.mark.asyncio
    async def test_store_alert_notification(self, repository):
        """測試儲存告警通知記錄"""
        notification_data = {
            'alert_id': 'test_alert_123',
            'notification_channel': 'email',
            'recipient': '<EMAIL>',
            'notification_status': 'sent',
            'sent_at': datetime.now(),
            'error_message': None,
            'retry_count': 0
        }
        
        result = await repository.store_alert_notification(notification_data)
        assert result is True
        
        # 驗證通知記錄是否正確儲存
        notifications = await repository.get_notification_history('test_alert_123')
        assert len(notifications) == 1
        assert notifications[0]['notification_channel'] == 'email'
        assert notifications[0]['recipient'] == '<EMAIL>'
        
    @pytest.mark.asyncio
    async def test_get_notification_history(self, repository):
        """測試獲取通知歷史"""
        # 儲存多個通知記錄
        for i in range(3):
            notification_data = {
                'alert_id': f'alert_{i}',
                'notification_channel': 'email' if i % 2 == 0 else 'line',
                'recipient': f'user{i}@example.com',
                'notification_status': 'sent' if i < 2 else 'failed',
                'sent_at': datetime.now() - timedelta(minutes=i*10),
                'error_message': f'Error {i}' if i == 2 else None,
                'retry_count': i
            }
            await repository.store_alert_notification(notification_data)
            
        # 獲取所有通知歷史
        all_notifications = await repository.get_notification_history()
        assert len(all_notifications) == 3
        
        # 獲取特定告警的通知歷史
        alert_notifications = await repository.get_notification_history('alert_0')
        assert len(alert_notifications) == 1
        assert alert_notifications[0]['alert_id'] == 'alert_0'
        
    @pytest.mark.asyncio
    async def test_cleanup_old_alerts(self, repository, sample_alert):
        """測試清理過期告警記錄"""
        # 儲存一些舊告警
        old_alert = DashboardAlert(
            id="old_alert",
            alert_type="old_type",
            level="warning",
            title="舊告警",
            message="這是一個舊告警",
            source="test_monitor",
            triggered_at=datetime.now() - timedelta(days=100),
            status="resolved"
        )
        await repository.store_alert(old_alert)
        
        # 儲存一些新告警
        await repository.store_alert(sample_alert)
        
        # 執行清理（保留90天）
        result = await repository.cleanup_old_alerts(90)
        assert result is True
        
        # 驗證舊告警已被清理，但活躍告警保留
        with repository._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) as count FROM alert_history")
            result = cursor.fetchone()
            # 應該只剩下活躍的告警
            assert result['count'] >= 1
            
    @pytest.mark.asyncio
    async def test_get_alert_trends(self, repository):
        """測試獲取告警趨勢分析"""
        # 儲存多個不同時間的告警
        for i in range(10):
            alert = DashboardAlert(
                id=f"trend_alert_{i}",
                alert_type="test_type",
                level="warning" if i % 3 == 0 else "error",
                title=f"趨勢告警 {i}",
                message=f"趨勢訊息 {i}",
                source="test_monitor",
                triggered_at=datetime.now() - timedelta(hours=i),
                status="resolved" if i % 4 == 0 else "active"
            )
            await repository.store_alert(alert)
            
        # 獲取趨勢分析
        trends = await repository.get_alert_trends(7)
        
        assert isinstance(trends, dict)
        assert 'hourly' in trends
        assert 'by_type' in trends
        assert 'resolution_time' in trends
        
        # 檢查趨勢資料結構
        assert isinstance(trends['hourly'], dict)
        assert isinstance(trends['by_type'], dict)
        
    @pytest.mark.asyncio
    async def test_error_handling(self, temp_db_path):
        """測試錯誤處理"""
        # 使用無效的資料庫路徑
        invalid_path = "/invalid/path/test.db"
        
        with pytest.raises(Exception):
            DashboardAlertRepository(invalid_path)
            
    @pytest.mark.asyncio
    async def test_concurrent_alert_operations(self, repository):
        """測試並發告警操作"""
        import asyncio
        
        # 創建多個並發告警儲存任務
        tasks = []
        for i in range(5):
            alert = DashboardAlert(
                id=f"concurrent_alert_{i}",
                alert_type="concurrent_test",
                level="warning",
                title=f"並發告警 {i}",
                message=f"並發訊息 {i}",
                source="concurrent_monitor",
                triggered_at=datetime.now() + timedelta(seconds=i)
            )
            tasks.append(repository.store_alert(alert))
            
        # 並發執行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 檢查所有任務都成功完成
        for result in results:
            assert result is True or not isinstance(result, Exception)
            
        # 驗證所有告警都已儲存
        active_alerts = await repository.get_active_alerts()
        concurrent_alerts = [a for a in active_alerts if a.alert_type == "concurrent_test"]
        assert len(concurrent_alerts) == 5