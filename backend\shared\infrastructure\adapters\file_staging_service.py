"""檔案暫存服務
負責管理檔案的暫存、複製和組織
"""

import asyncio
import hashlib
import shutil
import tempfile
import uuid
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field


class StagingStatus(Enum):
    """暫存任務狀態"""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StagingError(Exception):
    """暫存服務基礎錯誤"""
    pass


class InsufficientSpaceError(StagingError):
    """磁碟空間不足錯誤"""
    pass


class FileIntegrityError(StagingError):
    """檔案完整性錯誤"""
    pass


class StagingPermissionError(StagingError):
    """暫存權限錯誤"""
    pass


class StagingTimeoutError(StagingError):
    """暫存超時錯誤"""
    pass


class TaskCancellationError(StagingError):
    """任務取消錯誤"""
    pass


@dataclass
class FileInfo:
    """檔案信息"""
    source_path: Path
    relative_path: Path
    size: int
    checksum: Optional[str] = None
    copied: bool = False
    verified: bool = False
    error_message: Optional[str] = None


@dataclass
class StagingTask:
    """暫存任務"""
    task_id: str
    product_name: str
    source_files: List[Path]
    preserve_structure: bool
    staging_directory: Path
    status: StagingStatus = StagingStatus.PENDING
    progress: float = 0.0
    total_size: int = 0
    processed_size: int = 0
    file_infos: List[FileInfo] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    verify_integrity: bool = True
    timeout: Optional[float] = None
    cancelled: bool = False


@dataclass
class StagingResult:
    """暫存結果"""
    success: bool
    task_id: str
    staging_directory: Path
    staged_files: List[Path] = field(default_factory=list)
    total_files: int = 0
    staging_duration: float = 0.0
    integrity_check_passed: bool = False
    error_message: Optional[str] = None


class FileStagingService:
    """檔案暫存服務"""
    
    def __init__(
        self, 
        base_staging_path: str,
        max_workers: int = 4,
        verify_integrity: bool = True,
        chunk_size: int = 8192,
        enable_progress_batching: bool = False,
        progress_update_interval: int = 10
    ):
        self.base_staging_path = Path(base_staging_path)
        self.base_staging_path.mkdir(parents=True, exist_ok=True)
        self.max_workers = max_workers
        self.verify_integrity = verify_integrity
        self.chunk_size = chunk_size
        self.enable_progress_batching = enable_progress_batching
        self.progress_update_interval = progress_update_interval
        self.tasks: Dict[str, StagingTask] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.active_tasks = set()
        self.task_semaphore = asyncio.Semaphore(max_workers)
    
    def create_staging_task(
        self,
        product_name: str,
        source_files: List[Path],
        preserve_structure: bool = True,
        timeout: Optional[float] = None
    ) -> str:
        """建立暫存任務
        
        Args:
            product_name: 產品名稱
            source_files: 來源檔案列表
            preserve_structure: 是否保持目錄結構
            
        Returns:
            任務ID
        """
        task_id = f"staging_{uuid.uuid4().hex[:12]}"
        staging_dir = self.base_staging_path / task_id
        
        # 建立檔案信息列表
        file_infos = []
        total_size = 0
        
        for source_file in source_files:
            source_path = Path(source_file)
            if source_path.exists():
                if source_path.is_file():
                    size = source_path.stat().st_size
                    rel_path = source_path.name  # 單個文件總是使用文件名
                    file_infos.append(FileInfo(
                        source_path=source_path,
                        relative_path=Path(rel_path),
                        size=size
                    ))
                    total_size += size
                elif source_path.is_dir():
                    # 遞歸處理目錄
                    for file_path in source_path.rglob('*'):
                        if file_path.is_file():
                            size = file_path.stat().st_size
                            rel_path = file_path.relative_to(source_path.parent) if preserve_structure else file_path.name
                            file_infos.append(FileInfo(
                                source_path=file_path,
                                relative_path=Path(rel_path),
                                size=size
                            ))
                            total_size += size
        
        # 建立任務
        task = StagingTask(
            task_id=task_id,
            product_name=product_name,
            source_files=source_files,
            preserve_structure=preserve_structure,
            staging_directory=staging_dir,
            total_size=total_size,
            file_infos=file_infos,
            verify_integrity=self.verify_integrity,
            timeout=timeout
        )
        
        self.tasks[task_id] = task
        return task_id
    
    async def execute_staging_task(self, task_id: str) -> StagingResult:
        """執行暫存任務
        
        Args:
            task_id: 任務ID
            
        Returns:
            暫存結果
        """
        if task_id not in self.tasks:
            return StagingResult(
                success=False,
                task_id=task_id,
                staging_directory=Path(),
                error_message="任務不存在"
            )
        
        task = self.tasks[task_id]
        start_time = datetime.now()
        task.started_at = start_time
        task.status = StagingStatus.RUNNING
        
        # 添加到活動任務集合
        self.active_tasks.add(task_id)
        
        try:
            async with self.task_semaphore:  # 控制並發
                # 檢查任務是否已被取消
                if task.cancelled:
                    raise TaskCancellationError("任務已被取消")
                
                # 檢查磁碟空間
                if not self._check_disk_space(task.total_size):
                    raise InsufficientSpaceError("磁碟空間不足")
                
                # 建立暫存目錄
                task.staging_directory.mkdir(parents=True, exist_ok=True)
                
                # 執行任務（可能有超時）
                if task.timeout:
                    try:
                        await asyncio.wait_for(
                            self._execute_task_operations(task),
                            timeout=task.timeout
                        )
                    except asyncio.TimeoutError:
                        task.status = StagingStatus.CANCELLED
                        raise StagingTimeoutError(f"任務超時（{task.timeout}秒）")
                else:
                    await self._execute_task_operations(task)
                
                # 檢查是否被取消
                if task.cancelled:
                    task.status = StagingStatus.CANCELLED
                    raise TaskCancellationError("任務執行期間被取消")
                
                # 完成任務
                task.status = StagingStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100.0
                
                # 🚀 新增：暫存完成後自動觸發代碼比較
                await self._try_trigger_code_comparison_after_staging(task)
                
                staging_duration = (datetime.now() - start_time).total_seconds()
                
                staged_files = [
                    task.staging_directory / info.relative_path 
                    for info in task.file_infos if info.copied
                ]
                
                return StagingResult(
                    success=True,
                    task_id=task_id,
                    staging_directory=task.staging_directory,
                    staged_files=staged_files,
                    total_files=len(staged_files),
                    staging_duration=staging_duration,
                    integrity_check_passed=True
                )
                
        except Exception as e:
            if not task.cancelled:
                task.status = StagingStatus.FAILED
            task.error_message = str(e)
            
            return StagingResult(
                success=False,
                task_id=task_id,
                staging_directory=task.staging_directory,
                error_message=str(e)
            )
        finally:
            # 從活動任務集合中移除
            self.active_tasks.discard(task_id)
    
    async def _execute_task_operations(self, task: StagingTask):
        """執行任務操作（複製檔案和驗證）"""
        # 複製檔案
        await self._copy_files(task)
        
        # 驗證檔案完整性
        if task.verify_integrity:
            await self._verify_file_integrity(task)
    
    async def _copy_files(self, task: StagingTask):
        """複製檔案"""
        loop = asyncio.get_event_loop()
        
        for file_info in task.file_infos:
            try:
                # 在執行器中複製檔案
                await loop.run_in_executor(
                    self.executor,
                    self._copy_single_file,
                    task,
                    file_info
                )
                
                # 更新進度
                task.processed_size += file_info.size
                task.progress = (task.processed_size / task.total_size) * 100 if task.total_size > 0 else 100
                
            except Exception as e:
                file_info.error_message = str(e)
    
    def _copy_single_file(self, task: StagingTask, file_info: FileInfo):
        """複製單個檔案"""
        try:
            dest_path = task.staging_directory / file_info.relative_path
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(file_info.source_path, dest_path)
            file_info.copied = True
            
            # 計算校驗和
            if task.verify_integrity:
                file_info.checksum = self._calculate_checksum(file_info.source_path)
                
        except Exception as e:
            file_info.error_message = str(e)
    
    async def _verify_file_integrity(self, task: StagingTask) -> bool:
        """驗證檔案完整性"""
        loop = asyncio.get_event_loop()
        all_verified = True
        
        for file_info in task.file_infos:
            if file_info.copied and file_info.checksum:
                try:
                    dest_path = task.staging_directory / file_info.relative_path
                    dest_checksum = await loop.run_in_executor(
                        self.executor,
                        self._calculate_checksum,
                        dest_path
                    )
                    
                    file_info.verified = (file_info.checksum == dest_checksum)
                    if not file_info.verified:
                        all_verified = False
                        
                except Exception:
                    file_info.verified = False
                    all_verified = False
        
        return all_verified
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """計算檔案校驗和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _check_disk_space(self, required_space: int) -> bool:
        """檢查磁碟空間"""
        try:
            disk_usage = shutil.disk_usage(self.base_staging_path)
            return disk_usage.free >= required_space * 1.1  # 保留10%緩衝
        except Exception:
            return True  # 無法檢查則假設足夠
    
    def get_task_status(self, task_id: str) -> Optional[StagingTask]:
        """取得任務狀態"""
        return self.tasks.get(task_id)
    
    def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """取得任務進度"""
        if task_id not in self.tasks:
            return {"error": "任務不存在"}
        
        task = self.tasks[task_id]
        return {
            "task_id": task_id,
            "product_name": task.product_name,
            "status": task.status.value,
            "progress": task.progress,
            "total_files": len(task.file_infos),
            "processed_files": sum(1 for info in task.file_infos if info.copied),
            "staging_directory": str(task.staging_directory),
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "error_message": task.error_message
        }
    
    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任務"""
        return [self.get_task_progress(task_id) for task_id in self.tasks.keys()]
    
    async def cleanup_staging_directory(self, task_id: str) -> bool:
        """清理暫存目錄"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        try:
            if task.staging_directory.exists():
                shutil.rmtree(task.staging_directory)
            return True
        except Exception:
            return False
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的舊任務"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        tasks_to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task.status in [StagingStatus.COMPLETED, StagingStatus.FAILED] and
                task.completed_at and task.completed_at < cutoff_time):
                
                # 清理暫存目錄
                try:
                    if task.staging_directory.exists():
                        shutil.rmtree(task.staging_directory)
                except Exception:
                    pass
                
                tasks_to_remove.append(task_id)
        
        # 移除任務記錄
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任務
        
        Args:
            task_id: 任務ID
            
        Returns:
            是否成功取消
        """
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        if task.status in [StagingStatus.COMPLETED, StagingStatus.FAILED]:
            return False
        
        task.cancelled = True
        task.status = StagingStatus.CANCELLED
        task.error_message = "任務被取消"
        
        return True
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """獲取服務統計資料"""
        total_tasks = len(self.tasks)
        completed_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.COMPLETED)
        failed_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.FAILED)
        cancelled_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.CANCELLED)
        
        success_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        total_data_mb = sum(task.total_size for task in self.tasks.values()) / (1024 * 1024)
        
        return {
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "cancelled_tasks": cancelled_tasks,
            "pending_tasks": sum(1 for task in self.tasks.values() if task.status == StagingStatus.PENDING),
            "running_tasks": sum(1 for task in self.tasks.values() if task.status == StagingStatus.RUNNING),
            "success_rate": round(success_rate, 2),
            "total_data_processed_mb": round(total_data_mb, 2),
            "active_tasks_count": len(self.active_tasks),
            "max_workers": self.max_workers
        }
    
    def __del__(self):
        """清理資源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
    
    async def _try_trigger_code_comparison_after_staging(self, task: StagingTask):
        """
        暫存完成後嘗試觸發代碼比較任務
        只有當暫存路徑為 d:\temp 相關路徑時才觸發
        """
        try:
            staging_path = str(task.staging_directory)
            normalized_path = staging_path.lower().replace('/', '\\')
            
            # 檢查是否為 temp 路徑
            if 'd:\\temp' not in normalized_path and 'temp' not in normalized_path.lower():
                logger.debug(f"暫存路徑不是 temp 路徑，跳過觸發: {staging_path}")
                return
            
            logger.info(f"🎯 偵測到 temp 暫存完成，準備觸發代碼比較: {staging_path}")
            
            try:
                from backend.shared.infrastructure.adapters.download_completion_handler import get_download_completion_handler, TriggerReason
                
                handler = get_download_completion_handler()
                
                # 觸發處理
                event_id = await handler.handle_download_completion(
                    file_path=staging_path,
                    trigger_reason=TriggerReason.STAGING_COMPLETE,
                    metadata={
                        'source': 'file_staging_service',
                        'task_id': task.task_id,
                        'product_name': task.product_name,
                        'total_files': len(task.file_infos),
                        'staging_completed_at': task.completed_at.isoformat() if task.completed_at else None
                    }
                )
                
                logger.info(f"✅ 暫存後代碼比較觸發成功: event_id={event_id}")
                
            except ImportError as e:
                logger.debug(f"下載完成處理器不可用，跳過自動觸發: {e}")
            except Exception as e:
                logger.warning(f"暫存後自動觸發代碼比較失敗: {e}")
                
        except Exception as e:
            logger.debug(f"檢查暫存後代碼比較觸發時出錯: {e}")