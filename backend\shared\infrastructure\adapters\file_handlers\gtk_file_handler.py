"""
GTK 廠商檔案處理器
對應 VBA 的 CopyFilesGTK 和 CopyFilesGTK2 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class GTKFileHandler(BaseFileHandler):
    """
    🔧 簡化後的GTK廠商檔案處理器

    簡化邏輯：
    1. 只從 \GTK\temp\ 搜尋包含 MO 的壓縮檔
    2. 選擇日期最新的檔案
    3. 移除複雜的LOT搜尋和資料夾複製邏輯
    """
    
    def __init__(self, source_base_path: str):
        """初始化 GTK 檔案處理器"""
        super().__init__(source_base_path, "GTK")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        🔧 簡化後的GTK路徑：只使用temp目錄
        """
        return [
            self.source_base_path / "GTK" / "temp",  # 唯一搜尋路徑
        ]
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        🔧 簡化後的GTK搜尋模式：只搜尋包含MO的檔案
        """
        if mo:
            return [f"*{mo}*"]  # 只搜尋包含MO的檔案
        return []
        
    def _supports_folder_copy(self) -> bool:
        """🔧 簡化後：GTK不再支援資料夾複製"""
        return False
        
    def copy_files(self, file_name: str, file_temp: str,
                  pd: str = "default", lot: str = "default") -> bool:
        """
        🔧 簡化後的GTK邏輯：只搜尋包含MO的最新壓縮檔

        簡化策略：
        1. 只在 temp 目錄搜尋包含 MO 的壓縮檔
        2. 選擇日期最新的檔案
        3. 移除LOT搜尋和資料夾複製邏輯
        """
        self.logger.info(f"GTK 檔案處理開始 (簡化模式): MO={file_name}")

        # file_temp 已經是最終的目標目錄，直接使用
        destination_path = Path(file_temp)
        destination_path.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"建立目標資料夾: {destination_path}")

        # 🔧 簡化：只從 temp 目錄搜尋包含MO的最新壓縮檔
        temp_path = self.source_base_path / "GTK" / "temp"
        if self._safe_path_exists(temp_path):
            self.logger.info(f"🔍 GTK 搜尋路徑: {temp_path}")

            # 使用優化的MO搜尋方法
            if self._copy_latest_mo_archive(temp_path, destination_path, file_name):
                return True
        else:
            self.logger.warning(f"GTK temp 路徑不存在: {temp_path}")

        self.logger.error(f"GTK 檔案處理失敗: 在 temp 目錄找不到包含 MO '{file_name}' 的壓縮檔")
        return False

    def _copy_latest_mo_archive(self, source_path: Path, destination_path: Path, mo: str) -> bool:
        """
        🔧 GTK專用：搜尋包含MO的最新壓縮檔

        Args:
            source_path: 搜尋路徑
            destination_path: 目標路徑
            mo: MO編號

        Returns:
            bool: 成功返回True，失敗返回False
        """
        try:
            # 搜尋包含MO的壓縮檔
            pattern = f"*{mo}*"
            self.logger.info(f"      🔍 GTK搜尋模式: {pattern}")

            # 找到所有符合的檔案
            all_files = list(source_path.glob(pattern))
            archive_files = [f for f in all_files if f.suffix.lower() in self.archive_extensions]

            self.logger.info(f"      📁 找到 {len(all_files)} 個符合模式的檔案")
            self.logger.info(f"      📦 其中 {len(archive_files)} 個是壓縮檔")

            if not archive_files:
                self.logger.warning(f"      ❌ 沒有找到包含 '{mo}' 的壓縮檔")
                return False

            # 🔧 關鍵：選擇日期最新的壓縮檔
            latest_file = max(archive_files, key=lambda f: f.stat().st_mtime)

            self.logger.info(f"      ✅ 選擇最新的壓縮檔: {latest_file.name}")
            self.logger.info(f"      📅 檔案修改時間: {latest_file.stat().st_mtime}")

            # 複製檔案
            destination_file = destination_path / latest_file.name

            import shutil
            shutil.copy2(latest_file, destination_file)

            self.logger.info(f"      ✅ GTK檔案複製成功: {latest_file.name}")
            return True

        except Exception as e:
            self.logger.error(f"      ❌ GTK最新壓縮檔搜尋失敗: {e}")
            return False