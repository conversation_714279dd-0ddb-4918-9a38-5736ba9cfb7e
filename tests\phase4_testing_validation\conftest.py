"""Phase 4: Testing and Validation - Shared Test Configuration

This module provides comprehensive test fixtures and configuration for Phase 4
testing and validation of the dependency injection refactoring.
"""

import pytest
import asyncio
from unittest.mock import Mock, MagicMock, AsyncMock
from typing import Optional, Dict, Any
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Import the actual application and dependencies
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from frontend.api.dependencies import (
        get_api_state, get_staging_service, get_processing_service,
        require_product_search_service, require_llm_search_service
    )
    from frontend.api.ft_eqc_api import app as main_app
except ImportError as e:
    # Handle import errors gracefully for testing
    print(f"Warning: Could not import dependencies: {e}")
    get_api_state = None
    get_staging_service = None
    get_processing_service = None
    require_product_search_service = None
    require_llm_search_service = None
    main_app = None


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_api_state():
    """Provide a mock API state for testing."""
    mock = Mock()
    mock.increment_request_count = Mock()
    mock.increment_error_count = Mock()
    mock.get_request_count = Mock(return_value=100)
    mock.get_error_count = Mock(return_value=5)
    mock.is_healthy = Mock(return_value=True)
    return mock


@pytest.fixture
def mock_staging_service():
    """Provide a mock file staging service for testing."""
    mock = Mock()
    mock.service_id = "test_staging_service"
    mock.is_healthy = Mock(return_value=True)
    mock.get_status = Mock(return_value={"status": "healthy", "tasks": 0})
    mock.create_staging_task = AsyncMock(return_value={"task_id": "test-task-123"})
    mock.get_task_status = AsyncMock(return_value={"status": "completed"})
    return mock


@pytest.fixture
def mock_processing_service():
    """Provide a mock file processing service for testing."""
    mock = Mock()
    mock.service_id = "test_processing_service"
    mock.is_healthy = Mock(return_value=True)
    mock.get_status = Mock(return_value={"status": "healthy", "tasks": 0})
    mock.process_files = AsyncMock(return_value={"task_id": "test-process-456"})
    mock.get_task_status = AsyncMock(return_value={"status": "completed"})
    return mock


@pytest.fixture
def mock_product_search_service():
    """Provide a mock product search service for testing."""
    mock = Mock()
    mock.service_id = "test_product_search_service"
    mock.is_healthy = Mock(return_value=True)
    mock.search_products = AsyncMock(return_value={"results": [], "task_id": "search-789"})
    mock.get_task_status = AsyncMock(return_value={"status": "completed", "results": []})
    return mock


@pytest.fixture
def mock_llm_search_service():
    """Provide a mock LLM search service for testing."""
    mock = Mock()
    mock.service_id = "test_llm_search_service"
    mock.is_healthy = Mock(return_value=True)
    mock.smart_search = AsyncMock(return_value={"results": [], "task_id": "llm-search-101"})
    mock.get_search_results = AsyncMock(return_value={"status": "completed", "results": []})
    return mock


@pytest.fixture
def test_app(
    mock_api_state,
    mock_staging_service,
    mock_processing_service,
    mock_product_search_service,
    mock_llm_search_service
):
    """Create a test FastAPI application with dependency overrides."""
    app = FastAPI(title="Test App - Phase 4 Validation")

    # Only override dependencies if they are available
    if get_api_state:
        app.dependency_overrides[get_api_state] = lambda: mock_api_state
    if get_staging_service:
        app.dependency_overrides[get_staging_service] = lambda: mock_staging_service
    if get_processing_service:
        app.dependency_overrides[get_processing_service] = lambda: mock_processing_service
    if require_product_search_service:
        app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service
    if require_llm_search_service:
        app.dependency_overrides[require_llm_search_service] = lambda: mock_llm_search_service

    # Include the main app's routes if available
    if main_app:
        app.mount("/", main_app)

    yield app

    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest.fixture
def test_client(test_app):
    """Provide a test client for synchronous testing."""
    return TestClient(test_app)


@pytest.fixture
async def async_client(test_app):
    """Provide an async client for asynchronous testing."""
    async with AsyncClient(app=test_app, base_url="http://testserver") as client:
        yield client


@pytest.fixture
def sample_staging_request():
    """Provide sample staging request data."""
    return {
        "source_files": [
            "/test/path/file1.csv",
            "/test/path/file2.xlsx"
        ],
        "target_directory": "/test/output",
        "options": {
            "preserve_structure": True,
            "validate_files": True
        }
    }


@pytest.fixture
def sample_search_request():
    """Provide sample search request data."""
    return {
        "query": "test product search",
        "filters": {
            "category": "electronics",
            "price_range": [100, 1000]
        },
        "limit": 10
    }


@pytest.fixture
def sample_smart_search_request():
    """Provide sample smart search request data."""
    return {
        "query": "Find products similar to iPhone",
        "context": "mobile phones",
        "max_results": 5
    }


# Test constants
TEST_TASK_ID = "test-task-12345"
TEST_PRODUCT_NAME = "TestProduct"
TEST_SEARCH_QUERY = "test search query"

# API endpoint paths for testing
API_ENDPOINTS = {
    "STAGING_CREATE": "/api/staging/create",
    "STAGING_STATUS": "/api/staging/status/{task_id}",
    "SEARCH_PRODUCT": "/api/search/product",
    "SEARCH_TASK_STATUS": "/api/search/task/{task_id}",
    "SMART_SEARCH_GET": "/api/smart-search",
    "SMART_SEARCH_POST": "/api/smart-search",
    "TASK_STATUS": "/api/task/status/{task_id}",
    "CELERY_HEALTH": "/api/celery/health",
    "DASHBOARD": "/dashboard",
    "TEST_SIMPLE": "/api/test/simple"
}
