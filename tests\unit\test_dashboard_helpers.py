"""
測試儀表板輔助函數模組
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from backend.monitoring.utils.dashboard_helpers import (
    dashboard_error_handler,
    dashboard_async_error_handler,
    dashboard_performance_monitor,
    dashboard_async_performance_monitor,
    dashboard_monitor,
    dashboard_async_monitor,
    format_timestamp,
    calculate_percentage,
    format_bytes,
    format_duration,
    DashboardPerformanceTracker,
    safe_batch_operation,
    create_dashboard_log_context,
    log_dashboard_operation
)
from backend.shared.infrastructure.logging.logger_manager import LogLevel


class TestErrorHandlingDecorators:
    """測試錯誤處理裝飾器"""
    
    def test_dashboard_error_handler_success(self):
        """測試錯誤處理裝飾器 - 成功情況"""
        @dashboard_error_handler(fallback_value="fallback")
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
    
    def test_dashboard_error_handler_with_exception(self):
        """測試錯誤處理裝飾器 - 異常情況"""
        @dashboard_error_handler(fallback_value="fallback", suppress_exceptions=True)
        def test_function():
            raise ValueError("Test error")
        
        result = test_function()
        assert result == "fallback"
    
    def test_dashboard_error_handler_reraise(self):
        """測試錯誤處理裝飾器 - 重新拋出異常"""
        @dashboard_error_handler(fallback_value="fallback", suppress_exceptions=False)
        def test_function():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError):
            test_function()
    
    @pytest.mark.asyncio
    async def test_dashboard_async_error_handler_success(self):
        """測試非同步錯誤處理裝飾器 - 成功情況"""
        @dashboard_async_error_handler(fallback_value="fallback")
        async def test_function():
            return "success"
        
        result = await test_function()
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_dashboard_async_error_handler_with_exception(self):
        """測試非同步錯誤處理裝飾器 - 異常情況"""
        @dashboard_async_error_handler(fallback_value="fallback", suppress_exceptions=True)
        async def test_function():
            raise ValueError("Test error")
        
        result = await test_function()
        assert result == "fallback"


class TestPerformanceMonitoring:
    """測試效能監控功能"""
    
    def test_dashboard_performance_monitor(self):
        """測試效能監控裝飾器"""
        @dashboard_performance_monitor(operation_name="test_operation")
        def test_function():
            time.sleep(0.1)  # 模擬一些處理時間
            return "result"
        
        result = test_function()
        assert result == "result"
    
    @pytest.mark.asyncio
    async def test_dashboard_async_performance_monitor(self):
        """測試非同步效能監控裝飾器"""
        @dashboard_async_performance_monitor(operation_name="test_async_operation")
        async def test_function():
            await asyncio.sleep(0.1)  # 模擬一些處理時間
            return "result"
        
        result = await test_function()
        assert result == "result"
    
    def test_dashboard_performance_tracker(self):
        """測試效能追蹤器"""
        tracker = DashboardPerformanceTracker("test_operation")
        
        with tracker:
            time.sleep(0.1)
        
        # 驗證追蹤器記錄了指標
        assert "operation" in tracker.metrics
        assert tracker.metrics["operation"] == "test_operation"
        assert "duration_seconds" in tracker.metrics
        assert tracker.metrics["duration_seconds"] > 0


class TestUtilityFunctions:
    """測試通用輔助函數"""
    
    def test_format_timestamp_datetime(self):
        """測試時間戳格式化 - datetime 物件"""
        dt = datetime(2023, 12, 25, 15, 30, 45)
        result = format_timestamp(dt)
        assert result == "2023-12-25 15:30:45"
    
    def test_format_timestamp_unix(self):
        """測試時間戳格式化 - Unix 時間戳"""
        timestamp = 1703516445  # 2023-12-25 15:30:45 UTC
        result = format_timestamp(timestamp)
        assert "2023-12-25" in result
    
    def test_format_timestamp_iso_string(self):
        """測試時間戳格式化 - ISO 字串"""
        iso_string = "2023-12-25T15:30:45Z"
        result = format_timestamp(iso_string)
        assert "2023-12-25" in result
    
    def test_format_timestamp_none(self):
        """測試時間戳格式化 - None 值"""
        result = format_timestamp(None)
        assert result == ""
    
    def test_calculate_percentage_normal(self):
        """測試百分比計算 - 正常情況"""
        result = calculate_percentage(25, 100)
        assert result == 25.0
    
    def test_calculate_percentage_zero_division(self):
        """測試百分比計算 - 除零情況"""
        result = calculate_percentage(25, 0, handle_zero_division=True)
        assert result == 0.0
    
    def test_calculate_percentage_zero_division_error(self):
        """測試百分比計算 - 除零錯誤"""
        with pytest.raises(ZeroDivisionError):
            calculate_percentage(25, 0, handle_zero_division=False)
    
    def test_format_bytes_zero(self):
        """測試位元組格式化 - 零值"""
        result = format_bytes(0)
        assert result == "0 B"
    
    def test_format_bytes_kilobytes(self):
        """測試位元組格式化 - KB"""
        result = format_bytes(1536)  # 1.5 KB
        assert "1.50 KiB" in result
    
    def test_format_bytes_megabytes(self):
        """測試位元組格式化 - MB"""
        result = format_bytes(1572864)  # 1.5 MB
        assert "1.50 MiB" in result
    
    def test_format_bytes_negative(self):
        """測試位元組格式化 - 負值"""
        result = format_bytes(-1024)
        assert result.startswith("-")
        assert "1.00 KiB" in result
    
    def test_format_duration_seconds(self):
        """測試持續時間格式化 - 秒"""
        result = format_duration(45)
        assert "45秒" in result
    
    def test_format_duration_minutes(self):
        """測試持續時間格式化 - 分鐘"""
        result = format_duration(125)  # 2分5秒
        assert "2分鐘" in result
        assert "5秒" in result
    
    def test_format_duration_hours(self):
        """測試持續時間格式化 - 小時"""
        result = format_duration(3665)  # 1小時1分5秒
        assert "1小時" in result
        assert "1分鐘" in result
        assert "5秒" in result
    
    def test_format_duration_timedelta(self):
        """測試持續時間格式化 - timedelta 物件"""
        td = timedelta(hours=2, minutes=30, seconds=15)
        result = format_duration(td)
        assert "2小時" in result
        assert "30分鐘" in result
        assert "15秒" in result
    
    def test_format_duration_english_units(self):
        """測試持續時間格式化 - 英文單位"""
        result = format_duration(3665, chinese_units=False)
        assert "1h" in result
        assert "1m" in result
        assert "5s" in result


class TestCompositeDecorators:
    """測試組合裝飾器"""
    
    def test_dashboard_monitor_success(self):
        """測試組合監控裝飾器 - 成功情況"""
        @dashboard_monitor(fallback_value="fallback", operation_name="test_monitor")
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
    
    def test_dashboard_monitor_with_exception(self):
        """測試組合監控裝飾器 - 異常情況"""
        @dashboard_monitor(fallback_value="fallback", suppress_exceptions=True)
        def test_function():
            raise ValueError("Test error")
        
        result = test_function()
        assert result == "fallback"
    
    @pytest.mark.asyncio
    async def test_dashboard_async_monitor_success(self):
        """測試非同步組合監控裝飾器 - 成功情況"""
        @dashboard_async_monitor(fallback_value="fallback", operation_name="test_async_monitor")
        async def test_function():
            return "success"
        
        result = await test_function()
        assert result == "success"


class TestBatchOperations:
    """測試批量操作"""
    
    @pytest.mark.asyncio
    async def test_safe_batch_operation_success(self):
        """測試安全批量操作 - 成功情況"""
        def operation1():
            return "result1"
        
        def operation2():
            return "result2"
        
        async def async_operation():
            return "async_result"
        
        operations = [operation1, operation2, async_operation]
        results = await safe_batch_operation(operations, batch_size=2)
        
        assert len(results) == 3
        assert "result1" in results
        assert "result2" in results
        assert "async_result" in results
    
    @pytest.mark.asyncio
    async def test_safe_batch_operation_with_failures(self):
        """測試安全批量操作 - 包含失敗的情況"""
        def success_operation():
            return "success"
        
        def failure_operation():
            raise ValueError("Operation failed")
        
        operations = [success_operation, failure_operation]
        results = await safe_batch_operation(operations)
        
        assert len(results) == 2
        assert "success" in results
        assert None in results  # 失敗的操作返回 None


class TestLoggingHelpers:
    """測試日誌輔助函數"""
    
    def test_create_dashboard_log_context(self):
        """測試創建儀表板日誌上下文"""
        context = create_dashboard_log_context(
            "test_operation",
            "test_component",
            extra_field="extra_value"
        )
        
        assert context["dashboard_operation"] == "test_operation"
        assert context["dashboard_component"] == "test_component"
        assert context["extra_field"] == "extra_value"
        assert "timestamp" in context
    
    @patch('src.dashboard_monitoring.utils.dashboard_helpers.get_dashboard_logger_manager')
    def test_log_dashboard_operation(self, mock_get_logger_manager):
        """測試記錄儀表板操作日誌"""
        mock_logger_manager = MagicMock()
        mock_logger = MagicMock()
        mock_logger_manager.get_logger.return_value = mock_logger
        mock_get_logger_manager.return_value = mock_logger_manager
        
        log_dashboard_operation(
            "test_operation",
            "test_component",
            LogLevel.INFO,
            "Test message"
        )
        
        mock_logger_manager.get_logger.assert_called_once_with("dashboard_operations")
        mock_logger.info.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])