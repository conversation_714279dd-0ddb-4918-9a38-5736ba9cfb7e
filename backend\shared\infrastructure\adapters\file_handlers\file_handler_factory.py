"""
檔案處理器工廠
用於建立和管理各廠商的檔案處理器
"""

from typing import Optional, Dict, Type, List
import os
from pathlib import Path

from .base_file_handler import BaseFileHandler
from .gtk_file_handler import GTKFileHandler
from .xaht_file_handler import XAHTFileHandler
from .jcet_file_handler import JCETFileHandler
from .nfme_file_handler import NFMEFileHandler
from .etd_file_handler import ETDFileHandler
from .msec_file_handler import MSECFileHandler
from .tsht_file_handler import TSHTFileHandler
from .lingsen_file_handler import LINGSENFileHandler
from .chuzhou_file_handler import ChuzhouFileHandler
from .suqian_file_handler import SuqianFileHandler
from .nanotech_file_handler import NanotechFileHandler

from backend.shared.infrastructure.logging.logger_manager import LoggerManager


class FileHandlerFactory:
    """
    檔案處理器工廠
    
    負責：
    1. 註冊所有支援的廠商處理器
    2. 根據廠商代碼建立處理器實例
    3. 管理來源路徑設定
    """
    
    # 註冊的處理器類別
    _handlers: Dict[str, Type[BaseFileHandler]] = {
        'GTK': GTKFileHandler,
        'XAHT': XAHTFileHandler,
        'JCET': JCETFileHandler,
        'NFME': NFMEFileHandler,
        'ETD': ETDFileHandler,
        'ETREND': ETDFileHandler,  # ETD 的別名
        'MSEC': MSECFileHandler,
        'TSHT': TSHTFileHandler,
        'LINGSEN': LINGSENFileHandler,
        'CHUZHOU': ChuzhouFileHandler,
        'SUQIAN': SuqianFileHandler,
        'NANOTECH': NanotechFileHandler,
    }
    
    def __init__(self, source_base_path: Optional[str] = None):
        """
        初始化工廠
        
        Args:
            source_base_path: 來源基礎路徑，如果未提供則從環境變數讀取
        """
        self.logger = LoggerManager().get_logger("FileHandlerFactory")
        
        # 設定來源基礎路徑
        if source_base_path:
            self.source_base_path = self._normalize_unc_path(source_base_path)
        else:
            # 從環境變數讀取，使用 .env 中配置的路徑，默認使用網路路徑而非 /mnt/share
            env_path = os.getenv('FILE_SOURCE_BASE_PATH', r'\\192.168.1.60\test_log')
            self.source_base_path = self._normalize_unc_path(env_path)
            self.logger.debug(f"從環境變數讀取檔案來源路徑: {env_path}")
            
        self.logger.info(f"檔案處理器工廠已初始化，來源路徑: {self.source_base_path}")
        
        # 驗證路徑是否可訪問
        try:
            # UNC 路徑的存在檢查需要特殊處理
            if self._is_unc_path(str(self.source_base_path)):
                # 對於 UNC 路徑，嘗試列出目錄來驗證訪問性
                try:
                    list(self.source_base_path.iterdir())
                    self.logger.info(f"UNC 路徑驗證成功: {self.source_base_path}")
                except Exception as unc_e:
                    self.logger.warning(f"警告: UNC 路徑不存在或無法訪問: {self.source_base_path} - {unc_e}")
            else:
                # 本地路徑使用標準方法
                if hasattr(self.source_base_path, 'exists') and not self.source_base_path.exists():
                    self.logger.warning(f"警告: 來源路徑不存在或無法訪問: {self.source_base_path}")
        except Exception as e:
            self.logger.warning(f"無法驗證來源路徑: {e}")
        
    @classmethod
    def register_handler(cls, vendor_code: str, handler_class: Type[BaseFileHandler]):
        """
        註冊新的檔案處理器
        
        Args:
            vendor_code: 廠商代碼
            handler_class: 處理器類別
        """
        cls._handlers[vendor_code.upper()] = handler_class
        
    def create_handler(self, vendor_code: str) -> Optional[BaseFileHandler]:
        """
        建立檔案處理器
        
        Args:
            vendor_code: 廠商代碼
            
        Returns:
            檔案處理器實例，如果廠商不支援則返回 None
        """
        vendor_code_upper = vendor_code.upper()
        handler_class = self._handlers.get(vendor_code_upper)
        
        if handler_class:
            try:
                # 確保傳遞正確格式的字符串路徑
                source_path = str(self.source_base_path)
                # 對 UNC 路徑進行二次檢查和修正
                if self._is_unc_path(source_path):
                    source_path = self._ensure_unc_format(source_path)
                    
                handler = handler_class(source_path)
                self.logger.info(f"已建立 {vendor_code_upper} 檔案處理器，來源路徑: {source_path}")
                
                # 對 ETD 顯示完整路徑模式
                if vendor_code_upper == "ETD":
                    self.logger.info(f"ETD 完整路徑模式:")
                    self.logger.info(f"  1. {source_path}\\ETD\\FT\\{{pd}}\\ (搜尋包含mo檔名的檔案)")
                    self.logger.info(f"  2. {source_path}\\Etrend\\FT\\{{pd}}\\{{lot}}\\ (搜尋包含mo檔名的檔案)")
                
                return handler
            except Exception as e:
                self.logger.error(f"建立 {vendor_code_upper} 處理器失敗: {e}")
                return None
        else:
            self.logger.warning(f"不支援的廠商代碼: {vendor_code}")
            return None
            
    @classmethod
    def get_supported_vendors(cls) -> List[str]:
        """取得支援的廠商列表"""
        return list(cls._handlers.keys())
        
    def process_vendor_files(self, vendor_code: str, mo: str,
                           temp_path: str, pd: str = "default",
                           lot: str = "default", email_subject: str = "",
                           email_body: str = "") -> Dict[str, any]:
        """
        處理廠商檔案的便利方法

        Args:
            vendor_code: 廠商代碼
            mo: MO 編號
            temp_path: 暫存路徑
            pd: 產品名稱
            lot: 批號
            email_subject: 郵件主旨（用於路徑判斷）
            email_body: 郵件內文（用於路徑判斷）

        Returns:
            處理結果字典
        """
        result = {
            'success': False,
            'vendor': vendor_code,
            'mo': mo,
            'temp_path': temp_path,
            'error': None
        }
        
        # 建立處理器
        handler = self.create_handler(vendor_code)
        if not handler:
            result['error'] = f"不支援的廠商: {vendor_code}"
            self.logger.error(f"不支援的廠商代碼: {vendor_code}")
            return result
            
        # 記錄完整路徑資訊（特別是ETD）
        try:
            if vendor_code.upper() == 'ETD' and hasattr(handler, 'get_source_paths'):
                etd_paths = handler.get_source_paths(pd, lot, mo)
                self.logger.info(f"ETD 完整路徑模式:")
                for i, path in enumerate(etd_paths, 1):
                    self.logger.info(f"  路徑 {i}: {path}")
                self.logger.info(f"ETD 路徑解釋: 基本路徑\\ETD\\FT\\{pd}\\{lot} 或 基本路徑\\Etrend\\FT\\{pd}\\{lot}")
        except Exception as path_log_error:
            self.logger.warning(f"記錄路徑資訊失敗: {path_log_error}")
            
        # 執行檔案複製
        try:
            # 檢查處理器是否支援 email_subject 和 email_body 參數
            import inspect
            sig = inspect.signature(handler.copy_files)

            # 準備基本參數
            kwargs = {
                'file_name': mo,
                'file_temp': temp_path,
                'pd': pd,
                'lot': lot
            }

            # 只有當處理器支援時才添加額外參數
            if 'email_subject' in sig.parameters:
                kwargs['email_subject'] = email_subject
            if 'email_body' in sig.parameters:
                kwargs['email_body'] = email_body

            self.logger.info(f"開始處理 {vendor_code} 檔案: MO={mo}, PD={pd}, LOT={lot}")
            success = handler.copy_files(**kwargs)
            
            # 檢查處理器返回值
            if success is None:
                result['error'] = f"檔案處理器 {vendor_code} 返回 None 結果"
                self.logger.error(f"檔案處理器返回 None: vendor={vendor_code}, mo={mo}, pd={pd}, lot={lot}")
                result['success'] = False
            else:
                result['success'] = success
                if not success:
                    result['error'] = "檔案複製失敗"
                    self.logger.error(f"檔案複製失敗: vendor={vendor_code}, mo={mo}, pd={pd}, lot={lot}")
                else:
                    self.logger.info(f"檔案複製成功: vendor={vendor_code}, mo={mo}, pd={pd}, lot={lot}")
                
        except Exception as e:
            import traceback
            result['error'] = str(e)
            result['success'] = False
            self.logger.error(f"處理 {vendor_code} 檔案時發生錯誤: {e}")
            self.logger.error(f"錯誤詳情: {traceback.format_exc()}")
            
        return result
            
    def _normalize_unc_path(self, path_str: str) -> Path:
        """
        規範化 UNC 路徑格式
        
        Args:
            path_str: 原始路徑字符串
            
        Returns:
            規範化後的 Path 對象
        """
        if self._is_unc_path(path_str):
            # 確保 UNC 路徑格式正確
            normalized = self._ensure_unc_format(path_str)
            self.logger.debug(f"UNC 路徑規範化: {path_str} -> {normalized}")
            return Path(normalized)
        else:
            return Path(path_str)
            
    def _is_unc_path(self, path_str: str) -> bool:
        """
        檢查是否為 UNC 路徑
        
        Args:
            path_str: 路徑字符串
            
        Returns:
            是否為 UNC 路徑
        """
        return path_str.startswith('\\\\') or path_str.startswith('\\')
        
    def _ensure_unc_format(self, path_str: str) -> str:
        """
        確保 UNC 路徑格式正確
        
        Args:
            path_str: 原始 UNC 路徑
            
        Returns:
            格式正確的 UNC 路徑
        """
        # 移除多餘的反斜槓並重新構建
        path_clean = path_str.strip().replace('/', '\\')
        
        # 處理各種可能的格式
        if path_clean.startswith('\\\\\\'):
            # 移除過多的反斜槓
            parts = [p for p in path_clean.split('\\') if p]
            return '\\\\' + '\\'.join(parts)
        elif path_clean.startswith('\\\\'):
            # 已經是正確格式
            return path_clean
        elif path_clean.startswith('\\'):
            # 只有兩個反斜槓，需要補充
            return '\\' + path_clean
        else:
            # 不是 UNC 路徑，返回原樣
            return path_str