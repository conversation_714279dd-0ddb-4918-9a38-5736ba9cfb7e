#!/usr/bin/env python3
"""
整合服務啟動腳本 - Vue 前端遷移優化版
同時啟動 Flask (前端模組化架構) + FastAPI (FT-EQC 處理) 服務
增強版：包含深度健康檢查、服務發現和Vue遷移準備功能
"""

import os
import subprocess
import sys
import time
import signal
import threading
import asyncio
import json
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 設定預設端口
FLASK_PORT = 5000
FASTAPI_PORT = 8010

# 顏色代碼用於終端輸出
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_colored(text: str, color: str = Colors.WHITE):
    """印出彩色文字"""
    print(f"{color}{text}{Colors.END}")

def print_status(status: str, message: str, details: str = ""):
    """印出狀態信息"""
    if status == "success":
        print_colored(f"[OK] {message}", Colors.GREEN)
    elif status == "warning":
        print_colored(f"[WARN] {message}", Colors.YELLOW)
    elif status == "error":
        print_colored(f"[ERROR] {message}", Colors.RED)
    elif status == "info":
        print_colored(f"[INFO] {message}", Colors.BLUE)
    
    if details:
        print_colored(f"   {details}", Colors.WHITE)

def check_environment_variables() -> Dict[str, Any]:
    """檢查環境變數配置"""
    try:
        # 確保載入 .env 檔案
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path, override=True)
            print_status("success", f"已載入環境變數檔案: {env_path}")
        else:
            print_status("warning", "未找到 .env 檔案，使用系統環境變數")
        
        # 檢查必要的環境變數
        required_vars = {
            'EMAIL_ADDRESS': os.getenv('EMAIL_ADDRESS'),
            'EMAIL_PASSWORD': os.getenv('EMAIL_PASSWORD'),
            'POP3_SERVER': os.getenv('POP3_SERVER'),
            'POP3_PORT': os.getenv('POP3_PORT'),
        }
        
        # 檢查可選的 LINE 通知變數
        optional_vars = {
            'LINE_CHANNEL_ACCESS_TOKEN': os.getenv('LINE_CHANNEL_ACCESS_TOKEN'),
            'LINE_USER_ID': os.getenv('LINE_USER_ID'),
        }
        
        missing_required = [k for k, v in required_vars.items() if not v]
        missing_optional = [k for k, v in optional_vars.items() if not v]
        
        if missing_required:
            print_status("error", "缺少必要的環境變數", f"請在 .env 檔案中設定: {', '.join(missing_required)}")
            return {"success": False, "missing_required": missing_required, "missing_optional": missing_optional}
        
        if missing_optional:
            print_status("warning", "缺少可選的環境變數", f"LINE 通知功能將被禁用: {', '.join(missing_optional)}")
        else:
            print_status("success", "LINE 通知環境變數已設定")
        
        print_status("success", "環境變數檢查完成")
        return {"success": True, "missing_required": [], "missing_optional": missing_optional}
        
    except Exception as e:
        print_status("error", f"環境變數檢查失敗: {e}")
        return {"success": False, "error": str(e)}

async def test_line_notification() -> Dict[str, Any]:
    """測試 LINE 通知服務"""
    try:
        from backend.shared.infrastructure.adapters.notification.line_notification_service import LineNotificationService
        
        print_status("info", "正在測試 LINE 通知服務...")
        
        # 初始化 LINE 服務
        line_service = LineNotificationService(auto_load_env=True)
        
        # 獲取服務狀態
        status = line_service.get_service_status()
        
        if status['enabled']:
            # 執行測試
            test_result = line_service.test_service()
            if test_result['success']:
                print_status("success", "LINE 通知服務測試成功", "已發送測試訊息")
                return {"success": True, "enabled": True}
            else:
                print_status("error", "LINE 通知服務測試失敗", test_result['message'])
                return {"success": False, "enabled": True, "error": test_result['message']}
        else:
            print_status("warning", "LINE 通知服務未啟用", status['config_error'])
            return {"success": True, "enabled": False, "reason": status['config_error']}
            
    except ImportError as e:
        print_status("warning", "無法載入 LINE 通知服務模組", str(e))
        return {"success": False, "error": f"Import error: {e}"}
    except Exception as e:
        print_status("error", "測試 LINE 通知時發生錯誤", str(e))
        return {"success": False, "error": str(e)}

async def test_email_connection() -> Dict[str, Any]:
    """測試郵件服務器連接"""
    try:
        from backend.shared.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService
        from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
        
        print_status("info", "正在測試郵件服務器連接...")
        
        # 初始化服務
        database = EmailDatabase()
        sync_service = EmailSyncService(database)
        
        # 測試連接
        result = await sync_service.test_connection()
        
        if result['success']:
            unread_count = result['data'].get('unread_count', 0)
            print_status("success", f"郵件服務器連接成功", f"未讀郵件: {unread_count} 封")
            return {"success": True, "unread_count": unread_count}
        else:
            print_status("error", "郵件服務器連接失敗", result['message'])
            return {"success": False, "error": result['message']}
            
    except ImportError as e:
        print_status("warning", "無法載入郵件服務模組", f"請確認環境設置正確: {e}")
        return {"success": False, "error": f"Import error: {e}"}
    except Exception as e:
        print_status("error", "測試郵件連接時發生錯誤", str(e))
        return {"success": False, "error": str(e)}

def check_frontend_modules() -> Dict[str, Any]:
    """檢查前端模組化架構是否正確載入"""
    try:
        print_status("info", "正在檢查前端模組化架構...")
        
        # 檢查主要模組
        modules_to_check = [
            'frontend.app',
            'frontend.config',
            'frontend.email.routes.email_routes',
            'frontend.analytics.routes.analytics_routes',
            'frontend.eqc.routes.eqc_routes',
            'frontend.tasks.routes.task_routes',
            'frontend.monitoring.routes.monitoring_routes',
            'frontend.file_management.routes.file_routes'
        ]
        
        failed_modules = []
        for module_name in modules_to_check:
            try:
                __import__(module_name)
                print_status("success", f"模組載入成功: {module_name}")
            except ImportError as e:
                failed_modules.append(f"{module_name}: {e}")
                print_status("error", f"模組載入失敗: {module_name}", str(e))
        
        if failed_modules:
            return {"success": False, "failed_modules": failed_modules}
        
        print_status("success", "所有前端模組載入成功")
        return {"success": True, "modules_checked": len(modules_to_check)}
        
    except Exception as e:
        print_status("error", f"前端模組檢查失敗: {e}")
        return {"success": False, "error": str(e)}

def validate_new_architecture() -> Dict[str, Any]:
    """驗證新架構的完整性"""
    try:
        print_status("info", "正在驗證新架構完整性...")
        
        # 檢查關鍵目錄結構
        required_paths = [
            Path("frontend/app.py"),
            Path("frontend/config.py"),
            Path("frontend/shared/templates"),
            Path("frontend/shared/static"),
            Path("frontend/email/routes"),
            Path("frontend/analytics/routes"),
            Path("frontend/eqc/routes"),
            Path("frontend/tasks/routes"),
            Path("frontend/monitoring/routes"),
            Path("frontend/file_management/routes")
        ]
        
        missing_paths = []
        for path in required_paths:
            if not path.exists():
                missing_paths.append(str(path))
                print_status("error", f"缺少必要路徑: {path}")
            else:
                print_status("success", f"路徑驗證成功: {path}")
        
        if missing_paths:
            return {"success": False, "missing_paths": missing_paths}
        
        # 測試Flask應用創建
        try:
            from frontend.app import create_app
            app = create_app('development')
            print_status("success", "Flask應用創建成功")
            
            # 檢查關鍵路由
            with app.test_client() as client:
                response = client.get('/health')
                if response.status_code == 200:
                    print_status("success", "健康檢查端點正常")
                else:
                    print_status("warning", f"健康檢查端點異常: {response.status_code}")
            
        except Exception as e:
            print_status("error", f"Flask應用創建失敗: {e}")
            return {"success": False, "error": f"Flask app creation failed: {e}"}
        
        print_status("success", "新架構驗證完成")
        return {"success": True, "architecture": "modular_frontend"}

    except Exception as e:
        print_status("error", f"架構驗證失敗: {e}")
        return {"success": False, "error": str(e)}

def start_auto_email_sync():
    """啟動自動郵件同步（60秒間隔）"""
    def start_sync_in_background():
        try:
            # 等待服務完全啟動
            time.sleep(10)

            print_status("info", "正在啟動自動郵件同步...")

            # 導入郵件同步服務
            from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
            from backend.shared.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService

            # 初始化服務
            database = EmailDatabase()
            sync_service = EmailSyncService(database)

            # 啟動自動同步（60秒間隔）
            result = sync_service.start_auto_sync(60)

            if result['success']:
                print_status("success", "自動郵件同步已啟動", "間隔: 60秒")
            else:
                print_status("warning", "自動郵件同步啟動失敗", result['message'])

        except Exception as e:
            print_status("error", f"啟動自動郵件同步時發生錯誤: {e}")

    # 在背景線程中啟動
    sync_thread = threading.Thread(target=start_sync_in_background, daemon=True)
    sync_thread.start()
    print_status("info", "自動郵件同步啟動程序已在背景執行")

def deployment_readiness_check() -> Dict[str, Any]:
    """部署就緒性檢查"""
    try:
        print_status("info", "正在執行部署就緒性檢查...")
        
        checks = []
        
        # 1. 環境變數檢查
        env_check = check_environment_variables()
        checks.append(("environment", env_check['success']))
        
        # 2. 前端模組檢查
        frontend_check = check_frontend_modules()
        checks.append(("frontend_modules", frontend_check['success']))
        
        # 3. 架構驗證
        arch_check = validate_new_architecture()
        checks.append(("architecture", arch_check['success']))
        
        # 4. 資料庫連接檢查
        try:
            from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
            db = EmailDatabase()
            db_check = True
            print_status("success", "資料庫連接檢查通過")
        except Exception as e:
            db_check = False
            print_status("error", f"資料庫連接檢查失敗: {e}")
        checks.append(("database", db_check))
        
        # 5. 磁碟空間檢查
        try:
            import shutil
            total, used, free = shutil.disk_usage(Path.cwd())
            free_gb = free // (1024**3)
            if free_gb > 1:  # 至少1GB可用空間
                print_status("success", f"磁碟空間充足: {free_gb}GB 可用")
                disk_check = True
            else:
                print_status("warning", f"磁碟空間不足: 僅 {free_gb}GB 可用")
                disk_check = False
        except Exception as e:
            print_status("error", f"磁碟空間檢查失敗: {e}")
            disk_check = False
        checks.append(("disk_space", disk_check))
        
        # 總結檢查結果
        failed_checks = [name for name, status in checks if not status]
        passed_checks = [name for name, status in checks if status]
        
        if failed_checks:
            print_status("warning", f"部分檢查失敗: {', '.join(failed_checks)}")
            return {
                "success": False,
                "failed_checks": failed_checks,
                "passed_checks": passed_checks,
                "total_checks": len(checks)
            }
        else:
            print_status("success", "所有部署就緒性檢查通過")
            return {
                "success": True,
                "passed_checks": passed_checks,
                "total_checks": len(checks)
            }
            
    except Exception as e:
        print_status("error", f"部署就緒性檢查失敗: {e}")
        return {"success": False, "error": str(e)}

def format_log_line(line: str, highlight_errors: bool = True) -> str:
    """格式化和高亮日誌行"""
    if not line.strip():
        return line
    
    # 移除 ANSI 顏色代碼（如果有的話）
    clean_line = re.sub(r'\x1b\[[0-9;]*m', '', line)
    
    if not highlight_errors:
        return clean_line
    
    # 高亮錯誤信息
    if any(keyword in clean_line.lower() for keyword in ['error', 'failed', 'exception', '失敗', '錯誤']):
        return f"{Colors.RED}{clean_line}{Colors.END}"
    
    # 高亮警告信息
    if any(keyword in clean_line.lower() for keyword in ['warning', 'warn', '警告']):
        return f"{Colors.YELLOW}{clean_line}{Colors.END}"
    
    # 高亮同步相關信息
    if any(keyword in clean_line for keyword in ['同步完成', 'sync complete', '郵件已同步']):
        return f"{Colors.GREEN}{clean_line}{Colors.END}"
    
    # 高亮重要信息
    if any(keyword in clean_line for keyword in ['INFO', 'info', '資訊']):
        return f"{Colors.CYAN}{clean_line}{Colors.END}"
    
    return clean_line

class SyncMonitor:
    """同步狀態監控器 - Vue遷移架構優化版"""
    
    def __init__(self, flask_port: int):
        self.flask_port = flask_port
        self.sync_stats = {
            'total_success': 0,
            'total_errors': 0,
            'last_check': None,
            'error_details': [],
            'frontend_health': None,
            'module_status': {}
        }
        self.running = False
        self.thread = None
    
    def start(self):
        """啟動監控"""
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        print_status("info", "同步狀態監控已啟動")
    
    def stop(self):
        """停止監控"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=2)
    
    def _monitor_loop(self):
        """監控循環"""
        while self.running:
            try:
                self._check_sync_status()
                time.sleep(60)  # 每分鐘檢查一次
            except Exception as e:
                print_status("warning", f"監控檢查失敗: {e}")
                time.sleep(30)  # 錯誤後較短間隔
    
    def _check_sync_status(self):
        """檢查同步狀態 - 增強版支援新架構"""
        try:
            import requests
            
            # 1. 檢查健康狀態
            try:
                health_response = requests.get(f"http://localhost:{self.flask_port}/health", timeout=5)
                if health_response.status_code == 200:
                    health_data = health_response.json()
                    self.sync_stats['frontend_health'] = health_data.get('status', 'unknown')
                    self.sync_stats['module_status'] = health_data.get('modules', {})
                else:
                    self.sync_stats['frontend_health'] = 'unhealthy'
            except requests.exceptions.RequestException:
                self.sync_stats['frontend_health'] = 'unreachable'
            
            # 2. 呼叫同步狀態 API (維持向後兼容)
            try:
                sync_response = requests.get(f"http://localhost:{self.flask_port}/api/sync/status", timeout=5)
                
                if sync_response.status_code == 200:
                    data = sync_response.json()
                    stats = data.get('sync_stats', {})
                    
                    current_errors = stats.get('sync_errors', 0)
                    last_error_details = stats.get('last_error_details', [])
                    
                    # 檢查是否有新的錯誤
                    if current_errors > self.sync_stats['total_errors']:
                        new_errors = current_errors - self.sync_stats['total_errors']
                        print_status("warning", f"檢測到 {new_errors} 個新的同步錯誤")
                        
                        # 顯示錯誤詳情
                        for error in last_error_details[-new_errors:]:
                            subject = error.get('subject', 'Unknown')[:50]
                            sender = error.get('sender', 'Unknown')[:30]
                            error_msg = error.get('error', 'Unknown')[:100]
                            print_status("error", f"郵件同步失敗", f"主題: {subject}..., 寄件者: {sender}, 錯誤: {error_msg}")
                    
                    self.sync_stats['total_errors'] = current_errors
                    
            except requests.exceptions.RequestException:
                # API可能不存在，但健康檢查可以工作
                pass
            
            # 3. 檢查新架構特定端點
            try:
                # 檢查email模組狀態
                email_response = requests.get(f"http://localhost:{self.flask_port}/email/api/status", timeout=3)
                if email_response.status_code == 200:
                    print_status("info", "郵件模組運行正常")
                
                # 檢查analytics模組狀態  
                analytics_response = requests.get(f"http://localhost:{self.flask_port}/analytics/api/status", timeout=3)
                if analytics_response.status_code == 200:
                    print_status("info", "分析模組運行正常")
                    
            except requests.exceptions.RequestException:
                # 新端點可能還未實現，這是正常的
                pass
                
            self.sync_stats['last_check'] = datetime.now().isoformat()
                
        except Exception as e:
            print_status("warning", f"同步狀態檢查失敗: {e}")
    
    def get_status_summary(self) -> Dict[str, Any]:
        """獲取狀態摘要"""
        return {
            "frontend_health": self.sync_stats.get('frontend_health'),
            "module_status": self.sync_stats.get('module_status', {}),
            "total_errors": self.sync_stats.get('total_errors', 0),
            "last_check": self.sync_stats.get('last_check'),
            "monitoring_active": self.running
        }

def display_usage_tips():
    """顯示使用提示 - Vue遷移版"""
    print_colored("\n使用提示:", Colors.BOLD)
    print_colored("  • 使用 --test-connection 測試郵件連接和 LINE 通知", Colors.WHITE)
    print_colored("  • 使用 --debug-sync 查看詳細同步日誌", Colors.WHITE)
    print_colored("  • 使用 --no-highlight 禁用顏色輸出", Colors.WHITE)
    print_colored("  • 使用 --no-monitor 禁用同步監控", Colors.WHITE)
    print_colored("  • 使用 --check-deployment 執行完整部署就緒性檢查", Colors.WHITE)
    print_colored("  • 按 Ctrl+C 優雅停止所有服務", Colors.WHITE)
    print_colored("\n架構功能:", Colors.BOLD)
    print_colored("  • 統一的模組化前端架構 (frontend/app.py)", Colors.WHITE)
    print_colored("  • 增強的健康檢查和服務發現", Colors.WHITE)
    print_colored("  • 深度部署驗證和依賴檢查", Colors.WHITE)
    print_colored("  • 自動模組載入驗證", Colors.WHITE)
    print_colored("\n故障排除:", Colors.BOLD)
    print_colored("  • 如果郵件同步失敗，檢查 .env 文件配置", Colors.WHITE)
    print_colored("  • 如果 LINE 通知失敗，檢查 LINE_CHANNEL_ACCESS_TOKEN 和 LINE_USER_ID", Colors.WHITE)
    print_colored("  • 如果模組載入失敗，檢查 frontend/ 目錄結構", Colors.WHITE)
    print_colored("  • 如果健康檢查失敗，使用 --check-deployment 診斷", Colors.WHITE)
    print_colored("  • 查看彩色日誌輸出以快速識別問題", Colors.WHITE)
    print_colored("\nLINE 通知設定:", Colors.BOLD)
    print_colored("  • LINE 通知是可選功能，缺少配置時會自動禁用", Colors.WHITE)
    print_colored("  • 設定 LINE_NOTIFY_PARSING_FAILURE=true 啟用失敗通知", Colors.WHITE)
    print_colored("  • 設定 LINE_NOTIFY_PARSING_SUCCESS=true 啟用成功通知", Colors.WHITE)

def start_integrated_service(test_connection: bool = False, highlight_errors: bool = True,
                             debug_sync: bool = False, monitor_sync: bool = True, 
                             check_deployment: bool = False):
    """啟動整合服務 - Vue遷移架構優化版"""
    print_colored("啟動整合服務 (Vue遷移架構版)...", Colors.BOLD)

    # 設置基本編碼環境
    try:
        print_status("info", "設置編碼環境...")
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        if os.name == 'nt':
            os.system('chcp 65001 >nul 2>&1')
        print_status("success", "編碼環境設置完成")
    except Exception as e:
        print_status("warning", f"編碼設置失敗，但繼續啟動: {e}")
    
    # 部署就緒性檢查
    if check_deployment:
        print_status("info", "執行完整部署就緒性檢查...")
        deployment_result = deployment_readiness_check()
        if not deployment_result['success']:
            print_status("error", "部署就緒性檢查失敗")
            print_status("error", f"失敗項目: {', '.join(deployment_result.get('failed_checks', []))}")
            return
        print_status("success", "部署就緒性檢查通過")
    
    # 環境變數檢查
    env_check_result = check_environment_variables()
    if not env_check_result['success']:
        print_status("error", "環境變數檢查失敗，無法啟動服務")
        if 'missing_required' in env_check_result:
            print_status("error", f"請設定必要環境變數: {', '.join(env_check_result['missing_required'])}")
        return
    
    # 前端架構驗證
    print_status("info", "驗證前端模組化架構...")
    arch_check = validate_new_architecture()
    if not arch_check['success']:
        print_status("error", "前端架構驗證失敗，無法啟動服務")
        if 'missing_paths' in arch_check:
            print_status("error", f"缺少必要路徑: {', '.join(arch_check['missing_paths'])}")
        return
    print_status("success", "前端架構驗證通過")
    
    # 郵件連接測試
    if test_connection:
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 測試郵件連接
            connection_result = loop.run_until_complete(test_email_connection())
            
            # 測試 LINE 通知（如果配置了的話）
            if not env_check_result.get('missing_optional'):
                line_result = loop.run_until_complete(test_line_notification())
                if line_result['enabled'] and not line_result['success']:
                    print_status("warning", "LINE 通知測試失敗，但繼續啟動服務")
            
            loop.close()
            
            if not connection_result['success']:
                print_status("warning", "郵件連接測試失敗，但繼續啟動服務", 
                           "建議檢查 .env 文件中的郵件配置")
        except Exception as e:
            print_status("warning", f"無法執行連接測試: {e}")
    
    sync_monitor = None
    process = None
    
    try:
        # 使用新的模組化前端架構，設置編碼環境
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'
        env['FLASK_ENV'] = 'development'  # 可以根據需要調整
        
        # 直接使用新的模組化前端架構
        print_status("info", "使用新的模組化前端架構")
        
        # 使用統一的模組化前端架構
        start_args = [
                sys.executable, "-c",
                f"""
import sys
import os
from pathlib import Path

# 設定UTF-8編碼環境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'
os.environ['FLASK_ENV'] = 'development'

# 添加專案根目錄到Python路徑
sys.path.insert(0, str(Path.cwd()))

try:
    from frontend.app import create_app
    import threading
    import time
    
    print("正在創建Flask應用 (統一架構)...")
    app = create_app('development')
    
    # 啟動FastAPI服務 (背景執行)
    def start_fastapi_service():
        try:
            import subprocess
            import sys
            print("正在啟動FastAPI服務...")
            # 修復：重新啟用 FastAPI 服務（模組導入問題已解決）
            subprocess.run([
                sys.executable, "-m", "uvicorn",
                "frontend.eqc.routes.ft_eqc_api:app",
                "--host", "0.0.0.0",
                "--port", "{FASTAPI_PORT}",
                "--workers", "1"
            ])
        except Exception as e:
            print(f"FastAPI啟動失敗: {{e}}")
    
    # 在背景啟動FastAPI
    fastapi_thread = threading.Thread(target=start_fastapi_service, daemon=True)
    fastapi_thread.start()
    
    print(f"正在啟動Flask服務 (端口: {FLASK_PORT})...")
    # 啟動Flask應用
    app.run(host='0.0.0.0', port={FLASK_PORT}, debug=False, use_reloader=False)
    
except Exception as e:
    print(f"應用啟動失敗: {{e}}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
            ]

        process = subprocess.Popen(start_args, 
                                 stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True,
                                 encoding='utf-8', errors='replace', env=env)
        
        print_status("success", f"整合服務已啟動 (PID: {process.pid})")
        print_colored(f"[郵件] 收件夾: http://localhost:{FLASK_PORT}", Colors.GREEN)
        print_colored(f"[API] FT-EQC 處理: http://localhost:{FASTAPI_PORT}/ui", Colors.GREEN)
        print_colored(f"[文檔] FT-EQC API 說明: http://localhost:{FASTAPI_PORT}/docs", Colors.GREEN)
        
        if monitor_sync:
            # 等待服務啟動後再啟動監控
            time.sleep(5)
            sync_monitor = SyncMonitor(FLASK_PORT)
            sync_monitor.start()

        # 啟動自動郵件同步（60秒間隔）
        start_auto_email_sync()
        
        print_colored("\n按 Ctrl+C 停止服務...", Colors.YELLOW)
        print_colored("=" * 60, Colors.BLUE)
        
        # 實時輸出日誌
        for line in iter(process.stdout.readline, ''):
            formatted_line = format_log_line(line.strip(), highlight_errors)
            if formatted_line:
                # 如果是調試模式，顯示所有日誌
                if debug_sync:
                    print(formatted_line)
                else:
                    # 只顯示重要的日誌
                    if any(keyword in line.lower() for keyword in 
                          ['error', 'warning', 'failed', 'success', '同步', 'sync', '啟動', 'started']):
                        print(formatted_line)
        
        process.wait()
        
    except KeyboardInterrupt:
        print_colored("\n[停止] 正在停止服務...", Colors.YELLOW)
        
        if sync_monitor:
            sync_monitor.stop()
            
        if process:
            process.terminate()
            process.wait()
        print_status("success", "服務已停止")
        
    except Exception as e:
        print_status("error", f"啟動服務失敗: {e}")
        if sync_monitor:
            sync_monitor.stop()

def start_separate_services():
    """分別啟動兩個服務（使用新的模組化架構）"""
    print_colored("[啟動] 分離服務模式 (模組化架構)...", Colors.BOLD)
    
    processes = []
    
    try:
        # 設定編碼環境
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'
        env['FLASK_ENV'] = 'development'
        
        # 啟動 Flask 服務 (使用新的模組化架構)
        flask_process = subprocess.Popen([
            sys.executable, "-c", f"""
import sys
import os
from pathlib import Path

# 設定UTF-8編碼環境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'
os.environ['FLASK_ENV'] = 'development'

# 添加專案根目錄到Python路徑
sys.path.insert(0, str(Path.cwd()))

try:
    from frontend.app import create_app
    print("正在創建Flask應用 (模組化架構)...")
    app = create_app('development')
    print(f"正在啟動Flask服務 (端口: {FLASK_PORT})...")
    app.run(host='0.0.0.0', port={FLASK_PORT}, debug=False, use_reloader=False)
except Exception as e:
    print(f"Flask應用啟動失敗: {{e}}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
        ], env=env)
        processes.append(("Flask", flask_process))
        
        # 等待 Flask 服務啟動
        time.sleep(2)
        
        # 啟動 FastAPI 服務 (修復：重新啟用，模組導入問題已解決)
        print_status("info", "啟動 FastAPI 服務（模組導入問題已解決）")
        fastapi_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "frontend.eqc.routes.ft_eqc_api:app",
            "--host", "0.0.0.0",
            "--port", str(FASTAPI_PORT),
            "--workers", "1"
        ], env=env)
        processes.append(("FastAPI", fastapi_process))
        
        print_status("success", f"Flask 服務已啟動 (PID: {flask_process.pid})")
        print_status("success", f"FastAPI 服務已啟動 (PID: {fastapi_process.pid})")
        print_colored(f"[郵件] 收件夾: http://localhost:{FLASK_PORT}", Colors.GREEN)
        print_colored(f"[API] FT-EQC 處理: http://localhost:{FASTAPI_PORT}/ui", Colors.GREEN)
        print_colored(f"[文檔] FT-EQC API 說明: http://localhost:{FASTAPI_PORT}/docs", Colors.GREEN)
        print_colored("\n按 Ctrl+C 停止所有服務...", Colors.YELLOW)
        
        # 等待所有進程
        while True:
            time.sleep(1)
            for name, process in processes:
                if process.poll() is not None:
                    print_status("warning", f"{name} 服務意外停止")
                    return
        
    except KeyboardInterrupt:
        print_colored("\n[停止] 正在停止所有服務...", Colors.YELLOW)
        for name, process in processes:
            process.terminate()
            process.wait()
            print_status("success", f"{name} 服務已停止")
    except Exception as e:
        print_status("error", f"啟動服務失敗: {e}")
        for name, process in processes:
            if process.poll() is None:
                process.terminate()

def main():
    """主函數"""
    import argparse
    
    global FLASK_PORT, FASTAPI_PORT
    
    parser = argparse.ArgumentParser(description='整合服務啟動器 - Vue前端遷移優化版')
    parser.add_argument('--mode', choices=['integrated', 'separate'], default='integrated',
                       help='啟動模式: integrated (整合) 或 separate (分離)')
    parser.add_argument('--flask-port', type=int, default=FLASK_PORT,
                       help=f'Flask 服務端口 (預設: {FLASK_PORT})')
    parser.add_argument('--fastapi-port', type=int, default=FASTAPI_PORT,
                       help=f'FastAPI 服務端口 (預設: {FASTAPI_PORT})')
    
    # 診斷和測試選項
    parser.add_argument('--test-connection', action='store_true',
                       help='啟動前測試郵件服務器連接和 LINE 通知服務')
    parser.add_argument('--debug-sync', action='store_true',
                       help='啟用詳細的同步調試輸出')
    parser.add_argument('--no-highlight', action='store_true',
                       help='禁用錯誤高亮顯示')
    parser.add_argument('--no-monitor', action='store_true',
                       help='禁用同步狀態監控')
    
    # 新增的部署和架構選項
    parser.add_argument('--check-deployment', action='store_true',
                       help='執行完整的部署就緒性檢查')
    parser.add_argument('--validate-architecture', action='store_true',
                       help='僅驗證新的模組化架構，不啟動服務')
    
    args = parser.parse_args()
    
    FLASK_PORT = args.flask_port
    FASTAPI_PORT = args.fastapi_port
    
    print_colored("=" * 60, Colors.BLUE)
    print_colored("郵件收件夾 + FT-EQC 整合服務 (Vue遷移架構版)", Colors.BOLD)
    print_colored("=" * 60, Colors.BLUE)
    print_colored(f"模式: {args.mode}", Colors.WHITE)
    print_colored(f"Flask 端口: {FLASK_PORT}", Colors.WHITE)
    print_colored(f"FastAPI 端口: {FASTAPI_PORT}", Colors.WHITE)
    
    # 僅驗證架構模式
    if args.validate_architecture:
        print_colored("[檢查] 僅驗證新的模組化架構...", Colors.CYAN)
        arch_result = validate_new_architecture()
        if arch_result['success']:
            print_status("success", "架構驗證通過，可以安全啟動服務")
        else:
            print_status("error", "架構驗證失敗")
            if 'missing_paths' in arch_result:
                print_status("error", f"缺少路徑: {', '.join(arch_result['missing_paths'])}")
        return
    
    # 顯示啟用的功能
    features = []
    if args.test_connection:
        features.append("連接測試")
    if args.debug_sync:
        features.append("詳細同步日誌")
    if not args.no_highlight:
        features.append("錯誤高亮")
    if not args.no_monitor:
        features.append("同步監控")
    if args.check_deployment:
        features.append("部署就緒性檢查")
    
    if features:
        print_colored(f"啟用功能: {', '.join(features)}", Colors.CYAN)
    
    print_colored("=" * 60, Colors.BLUE)
    
    # 顯示使用提示
    display_usage_tips()
    
    if args.mode == 'integrated':
        start_integrated_service(
            test_connection=args.test_connection,
            highlight_errors=not args.no_highlight,
            debug_sync=args.debug_sync,
            monitor_sync=not args.no_monitor,
            check_deployment=args.check_deployment
        )
    else:
        start_separate_services()

if __name__ == '__main__':
    main()