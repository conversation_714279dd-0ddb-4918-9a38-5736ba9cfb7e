#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EQC 程式碼差異檢測器 - 簡化版
只保留核心 FindDifferentColumn 功能，專注於檢測程式碼區間（第298-335欄）

基於 VBA FindDifferentColumn 函數的核心邏輯：
1. 找 Golden EQC 檔案
2. 檢測程式碼差異區間
3. 驗證條件（start1 > 10 && (end1 - start1) > 5）
"""

import os
import logging
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime
from .code_region_detailed_detector import CodeRegionDetailedDetector
from ..ft_eqc_grouping_processor import FileFilterConfig


class EQCSimpleDetector:
    """
    EQC 程式碼差異檢測器 - 簡化版
    完全對應 VBA FindDifferentColumn 函數核心邏輯
    移[EXCEPT_CHAR]所有複雜的 Phase 2/3 功能，專注於核心檢測
    """
    
    def __init__(self):
        self.min_start_column = 10  # 從第10欄開始檢測
        self.min_consecutive_threshold = 230  # 連續整數閾值
        self.skip_columns = 3  # 跳過前3個基準欄位
        self.filter_config = FileFilterConfig()  # 整合環境變數排[EXCEPT_CHAR]機制
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)

    def find_code_region(self, folder_path: str, code_regions: dict = None) -> Dict[str, Any]:
        """
        在指定資料夾中找到程式碼區間（目標：第298-335欄）並檢測備用區間
        
        Args:
            folder_path: 包含 CSV 檔案的資料夾路徑
            code_regions: 前端指定的程式碼區間設定，優先使用此設定
            
        Returns:
            Dict: 檢測結果，包含主要程式碼區間和備用區間
        """
        print("[SEARCH] [eqc_simple_detector.py] find_code_region() - 開始執行")
        try:
            self.logger.info(f"開始檢測程式碼區間: {folder_path}")
            
            # 功能替換原則：優先使用前端指定的程式碼區間
            # 支援多種參數格式：main_start/main_end 或 code_start/code_end
            main_start = code_regions.get('main_start') or code_regions.get('code_start') if code_regions else None
            main_end = code_regions.get('main_end') or code_regions.get('code_end') if code_regions else None
            
            if code_regions and main_start is not None and main_end is not None:
                self.logger.info("[TARGET] 使用前端指定的程式碼區間設定")
                start1 = main_start - 1  # 轉換為0-based索引
                end1 = main_end - 1
                start1x = 1  # 標記為有效
                
                self.logger.info(f"   前端設定: 第{main_start}-{main_end}欄")
                self.logger.info(f"   內部索引: start1={start1}, end1={end1}")
                
                # 條件驗證
                is_valid = self._validate_code_range(start1, end1)
                if not is_valid:
                    self.logger.warning(f"[WARNING] 前端指定的程式碼區間驗證失敗，將使用自動檢測")
                    # 繼續執行自動檢測
                else:
                    # 直接返回前端設定的結果
                    # 處理備用區間（如果前端有設定）
                    backup_start = code_regions.get('backup_start')
                    backup_end = code_regions.get('backup_end')
                    
                    if backup_start is not None and backup_end is not None:
                        backup_region = {
                            'found': True,
                            'backup_start_column': backup_start,
                            'backup_end_column': backup_end,
                            'backup_start1': backup_start - 1,  # 索引
                            'backup_end1': backup_end - 1,
                            'source': 'frontend_specified'
                        }
                        self.logger.info(f"   備用區間設定: 第{backup_start}-{backup_end}欄")
                    else:
                        backup_region = {
                            'found': False,
                            'source': 'frontend_not_specified'
                        }
                    
                    return {
                        'status': 'success',
                        'message': f'成功使用前端指定的程式碼區間: 第{start1+1}-{end1+1}欄',
                        'folder_path': folder_path,
                        'golden_eqc_file': None,  # 前端指定時不需要檢測檔案
                        'code_region': {
                            'start1': start1,
                            'end1': end1,
                            'column_count': end1 - start1 + 1,
                            'start1x': start1x,
                            'start_column_number': start1 + 1,  # Excel 欄位編號
                            'end_column_number': end1 + 1,
                            'source': 'frontend_specified'
                        },
                        'backup_region': backup_region
                    }
            
            # 自動檢測模式
            self.logger.info("[SEARCH] 執行自動程式碼區間檢測")
            
            # 步驟 1: 找 Golden EQC 檔案
            golden_eqc_file = self._find_golden_eqc_file(folder_path)
            if not golden_eqc_file:
                return {
                    'status': 'no_golden_eqc',
                    'message': '未找到有效的 Golden EQC 檔案',
                    'folder_path': folder_path
                }
            
            self.logger.info(f"[OK] 找到 Golden EQC 檔案: {os.path.basename(golden_eqc_file)}")
            
            # 步驟 2: 執行核心差異檢測
            start1, end1, start1x = self._find_different_column(golden_eqc_file)
            
            if start1x == 0:
                return {
                    'status': 'no_difference_found',
                    'message': '未找到有效的程式碼差異',
                    'golden_eqc_file': golden_eqc_file,
                    'folder_path': folder_path
                }
            
            # 步驟 3: 條件驗證
            is_valid = self._validate_code_range(start1, end1)
            
            # 步驟 4: 新增備用區間檢測
            backup_region = self._find_backup_region(golden_eqc_file, start1, end1)
            
            self.logger.info(f"[OK] 程式碼區間檢測完成:")
            self.logger.info(f"   主要差異區間: 第{start1+1}-{end1+1}欄 (長度: {end1-start1+1})")
            self.logger.info(f"   精確差異位置: 第{start1x+1}欄")
            self.logger.info(f"   條件驗證: {'[OK] 通過' if is_valid else '[ERROR] 失敗'}")
            
            if backup_region['found']:
                self.logger.info(f"[OK] 備用區間檢測完成:")
                self.logger.info(f"   備用區間起始: 第{backup_region['backup_start_column']}欄")
                self.logger.info(f"   備用區間長度: {backup_region['backup_range_length']} 個欄位")
                self.logger.info(f"   匹配欄位數: {backup_region['matched_fields_count']}")
            else:
                self.logger.info(f"[WARNING] 未找到備用區間")
            
            return {
                'status': 'success',
                'golden_eqc_file': golden_eqc_file,
                'code_region': {
                    'start1': start1,
                    'end1': end1,
                    'start1x': start1x,
                    'range_length': end1 - start1 + 1,
                    'column_count': end1 - start1 + 1,  # 統一使用 column_count
                    'start_column_number': start1 + 1,  # Excel 欄位編號
                    'end_column_number': end1 + 1,
                    'precise_difference_column': start1x + 1,
                    'source': 'auto_detected'
                },
                'backup_region': backup_region,
                'validation': {
                    'is_valid_range': is_valid,
                    'start1_gt_10': start1 > 10,
                    'range_length_gt_5': (end1 - start1) > 5,
                    'ready_for_processing': is_valid
                },
                'processing_timestamp': datetime.now().isoformat(),
                'folder_path': folder_path
            }
            
        except Exception as e:
            self.logger.error(f"程式碼區間檢測失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e),
                'folder_path': folder_path
            }

    def _find_golden_eqc_file(self, folder_path: str) -> Optional[str]:
        """
        找 Golden EQC 檔案
        對應 VBA: CheckEQCBIN1CSVFile + FindCSVWith2Ones
        """
        print("[SEARCH] [eqc_simple_detector.py] _find_golden_eqc_file() - 開始執行")
        try:
            self.logger.info("[SEARCH] 搜尋 Golden EQC 檔案...")
            
            # 掃描所有 CSV 檔案（使用環境變數排[EXCEPT_CHAR]機制）
            csv_files = []
            for root, dirs, files in os.walk(folder_path):
                # 檢查是否應排[EXCEPT_CHAR]整個資料夾
                if self.filter_config.should_exclude_folder(root):
                    continue
                    
                for file in files:
                    if file.lower().endswith('.csv'):
                        file_path = os.path.join(root, file)
                        # 使用統一的檔案排[EXCEPT_CHAR]機制（替換硬編碼）
                        if self.filter_config.should_exclude_file(file_path):
                            continue
                        csv_files.append(file_path)
            
            self.logger.info(f"   找到 {len(csv_files)} 個 CSV 檔案")
            
            # 檢查每個檔案是否為 Golden EQC
            for csv_file in csv_files:
                if self._is_eqc_bin1_file(csv_file) and self._has_two_bin1_rows(csv_file):
                    self.logger.info(f"   [OK] Golden EQC: {os.path.basename(csv_file)}")
                    return csv_file
            
            return None
            
        except Exception as e:
            self.logger.error(f"搜尋 Golden EQC 檔案失敗: {e}")
            return None

    def _is_eqc_bin1_file(self, file_path: str) -> bool:
        """檢查是否為 EQC BIN1 檔案"""
        try:
            filename_lower = file_path.lower()
            # 檢查檔案名稱是否包含 EQC 相關關鍵字
            if 'eqcfaildata' in filename_lower or 'onlineeqc' in filename_lower:
                return True
                
            # 檢查檔案內容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return '(qc)' in content.lower() or 'onlineeqc' in content.lower()
            
        except Exception:
            return False

    def _has_two_bin1_rows(self, file_path: str) -> bool:
        """檢查是否包含兩個 BIN=1 行"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            bin1_count = 0
            data_start_row = 12  # 從第13行開始（索引12）
            
            for i in range(data_start_row, len(lines)):
                try:
                    elements = lines[i].split(',')
                    # 檢查第2欄 (elements[1]) 是否為 BIN=1
                    if len(elements) > 1 and elements[1].strip() == '1':
                        bin1_count += 1
                        if bin1_count >= 2:
                            return True
                except (IndexError, ValueError):
                    continue
            
            return False
            
        except Exception as e:
            # 調試：記錄檢查失敗的原因
            self.logger.debug(f"檢查 BIN=1 行失敗: {e}")
            return False

    def _find_different_column(self, csv_file_path: str) -> Tuple[int, int, int]:
        """
        核心差異檢測邏輯
        完全對應 VBA FindDifferentColumn 函數
        
        Returns:
            tuple: (start1, end1, start1x) 差異區間與最終位置
        """
        print("[SEARCH] [eqc_simple_detector.py] _find_different_column() - 開始執行")
        try:
            self.logger.info("[SEARCH] 執行程式碼差異檢測...")
            
            # 檢查檔案鎖定
            if self._is_file_locked(csv_file_path):
                self.logger.warning("   檔案被鎖定")
                return (0, 0, 0)
            
            # 讀取檔案內容
            file_content = self._read_file_content(csv_file_path)
            if not file_content:
                return (0, 0, 0)
            
            # 分析資料結構：找兩個 BIN=1 行
            rows = file_content.split('\n')
            a_row_index, b_row_index = self._find_bin1_rows(rows)
            
            if a_row_index == -1 or b_row_index == -1:
                self.logger.warning("   未找到兩個 BIN=1 行")
                return (0, 0, 0)
            
            self.logger.info(f"   找到 BIN=1 行: A行={a_row_index+1}, B行={b_row_index+1}")
            
            # 分割數值
            a_values = rows[a_row_index].split(',')
            b_values = rows[b_row_index].split(',')
            
            # 第一階段：連續整數區間檢測（使用 CodeRegionDetailedDetector）
            print("[SEARCH] [eqc_simple_detector.py] 第一階段: 調用 CodeRegionDetailedDetector")
            folder_path = os.path.dirname(csv_file_path)
            detailed_detector = CodeRegionDetailedDetector(folder_path)
            start2, end1 = detailed_detector.get_max_consecutive_region()
            if start2 == -1:
                self.logger.warning("   未找到連續整數區間")
                return (0, 0, 0)
            
            self.logger.info(f"   連續整數區間: 第{start2+1}-{end1+1}欄 (長度: {end1-start2+1})")
            self.logger.info(f"   [SEARCH] 第一階段結果: start2={start2}, end1={end1}")
            
            # 第二階段：第一次差異檢測
            start1 = self._find_first_difference(a_values, b_values, start2, end1)
            self.logger.info(f"   [SEARCH] 第二階段結果: start1={start1}")
            
            # 第三階段：第二次區間檢測與最終定位
            start1x = self._find_second_difference_range(a_values, b_values, end1)
            self.logger.info(f"   [SEARCH] 第三階段結果: start1x={start1x}")
            
            self.logger.info(f"   差異檢測結果: start1={start1+1}, end1={end1+1}, start1x={start1x+1}")
            self.logger.info(f"   [TARGET] 最終返回值: (start1={start1}, end1={end1}, start1x={start1x})")
            
            return (start1, end1, start1x)
            
        except Exception as e:
            self.logger.error(f"差異檢測失敗: {e}")
            return (0, 0, 0)

    def _is_file_locked(self, file_path: str) -> bool:
        """檢查檔案是否被鎖定"""
        try:
            with open(file_path, 'r+', encoding='utf-8') as f:
                pass
            return False
        except (IOError, PermissionError):
            return True

    def _read_file_content(self, file_path: str) -> Optional[str]:
        """讀取檔案內容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            return None

    def _find_bin1_rows(self, rows: List[str]) -> Tuple[int, int]:
        """尋找兩個 BIN=1 的資料行"""
        data_start_row = 12  # 從第13行開始
        a_row_index = -1
        b_row_index = -1
        
        num_rows = len(rows) - data_start_row
        
        # 尋找第一個 BIN=1 行
        for i in range(num_rows):
            try:
                row_index = i + data_start_row
                if row_index >= len(rows):
                    break
                    
                line_elements = rows[row_index].split(',')
                if len(line_elements) > 1 and line_elements[1].strip() == '1':
                    a_row_index = row_index
                    
                    # 尋找第二個 BIN=1 行
                    for j in range(i + 1, num_rows):
                        b_row_index_candidate = j + data_start_row
                        if b_row_index_candidate >= len(rows):
                            break
                            
                        b_line_elements = rows[b_row_index_candidate].split(',')
                        if len(b_line_elements) > 1 and b_line_elements[1].strip() == '1':
                            b_row_index = b_row_index_candidate
                            break
                    break
            except (IndexError, ValueError):
                continue
        
        return (a_row_index, b_row_index)


    def _find_first_difference(self, a_values: List[str], b_values: List[str], 
                              start2: int, end1: int) -> int:
        """在第一個連續區間中尋找差異"""
        # 從 start2+3 開始比較，跳過基準欄位
        for i in range(start2 + self.skip_columns, end1 + 1):
            try:
                if i < len(a_values) and i < len(b_values):
                    a_val = self._safe_float_conversion(a_values[i])
                    b_val = self._safe_float_conversion(b_values[i])
                    
                    if a_val != b_val:
                        return i
            except (IndexError, ValueError):
                continue
        
        return end1  # 如果沒有找到差異，回傳區間結束位置

    def _find_second_difference_range(self, a_values: List[str], b_values: List[str], 
                                     end1: int) -> int:
        """尋找第二個連續整數區間並檢測差異"""
        # 第二次連續整數區間檢測
        max_count = 0
        current_count = 0
        start1x = -1
        end1x = -1
        
        for i in range(end1, len(a_values)):
            try:
                if self._is_numeric(a_values[i]):
                    temp_val = float(a_values[i])
                    
                    if temp_val == int(temp_val):
                        current_count += 1
                        
                        if current_count > max_count:
                            max_count = current_count
                            start1x = i - max_count + 1
                            end1x = i
                    else:
                        current_count = 0
                else:
                    current_count = 0
            except (ValueError, IndexError):
                current_count = 0
        
        # 在第二個區間中尋找差異
        if start1x != -1 and end1x != -1:
            for i in range(start1x, end1x + 1):
                try:
                    if i < len(a_values) and i < len(b_values):
                        a_val = self._safe_float_conversion(a_values[i])
                        b_val = self._safe_float_conversion(b_values[i])
                        
                        if a_val != b_val:
                            return i
                except (IndexError, ValueError):
                    continue
        
        return start1x if start1x != -1 else 0

    def _is_numeric(self, value: str) -> bool:
        """檢查字串是否為數值"""
        try:
            float(value.strip())
            return True
        except (ValueError, AttributeError):
            return False

    def _safe_float_conversion(self, value: str) -> float:
        """安全的浮點數轉換"""
        try:
            return float(value.strip())
        except (ValueError, AttributeError):
            return 999.9  # 對應 VBA 的預設值

    def _validate_code_range(self, start1: int, end1: int) -> bool:
        """
        條件驗證
        對應 VBA: If start1 > 10 And (end1 - start1) > 5
        """
        is_valid = start1 > 10 and (end1 - start1) > 5
        
        self.logger.info("[SEARCH] 條件驗證:")
        self.logger.info(f"   start1 > 10: {start1} > 10 = {start1 > 10}")
        self.logger.info(f"   (end1 - start1) > 5: ({end1} - {start1}) > 5 = {(end1 - start1) > 5}")
        self.logger.info(f"   整體驗證結果: {is_valid}")
        
        return is_valid

    def _find_backup_region(self, golden_eqc_file: str, start1: int, end1: int) -> Dict[str, Any]:
        """
        找備用區間：從程式碼區間結束後找相同名稱的欄位（連續匹配模式）
        
        修改後邏輯：
        1. 取得主要區間的欄位名稱序列
        2. 從主要區間結束後開始搜尋
        3. 找到第一個匹配欄位作為起始點
        4. 連續檢查後續欄位是否匹配
        5. 遇到第一個不匹配就停止，只保留連續匹配部分
        
        Args:
            golden_eqc_file: Golden EQC 檔案路徑
            start1: 主要程式碼區間起始位置  
            end1: 主要程式碼區間結束位置
            
        Returns:
            Dict: 備用區間檢測結果（只包含連續匹配部分）
        """
        try:
            self.logger.info("[SEARCH] 開始備用區間檢測（連續匹配模式）...")
            
            with open(golden_eqc_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 讀取第8行（索引7）的完整欄位名稱
            if len(lines) <= 7:
                return {
                    'found': False,
                    'error': '檔案格式不正確，無法讀取第8行欄位名稱'
                }
            
            header_row = lines[7].split(',')
            all_column_names = [name.strip() for name in header_row]
            
            self.logger.info(f"   完整欄位名稱數量: {len(all_column_names)}")
            self.logger.info(f"   主要程式碼區間: 第{start1+1}-{end1+1}欄")
            
            # 取得主要程式碼區間的欄位名稱序列
            main_region_names = []
            for i in range(start1, min(end1 + 1, len(all_column_names))):
                col_name = all_column_names[i]
                main_region_names.append(col_name)
            
            self.logger.info(f"   主要區間欄位數: {len(main_region_names)}")
            
            # 從程式碼區間結束後開始搜尋
            search_start_index = end1 + 1
            self.logger.info(f"   從第{search_start_index+1}欄開始搜尋連續匹配...")
            
            # 找到第一個匹配位置作為備用區間起始點
            backup_start_index = -1
            backup_end_index = -1
            consecutive_matches = []
            
            # 搜尋第一個匹配位置
            for search_idx in range(search_start_index, len(all_column_names)):
                search_col_name = all_column_names[search_idx]
                
                # 檢查是否與主要區間任何欄位匹配
                if search_col_name in main_region_names and search_col_name.strip() != "":
                    backup_start_index = search_idx
                    self.logger.info(f"   [TARGET] 找到第一個匹配: 第{search_idx+1}欄 '{search_col_name}'")
                    break
            
            # 如果找到第一個匹配，繼續檢查連續匹配
            if backup_start_index != -1:
                current_idx = backup_start_index
                
                while current_idx < len(all_column_names):
                    current_col_name = all_column_names[current_idx]
                    
                    # 檢查當前欄位是否與主要區間匹配
                    if current_col_name in main_region_names and current_col_name.strip() != "":
                        # 找到匹配的主要區間位置
                        main_field_idx = main_region_names.index(current_col_name)
                        consecutive_matches.append({
                            'main_field': {
                                'index': start1 + main_field_idx,
                                'column_number': start1 + main_field_idx + 1,
                                'original_name': current_col_name
                            },
                            'backup_field': {
                                'index': current_idx,
                                'column_number': current_idx + 1,
                                'original_name': current_col_name
                            },
                            'match_type': 'consecutive'
                        })
                        
                        backup_end_index = current_idx
                        self.logger.info(f"   [OK] 連續匹配: 第{current_idx+1}欄 '{current_col_name}'")
                        current_idx += 1
                    else:
                        # 遇到不匹配，停止連續檢查
                        self.logger.info(f"   [STOP] 連續匹配中斷於第{current_idx+1}欄 '{current_col_name}'")
                        break
            
            # 生成結果
            if backup_start_index != -1 and consecutive_matches:
                backup_range_length = backup_end_index - backup_start_index + 1
                
                self.logger.info(f"[OK] 備用區間檢測成功（連續匹配模式）:")
                self.logger.info(f"   備用區間範圍: 第{backup_start_index+1}-{backup_end_index+1}欄")
                self.logger.info(f"   備用區間總長度: {backup_range_length} 個欄位")
                self.logger.info(f"   連續匹配欄位: {len(consecutive_matches)} 個")
                self.logger.info(f"   匹配覆蓋率: {len(consecutive_matches) / backup_range_length * 100:.1f}%")
                
                return {
                    'found': True,
                    'backup_start_index': backup_start_index,
                    'backup_end_index': backup_end_index,
                    'backup_start_column': backup_start_index + 1,
                    'backup_end_column': backup_end_index + 1,
                    'backup_range_length': backup_range_length,
                    'matched_fields': consecutive_matches,
                    'matched_fields_count': len(consecutive_matches),
                    'search_start_column': search_start_index + 1,
                    'match_statistics': {
                        'consecutive_matches': len(consecutive_matches),
                        'total_matches': len(consecutive_matches),
                        'coverage_percentage': len(consecutive_matches) / backup_range_length * 100,
                        'match_type': 'consecutive_matching'
                    }
                }
            else:
                self.logger.warning("[WARNING] 未找到任何連續匹配的備用區間欄位")
                return {
                    'found': False,
                    'message': '在程式碼區間後未找到任何連續匹配的欄位',
                    'search_start_column': search_start_index + 1,
                    'main_region_field_count': len(main_region_names)
                }
            
        except Exception as e:
            self.logger.error(f"備用區間檢測失敗: {e}")
            return {
                'found': False,
                'error': str(e)
            }
    


def main():
    """主程式入口 - 測試簡化版檢測器"""
    # 測試用資料夾路徑
    test_folder = "/mnt/d/project/python/outlook_summary/doc/20250523"
    
    print("[ROCKET] EQC 程式碼差異檢測器 - 簡化版")
    print("專注於核心 FindDifferentColumn 功能")
    print("=" * 60)
    
    detector = EQCSimpleDetector()
    result = detector.find_code_region(test_folder)
    
    print(f"\n[CHART] 檢測結果:")
    print(f"狀態: {result['status']}")
    
    if result['status'] == 'success':
        code_region = result['code_region']
        validation = result['validation']
        
        print(f"Golden EQC 檔案: {os.path.basename(result['golden_eqc_file'])}")
        print(f"程式碼區間: 第{code_region['start_column_number']}欄 - 第{code_region['end_column_number']}欄")
        print(f"區間長度: {code_region['range_length']} 個欄位")
        print(f"精確差異位置: 第{code_region['precise_difference_column']}欄")
        print(f"條件驗證: {'[OK] 通過' if validation['is_valid_range'] else '[ERROR] 失敗'}")
        
        if validation['is_valid_range']:
            print(f"[TARGET] 結果: 成功找到程式碼區間，可執行後續 EQC RT 資料插入")
        else:
            print(f"[WARNING] 結果: 程式碼區間不符合條件，跳過後續處理")
    else:
        print(f"錯誤訊息: {result.get('message', result.get('error_message', '未知錯誤'))}")


if __name__ == "__main__":
    main()