"""
測試郵件地址驗證修復
驗證郵件白名單管理器能正確處理包含顯示名稱的郵件地址格式
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import patch

from backend.shared.infrastructure.adapters.email.whitelist import EmailWhitelistManager
from backend.shared.infrastructure.adapters.email.models import WhitelistEntryType
from backend.shared.infrastructure.adapters.email.exceptions import EmailAddressValidationError


class TestEmailValidationFix:
    """測試郵件地址驗證修復功能"""

    def setup_method(self):
        """設定測試環境"""
        # 建立臨時目錄作為配置目錄
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir)
        
        # 建立白名單管理器
        with patch.dict('os.environ', {
            'EMAIL_WHITELIST_ENABLED': 'true',
            'EMAIL_WHITELIST_DEFAULT_ACTION': 'deny'
        }):
            self.whitelist_manager = EmailWhitelistManager(config_dir=self.config_dir)
    
    def teardown_method(self):
        """清理測試環境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_validate_pure_email_format(self):
        """測試驗證純郵件地址格式"""
        # 有效的純郵件地址
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for email in valid_emails:
            assert self.whitelist_manager._validate_email_format(email), f"應該接受有效郵件地址: {email}"
    
    def test_validate_display_name_email_format(self):
        """測試驗證包含顯示名稱的郵件地址格式"""
        # 有效的顯示名稱格式
        valid_display_emails = [
            'John Doe <<EMAIL>>',
            'Tong.Wu <<EMAIL>>',
            'User Name <<EMAIL>>',
            'Test User123 <<EMAIL>>',
            'Simple <<EMAIL>>'
        ]
        
        for email in valid_display_emails:
            assert self.whitelist_manager._validate_email_format(email), f"應該接受有效的顯示名稱郵件地址: {email}"
    
    def test_validate_invalid_email_formats(self):
        """測試拒絕無效的郵件地址格式"""
        invalid_emails = [
            'invalid-email',
            '@domain.com',
            'user@',
            'user@domain',
            'user <EMAIL>',  # 空格在本地部分
            '<<EMAIL>',     # 缺少右尖括號
            '<EMAIL>>',     # 缺少左尖括號
            'Name <invalid-email>',  # 尖括號內無效郵件
            ''
        ]
        
        for email in invalid_emails:
            assert not self.whitelist_manager._validate_email_format(email), f"應該拒絕無效郵件地址: {email}"
    
    def test_extract_email_address(self):
        """測試提取純郵件地址功能"""
        test_cases = [
            ('<EMAIL>', '<EMAIL>'),  # 純郵件地址
            ('Tong.Wu <<EMAIL>>', '<EMAIL>'),  # 顯示名稱格式
            ('John Doe <<EMAIL>>', '<EMAIL>'),
            ('Simple User <<EMAIL>>', '<EMAIL>')
        ]
        
        for input_email, expected in test_cases:
            result = self.whitelist_manager._extract_email_address(input_email)
            assert result == expected, f"從 '{input_email}' 應該提取出 '{expected}'，實際得到 '{result}'"
    
    def test_check_email_with_display_name(self):
        """測試檢查包含顯示名稱的郵件地址"""
        # 添加白名單條目
        self.whitelist_manager.add_entry('<EMAIL>', WhitelistEntryType.EMAIL)
        
        # 測試不同格式的相同郵件地址
        test_emails = [
            '<EMAIL>',              # 純郵件地址
            'Tong.Wu <<EMAIL>>',    # 顯示名稱格式
            'Tong Wu <<EMAIL>>',    # 不同顯示名稱
            'Mr. Tong <<EMAIL>>'    # 另一個顯示名稱
        ]
        
        for email in test_emails:
            result = self.whitelist_manager.check_email(email)
            assert result.is_whitelisted, f"郵件地址 '{email}' 應該在白名單中"
            assert result.email_address == email, f"結果應該保留原始郵件地址格式 '{email}'"
            assert result.matched_entry is not None, "應該有匹配的條目"
            assert result.matched_entry.pattern == '<EMAIL>', "匹配的條目應該是純郵件地址"
    
    def test_check_email_not_in_whitelist(self):
        """測試檢查不在白名單中的郵件地址"""
        # 添加白名單條目
        self.whitelist_manager.add_entry('<EMAIL>', WhitelistEntryType.EMAIL)
        
        # 測試不在白名單中的郵件地址
        test_emails = [
            '<EMAIL>',
            'User <<EMAIL>>',
            '<EMAIL>',
            'Test User <<EMAIL>>'
        ]
        
        for email in test_emails:
            result = self.whitelist_manager.check_email(email)
            assert not result.is_whitelisted, f"郵件地址 '{email}' 不應該在白名單中"
            assert result.matched_entry is None, "不應該有匹配的條目"
    
    def test_check_email_domain_whitelist(self):
        """測試域名白名單功能"""
        # 添加域名白名單條目
        self.whitelist_manager.add_entry('@gmt.com.tw', WhitelistEntryType.DOMAIN)
        
        # 測試同域名下的不同郵件地址
        test_emails = [
            '<EMAIL>',
            'User One <<EMAIL>>',
            '<EMAIL>',
            'Another User <<EMAIL>>',
            '<EMAIL>',
            'Tong.Wu <<EMAIL>>'
        ]
        
        for email in test_emails:
            result = self.whitelist_manager.check_email(email)
            assert result.is_whitelisted, f"域名白名單應該允許 '{email}'"
            assert result.matched_entry is not None, "應該有匹配的條目"
            assert result.matched_entry.pattern == '@gmt.com.tw', "匹配的條目應該是域名模式"
    
    def test_error_handling_for_invalid_format(self):
        """測試無效格式的錯誤處理"""
        invalid_emails = [
            'invalid-email',
            'Name <invalid-email>',
            '@domain.com',
            '<incomplete'
        ]
        
        for email in invalid_emails:
            with pytest.raises(EmailAddressValidationError) as exc_info:
                self.whitelist_manager.check_email(email)
            
            assert email in str(exc_info.value), "錯誤訊息應該包含導致錯誤的郵件地址"
            assert "郵件地址格式無效" in str(exc_info.value), "錯誤訊息應該說明格式無效"
    
    def test_whitespace_handling(self):
        """測試空白字符處理"""
        # 添加白名單條目
        self.whitelist_manager.add_entry('<EMAIL>', WhitelistEntryType.EMAIL)
        
        # 測試帶有不同空白字符的郵件地址
        test_emails = [
            ' <EMAIL> ',
            '  Test User <<EMAIL>>  ',
            '\tTest User\t<<EMAIL>>\t',
            'Test User < <EMAIL> >'  # 尖括號內有空格
        ]
        
        for email in test_emails:
            result = self.whitelist_manager.check_email(email)
            assert result.is_whitelisted, f"應該正確處理帶空白字符的郵件地址: '{email}'"
    
    def test_case_sensitivity(self):
        """測試大小寫敏感性"""
        # 添加小寫白名單條目
        self.whitelist_manager.add_entry('<EMAIL>', WhitelistEntryType.EMAIL)
        
        # 測試不同大小寫的郵件地址
        test_emails = [
            '<EMAIL>',
            '<EMAIL>',  # 大寫
            '<EMAIL>',  # 全大寫
            'User <<EMAIL>>',  # 顯示名稱格式大寫
        ]
        
        # 注意：郵件地址的本地部分（@前面）通常是大小寫敏感的
        # 但域名部分是大小寫不敏感的
        # 這裡的測試取決於 EmailWhitelistEntry.matches 方法的實現
        for email in test_emails:
            result = self.whitelist_manager.check_email(email)
            # 如果白名單條目匹配是大小寫不敏感的，這些應該都通過
            # 如果是大小寫敏感的，只有第一個會通過


if __name__ == '__main__':
    # 可以直接運行這個測試文件
    pytest.main([__file__, '-v'])