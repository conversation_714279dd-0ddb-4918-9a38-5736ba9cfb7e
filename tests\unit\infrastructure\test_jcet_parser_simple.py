"""
JCET 解析器簡化測試
基於 VBA JCETInfoFromStrings 邏輯，支援 KUI/GYC 模式解析，遵循 TDD 開發

VBA 邏輯參考：
- 識別條件：內文包含 "jcet" 或寄件者包含 "jcetglobal.com"
- 解析規則：尋找包含 KUI/GYC 的字串作為 MO，位置後的詞作為 lot 和 product
- 範例：FW: KUI Q48W91.1 B802-1V KUIC31N001-D003量?批次??良率98.4%
"""

import pytest
from datetime import datetime

from backend.email.models.email_models import EmailData
from backend.email.parsers.jcet_parser import JCETParser
from backend.email.parsers.base_parser import ParsingContext


class TestJCETParserSimple:
    """JCET 解析器簡化測試"""

    def setup_method(self):
        """每個測試方法前的設置"""
        self.parser = JCETParser()

    def test_jcet_parser_initialization(self):
        """測試 JCET 解析器初始化"""
        assert self.parser.vendor_name == "JCET"
        assert self.parser.vendor_code == "JCET"
        assert self.parser.get_confidence_threshold() == 0.8
        assert "jcet" in self.parser.supported_patterns
        assert "jcetglobal.com" in self.parser.supported_patterns

    def test_identify_vendor_jcet_in_body(self):
        """測試識別內文包含 'jcet' 的郵件"""
        # 基於 VBA 邏輯：InStr(1, LCase(body), "jcet", vbTextCompare) > 0
        email = EmailData(
            message_id="test-jcet-001",
            subject="Test Subject",
            sender="<EMAIL>",
            body="Processing report from JCET facility. Status: LOW YIELD",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "jcet" in result.matching_patterns
        assert result.vendor_code == "JCET"

    def test_identify_vendor_jcetglobal_sender(self):
        """測試識別寄件者包含 'jcetglobal.com' 的郵件"""
        # 基於 VBA 邏輯：InStr(1, LCase(senderAddress), "jcetglobal.com", vbTextCompare) > 0
        email = EmailData(
            message_id="test-jcet-002",
            subject="JCET Production Report",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "jcetglobal.com" in result.matching_patterns
        assert result.vendor_code == "JCET"

    def test_cannot_identify_non_jcet_email(self):
        """測試不能識別非 JCET 格式的郵件"""
        email = EmailData(
            message_id="test-non-jcet-001",
            subject="GTK FT HOLD MO:F123456 LOT:ABC123",
            sender="<EMAIL>",
            body="Test email body without any relevant keywords",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is False
        assert result.confidence_score < 0.8

    def test_parse_kui_pattern_long_word(self):
        """測試解析 KUI 模式：長度 > 4 的詞"""
        # 基於 VBA 邏輯：尋找包含 KUI 且長度 > 4 的詞，取前 15 字符作為 MO
        subject = "FW: KUI Q48W91.1 B802-1V KUIC31N001-D003量?批次??良率98.4%"
        
        result = self.parser.parse_jcet_keywords(subject)
        
        # 應該找到 KUIC31N001-D003，取前 15 字符
        assert result["mo_number"] == "KUIC31N001-D003"  # 實際長度 < 15，所以完整保留
        assert result["method"] == "kui_pattern_long"

    def test_parse_gyc_pattern_long_word(self):
        """測試解析 GYC 模式：長度 > 4 的詞"""
        # 基於 VBA 邏輯：尋找包含 GYC 且長度 > 4 的詞，取前 15 字符作為 MO
        subject = "Testing GYC12345678901234567890 Production Report"
        
        result = self.parser.parse_jcet_keywords(subject)
        
        # 應該找到 GYC12345678901234567890，取前 15 字符
        assert result["mo_number"] == "GYC123456789012"  # 前 15 字符
        assert result["method"] == "gyc_pattern_long"

    def test_parse_kui_pattern_short_word(self):
        """測試解析 KUI 模式：長度 <= 4 的詞，需要找後面的詞作為 lot 和 product"""
        # 基於 VBA 邏輯：如果關鍵字詞長度 <= 4，記錄位置，後面的詞作為 lot 和 product
        subject = "FW: KUI LOT123 PRODUCT_ABC Status Report"
        
        result = self.parser.parse_jcet_keywords(subject)
        
        # KUI 長度 = 3 <= 4，所以找後面的詞
        assert result["lot_number"] == "LOT123"     # KUI 後第一個詞
        assert result["product"] == "PRODUCT_ABC"   # KUI 後第二個詞
        assert result["method"] == "kui_pattern_short"

    def test_parse_gyc_pattern_short_word(self):
        """測試解析 GYC 模式：長度 <= 4 的詞"""
        subject = "Testing GYC BATCH456 CHIP_XYZ Processing"
        
        result = self.parser.parse_jcet_keywords(subject)
        
        # GYC 長度 = 3 <= 4，所以找後面的詞
        assert result["lot_number"] == "BATCH456"   # GYC 後第一個詞
        assert result["product"] == "CHIP_XYZ"     # GYC 後第二個詞
        assert result["method"] == "gyc_pattern_short"

    def test_parse_no_pattern_found(self):
        """測試解析找不到 KUI/GYC 模式時的處理"""
        subject = "Simple subject without any special keywords"
        
        result = self.parser.parse_jcet_keywords(subject)
        
        assert result["mo_number"] == ""
        assert result["lot_number"] == ""
        assert result["product"] == ""
        assert result["method"] == "no_pattern"

    def test_parse_email_complete_kui_long(self):
        """測試完整的 JCET 郵件解析：KUI 長模式"""
        email = EmailData(
            message_id="test-jcet-complete-1",
            subject="FW: KUI Q48W91.1 B802-1V KUIC31N001-D003量?批次??良率98.4%<98.5%",
            sender="<EMAIL>",
            body="JCET 生產報告\n狀態：低良率問題\n需要進一步檢查",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="JCET"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "JCET"
        assert result.is_success is True
        assert result.extracted_data["mo_number"] == "KUIC31N001-D003"
        assert result.extracted_data["parsing_method"] == "kui_pattern_long"

    def test_parse_email_complete_kui_short(self):
        """測試完整的 JCET 郵件解析：KUI 短模式"""
        email = EmailData(
            message_id="test-jcet-complete-2",
            subject="FW: KUI Q48W91.1 B802-1V 量?批次??良率98.4%<98.5%",
            sender="<EMAIL>", 
            body="來自 JCET 的生產數據報告",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="JCET"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "JCET"
        assert result.is_success is True
        assert result.extracted_data["lot_number"] == "Q48W91.1"  # KUI 後第一個詞
        assert result.extracted_data["product"] == "B802-1V"     # KUI 後第二個詞
        assert result.extracted_data["parsing_method"] == "kui_pattern_short"

    def test_parse_email_complete_gyc_pattern(self):
        """測試完整的 JCET 郵件解析：GYC 模式"""
        email = EmailData(
            message_id="test-jcet-complete-3",
            subject="Testing GYC12345678901234567890 BATCH789 PRODUCT_DEF Status",
            sender="<EMAIL>",
            body="GYC processing report from JCET facility",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="JCET"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "JCET"
        assert result.is_success is True
        assert result.extracted_data["mo_number"] == "GYC123456789012"  # 前 15 字符
        assert result.extracted_data["parsing_method"] == "gyc_pattern_long"

    def test_mixed_patterns_priority(self):
        """測試混合模式的優先級：長模式優先於短模式"""
        subject = "FW: KUI GYC12345678901234567890 Q48W91.1 B802-1V Processing"
        
        result = self.parser.parse_jcet_keywords(subject)
        
        # 應該優先選擇長模式的 GYC12345678901234567890
        assert result["mo_number"] == "GYC123456789012"  # 前 15 字符
        assert result["method"] == "gyc_pattern_long"

    def test_chinese_character_handling(self):
        """測試中文字元處理"""
        # 測試包含中文字元的 JCET 郵件
        chinese_subjects = [
            "FW: KUI KUIC31N001-D003量?批次??良率98.4%<98.5%",
            "JCET 生產報告 - GYC12345678901234567890 測試",
            "測試報告 - KUI LOT123 產品ABC - 低良率",
        ]
        
        for subject in chinese_subjects:
            email = EmailData(
                message_id="test-chinese",
                subject=subject,
                sender="<EMAIL>",
                body="測試中文字元處理功能 from JCET",
                received_time=datetime.now()
            )
            
            result = self.parser.identify_vendor(email)
            # 應該能正確識別包含中文的 JCET 郵件
            assert result.is_identified is True

    def test_vendor_confidence_scoring(self):
        """測試 JCET 解析器的信心分數計算"""
        # 完全匹配的情況
        perfect_email = EmailData(
            message_id="test-perfect",
            subject="JCET Production Report - KUI KUIC31N001-D003",
            sender="<EMAIL>",
            body="來自 JCET 的生產報告",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(perfect_email)
        assert result.confidence_score >= 0.9

    def test_error_handling_malformed_subject(self):
        """測試格式錯誤主旨的錯誤處理"""
        email = EmailData(
            message_id="test-jcet-malformed",
            subject="MALFORMED",  # 格式錯誤的主旨
            sender="<EMAIL>",
            body="JCET 測試報告",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="JCET")
        result = self.parser.parse_email(context)
        
        # 應該優雅處理格式錯誤主旨，不拋出異常
        assert result.vendor_code == "JCET"
        # 可能成功（從內文識別）或失敗，但不應該崩潰
        assert result.is_success in [True, False]

    def test_case_insensitive_matching(self):
        """測試大小寫不敏感匹配"""
        test_cases = [
            ("FW: kui Q48W91.1 B802-1V test", "小寫 kui"),
            ("FW: KUI Q48W91.1 B802-1V test", "大寫 KUI"), 
            ("FW: Kui Q48W91.1 B802-1V test", "首字母大寫 Kui"),
            ("FW: gyc12345678901234567890 test", "小寫 gyc"),
            ("FW: GYC12345678901234567890 test", "大寫 GYC"),
        ]
        
        for subject, description in test_cases:
            result = self.parser.parse_jcet_keywords(subject)
            # 應該能識別各種大小寫組合
            assert result["method"] != "no_pattern", f"無法識別 {description}: {subject}"