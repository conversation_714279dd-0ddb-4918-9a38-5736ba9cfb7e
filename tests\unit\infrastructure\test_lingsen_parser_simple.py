"""
LINGSEN 解析器簡化測試
基於 VBA LINGSENInfoFromStrings 邏輯，支援產品代碼解析，遵循 TDD 開發

VBA 邏輯參考：
- 識別條件：內文包含 "LINGSEN" 或寄件者包含 "lingsen"
- 解析規則：正則匹配 (G|M|AT)\d 產品代碼，Run# 和 Lot# 提取，yield 百分比解析
- 範例：[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09%
"""

import pytest
from datetime import datetime

from backend.email.models.email_models import EmailData
from backend.email.parsers.lingsen_parser import LINGSENParser
from backend.email.parsers.base_parser import ParsingContext


class TestLINGSENParserSimple:
    """LINGSEN 解析器簡化測試"""

    def setup_method(self):
        """每個測試方法前的設置"""
        self.parser = LINGSENParser()

    def test_lingsen_parser_initialization(self):
        """測試 LINGSEN 解析器初始化"""
        assert self.parser.vendor_name == "LINGSEN"
        assert self.parser.vendor_code == "LINGSEN"
        assert self.parser.get_confidence_threshold() == 0.8
        assert "lingsen" in self.parser.supported_patterns
        assert "LINGSEN" in self.parser.supported_patterns

    def test_identify_vendor_lingsen_in_body(self):
        """測試識別內文包含 'LINGSEN' 的郵件"""
        # 基於 VBA 邏輯：InStr(1, UCase(body), "LINGSEN", vbTextCompare) > 0
        email = EmailData(
            message_id="test-lingsen-001",
            subject="Test Subject",
            sender="<EMAIL>",
            body="Processing report from LINGSEN facility. Status: LOW YIELD",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "LINGSEN" in result.matching_patterns
        assert result.vendor_code == "LINGSEN"

    def test_identify_vendor_lingsen_sender(self):
        """測試識別寄件者包含 'lingsen' 的郵件"""
        # 基於 VBA 邏輯：InStr(1, LCase(senderAddress), "lingsen", vbTextCompare) > 0
        email = EmailData(
            message_id="test-lingsen-002",
            subject="LINGSEN Production Report",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "lingsen" in result.matching_patterns
        assert result.vendor_code == "LINGSEN"

    def test_cannot_identify_non_lingsen_email(self):
        """測試不能識別非 LINGSEN 格式的郵件"""
        email = EmailData(
            message_id="test-non-lingsen-001",
            subject="GTK FT HOLD MO:F123456 LOT:ABC123",
            sender="<EMAIL>",
            body="Test email body without any relevant keywords",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is False
        assert result.confidence_score < 0.8

    def test_parse_product_code_g_pattern(self):
        """測試解析 G 開頭的產品代碼"""
        # 基於 VBA 邏輯：正則匹配 \b(G|M|AT)\d
        subject = "[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到 G509H25RT11G 作為產品代碼
        assert result["product"] == "G509H25RT11G"
        assert result["method"] == "g_pattern"

    def test_parse_product_code_m_pattern(self):
        """測試解析 M 開頭的產品代碼"""
        subject = "[ LowYield] GMT WQFN0403565L M2518KK1U Run#333124 Lot#Q2VC62.1M"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到 M2518KK1U 作為產品代碼
        assert result["product"] == "M2518KK1U"
        assert result["method"] == "m_pattern"

    def test_parse_product_code_at_pattern(self):
        """測試解析 AT 開頭的產品代碼"""
        subject = "Testing ********* Production Run#555666 Lot#TEST123"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到 ********* 作為產品代碼
        assert result["product"] == "*********"
        assert result["method"] == "at_pattern"

    def test_parse_run_number(self):
        """測試解析 Run# 後的 MO 編號"""
        # 基於 VBA 邏輯：尋找 Run# 後的數字
        subject = "[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到 320833 作為 MO 編號
        assert result["mo_number"] == "320833"

    def test_parse_lot_number(self):
        """測試解析 Lot# 後的批次編號"""
        # 基於 VBA 邏輯：尋找 Lot# 後的字串
        subject = "[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到 KB32750D2.D 作為 LOT 編號
        assert result["lot_number"] == "KB32750D2.D"

    def test_parse_yield_percentage(self):
        """測試解析 yield 百分比"""
        # 基於 VBA 邏輯：正則匹配 LowYield = xx.xx%
        subject = "[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09% < 98.5%"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到 95.09% 作為 yield
        assert result["yield_rate"] == "95.09%"

    def test_parse_yield_percentage_no_space(self):
        """測試解析 yield 百分比（無空格格式）"""
        subject = "FW: [LowYield] GMT WQFN0403565L G2518KK1U Run#333124 Lot#Q2VC62.1M LowYield=96.85% < 97%"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到 96.85% 作為 yield
        assert result["yield_rate"] == "96.85%"

    def test_parse_no_pattern_found(self):
        """測試解析找不到產品代碼模式時的處理"""
        subject = "Simple subject without any product codes"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        assert result["product"] == ""
        assert result["mo_number"] == ""
        assert result["lot_number"] == ""
        assert result["yield_rate"] == ""
        assert result["method"] == "no_pattern"

    def test_parse_email_complete_g_pattern(self):
        """測試完整的 LINGSEN 郵件解析：G 產品代碼"""
        email = EmailData(
            message_id="test-lingsen-complete-1",
            subject="[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09% < 98.5%",
            sender="<EMAIL>",
            body="LINGSEN 生產報告\n狀態：低良率問題\n需要進一步檢查",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="LINGSEN"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "LINGSEN"
        assert result.is_success is True
        assert result.extracted_data["product"] == "G509H25RT11G"
        assert result.mo_number == "R320833"  # 標準化後的 MO 編號
        assert result.extracted_data["lot_number"] == "KB32750D2.D"
        assert result.extracted_data["yield_rate"] == "95.09%"
        assert result.extracted_data["parsing_method"] == "g_pattern"

    def test_parse_email_complete_m_pattern(self):
        """測試完整的 LINGSEN 郵件解析：M 產品代碼"""
        email = EmailData(
            message_id="test-lingsen-complete-2",
            subject="FW: [LowYield] GMT WQFN0403565L M2518KK1U(G2518XXXXDB1KK1451) Run#333124 Lot#Q2VC62.1M LowYield=96.85% < 97%",
            sender="<EMAIL>", 
            body="來自 LINGSEN 的生產數據報告",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="LINGSEN"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "LINGSEN"
        assert result.is_success is True
        assert result.extracted_data["product"] == "G2518XXXXDB1KK1451"  # G 優先級高於 M，找到括號內的 G 產品代碼
        assert result.mo_number == "R333124"  # 標準化後的 MO 編號
        assert result.extracted_data["lot_number"] == "Q2VC62.1M"
        assert result.extracted_data["yield_rate"] == "96.85%"
        assert result.extracted_data["parsing_method"] == "g_pattern"  # 實際使用 G 模式

    def test_complex_product_code_in_parentheses(self):
        """測試括號內的複雜產品代碼"""
        subject = "FW: [LowYield] GMT WQFN0403565L G2518KK1U(G2518XXXXDB1KK1451) Run#333124 Lot#Q2VC62.1M"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該找到第一個匹配的 G 產品代碼（按優先級和出現順序）
        assert result["product"] == "G2518KK1U"
        assert result["method"] == "g_pattern"

    def test_multiple_patterns_priority(self):
        """測試多重模式的優先級：G > M > AT"""
        subject = "Testing AT1234 M5678 G9012 Run#111 Lot#TEST"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該優先選擇 G 模式
        assert result["product"] == "G9012"
        assert result["method"] == "g_pattern"

    def test_chinese_character_handling(self):
        """測試中文字元處理"""
        # 測試包含中文字元的 LINGSEN 郵件
        chinese_subjects = [
            "[ 低良率] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D",
            "LINGSEN 生產報告 - M2518KK1U Run#333124 測試",
            "測試報告 - ********* Run#555 批次ABC - 低良率",
        ]
        
        for subject in chinese_subjects:
            email = EmailData(
                message_id="test-chinese",
                subject=subject,
                sender="<EMAIL>",
                body="測試中文字元處理功能 from LINGSEN",
                received_time=datetime.now()
            )
            
            result = self.parser.identify_vendor(email)
            # 應該能正確識別包含中文的 LINGSEN 郵件
            assert result.is_identified is True

    def test_vendor_confidence_scoring(self):
        """測試 LINGSEN 解析器的信心分數計算"""
        # 完全匹配的情況
        perfect_email = EmailData(
            message_id="test-perfect",
            subject="LINGSEN Production Report - G509H25RT11G Run#320833",
            sender="<EMAIL>",
            body="來自 LINGSEN 的生產報告",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(perfect_email)
        assert result.confidence_score >= 0.9

    def test_error_handling_malformed_subject(self):
        """測試格式錯誤主旨的錯誤處理"""
        email = EmailData(
            message_id="test-lingsen-malformed",
            subject="MALFORMED",  # 格式錯誤的主旨
            sender="<EMAIL>",
            body="LINGSEN 測試報告",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="LINGSEN")
        result = self.parser.parse_email(context)
        
        # 應該優雅處理格式錯誤主旨，不拋出異常
        assert result.vendor_code == "LINGSEN"
        # 可能成功（從內文識別）或失敗，但不應該崩潰
        assert result.is_success in [True, False]

    def test_case_insensitive_matching(self):
        """測試大小寫不敏感匹配"""
        test_cases = [
            ("[ lowyield] GMT SOT-25M g509h25rt11g run#320833 lot#kb32750d2.d", "小寫產品代碼"),
            ("[ LOWYIELD] GMT SOT-25M G509H25RT11G RUN#320833 LOT#KB32750D2.D", "大寫產品代碼"), 
            ("[ LowYield] GMT SOT-25M G509h25rt11g Run#320833 Lot#KB32750d2.d", "混合大小寫"),
        ]
        
        for subject, description in test_cases:
            result = self.parser.parse_lingsen_keywords(subject)
            # 應該能識別各種大小寫組合
            assert result["method"] != "no_pattern", f"無法識別 {description}: {subject}"
            assert result["product"] != "", f"產品代碼解析失敗 {description}: {subject}"

    def test_fw_prefix_handling(self):
        """測試 FW: 前綴處理"""
        subject = "FW: [ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09%"
        
        result = self.parser.parse_lingsen_keywords(subject)
        
        # 應該正確解析有 FW: 前綴的郵件
        assert result["product"] == "G509H25RT11G"
        assert result["mo_number"] == "320833"
        assert result["lot_number"] == "KB32750D2.D"
        assert result["yield_rate"] == "95.09%"