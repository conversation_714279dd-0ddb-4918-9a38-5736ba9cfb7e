#!/usr/bin/env python3
"""
簡化版整合服務啟動器
統一在端口 5555 提供 Flask + FastAPI 服務
避免複雜的依賴和配置問題
"""

import os
import sys
import asyncio
import threading
import time
from pathlib import Path

# 設置 Unicode 環境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn

def create_integrated_app():
    """創建整合的 FastAPI 應用"""
    app = FastAPI(
        title="Outlook Summary 整合服務",
        description="Flask + FastAPI 整合服務",
        version="1.0.0"
    )
    
    # 添加 CORS 中間件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app

def mount_flask_app(app: FastAPI):
    """掛載 Flask 應用到 FastAPI"""
    try:
        from frontend.app import create_app
        flask_app = create_app('development')
        
        # 將 Flask 應用掛載到根路徑
        from fastapi.middleware.wsgi import WSGIMiddleware
        app.mount("/", WSGIMiddleware(flask_app))
        
        print("✅ Flask 應用已掛載到根路徑")
        return True
    except Exception as e:
        print(f"❌ Flask 應用掛載失敗: {e}")
        return False

def mount_ft_eqc_api(app: FastAPI):
    """掛載 FT-EQC FastAPI 服務"""
    try:
        from frontend.api.ft_eqc_api import app as ft_eqc_app
        
        # 將 FT-EQC API 掛載到 /ft-eqc 路徑
        app.mount("/ft-eqc", ft_eqc_app)
        
        print("✅ FT-EQC API 已掛載到 /ft-eqc")
        print("   • FT-EQC UI: http://localhost:5555/ft-eqc/ui")
        print("   • FT-EQC API 文檔: http://localhost:5555/ft-eqc/docs")
        return True
    except Exception as e:
        print(f"❌ FT-EQC API 掛載失敗: {e}")
        return False

async def start_integrated_service():
    """啟動整合服務"""
    print("🚀 啟動簡化版整合服務...")
    print("=" * 50)

    # 創建 FastAPI 應用
    app = create_integrated_app()

    # 掛載 FT-EQC API（先掛載，避免被 Flask 覆蓋）
    ft_eqc_success = mount_ft_eqc_api(app)

    # 掛載 Flask 應用
    flask_success = mount_flask_app(app)

    if not flask_success:
        print("❌ 無法啟動服務：Flask 應用掛載失敗")
        return

    print("\n" + "=" * 50)
    print("🎉 整合服務啟動成功！")
    print(f"📍 服務地址: http://localhost:5555")
    print(f"📍 Flask 前端: http://localhost:5555/")
    if ft_eqc_success:
        print(f"📍 FT-EQC UI: http://localhost:5555/ft-eqc/ui")
        print(f"📍 FT-EQC API: http://localhost:5555/ft-eqc/docs")
    print("=" * 50)

    # 啟動 uvicorn 服務器
    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=5555,
        log_level="info",
        reload=False
    )
    server = uvicorn.Server(config)
    await server.serve()

def main():
    """主函數"""
    try:
        # 運行整合服務
        asyncio.run(start_integrated_service())
    except KeyboardInterrupt:
        print("\n👋 服務已停止")
    except Exception as e:
        print(f"❌ 服務啟動失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
