"""
簡單驗證 Phase 3 重構是否成功
檢查端點是否正確使用依賴注入
"""

import pytest
from unittest.mock import Mock
import inspect

from frontend.api.search_routes import (
    search_product_submit, get_search_task_status, smart_search, smart_search_post,
    get_task_status, celery_health_check
)
from frontend.api.ui_routes import get_realtime_dashboard, test_simple


class TestDependencyInjectionValidation:
    """驗證端點是否使用依賴注入"""

    def test_search_endpoints_have_dependency_injection(self):
        """測試搜尋端點是否有依賴注入參數"""
        
        # 檢查 search_product_submit
        sig = inspect.signature(search_product_submit)
        params = list(sig.parameters.keys())
        assert 'api_state' in params, "search_product_submit 應該有 api_state 參數"
        
        # 檢查 get_search_task_status
        sig = inspect.signature(get_search_task_status)
        params = list(sig.parameters.keys())
        assert 'product_search_service' in params, "get_search_task_status 應該有 product_search_service 參數"
        assert 'api_state' in params, "get_search_task_status 應該有 api_state 參數"
        
        # 檢查 smart_search
        sig = inspect.signature(smart_search)
        params = list(sig.parameters.keys())
        assert 'llm_search_service' in params, "smart_search 應該有 llm_search_service 參數"
        assert 'api_state' in params, "smart_search 應該有 api_state 參數"
        
        # 檢查 smart_search_post
        sig = inspect.signature(smart_search_post)
        params = list(sig.parameters.keys())
        assert 'llm_search_service' in params, "smart_search_post 應該有 llm_search_service 參數"
        assert 'api_state' in params, "smart_search_post 應該有 api_state 參數"
        
        # 檢查 get_task_status
        sig = inspect.signature(get_task_status)
        params = list(sig.parameters.keys())
        assert 'api_state' in params, "get_task_status 應該有 api_state 參數"
        
        # 檢查 celery_health_check
        sig = inspect.signature(celery_health_check)
        params = list(sig.parameters.keys())
        assert 'api_state' in params, "celery_health_check 應該有 api_state 參數"

    def test_ui_endpoints_have_dependency_injection(self):
        """測試 UI 端點是否有依賴注入參數"""
        
        # 檢查 get_realtime_dashboard
        sig = inspect.signature(get_realtime_dashboard)
        params = list(sig.parameters.keys())
        assert 'staging_service' in params, "get_realtime_dashboard 應該有 staging_service 參數"
        assert 'processing_service' in params, "get_realtime_dashboard 應該有 processing_service 參數"
        assert 'api_state' in params, "get_realtime_dashboard 應該有 api_state 參數"
        
        # 檢查 test_simple
        sig = inspect.signature(test_simple)
        params = list(sig.parameters.keys())
        assert 'api_state' in params, "test_simple 應該有 api_state 參數"

    def test_dependency_injection_functions_exist(self):
        """測試依賴注入函數是否存在"""
        from frontend.api.dependencies import (
            require_product_search_service, require_llm_search_service,
            get_api_state, get_staging_service, get_processing_service
        )
        
        # 檢查函數是否可調用
        assert callable(require_product_search_service), "require_product_search_service 應該是可調用的"
        assert callable(require_llm_search_service), "require_llm_search_service 應該是可調用的"
        assert callable(get_api_state), "get_api_state 應該是可調用的"
        assert callable(get_staging_service), "get_staging_service 應該是可調用的"
        assert callable(get_processing_service), "get_processing_service 應該是可調用的"

    def test_search_routes_no_module_level_services(self):
        """測試搜尋路由文件沒有模組級服務初始化"""
        import frontend.api.search_routes as search_module
        
        # 檢查模組中不應該有這些變數
        assert not hasattr(search_module, 'product_search_service') or \
               getattr(search_module, 'product_search_service', None) is None, \
               "search_routes 不應該有模組級 product_search_service"
        
        assert not hasattr(search_module, 'llm_search_service') or \
               getattr(search_module, 'llm_search_service', None) is None, \
               "search_routes 不應該有模組級 llm_search_service"

    def test_endpoints_use_depends_annotation(self):
        """測試端點是否使用 Depends 註解"""
        from fastapi import Depends
        
        # 檢查 get_search_task_status 的註解
        sig = inspect.signature(get_search_task_status)
        for param_name, param in sig.parameters.items():
            if param_name in ['product_search_service', 'api_state']:
                assert param.default is not inspect.Parameter.empty, \
                    f"{param_name} 應該有默認值（Depends）"
        
        # 檢查 smart_search 的註解
        sig = inspect.signature(smart_search)
        for param_name, param in sig.parameters.items():
            if param_name in ['llm_search_service', 'api_state']:
                assert param.default is not inspect.Parameter.empty, \
                    f"{param_name} 應該有默認值（Depends）"
        
        # 檢查 get_realtime_dashboard 的註解
        sig = inspect.signature(get_realtime_dashboard)
        for param_name, param in sig.parameters.items():
            if param_name in ['staging_service', 'processing_service', 'api_state']:
                assert param.default is not inspect.Parameter.empty, \
                    f"{param_name} 應該有默認值（Depends）"


class TestRefactoringCompletion:
    """測試重構完成度"""

    def test_all_search_endpoints_refactored(self):
        """測試所有搜尋端點都已重構"""
        import frontend.api.search_routes as search_module
        
        # 檢查源碼中不應該包含舊的模式
        import inspect
        source = inspect.getsource(search_module)
        
        # 不應該有直接的服務調用
        assert 'if product_search_service is None:' not in source, \
            "不應該有舊的 None 檢查模式"
        assert 'if llm_search_service is None:' not in source, \
            "不應該有舊的 None 檢查模式"

    def test_ui_endpoints_refactored(self):
        """測試 UI 端點都已重構"""
        import frontend.api.ui_routes as ui_module
        
        # 檢查源碼中不應該包含舊的模式
        import inspect
        source = inspect.getsource(ui_module)
        
        # 不應該有直接的服務導入和調用
        assert 'from ...services.staging import get_file_staging_service' not in source, \
            "不應該有直接的服務導入"
        assert 'from ...services.processing import get_file_processing_service' not in source, \
            "不應該有直接的服務導入"

    def test_dependency_injection_infrastructure_complete(self):
        """測試依賴注入基礎設施完整"""
        from frontend.api.dependencies import (
            require_product_search_service, require_llm_search_service,
            get_product_search_service, get_llm_search_service,
            get_api_state, APIState
        )
        
        # 測試 APIState 類存在且有必要方法
        assert hasattr(APIState, 'increment_request_count'), \
            "APIState 應該有 increment_request_count 方法"
        assert hasattr(APIState, 'increment_error_count'), \
            "APIState 應該有 increment_error_count 方法"
        
        # 測試依賴注入函數返回正確類型
        api_state = get_api_state()
        assert isinstance(api_state, APIState), "get_api_state 應該返回 APIState 實例"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
