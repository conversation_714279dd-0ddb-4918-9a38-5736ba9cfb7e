"""
XAHT 解析器簡化測試
基於 VBA 雙重解析邏輯，支援中文字元，遵循 TDD 開發

VBA 邏輯參考：
- 識別條件：內文包含 "tianshui" 或 "西安"，主旨包含 "西安"
- 雙重解析：XAHTInfoFromStrings 和 XAHTInfoFromStrings2
- 中文字元支援：正確處理中文編碼
"""

import pytest
from datetime import datetime

from backend.email.models.email_models import EmailData
from backend.email.parsers.xaht_parser import XAHTParser
from backend.email.parsers.base_parser import ParsingContext


class TestXAHTParserSimple:
    """XAHT 解析器簡化測試"""

    def setup_method(self):
        """每個測試方法前的設置"""
        self.parser = XAHTParser()

    def test_xaht_parser_initialization(self):
        """測試 XAHT 解析器初始化"""
        assert self.parser.vendor_name == "XAHT"
        assert self.parser.vendor_code == "XAHT"
        assert self.parser.get_confidence_threshold() == 0.8
        assert "tianshui" in self.parser.supported_patterns
        assert "西安" in self.parser.supported_patterns

    def test_identify_vendor_tianshui_in_body(self):
        """測試識別內文包含 'tianshui' 的郵件"""
        # 基於 VBA 邏輯：InStr(1, LCase(body), "tianshui", vbTextCompare) > 0
        email = EmailData(
            message_id="test-xaht-001",
            subject="XAHT Production Report",
            sender="<EMAIL>",
            body="Processing report from tianshui facility. Status: OK",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "tianshui" in result.matching_patterns
        assert result.vendor_code == "XAHT"

    def test_identify_vendor_xian_in_body(self):
        """測試識別內文包含 '西安' 的郵件"""
        # 基於 VBA 邏輯：InStr(1, LCase(body), "西安", vbTextCompare) > 0
        email = EmailData(
            message_id="test-xaht-002",
            subject="XAHT Production Report",
            sender="<EMAIL>",
            body="來自西安測試中心的生產報告。狀態：正常",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "西安" in result.matching_patterns
        assert result.vendor_code == "XAHT"

    def test_identify_vendor_xian_in_subject(self):
        """測試識別主旨包含 '西安' 的郵件"""
        # 基於 VBA 邏輯：InStr(1, LCase(subject), "西安", vbTextCompare) > 0
        email = EmailData(
            message_id="test-xaht-003",
            subject="西安測試中心生產報告 - HOLD LOT",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "西安" in result.matching_patterns

    def test_cannot_identify_non_xaht_email(self):
        """測試不能識別非 XAHT 格式的郵件"""
        email = EmailData(
            message_id="test-non-xaht-001",
            subject="GTK FT HOLD MO:F123456 LOT:ABC123",
            sender="<EMAIL>",
            body="Test email body without XAHT keywords",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is False
        assert result.confidence_score < 0.8

    def test_parse_method_1_wa_pattern(self):
        """測試解析方法 1：包含 'wa' 的模式"""
        # 基於 VBA XAHTInfoFromStrings 邏輯
        subject = "TW083 G2794AK11U-K(AB) KD29114.8 WB070wa50102 Hold Lot"
        
        result = self.parser.parse_method_1(subject)
        
        # 應該找到包含 'wa' 的詞並提取前 11 字符作為 MO
        assert result["mo_number"] == "WB070wa5010"  # 前 11 字符
        assert result["product"] == "KD29114.8"  # MO 前一個詞
        assert result["method"] == "wa_pattern"

    def test_parse_method_1_gyc_pattern(self):
        """測試解析方法 1：包含 'GYC' 的模式"""
        # 基於 VBA XAHTInfoFromStrings 邏輯
        subject = "TW083 PRODUCT_ABC LOT123 MO_GYC12345678901 Status"
        
        result = self.parser.parse_method_1(subject)
        
        # 應該找到包含 'GYC' 的詞並提取前 11 字符作為 MO
        assert result["mo_number"] == "MO_GYC12345"  # 前 11 字符
        assert result["product"] == "LOT123"  # MO 前一個詞
        assert result["method"] == "gyc_pattern"

    def test_parse_method_1_no_pattern_found(self):
        """測試解析方法 1：找不到模式時的處理"""
        subject = "Simple subject without wa or GYC pattern"
        
        result = self.parser.parse_method_1(subject)
        
        assert result["mo_number"] == ""  # 空字串表示未找到
        assert result["product"] == ""
        assert result["method"] == "no_pattern"

    def test_parse_method_2_underscore_format(self):
        """測試解析方法 2：下劃線分隔格式"""
        # 基於 VBA XAHTInfoFromStrings2 邏輯
        subject = "FW: TW083_?天西安_G2794AK11U-K(AB)_KD29114.8_WB070W50102_Hold Lot反?"
        
        result = self.parser.parse_method_2(subject)
        
        # 基於 VBA 邏輯：parts[2]=product, parts[3]=lotString, parts[4]=moString
        assert result["product"] == "G2794AK11U-K(AB)"  # parts[2]
        assert result["lot_number"] == "KD29114.8"  # parts[3]
        assert result["mo_number"] == "WB070W50102"  # parts[4]
        assert result["method"] == "underscore_split"

    def test_parse_method_2_remove_fw_prefix(self):
        """測試解析方法 2：移[EXCEPT_CHAR] 'FW: ' 前綴"""
        subject = "FW: Part1_Part2_PRODUCT_LOT123_MO456789_Status"
        
        result = self.parser.parse_method_2(subject)
        
        assert result["product"] == "PRODUCT"
        assert result["lot_number"] == "LOT123"
        assert result["mo_number"] == "MO456789"

    def test_parse_method_2_insufficient_parts(self):
        """測試解析方法 2：部分不足的處理"""
        subject = "TW083_Part1_Part2"  # 只有 3 部分，不足 5 部分
        
        result = self.parser.parse_method_2(subject)
        
        assert result["product"] == "Part2"  # parts[2] 存在
        assert result["lot_number"] == "?"  # parts[3] 不存在
        assert result["mo_number"] == "?"   # parts[4] 不存在

    def test_dual_parsing_mechanism(self):
        """測試雙重解析機制"""
        # 基於 VBA 邏輯：先用方法 1，如果 MO 為空則用方法 2
        subject = "FW: TW083_西安測試_CHIP_A100_LOT_ABC123_MO_H123456_Status"
        
        result = self.parser.parse_dual_method(subject)
        
        # 方法 1 找不到 wa/GYC，應該使用方法 2 的結果
        # 實際分割後：TW083_西安測試_CHIP_A100_LOT_ABC123_MO_H123456_Status
        # parts[2]=CHIP, parts[3]=A100, parts[4]=LOT
        assert result["mo_number"] == "LOT"    # parts[4]
        assert result["product"] == "CHIP"     # parts[2]
        assert result["lot_number"] == "A100"  # parts[3]
        assert result["final_method"] == "method_2"  # 記錄最終使用的方法

    def test_dual_parsing_method_1_success(self):
        """測試雙重解析：方法 1 成功時不調用方法 2"""
        subject = "TW083 PRODUCT_XYZ LOT789 MO_wa12345678901 Processing"
        
        result = self.parser.parse_dual_method(subject)
        
        # 方法 1 成功找到 wa 模式，不需要方法 2
        assert result["mo_number"] == "MO_wa123456"  # 前 11 字符
        assert result["product"] == "LOT789"
        assert result["final_method"] == "method_1"

    def test_parse_email_complete(self):
        """測試完整的 XAHT 郵件解析"""
        email = EmailData(
            message_id="test-xaht-complete",
            subject="FW: TW083_西安_CHIP_B200_LOT_XYZ789_MO_G123456_HOLD",
            sender="<EMAIL>",
            body="來自西安測試中心的生產報告\n狀態：HOLD LOT\n需要進一步檢查",
            received_time=datetime.now()
        )
        
        context = ParsingContext(
            email_data=email,
            vendor_code="XAHT"
        )
        
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "XAHT"
        assert result.is_success is True
        # 實際分割結果：FW: TW083_西安_CHIP_B200_LOT_XYZ789_MO_G123456_HOLD
        # parts[2]=CHIP, parts[3]=B200, parts[4]=LOT
        assert result.extracted_data["product"] == "CHIP"     # parts[2]
        assert result.extracted_data["lot_number"] == "B200"  # parts[3]
        assert result.extracted_data["mo_number"] == "LOT"    # parts[4]
        assert result.extracted_data["parsing_method"] in ["method_1", "method_2"]

    def test_chinese_character_handling(self):
        """測試中文字元處理"""
        # 測試各種中文字元場景
        chinese_subjects = [
            "西安測試中心報告",
            "FW: TW083_天水_產品_批次_訂單_狀態",
            "測試報告 - 西安廠 - HOLD LOT",
            "tianshui 工廠生產數據"
        ]
        
        for subject in chinese_subjects:
            email = EmailData(
                message_id="test-chinese",
                subject=subject,
                sender="<EMAIL>",
                body="測試中文字元處理功能",
                received_time=datetime.now()
            )
            
            result = self.parser.identify_vendor(email)
            # 至少應該識別包含中文關鍵字的郵件
            if "西安" in subject or "tianshui" in subject:
                assert result.is_identified is True

    def test_encoding_robustness(self):
        """測試編碼穩健性"""
        # 模擬可能的編碼問題
        problematic_subjects = [
            "FW: TW083_?天西安_G2794AK11U-K(AB)_KD29114.8_WB070W50102_Hold Lot反?",
            "FW: TW083-G2732PK81D-K(AD-CTAXP)-WA330L30201工?低良率",
            "測試?字元?編碼?問題"
        ]
        
        for subject in problematic_subjects:
            email = EmailData(
                message_id="test-encoding",
                subject=subject,
                sender="<EMAIL>", 
                body="Testing encoding issues",
                received_time=datetime.now()
            )
            
            # 應該不會拋出異常，即使有編碼問題
            try:
                result = self.parser.identify_vendor(email)
                context = ParsingContext(email_data=email, vendor_code="XAHT")
                parsing_result = self.parser.parse_email(context)
                assert True  # 如果沒有異常就算成功
            except Exception as e:
                pytest.fail(f"編碼處理失敗: {e}")

    def test_performance_with_chinese_content(self):
        """測試包含大量中文內容的效能"""
        chinese_content = "西安測試中心生產報告。" * 1000 + "tianshui 工廠數據。" * 1000
        
        email = EmailData(
            message_id="test-xaht-performance",
            subject="FW: TW083_西安_PRODUCT_LOT123_MO456_STATUS",
            sender="<EMAIL>",
            body=chinese_content,
            received_time=datetime.now()
        )
        
        import time
        start_time = time.time()
        
        context = ParsingContext(email_data=email, vendor_code="XAHT")
        result = self.parser.parse_email(context)
        
        end_time = time.time()
        
        # 即使有大量中文內容，解析也應該在 1 秒內完成
        assert end_time - start_time < 1.0
        assert result.vendor_code == "XAHT"

    def test_vendor_confidence_scoring(self):
        """測試 XAHT 解析器的信心分數計算"""
        # 完全匹配的情況
        perfect_email = EmailData(
            message_id="test-perfect",
            subject="西安測試中心 - XAHT 生產報告",
            sender="<EMAIL>",
            body="來自西安和tianshui的聯合報告",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(perfect_email)
        assert result.confidence_score >= 0.9
        
        # 部分匹配的情況
        partial_email = EmailData(
            message_id="test-partial",
            subject="Some subject with tianshui keyword",
            sender="<EMAIL>",
            body="Generic body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(partial_email)
        assert 0.5 <= result.confidence_score < 0.9

    def test_error_handling_malformed_subject(self):
        """測試格式錯誤主旨的錯誤處理"""
        email = EmailData(
            message_id="test-xaht-malformed",
            subject="MALFORMED",  # 格式錯誤的主旨
            sender="<EMAIL>",
            body="西安測試中心報告",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="XAHT")
        result = self.parser.parse_email(context)
        
        # 應該優雅處理空主旨，不拋出異常
        assert result.vendor_code == "XAHT"
        # 可能成功（從內文識別）或失敗，但不應該崩潰
        assert result.is_success in [True, False]