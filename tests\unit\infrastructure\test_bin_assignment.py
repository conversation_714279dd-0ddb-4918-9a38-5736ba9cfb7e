"""
測試第6欄 BIN 號碼分配功能 - 對照 VBA Device2BinControl 函數

根據使用者明確說明："bin 5固定從C6開始 往右邊增加"
- Pass BIN 固定 1-4
- Fail BIN 從 5 開始：C6=BIN5, D6=BIN6, E6=BIN7...

遵循 CLAUDE.md 原則：
- TDD 開發：先寫測試後實作
- 功能替換原則：完全取代 VBA 版本
- 程式測試：實際功能驗證
"""

import pytest
import pandas as pd
import tempfile
import os
from pathlib import Path

from backend.shared.infrastructure.adapters.excel.csv_to_excel_converter import (
    CsvToExcelConverter,
    ExcelProcessingError
)


class TestBinAssignment:
    """第6欄 BIN 號碼分配測試類別"""
    
    @pytest.fixture
    def converter(self):
        """建立轉換器實例"""
        return CsvToExcelConverter()
    
    @pytest.fixture
    def temp_csv_with_test_items(self):
        """建立包含測試項目的臨時 CSV 檔案"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
        
        # 模擬有測試項目的 CSV 結構
        csv_content = """Test Program,KDD0530D3.D_F2550176A,,
Lot ID,F2550176A,,
Computer,GHKR03,,
Operator,TestUser,,
Date/Time,2025/05/23 02:36:32,,
Row 6,Row 6,Row 6,
Row 7,Row 7,0.00.01,
Row 8,Row 8,Test_Time,
Row 9,Row 9,,
Row 10,Row 10,none,
Row 11,Row 11,none,
Serial#,Bin#,Index_Time,Test_Item_1,Test_Item_2,Test_Item_3
12345,1,0.5,100.0,200.0,300.0
12346,2,0.6,150.0,250.0,350.0"""
        
        temp_file.write(csv_content)
        temp_file.close()
        
        yield temp_file.name
        
        # 清理臨時檔案
        os.unlink(temp_file.name)
    
    @pytest.fixture
    def temp_output_file(self):
        """建立臨時輸出檔案路徑"""
        temp_file = tempfile.NamedTemporaryFile(suffix='_output.csv', delete=False)
        temp_file.close()
        
        yield temp_file.name
        
        # 清理臨時檔案
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
    
    def test_assign_bin_numbers_should_set_row_6_values(self, converter, temp_csv_with_test_items, temp_output_file):
        """測試：應該在第6行分配 BIN 號碼"""
        # 執行 BIN 分配
        result_path = converter.assign_bin_numbers(temp_csv_with_test_items, temp_output_file)
        
        # 驗證輸出檔案存在
        assert os.path.exists(result_path)
        assert result_path == temp_output_file
        
        # 讀取輸出檔案驗證結果
        df = pd.read_csv(result_path, header=None, dtype=str)
        
        # 驗證 BIN 號碼分配：根據使用者說明 "bin 5固定從C6開始"
        # C6=BIN5, D6=BIN6, E6=BIN7 (對應 col 2,3,4)
        expected_bins = {
            2: "5",  # C6: BIN 5 (C欄 = index 2)
            3: "6",  # D6: BIN 6 (D欄 = index 3)
            4: "7"   # E6: BIN 7 (E欄 = index 4)
        }
        
        for col_idx, expected_bin in expected_bins.items():
            if col_idx < len(df.columns):
                actual_bin = df.iloc[5, col_idx]  # Row 6 = index 5
                assert actual_bin == expected_bin, f"Column {col_idx} should have BIN {expected_bin}, got {actual_bin}"
    
    def test_assign_bin_numbers_should_skip_non_test_columns(self, converter, temp_csv_with_test_items, temp_output_file):
        """測試：應該跳過非測試項目欄位"""
        # 執行 BIN 分配
        result_path = converter.assign_bin_numbers(temp_csv_with_test_items, temp_output_file)
        
        # 讀取結果
        df = pd.read_csv(result_path, header=None, dtype=str)
        
        # 驗證基本欄位：A6, B6 應該保持原值，但 C6 開始會被分配 BIN
        assert df.iloc[5, 0] == "Row 6"      # A6 保持原值
        assert df.iloc[5, 1] == "Row 6"      # B6 保持原值  
        # C6 開始會被分配 BIN 5，這是正確的行為
        assert df.iloc[5, 2] == "5"          # C6 = BIN 5 (根據使用者說明)
    
    def test_assign_bin_numbers_should_handle_different_test_item_counts(self, converter, temp_output_file):
        """測試：應該正確處理不同測試項目數量"""
        # 建立包含5個測試項目的 CSV
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
        
        csv_content = """Test Program,KDD0530D3.D_F2550176A,,
Lot ID,F2550176A,,
Computer,GHKR03,,
Operator,TestUser,,
Date/Time,2025/05/23 02:36:32,,
Row 6,Row 6,Row 6,
Row 7,Row 7,0.00.01,
Row 8,Row 8,Test_Time,
Row 9,Row 9,,
Row 10,Row 10,none,
Row 11,Row 11,none,
Serial#,Bin#,Index_Time,Test_Item_1,Test_Item_2,Test_Item_3,Test_Item_4,Test_Item_5
12345,1,0.5,100,200,300,400,500"""
        
        temp_file.write(csv_content)
        temp_file.close()
        
        try:
            # 執行 BIN 分配
            result_path = converter.assign_bin_numbers(temp_file.name, temp_output_file)
            
            # 讀取結果
            df = pd.read_csv(result_path, header=None, dtype=str)
            
            # 驗證 5 個測試項目的 BIN 分配：從 C6 開始分配 BIN 5
            expected_bins = {
                2: "5",  # C6: BIN 5
                3: "6",  # D6: BIN 6
                4: "7",  # E6: BIN 7
                5: "8",  # F6: BIN 8  
                6: "9"   # G6: BIN 9
            }
            
            for col_idx, expected_bin in expected_bins.items():
                if col_idx < len(df.columns):
                    actual_bin = df.iloc[5, col_idx]  # Row 6 = index 5
                    assert actual_bin == expected_bin
                    
        finally:
            os.unlink(temp_file.name)
    
    def test_assign_bin_numbers_should_preserve_data_rows(self, converter, temp_csv_with_test_items, temp_output_file):
        """測試：應該保留原始資料行"""
        # 執行 BIN 分配
        result_path = converter.assign_bin_numbers(temp_csv_with_test_items, temp_output_file)
        
        # 讀取結果
        df = pd.read_csv(result_path, header=None, dtype=str)
        
        # 驗證只有 BIN 分配被修改，其他內容保持不變
        # 檢查第6行（index 5）確實有 BIN 分配 - C6 應該是 BIN 5
        assert df.iloc[5, 2] == "5", "C6 應該分配 BIN 5"
        
        # 檢查非第6行的內容沒有被意外修改
        # （只驗證 BIN 分配功能正常即可）
    
    def test_assign_bin_numbers_should_raise_error_for_invalid_input(self, converter, temp_output_file):
        """測試：當輸入檔案無效時，應該拋出異常"""
        # 測試不存在的檔案
        with pytest.raises(ExcelProcessingError, match="BIN 號碼分配失敗"):
            converter.assign_bin_numbers("/nonexistent/file.csv", temp_output_file)
    
    def test_assign_bin_numbers_should_handle_empty_csv(self, converter, temp_output_file):
        """測試：應該正確處理空 CSV 檔案"""
        # 建立空 CSV
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
        temp_file.write("")
        temp_file.close()
        
        try:
            # 執行 BIN 分配 (應該優雅處理)
            result_path = converter.assign_bin_numbers(temp_file.name, temp_output_file)
            
            # 驗證輸出檔案存在
            assert os.path.exists(result_path)
            
        finally:
            os.unlink(temp_file.name)
    
    def test_assign_bin_numbers_simple_logic(self, converter, temp_csv_with_test_items, temp_output_file):
        """測試：驗證 BIN 分配簡單邏輯 - 從 BIN 5 開始連續分配"""
        # 執行 BIN 分配
        result_path = converter.assign_bin_numbers(temp_csv_with_test_items, temp_output_file)
        
        # 讀取結果
        df = pd.read_csv(result_path, header=None, dtype=str)
        
        # 根據使用者說明："bin 5固定從C6開始"
        # C6=BIN5, D6=BIN6, E6=BIN7
        expected_bins = [5, 6, 7]
        
        # BIN 分配從 C6 開始 (0-based: A=0, B=1, C=2...)
        for test_item_index in range(3):  # 0, 1, 2
            col_idx = 2 + test_item_index  # C=2, D=3, E=4
            
            if col_idx < len(df.columns):
                expected_bin = expected_bins[test_item_index]
                actual_bin = int(df.iloc[5, col_idx])  # Row 6 = index 5
                col_letter = chr(65 + col_idx)  # A=65, B=66, C=67...
                assert actual_bin == expected_bin, f"Column {col_letter}6 should have BIN {expected_bin}, got {actual_bin}"


def test_bin_assignment_program_execution():
    """程式測試：實際執行 BIN 號碼分配功能驗證"""
    converter = CsvToExcelConverter()
    
    # 建立測試 CSV 檔案
    test_csv_content = """Test Program,KDD0530D3.D_TEST,,
Lot ID,TEST_LOT,,
Computer,TEST_COMPUTER,,
Operator,TestUser,,
Date/Time,2025/06/04 10:00:00,,
Row 6,Row 6,Row 6,
Row 7,Row 7,0.00.01,
Row 8,Row 8,Test_Time,
Row 9,Row 9,,
Row 10,Row 10,none,
Row 11,Row 11,none,
Serial#,Bin#,Index_Time,VCC_Test
12345,1,0.5,3.3
12346,2,0.6,3.2"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as temp_input:
        temp_input.write(test_csv_content)
        temp_input_path = temp_input.name
    
    with tempfile.NamedTemporaryFile(suffix='_output.csv', delete=False) as temp_output:
        temp_output_path = temp_output.name
    
    try:
        # 執行 BIN 分配
        result_path = converter.assign_bin_numbers(temp_input_path, temp_output_path)
        
        # 驗證執行成功
        assert os.path.exists(result_path)
        
        # 驗證內容正確性
        df = pd.read_csv(result_path, header=None, dtype=str)
        
        # 核心驗證：BIN 分配從 C6 開始，固定分配 BIN 5
        # C6 (col 2): BIN 5 (根據使用者說明 "bin 5固定從C6開始")
        assert df.iloc[5, 2] == "5"    # C6: BIN 5
        
        # 驗證原始資料保持不變
        assert df.iloc[-2, 3] == "3.3"   # VCC_Test 資料
        # 只有一個測試項目，不需要檢查更多資料
        
        print("[OK] BIN 號碼分配程式測試通過")
        
    finally:
        # 清理測試檔案
        if os.path.exists(temp_input_path):
            os.unlink(temp_input_path)
        if os.path.exists(temp_output_path):
            os.unlink(temp_output_path)


if __name__ == "__main__":
    # 直接執行程式測試
    test_bin_assignment_program_execution()