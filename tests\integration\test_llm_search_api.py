"""LLM 搜尋 API 整合測試"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from datetime import datetime
from pathlib import Path

from frontend.api.network_browser_api import app
from backend.shared.domain.entities.file_search import (
    FileInfo, ProductSearchResult, SearchFilters, SearchStatus, TimeRange
)


@pytest.fixture
def client():
    """測試客戶端"""
    return TestClient(app)


@pytest.fixture
def mock_llm_search_service():
    """模擬 LLM 搜尋服務"""
    with patch('src.presentation.api.network_browser_api.llm_search_service') as mock_service:
        mock_service.smart_search = AsyncMock()
        yield mock_service


class TestLLMSearchAPI:
    """LLM 搜尋 API 測試類"""
    
    def test_smart_search_get_success(self, client, mock_llm_search_service):
        """測試 GET 智慧搜尋成功"""
        # 設定模擬回應
        mock_response = {
            "status": "success",
            "query": "搜尋產品 AAA",
            "interpretation": {
                "product_names": ["AAA"],
                "time_range": "last_6_months",
                "confidence": 0.9
            },
            "results": [
                {
                    "product_name": "AAA",
                    "product_folder": "\\\\1***********\\test_log\\AAA",
                    "files_count": 2,
                    "displayed_files_count": 2,
                    "total_size_mb": 1.5,
                    "search_duration": 2.3,
                    "files": [
                        {
                            "path": "\\\\1***********\\test_log\\AAA\\file1.csv",
                            "name": "file1.csv",
                            "size_mb": 0.5,
                            "modified_time": "2024-01-15T10:30:00",
                            "file_type": "CSV 檔案",
                            "is_directory": False
                        },
                        {
                            "path": "\\\\1***********\\test_log\\AAA\\file2.xlsx",
                            "name": "file2.xlsx",
                            "size_mb": 1.0,
                            "modified_time": "2024-01-16T14:20:00",
                            "file_type": "Excel 檔案",
                            "is_directory": False
                        }
                    ]
                }
            ],
            "analysis": {
                "summary": "找到 1 個產品，共 2 個檔案",
                "total_products": 1,
                "total_files": 2,
                "total_size_mb": 1.5,
                "file_types": {
                    "CSV 檔案": 1,
                    "Excel 檔案": 1
                }
            },
            "suggestions": [
                {
                    "type": "process_files",
                    "title": "處理檔案",
                    "description": "對找到的 2 個檔案執行處理操作",
                    "priority": "high",
                    "actions": ["generate_summary", "compare_code"]
                }
            ],
            "search_duration": 2.3,
            "timestamp": "2024-01-17T09:15:30"
        }
        
        mock_llm_search_service.smart_search.return_value = mock_response
        
        # 執行請求
        response = client.get("/api/smart-search?query=搜尋產品 AAA&max_results=100")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert data["query"] == "搜尋產品 AAA"
        assert "interpretation" in data
        assert "results" in data
        assert "analysis" in data
        assert "suggestions" in data
        assert len(data["results"]) == 1
        assert data["results"][0]["product_name"] == "AAA"
        assert len(data["results"][0]["files"]) == 2
    
    def test_smart_search_post_success(self, client, mock_llm_search_service):
        """測試 POST 智慧搜尋成功"""
        # 設定模擬回應
        mock_response = {
            "status": "success",
            "query": "找出產品 BBB 最近 3 個月的 Excel 檔案",
            "interpretation": {
                "product_names": ["BBB"],
                "time_range": "last_3_months",
                "file_types": [".xlsx"],
                "confidence": 0.95
            },
            "results": [
                {
                    "product_name": "BBB",
                    "product_folder": "\\\\1***********\\test_log\\BBB",
                    "files_count": 1,
                    "displayed_files_count": 1,
                    "total_size_mb": 2.1,
                    "search_duration": 1.8,
                    "files": [
                        {
                            "path": "\\\\1***********\\test_log\\BBB\\report.xlsx",
                            "name": "report.xlsx",
                            "size_mb": 2.1,
                            "modified_time": "2024-01-10T16:45:00",
                            "file_type": "Excel 檔案",
                            "is_directory": False
                        }
                    ]
                }
            ],
            "analysis": {
                "summary": "找到 1 個產品，共 1 個檔案",
                "total_products": 1,
                "total_files": 1,
                "total_size_mb": 2.1
            },
            "suggestions": [],
            "search_duration": 1.8,
            "timestamp": "2024-01-17T09:20:15"
        }
        
        mock_llm_search_service.smart_search.return_value = mock_response
        
        # 執行請求
        request_data = {
            "query": "找出產品 BBB 最近 3 個月的 Excel 檔案",
            "path": "\\\\1***********\\test_log",
            "max_results": 50
        }
        
        response = client.post("/api/smart-search", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert data["query"] == "找出產品 BBB 最近 3 個月的 Excel 檔案"
        assert data["interpretation"]["product_names"] == ["BBB"]
        assert data["interpretation"]["time_range"] == "last_3_months"
        assert data["interpretation"]["file_types"] == [".xlsx"]
        assert len(data["results"]) == 1
        assert data["results"][0]["files"][0]["file_type"] == "Excel 檔案"
    
    def test_smart_search_service_unavailable(self, client):
        """測試搜尋服務不可用"""
        with patch('src.presentation.api.network_browser_api.llm_search_service', None):
            response = client.get("/api/smart-search?query=測試查詢")
            
            assert response.status_code == 503
            data = response.json()
            
            assert data["status"] == "service_unavailable"
            assert "LLM 智慧搜尋服務未初始化" in data["message"]
            assert "fallback_endpoint" in data
    
    def test_smart_search_get_error(self, client, mock_llm_search_service):
        """測試 GET 智慧搜尋錯誤"""
        # 模擬服務拋出異常
        mock_llm_search_service.smart_search.side_effect = Exception("搜尋服務內部錯誤")
        
        response = client.get("/api/smart-search?query=測試查詢")
        
        assert response.status_code == 500
        data = response.json()
        
        assert data["status"] == "error"
        assert "搜尋服務內部錯誤" in data["error_message"]
    
    def test_smart_search_post_error(self, client, mock_llm_search_service):
        """測試 POST 智慧搜尋錯誤"""
        # 模擬服務拋出異常
        mock_llm_search_service.smart_search.side_effect = Exception("LLM 服務連接失敗")
        
        request_data = {
            "query": "測試查詢",
            "path": "\\\\1***********\\test_log",
            "max_results": 100
        }
        
        response = client.post("/api/smart-search", json=request_data)
        
        assert response.status_code == 200  # 錯誤在回應體中返回
        data = response.json()
        
        assert data["status"] == "error"
        assert "LLM 服務連接失敗" in data["error_message"]
    
    def test_smart_search_invalid_request(self, client):
        """測試無效請求"""
        # 測試空查詢
        response = client.get("/api/smart-search?query=")
        assert response.status_code == 422  # Validation error
        
        # 測試 POST 無效資料
        request_data = {
            "query": "",  # 空查詢
            "max_results": -1  # 無效的最大結果數
        }
        
        response = client.post("/api/smart-search", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_smart_search_default_parameters(self, client, mock_llm_search_service):
        """測試預設參數"""
        mock_response = {
            "status": "success",
            "query": "簡單查詢",
            "interpretation": {},
            "results": [],
            "analysis": {},
            "suggestions": [],
            "search_duration": 0.5,
            "timestamp": "2024-01-17T09:25:00"
        }
        
        mock_llm_search_service.smart_search.return_value = mock_response
        
        # 測試 GET 預設參數
        response = client.get("/api/smart-search?query=簡單查詢")
        
        assert response.status_code == 200
        
        # 驗證服務被正確調用
        mock_llm_search_service.smart_search.assert_called_once()
        call_args = mock_llm_search_service.smart_search.call_args
        
        assert call_args[0][0] == "簡單查詢"  # query
        assert str(call_args[0][1]) == "\\\\1***********\\test_log"  # default path
        assert call_args[0][2] == 100  # default max_results
    
    def test_smart_search_custom_parameters(self, client, mock_llm_search_service):
        """測試自訂參數"""
        mock_response = {
            "status": "success",
            "query": "自訂查詢",
            "interpretation": {},
            "results": [],
            "analysis": {},
            "suggestions": [],
            "search_duration": 1.2,
            "timestamp": "2024-01-17T09:30:00"
        }
        
        mock_llm_search_service.smart_search.return_value = mock_response
        
        # 測試自訂參數
        custom_path = "\\\\1***********\\custom_path"
        response = client.get(f"/api/smart-search?query=自訂查詢&path={custom_path}&max_results=50")
        
        assert response.status_code == 200
        
        # 驗證服務被正確調用
        call_args = mock_llm_search_service.smart_search.call_args
        
        assert call_args[0][0] == "自訂查詢"  # query
        assert str(call_args[0][1]) == custom_path  # custom path
        assert call_args[0][2] == 50  # custom max_results


class TestLLMSearchAPIPerformance:
    """LLM 搜尋 API 效能測試"""
    
    def test_search_response_time(self, client, mock_llm_search_service):
        """測試搜尋回應時間"""
        import time
        
        mock_response = {
            "status": "success",
            "query": "效能測試",
            "interpretation": {},
            "results": [],
            "analysis": {},
            "suggestions": [],
            "search_duration": 0.1,
            "timestamp": "2024-01-17T09:35:00"
        }
        
        mock_llm_search_service.smart_search.return_value = mock_response
        
        start_time = time.time()
        response = client.get("/api/smart-search?query=效能測試")
        end_time = time.time()
        
        assert response.status_code == 200
        
        # API 回應時間應該在合理範圍內（不包括實際搜尋時間）
        response_time = end_time - start_time
        assert response_time < 1.0  # 1 秒內回應
    
    def test_concurrent_requests(self, client, mock_llm_search_service):
        """測試並行請求處理"""
        import threading
        import time
        
        mock_response = {
            "status": "success",
            "query": "並行測試",
            "interpretation": {},
            "results": [],
            "analysis": {},
            "suggestions": [],
            "search_duration": 0.2,
            "timestamp": "2024-01-17T09:40:00"
        }
        
        mock_llm_search_service.smart_search.return_value = mock_response
        
        results = []
        
        def make_request():
            response = client.get("/api/smart-search?query=並行測試")
            results.append(response.status_code)
        
        # 建立多個並行請求
        threads = []
        for i in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有請求完成
        for thread in threads:
            thread.join()
        
        # 驗證所有請求都成功
        assert len(results) == 5
        assert all(status == 200 for status in results)