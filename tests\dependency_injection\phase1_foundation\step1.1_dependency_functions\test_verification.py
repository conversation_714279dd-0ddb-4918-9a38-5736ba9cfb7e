"""Step 1.1.3: 驗證依賴函數測試
完整驗證修正後的依賴函數在各種場景下的行為
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch

# 設置導入路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from fastapi import HTTPException


def test_require_staging_service_success():
    """測試：暫存服務可用時的正確行為"""
    print("🧪 測試暫存服務可用的情況...")
    
    with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
        # 安排：模擬服務可用
        mock_service = Mock()
        mock_service.service_id = "test_staging_service"
        mock_service.create_staging_task = Mock(return_value="task-123")
        mock_get.return_value = mock_service
        
        # 導入並執行
        from frontend.api.dependencies import require_staging_service
        result = require_staging_service()
        
        # 驗證
        assert result is mock_service
        assert hasattr(result, 'create_staging_task')
        mock_get.assert_called_once()
        
        print("✅ 暫存服務可用測試通過")


def test_require_staging_service_unavailable():
    """測試：暫存服務不可用時的錯誤處理"""
    print("🧪 測試暫存服務不可用的情況...")
    
    with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
        # 安排：模擬服務不可用
        mock_get.return_value = None
        
        # 導入並執行
        from frontend.api.dependencies import require_staging_service
        
        # 驗證：應該拋出 HTTPException
        with pytest.raises(HTTPException) as exc_info:
            require_staging_service()
        
        assert exc_info.value.status_code == 503
        assert "檔案暫存服務不可用" in str(exc_info.value.detail)
        mock_get.assert_called_once()
        
        print("✅ 暫存服務不可用測試通過")


def test_require_staging_service_with_error_details():
    """測試：暫存服務初始化錯誤時的詳細錯誤訊息"""
    print("🧪 測試暫存服務初始化錯誤的情況...")
    
    with patch('src.presentation.api.dependencies.get_staging_service') as mock_get, \
         patch('src.presentation.api.dependencies.get_service_container') as mock_container:
        
        # 安排：模擬服務不可用且有初始化錯誤
        mock_get.return_value = None
        mock_container_instance = Mock()
        mock_container_instance.get_initialization_errors.return_value = {
            'staging': '資料庫連接失敗'
        }
        mock_container.return_value = mock_container_instance
        
        # 導入並執行
        from frontend.api.dependencies import require_staging_service
        
        # 驗證：應該包含詳細錯誤訊息
        with pytest.raises(HTTPException) as exc_info:
            require_staging_service()
        
        assert exc_info.value.status_code == 503
        assert "資料庫連接失敗" in str(exc_info.value.detail)
        
        print("✅ 暫存服務初始化錯誤測試通過")


def test_require_processing_service_success():
    """測試：處理服務可用時的正確行為"""
    print("🧪 測試處理服務可用的情況...")
    
    with patch('src.presentation.api.dependencies.get_processing_service') as mock_get:
        # 安排：模擬服務可用
        mock_service = Mock()
        mock_service.service_id = "test_processing_service"
        mock_service.create_task_with_staging = Mock(return_value="task-456")
        mock_get.return_value = mock_service
        
        # 導入並執行
        from frontend.api.dependencies import require_processing_service
        result = require_processing_service()
        
        # 驗證
        assert result is mock_service
        assert hasattr(result, 'create_task_with_staging')
        mock_get.assert_called_once()
        
        print("✅ 處理服務可用測試通過")


def test_require_processing_service_unavailable():
    """測試：處理服務不可用時的錯誤處理"""
    print("🧪 測試處理服務不可用的情況...")
    
    with patch('src.presentation.api.dependencies.get_processing_service') as mock_get:
        # 安排：模擬服務不可用
        mock_get.return_value = None
        
        # 導入並執行
        from frontend.api.dependencies import require_processing_service
        
        # 驗證：應該拋出 HTTPException
        with pytest.raises(HTTPException) as exc_info:
            require_processing_service()
        
        assert exc_info.value.status_code == 503
        assert "檔案處理服務不可用" in str(exc_info.value.detail)
        mock_get.assert_called_once()
        
        print("✅ 處理服務不可用測試通過")


def test_dependency_functions_return_types():
    """測試：依賴函數返回正確的類型"""
    print("🧪 測試依賴函數返回類型...")

    # 導入函數
    from frontend.api.dependencies import require_staging_service, require_processing_service

    # 驗證函數本身是可調用的
    assert callable(require_staging_service)
    assert callable(require_processing_service)

    # 測試返回值類型（使用真實服務或 None）
    with patch('src.presentation.api.dependencies.get_staging_service') as mock_staging, \
         patch('src.presentation.api.dependencies.get_processing_service') as mock_processing:

        # 創建簡單的非可調用對象
        class MockService:
            def __init__(self, name):
                self.name = name
                self.service_id = name

        mock_staging_service = MockService("staging")
        mock_processing_service = MockService("processing")

        mock_staging.return_value = mock_staging_service
        mock_processing.return_value = mock_processing_service

        # 測試返回值
        staging_result = require_staging_service()
        processing_result = require_processing_service()

        # 檢查返回值類型
        print(f"暫存服務返回類型: {type(staging_result)}")
        print(f"處理服務返回類型: {type(processing_result)}")

        # 驗證返回的是我們設置的對象
        assert staging_result is mock_staging_service
        assert processing_result is mock_processing_service
        assert staging_result.name == "staging"
        assert processing_result.name == "processing"

        print("✅ 依賴函數返回類型測試通過")


def test_error_message_consistency():
    """測試：錯誤訊息的一致性"""
    print("🧪 測試錯誤訊息一致性...")
    
    from frontend.api.dependencies import require_staging_service, require_processing_service
    
    # 測試暫存服務錯誤訊息
    with patch('src.presentation.api.dependencies.get_staging_service', return_value=None):
        try:
            require_staging_service()
            assert False, "應該拋出異常"
        except HTTPException as e:
            assert e.status_code == 503
            assert "檔案暫存服務不可用" in str(e.detail)
    
    # 測試處理服務錯誤訊息
    with patch('src.presentation.api.dependencies.get_processing_service', return_value=None):
        try:
            require_processing_service()
            assert False, "應該拋出異常"
        except HTTPException as e:
            assert e.status_code == 503
            assert "檔案處理服務不可用" in str(e.detail)
    
    print("✅ 錯誤訊息一致性測試通過")


def run_all_verification_tests():
    """運行所有驗證測試"""
    print("🎯 Step 1.1.3: 驗證依賴函數測試")
    print("=" * 60)
    
    tests = [
        test_require_staging_service_success,
        test_require_staging_service_unavailable,
        test_require_staging_service_with_error_details,
        test_require_processing_service_success,
        test_require_processing_service_unavailable,
        test_dependency_functions_return_types,
        test_error_message_consistency
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} 失敗: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 測試結果: {passed} 通過, {failed} 失敗")
    
    if failed == 0:
        print("🎉 所有驗證測試都通過了！")
        print("✅ Step 1.1.3 完成")
    else:
        print("⚠️ 有測試失敗，需要檢查")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_verification_tests()
    exit(0 if success else 1)
