"""Phase 4: Error Handling Validation Testing

This module tests the unified error handling mechanisms implemented
during the dependency injection refactoring.
"""

import pytest
from unittest.mock import Mock, patch
from fastapi import HTTPException

from .conftest import API_ENDPOINTS, TEST_TASK_ID

# Import dependencies for testing
try:
    from frontend.api.dependencies import (
        get_api_state, get_staging_service, get_processing_service,
        require_product_search_service, require_llm_search_service
    )
except ImportError:
    # Handle case where dependencies are not available
    get_api_state = None
    get_staging_service = None
    get_processing_service = None
    require_product_search_service = None
    require_llm_search_service = None


class TestServiceExceptionHandling:
    """Test handling of service-level exceptions."""
    
    def test_staging_service_exception_handling(self, test_app, test_client, mock_staging_service):
        """Test that staging service exceptions are handled gracefully."""
        # Configure mock to raise exception
        mock_staging_service.create_staging_task.side_effect = Exception("Staging service error")
        
        # Override dependency
        test_app.dependency_overrides[get_staging_service] = lambda: mock_staging_service
        
        # Make request that should trigger the exception
        response = test_client.post(
            API_ENDPOINTS["STAGING_CREATE"],
            json={"source_files": ["/test/file.csv"], "target_directory": "/test/output"}
        )
        
        # Should handle exception gracefully (not crash)
        assert response.status_code in [500, 503, 422], "Service exception not handled properly"
        
        # Response should be JSON
        try:
            response_data = response.json()
            assert isinstance(response_data, dict), "Error response should be JSON"
        except:
            # Some endpoints might return HTML error pages, which is also acceptable
            pass
    
    def test_search_service_exception_handling(self, test_app, test_client, mock_product_search_service):
        """Test that search service exceptions are handled gracefully."""
        # Configure mock to raise exception
        mock_product_search_service.get_task_status.side_effect = Exception("Search service error")
        
        # Override dependency
        test_app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service
        
        # Make request that should trigger the exception
        response = test_client.get(
            API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id=TEST_TASK_ID)
        )
        
        # Should handle exception gracefully
        assert response.status_code in [500, 503, 404], "Search service exception not handled properly"
    
    def test_llm_service_exception_handling(self, test_app, test_client, mock_llm_search_service):
        """Test that LLM service exceptions are handled gracefully."""
        # Configure mock to raise exception
        mock_llm_search_service.smart_search.side_effect = Exception("LLM service error")
        
        # Override dependency
        test_app.dependency_overrides[require_llm_search_service] = lambda: mock_llm_search_service
        
        # Make request that should trigger the exception
        response = test_client.get(
            API_ENDPOINTS["SMART_SEARCH_GET"],
            params={"query": "test query"}
        )
        
        # Should handle exception gracefully
        assert response.status_code in [500, 503, 422], "LLM service exception not handled properly"
    
    def test_api_state_exception_handling(self, test_app, test_client, mock_api_state):
        """Test that API state exceptions are handled gracefully."""
        # Configure mock to raise exception
        mock_api_state.increment_request_count.side_effect = Exception("API state error")
        
        # Override dependency
        test_app.dependency_overrides[get_api_state] = lambda: mock_api_state
        
        # Make request that should trigger the exception
        response = test_client.get(API_ENDPOINTS["CELERY_HEALTH"])
        
        # Should handle exception gracefully (might still return 200 if error is caught)
        assert response.status_code in [200, 500, 503], "API state exception not handled properly"


class TestServiceUnavailabilityHandling:
    """Test handling of service unavailability."""
    
    def test_none_staging_service_handling(self, test_app, test_client):
        """Test handling when staging service is None."""
        # Override dependency to return None
        test_app.dependency_overrides[get_staging_service] = lambda: None
        
        # Test dashboard endpoint which should handle None services
        response = test_client.get(API_ENDPOINTS["DASHBOARD"])
        
        # Should handle None service gracefully
        assert response.status_code in [200, 500, 503], "None staging service not handled properly"
    
    def test_none_processing_service_handling(self, test_app, test_client):
        """Test handling when processing service is None."""
        # Override dependency to return None
        test_app.dependency_overrides[get_processing_service] = lambda: None
        
        # Test dashboard endpoint which should handle None services
        response = test_client.get(API_ENDPOINTS["DASHBOARD"])
        
        # Should handle None service gracefully
        assert response.status_code in [200, 500, 503], "None processing service not handled properly"
    
    def test_unhealthy_service_handling(self, test_app, test_client, mock_staging_service):
        """Test handling when services report as unhealthy."""
        # Configure mock to be unhealthy
        mock_staging_service.is_healthy.return_value = False
        
        # Override dependency
        test_app.dependency_overrides[get_staging_service] = lambda: mock_staging_service
        
        # Test dashboard endpoint
        response = test_client.get(API_ENDPOINTS["DASHBOARD"])
        
        # Should handle unhealthy service appropriately
        assert response.status_code in [200, 500, 503], "Unhealthy service not handled properly"


class TestHTTPExceptionHandling:
    """Test handling of HTTP exceptions."""
    
    def test_404_error_handling(self, test_client):
        """Test handling of 404 errors."""
        # Request a non-existent task
        response = test_client.get(
            API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id="non-existent-task")
        )
        
        # Should return appropriate error code
        assert response.status_code in [404, 500], "404 error not handled properly"
    
    def test_422_validation_error_handling(self, test_client):
        """Test handling of validation errors."""
        # Send invalid data
        response = test_client.post(
            API_ENDPOINTS["SEARCH_PRODUCT"],
            json={"invalid": "data"}
        )
        
        # Should return validation error
        assert response.status_code in [422, 400], "Validation error not handled properly"
        
        # Response should contain error details
        try:
            response_data = response.json()
            assert "detail" in response_data or "error" in response_data, "Error details missing"
        except:
            # Some endpoints might handle validation differently
            pass
    
    def test_500_internal_error_handling(self, test_app, test_client, mock_product_search_service):
        """Test handling of internal server errors."""
        # Configure mock to raise a generic exception
        mock_product_search_service.get_task_status.side_effect = RuntimeError("Internal error")
        
        # Override dependency
        test_app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service
        
        # Make request that should trigger internal error
        response = test_client.get(
            API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id=TEST_TASK_ID)
        )
        
        # Should return internal server error
        assert response.status_code in [500, 503], "Internal error not handled properly"


class TestErrorResponseFormat:
    """Test that error responses follow consistent format."""
    
    def test_error_response_structure(self, test_client):
        """Test that error responses have consistent structure."""
        # Make a request that should return an error
        response = test_client.get(
            API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id="invalid-task-id")
        )
        
        # Should return an error status
        assert response.status_code >= 400, "Should return error status"
        
        # Try to parse response as JSON
        try:
            response_data = response.json()
            
            # Should have error information
            assert isinstance(response_data, dict), "Error response should be JSON object"
            
            # Common error fields
            error_fields = ["detail", "error", "message", "status"]
            has_error_field = any(field in response_data for field in error_fields)
            assert has_error_field, "Error response should contain error information"
            
        except:
            # Some endpoints might return HTML error pages, which is acceptable
            assert response.headers.get("content-type", "").startswith(("text/html", "application/json"))
    
    def test_error_logging_integration(self, test_app, test_client, mock_product_search_service):
        """Test that errors are properly logged."""
        # Configure mock to raise exception
        mock_product_search_service.get_task_status.side_effect = Exception("Test logging error")
        
        # Override dependency
        test_app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service
        
        # Capture logs
        with patch('logging.Logger.error') as mock_logger:
            # Make request that should trigger error
            response = test_client.get(
                API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id=TEST_TASK_ID)
            )
            
            # Should handle error
            assert response.status_code in [500, 503], "Error not handled"
            
            # Should log error (this is optional, depends on implementation)
            # We don't assert this strictly since logging implementation may vary


class TestErrorRecovery:
    """Test error recovery mechanisms."""
    
    def test_service_recovery_after_error(self, test_app, test_client, mock_product_search_service):
        """Test that services can recover after errors."""
        # First, configure mock to raise exception
        mock_product_search_service.get_task_status.side_effect = Exception("Temporary error")
        
        # Override dependency
        test_app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service
        
        # Make request that should fail
        response1 = test_client.get(
            API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id=TEST_TASK_ID)
        )
        assert response1.status_code in [500, 503], "First request should fail"
        
        # Now configure mock to work normally
        mock_product_search_service.get_task_status.side_effect = None
        mock_product_search_service.get_task_status.return_value = {"status": "completed"}
        
        # Make another request that should succeed
        response2 = test_client.get(
            API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id=TEST_TASK_ID)
        )
        assert response2.status_code in [200, 404], "Second request should succeed"
    
    def test_partial_service_failure_handling(self, test_app, test_client, mock_staging_service, mock_processing_service):
        """Test handling when only some services fail."""
        # Configure one service to fail
        mock_staging_service.get_status.side_effect = Exception("Staging service error")
        
        # Configure other service to work
        mock_processing_service.get_status.return_value = {"status": "healthy"}
        
        # Override dependencies
        test_app.dependency_overrides[get_staging_service] = lambda: mock_staging_service
        test_app.dependency_overrides[get_processing_service] = lambda: mock_processing_service
        
        # Test dashboard endpoint which uses both services
        response = test_client.get(API_ENDPOINTS["DASHBOARD"])
        
        # Should handle partial failure gracefully
        assert response.status_code in [200, 500, 503], "Partial service failure not handled properly"


class TestConcurrentErrorHandling:
    """Test error handling under concurrent load."""
    
    def test_concurrent_error_handling(self, test_app, test_client, mock_product_search_service):
        """Test that errors are handled properly under concurrent load."""
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        # Configure mock to raise exception
        mock_product_search_service.get_task_status.side_effect = Exception("Concurrent error")
        
        # Override dependency
        test_app.dependency_overrides[require_product_search_service] = lambda: mock_product_search_service
        
        def make_error_request():
            response = test_client.get(
                API_ENDPOINTS["SEARCH_TASK_STATUS"].format(task_id=TEST_TASK_ID)
            )
            return response.status_code
        
        # Run concurrent requests that should all fail
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_error_request) for _ in range(10)]
            status_codes = [future.result() for future in as_completed(futures)]
        
        # All should return error status codes
        error_codes = [code for code in status_codes if code >= 400]
        assert len(error_codes) >= 8, "Concurrent errors not handled consistently"
        
        # Should not crash or return unexpected codes
        valid_error_codes = [500, 503, 404]
        invalid_codes = [code for code in error_codes if code not in valid_error_codes]
        assert len(invalid_codes) == 0, f"Invalid error codes returned: {invalid_codes}"
