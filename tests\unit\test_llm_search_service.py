"""LLM 搜尋服務單元測試"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from pathlib import Path

from backend.shared.infrastructure.adapters.llm_search_service import LLMSearchService
from backend.shared.infrastructure.adapters.product_search_service import ProductSearchService
from backend.shared.domain.entities.file_search import (
    FileInfo, ProductSearchResult, SearchFilters, SearchStatus, TimeRange
)
from backend.shared.models.search_models import TimeRangeType


@pytest.fixture
def mock_product_search_service():
    """模擬產品搜尋服務"""
    service = Mock(spec=ProductSearchService)
    service.create_time_range = Mock(return_value=TimeRange.last_months(6))
    return service


@pytest.fixture
def mock_llm_client():
    """模擬 LLM 客戶端"""
    with patch('src.services.llm_search_service.UnifiedLLMClient') as mock_client_class:
        mock_client = Mock()
        mock_client.is_available.return_value = True
        mock_client.parse_email.return_value = Mock(
            is_success=True,
            raw_response='{"product_names": ["AAA"], "time_range": "last_6_months", "confidence": 0.9}'
        )
        mock_client_class.return_value = mock_client
        yield mock_client


@pytest.fixture
def llm_search_service(mock_product_search_service, mock_llm_client):
    """LLM 搜尋服務實例"""
    return LLMSearchService(mock_product_search_service)


class TestLLMSearchService:
    """LLM 搜尋服務測試類"""
    
    @pytest.mark.asyncio
    async def test_interpret_natural_query_success(self, llm_search_service, mock_llm_client):
        """測試自然語言查詢解析成功"""
        query = "搜尋產品 AAA 最近 6 個月的檔案"
        
        result = await llm_search_service.interpret_natural_query(query)
        
        assert result is not None
        assert "product_names" in result
        assert "time_range" in result
        assert "confidence" in result
    
    @pytest.mark.asyncio
    async def test_interpret_natural_query_llm_unavailable(self, llm_search_service, mock_llm_client):
        """測試 LLM 不可用時的後備解析"""
        mock_llm_client.is_available.return_value = False
        
        query = "搜尋產品 AAA 最近一個月的檔案"
        result = await llm_search_service.interpret_natural_query(query)
        
        assert result is not None
        assert result["product_names"] == ["AAA"]
        assert result["time_range"] == "last_month"
        assert result["confidence"] == 0.6  # 規則式解析的信心分數
    
    @pytest.mark.asyncio
    async def test_smart_search_success(self, llm_search_service, mock_product_search_service):
        """測試智慧搜尋成功"""
        # 設定模擬搜尋結果
        mock_file = FileInfo(
            path=Path("test/file.csv"),
            name="file.csv",
            size=1024,
            modified_time=datetime.now(),
            file_type="CSV 檔案",
            is_directory=False
        )
        
        mock_result = ProductSearchResult(
            product_name="AAA",
            product_folder=Path("test/AAA"),
            matched_files=[mock_file],
            total_files=1,
            search_duration=1.5,
            filters_applied=SearchFilters(),
            status=SearchStatus.COMPLETED
        )
        
        mock_product_search_service.search_product_folder = AsyncMock(return_value=mock_result)
        
        query = "搜尋產品 AAA 的檔案"
        base_path = Path("\\\\************\\test_log")
        
        result = await llm_search_service.smart_search(query, base_path, 100)
        
        assert result["status"] == "success"
        assert result["query"] == query
        assert "interpretation" in result
        assert "results" in result
        assert "analysis" in result
        assert "suggestions" in result
        assert len(result["results"]) > 0
    
    @pytest.mark.asyncio
    async def test_analyze_search_results_empty(self, llm_search_service):
        """測試分析空搜尋結果"""
        results = []
        query = "測試查詢"
        
        analysis = await llm_search_service.analyze_search_results(results, query)
        
        assert analysis["total_products"] == 0
        assert analysis["total_files"] == 0
        assert "recommendations" in analysis
        assert len(analysis["recommendations"]) > 0
    
    @pytest.mark.asyncio
    async def test_analyze_search_results_with_data(self, llm_search_service):
        """測試分析有資料的搜尋結果"""
        mock_file1 = FileInfo(
            path=Path("test/file1.csv"),
            name="file1.csv",
            size=1024,
            modified_time=datetime.now(),
            file_type="CSV 檔案",
            is_directory=False
        )
        
        mock_file2 = FileInfo(
            path=Path("test/file2.xlsx"),
            name="file2.xlsx",
            size=2048,
            modified_time=datetime.now() - timedelta(days=10),
            file_type="Excel 檔案",
            is_directory=False
        )
        
        mock_result = ProductSearchResult(
            product_name="AAA",
            product_folder=Path("test/AAA"),
            matched_files=[mock_file1, mock_file2],
            total_files=2,
            search_duration=1.5,
            filters_applied=SearchFilters(),
            status=SearchStatus.COMPLETED
        )
        
        results = [mock_result]
        query = "測試查詢"
        
        analysis = await llm_search_service.analyze_search_results(results, query)
        
        assert analysis["total_products"] == 1
        assert analysis["total_files"] == 2
        assert analysis["total_size_mb"] > 0
        assert "file_types" in analysis
        assert "CSV 檔案" in analysis["file_types"]
        assert "Excel 檔案" in analysis["file_types"]
        assert "product_breakdown" in analysis
        assert len(analysis["product_breakdown"]) == 1
    
    @pytest.mark.asyncio
    async def test_generate_action_suggestions_no_results(self, llm_search_service):
        """測試無結果時的建議生成"""
        results = []
        search_params = {"product_names": ["AAA"]}
        
        suggestions = await llm_search_service.generate_action_suggestions(results, search_params)
        
        assert len(suggestions) > 0
        assert any(s["type"] == "search_refinement" for s in suggestions)
        assert any(s["type"] == "path_check" for s in suggestions)
    
    @pytest.mark.asyncio
    async def test_generate_action_suggestions_with_results(self, llm_search_service):
        """測試有結果時的建議生成"""
        mock_file = FileInfo(
            path=Path("test/file.csv"),
            name="file.csv",
            size=1024,
            modified_time=datetime.now(),
            file_type="CSV 檔案",
            is_directory=False
        )
        
        mock_result = ProductSearchResult(
            product_name="AAA",
            product_folder=Path("test/AAA"),
            matched_files=[mock_file],
            total_files=1,
            search_duration=1.5,
            filters_applied=SearchFilters(),
            status=SearchStatus.COMPLETED
        )
        
        results = [mock_result]
        search_params = {"product_names": ["AAA"]}
        
        suggestions = await llm_search_service.generate_action_suggestions(results, search_params)
        
        assert len(suggestions) > 0
        assert any(s["type"] == "process_files" for s in suggestions)
    
    def test_fallback_query_parsing(self, llm_search_service):
        """測試後備查詢解析"""
        query = "搜尋產品 AAA 最近一個月的 CSV 檔案"
        
        result = llm_search_service._fallback_query_parsing(query)
        
        assert "AAA" in result["product_names"]
        assert result["time_range"] == "last_month"
        assert ".csv" in result["file_types"]
        assert result["confidence"] == 0.6
    
    def test_create_time_range_from_params(self, llm_search_service, mock_product_search_service):
        """測試從參數建立時間範圍"""
        params = {"time_range": "last_3_months"}
        
        time_range = llm_search_service._create_time_range_from_params(params)
        
        mock_product_search_service.create_time_range.assert_called_once()
        assert time_range is not None
    
    def test_create_filters_from_params(self, llm_search_service):
        """測試從參數建立篩選條件"""
        time_range = TimeRange.last_months(6)
        params = {
            "file_types": [".csv", ".xlsx"],
            "min_size_mb": 1.0,
            "max_size_mb": 10.0
        }
        
        filters = llm_search_service._create_filters_from_params(params, time_range)
        
        assert filters.time_range == time_range
        assert filters.file_types == [".csv", ".xlsx"]
        assert filters.min_size == 1024 * 1024  # 1MB in bytes
        assert filters.max_size == 10 * 1024 * 1024  # 10MB in bytes
    
    def test_format_search_results(self, llm_search_service):
        """測試搜尋結果格式化"""
        mock_file = FileInfo(
            path=Path("test/file.csv"),
            name="file.csv",
            size=1024,
            modified_time=datetime.now(),
            file_type="CSV 檔案",
            is_directory=False
        )
        
        mock_result = ProductSearchResult(
            product_name="AAA",
            product_folder=Path("test/AAA"),
            matched_files=[mock_file],
            total_files=1,
            search_duration=1.5,
            filters_applied=SearchFilters(),
            status=SearchStatus.COMPLETED
        )
        
        results = [mock_result]
        formatted = llm_search_service._format_search_results(results, 100)
        
        assert len(formatted) == 1
        assert formatted[0]["product_name"] == "AAA"
        assert formatted[0]["files_count"] == 1
        assert len(formatted[0]["files"]) == 1
        assert formatted[0]["files"][0]["name"] == "file.csv"
    
    def test_analyze_time_distribution(self, llm_search_service):
        """測試時間分佈分析"""
        now = datetime.now()
        
        # 建立不同時間的檔案
        files = [
            FileInfo(
                path=Path("recent.csv"),
                name="recent.csv",
                size=1024,
                modified_time=now - timedelta(days=3),  # 最近一週
                file_type="CSV 檔案",
                is_directory=False
            ),
            FileInfo(
                path=Path("month.csv"),
                name="month.csv",
                size=1024,
                modified_time=now - timedelta(days=20),  # 最近一個月
                file_type="CSV 檔案",
                is_directory=False
            ),
            FileInfo(
                path=Path("old.csv"),
                name="old.csv",
                size=1024,
                modified_time=now - timedelta(days=120),  # 較舊
                file_type="CSV 檔案",
                is_directory=False
            )
        ]
        
        mock_result = ProductSearchResult(
            product_name="AAA",
            product_folder=Path("test/AAA"),
            matched_files=files,
            total_files=3,
            search_duration=1.5,
            filters_applied=SearchFilters(),
            status=SearchStatus.COMPLETED
        )
        
        results = [mock_result]
        distribution = llm_search_service._analyze_time_distribution(results)
        
        assert distribution["last_week"] == 1
        assert distribution["last_month"] == 1
        assert distribution["older"] == 1
        assert distribution["last_3_months"] == 0


class TestLLMSearchServiceIntegration:
    """LLM 搜尋服務整合測試"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_search_flow(self, mock_product_search_service):
        """測試端對端搜尋流程"""
        # 這個測試需要真實的 LLM 服務，所以先跳過
        pytest.skip("需要真實的 LLM 服務進行整合測試")
    
    @pytest.mark.asyncio
    async def test_error_handling(self, llm_search_service, mock_product_search_service):
        """測試錯誤處理"""
        # 模擬產品搜尋服務拋出異常
        mock_product_search_service.search_product_folder = AsyncMock(
            side_effect=Exception("搜尋服務錯誤")
        )
        
        query = "搜尋產品 AAA"
        base_path = Path("\\\\************\\test_log")
        
        result = await llm_search_service.smart_search(query, base_path, 100)
        
        assert result["status"] == "error"
        assert "error_message" in result
        assert "搜尋服務錯誤" in result["error_message"]