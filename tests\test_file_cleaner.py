"""
測試檔案清理服務
遵循TDD原則：先寫測試，確保測試先失敗
"""

import pytest
import os
import tempfile
import time
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# 這個import會失敗，因為我們還沒有實作 FileCleaner
# 這正是TDD的第一步：確保測試先失敗
try:
    from backend.shared.infrastructure.adapters.file_cleaner import FileCleaner
except ImportError:
    # 預期的失敗，FileCleaner 還不存在
    FileCleaner = None


class TestFileCleaner:
    """檔案清理服務測試類別"""
    
    def setup_method(self):
        """測試前準備"""
        # 創建臨時測試目錄
        self.test_dir = tempfile.mkdtemp()
        self.cleaner = FileCleaner() if FileCleaner else None
        
    def teardown_method(self):
        """測試後清理"""
        # 清理測試目錄
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_file_cleaner_can_be_imported(self):
        """測試：FileCleaner 類別現在應該可以正常匯入（TDD完成）"""
        # FileCleaner 已經實作完成，應該可以正常匯入
        assert FileCleaner is not None, "FileCleaner 應該已經實作完成"
        assert hasattr(FileCleaner, 'clean_old_files'), "應該有 clean_old_files 方法"
    
    def test_clean_old_files_basic_functionality(self):
        """測試：清理超過指定時間的檔案 - 基本功能"""
        if FileCleaner is None:
            pytest.skip("FileCleaner 尚未實作")
            
        # 創建測試檔案
        old_file = os.path.join(self.test_dir, "old_file.txt")
        new_file = os.path.join(self.test_dir, "new_file.txt")
        
        # 創建檔案
        with open(old_file, 'w') as f:
            f.write("old content")
        with open(new_file, 'w') as f:
            f.write("new content")
            
        # 修改舊檔案的時間戳（設為25小時前）
        old_time = time.time() - (25 * 3600)
        os.utime(old_file, (old_time, old_time))
        
        # 執行清理
        cleaned_files = self.cleaner.clean_old_files(self.test_dir, hours=24)
        
        # 驗證結果
        assert "old_file.txt" in cleaned_files
        assert not os.path.exists(old_file), "舊檔案應該被刪[EXCEPT_CHAR]"
        assert os.path.exists(new_file), "新檔案應該保留"
    
    def test_clean_old_files_with_custom_hours(self):
        """測試：使用自訂小時數清理檔案"""
        if FileCleaner is None:
            pytest.skip("FileCleaner 尚未實作")
            
        # 創建測試檔案
        test_file = os.path.join(self.test_dir, "test_file.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
            
        # 設定檔案時間為2小時前
        old_time = time.time() - (2 * 3600)
        os.utime(test_file, (old_time, old_time))
        
        # 使用1小時的清理規則
        cleaned_files = self.cleaner.clean_old_files(self.test_dir, hours=1)
        
        # 驗證結果
        assert "test_file.txt" in cleaned_files
        assert not os.path.exists(test_file)
    
    def test_clean_old_files_preserves_new_files(self):
        """測試：保留新檔案不被清理"""
        if FileCleaner is None:
            pytest.skip("FileCleaner 尚未實作")
            
        # 創建新檔案
        new_file = os.path.join(self.test_dir, "new_file.txt")
        with open(new_file, 'w') as f:
            f.write("new content")
            
        # 執行清理
        cleaned_files = self.cleaner.clean_old_files(self.test_dir, hours=24)
        
        # 驗證結果
        assert len(cleaned_files) == 0, "不應該清理任何檔案"
        assert os.path.exists(new_file), "新檔案應該保留"
    
    def test_clean_old_files_handles_empty_directory(self):
        """測試：處理空目錄"""
        if FileCleaner is None:
            pytest.skip("FileCleaner 尚未實作")
            
        # 執行清理空目錄
        cleaned_files = self.cleaner.clean_old_files(self.test_dir, hours=24)
        
        # 驗證結果
        assert len(cleaned_files) == 0, "空目錄不應該清理任何檔案"
    
    def test_clean_old_files_handles_nonexistent_directory(self):
        """測試：處理不存在的目錄"""
        if FileCleaner is None:
            pytest.skip("FileCleaner 尚未實作")
            
        # 測試不存在的目錄
        nonexistent_dir = "/nonexistent/directory"
        
        # 應該拋出異常或返回空列表
        with pytest.raises((FileNotFoundError, OSError)):
            self.cleaner.clean_old_files(nonexistent_dir, hours=24)
    
    def test_clean_old_files_with_subdirectories(self):
        """測試：只清理檔案，不處理子目錄"""
        if FileCleaner is None:
            pytest.skip("FileCleaner 尚未實作")
            
        # 創建子目錄和檔案
        sub_dir = os.path.join(self.test_dir, "subdir")
        os.makedirs(sub_dir)
        
        old_file = os.path.join(self.test_dir, "old_file.txt")
        with open(old_file, 'w') as f:
            f.write("old content")
            
        # 設定舊檔案時間
        old_time = time.time() - (25 * 3600)
        os.utime(old_file, (old_time, old_time))
        
        # 執行清理
        cleaned_files = self.cleaner.clean_old_files(self.test_dir, hours=24)
        
        # 驗證結果
        assert "old_file.txt" in cleaned_files
        assert os.path.exists(sub_dir), "子目錄應該保留"
        assert not os.path.exists(old_file), "舊檔案應該被刪[EXCEPT_CHAR]"


class TestFileCleanerScheduler:
    """測試檔案清理調度器"""
    
    def test_scheduler_should_be_importable(self):
        """測試：調度器應該可以匯入（當實作完成後）"""
        try:
            from src.background.scheduler import FileCleanupScheduler
            assert FileCleanupScheduler is not None
        except ImportError:
            # 目前預期會失敗
            assert True, "調度器尚未實作，這是預期的"


if __name__ == "__main__":
    # 執行測試以確保測試先失敗
    pytest.main([__file__, "-v"])