"""
CSV 到 Excel 轉換器 - 核心 3 步驟版本
遵循 CLAUDE.md 功能替換原則：直接取代舊版本

步驟 1: 結構補全 (C7-C11)
步驟 2: BIN 號碼分配 (第6欄)
步驟 3: BIN1 保護機制
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import openpyxl
from openpyxl.styles import Font, PatternFill
from dataclasses import dataclass
from dotenv import load_dotenv
import time
import xlsxwriter
from backend.shared.infrastructure.adapters.excel.advanced_performance_manager import performance_manager
from backend.shared.infrastructure.adapters.excel.site_column_finder import SiteColumnFinder
from backend.shared.infrastructure.adapters.excel.strategy_b_processor import StrategyBProcessor
from backend.shared.infrastructure.adapters.excel.summary_generator import SummaryGenerator
from backend.shared.infrastructure.adapters.excel.ft_summary_converter import FTSummaryConverter

# 載入環境變數
load_dotenv()


@dataclass
class ConversionResult:
    """轉換結果 - 精簡版"""
    success: bool
    output_file: str
    protection_positions: List[Tuple[int, int]] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    total_rows: int = 0
    total_columns: int = 0

    def __post_init__(self):
        if self.protection_positions is None:
            self.protection_positions = []


class CsvToExcelConverter:
    """
    CSV 轉換器 - 核心 3 步驟
    
    功能替換原則：取代舊版本，只保留必要功能
    """
    
    def __init__(self):
        self.max_pass_bin = 4  # 業務邏輯固定
        
        # 載入 BIN1 保護配置
        self.bin1_protection = os.getenv("BIN1_PROTECTION", "true").lower() == "true"
        
        # 初始化 Site 欄位查找器、策略 B 處理器和 Summary 生成器
        self.site_finder = SiteColumnFinder(enable_logging=True)
        self.strategy_b_processor = StrategyBProcessor(enable_logging=True)
        self.summary_generator = SummaryGenerator(enable_logging=True)
        self.ft_summary_converter = FTSummaryConverter()  # FT_SUMMARY 專用轉換器
        self.site_column = None  # Site 欄位位置
        
        print(f"初始化核心 4 步驟轉換器 (BIN1 保護: {'開啟' if self.bin1_protection else '關閉'})")

    def _safe_read_csv(self, csv_file_path: str) -> pd.DataFrame:
        """安全讀取 CSV 檔案"""
        try:
            lines = []
            max_cols = 0
            
            with open(csv_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    parts = line.strip().split(',')
                    lines.append(parts)
                    max_cols = max(max_cols, len(parts))
            
            # 統一欄數
            for parts in lines:
                while len(parts) < max_cols:
                    parts.append('')
            
            df = pd.DataFrame(lines)
            
            # 動態更新配置
            self.max_devices = len(df)
            self.max_test_items = len(df.columns)
            
            print(f"CSV 讀取成功: {len(df)} 行 x {len(df.columns)} 欄")
            return df
            
        except Exception as e:
            print(f"CSV 讀取失敗: {str(e)}")
            return pd.DataFrame()

    def complete_csv_structure(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        步驟 1: CSV 結構補全 (C7-C11)
        """
        print("執行步驟 1: CSV 結構補全")
        
        # 確保有足夠的行和列
        min_rows_needed = 11
        min_cols_needed = 3
        
        if len(df) < min_rows_needed:
            additional_rows = min_rows_needed - len(df)
            for i in range(additional_rows):
                new_row = [''] * len(df.columns)
                df.loc[len(df)] = new_row
        
        if len(df.columns) < min_cols_needed:
            additional_cols = min_cols_needed - len(df.columns)
            for i in range(additional_cols):
                df[len(df.columns)] = None
        
        # 關鍵結構補全規則
        critical_rules = {
            6: "0.00.01",    # C7: 測試項目分組
            7: "Test_Time",  # C8: 測試項目名稱
            9: "none",       # C10: Max 限值
            10: "none",      # C11: Min 限值
        }
        
        structure_modified = False
        for row_idx, default_value in critical_rules.items():
            if row_idx < len(df):
                current_value = df.iloc[row_idx, 2]  # C 欄 (index 2)
                is_empty = (pd.isna(current_value) or 
                           current_value == '' or 
                           str(current_value).strip().lower() in ['none', 'null', 'na', 'n/a'])
                
                if is_empty:
                    df.iloc[row_idx, 2] = default_value
                    structure_modified = True
                    print(f"結構補全: 行{row_idx+1} C欄 空白 → '{default_value}'")
        
        if structure_modified:
            print("[OK] CSV 結構補全完成")
        else:
            print("CSV 結構無需補全")
            
        return df

    def assign_bin_numbers_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        步驟 2: BIN 號碼分配 (第6欄)
        """
        print("執行步驟 2: BIN 號碼分配")
        
        if df.empty:
            print("DataFrame 為空，跳過 BIN 分配")
            return df
        
        # 確保第6行存在
        while len(df) <= 5:
            new_row = [''] * len(df.columns)
            df.loc[len(df)] = new_row
        
        # 從第3欄開始分配 BIN (C欄開始，對照 VBA)
        bin_assigned = False
        current_bin = 5  # 從 BIN 5 開始
        bin_count = 0
        
        for col_idx in range(2, len(df.columns)):  # 從 C 欄 (index 2) 開始
            if col_idx >= 2:  # C 欄以後才分配
                df.iloc[5, col_idx] = current_bin  # 第6行 (index 5)
                bin_assigned = True
                bin_count += 1
                # 只顯示前 10 個和最後幾個 BIN 分配
                if bin_count <= 10 or bin_count >= len(df.columns) - 7:
                    col_letter = chr(65 + col_idx) if col_idx < 26 else f"Col{col_idx+1}"
                    print(f"BIN 分配: {col_letter}6 → BIN {current_bin}")
                elif bin_count == 11:
                    print("  ... (省略中間 BIN 分配) ...")
                current_bin += 1
        
        if bin_assigned:
            print("[OK] BIN 號碼分配完成")
        else:
            print("未分配任何 BIN 號碼")
            
        return df

    def apply_bin1_protection_df(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Tuple[int, int]]]:
        """
        步驟 3: BIN1 保護機制 (向量化優化版本)
        功能替換原則：保持邏輯完全一致，大幅提升性能
        """
        print("執行步驟 3: BIN1 保護機制 (向量化優化版本)")
        
        start_time = time.perf_counter()
        protection_positions = []
        
        if df.empty:
            print("DataFrame 為空，跳過 BIN1 保護")
            return df, protection_positions
        
        # 常數定義
        test_item_start_col = 2  # C 欄
        device_data_start_row = 12  # 第13行
        max_limit_row = 9   # 第10行 (Max)
        min_limit_row = 10  # 第11行 (Min)
        
        # [ROCKET] 向量化步驟 1: 快速識別 BIN1 設備
        bin1_start = time.perf_counter()
        if len(df) <= device_data_start_row or len(df.columns) <= 1:
            print("資料不足，跳過 BIN1 保護")
            return df, protection_positions
        
        # 使用向量化操作識別 BIN1 設備
        bin_column_data = df.iloc[device_data_start_row:, 1].astype(str).str.strip()
        bin1_mask = bin_column_data == "1"
        bin1_device_indices = bin1_mask[bin1_mask].index.tolist()
        
        if not bin1_device_indices:
            print("沒有 BIN1 設備，無需 BIN1 保護")
            return df, protection_positions
        
        bin1_time = time.perf_counter() - bin1_start
        print(f"[SEARCH] 發現 {len(bin1_device_indices)} 個 BIN1 設備，識別時間: {bin1_time:.3f} 秒")
        
        # [ROCKET] 向量化步驟 2: 批量提取限值和設備資料
        extract_start = time.perf_counter()
        
        # 批量提取限值
        max_limits = df.iloc[max_limit_row, test_item_start_col:].values
        min_limits = df.iloc[min_limit_row, test_item_start_col:].values
        
        # 批量提取 BIN1 設備的測試資料
        bin1_device_data = df.iloc[bin1_device_indices, test_item_start_col:].values
        
        num_test_items = len(max_limits)
        num_bin1_devices = len(bin1_device_indices)
        
        extract_time = time.perf_counter() - extract_start
        print(f"[CHART] 批量資料提取完成: {extract_time:.3f} 秒，{num_bin1_devices} 設備 × {num_test_items} 測試項目")
        
        # [ROCKET] 向量化步驟 3: 批量限值有效性檢查
        valid_start = time.perf_counter()
        
        # 向量化檢查限值有效性
        max_valid = self._vectorized_limit_validity_check(max_limits)
        min_valid = self._vectorized_limit_validity_check(min_limits)
        has_valid_limits = max_valid | min_valid
        
        # 只處理有有效限值的測試項目
        valid_test_indices = np.where(has_valid_limits)[0]
        
        valid_time = time.perf_counter() - valid_start
        print(f"[OK] 限值有效性檢查完成: {valid_time:.3f} 秒，{len(valid_test_indices)} 個有效測試項目")
        
        if len(valid_test_indices) == 0:
            print("沒有有效限值的測試項目，無需 BIN1 保護")
            return df, protection_positions
        
        # [ROCKET] 向量化步驟 4: 批量失敗和零值檢查
        check_start = time.perf_counter()
        
        protection_log = []
        protection_applied = False
        
        # 對有效測試項目進行向量化檢查
        for i, test_col_offset in enumerate(valid_test_indices):
            actual_col_idx = test_item_start_col + test_col_offset
            max_limit = max_limits[test_col_offset]
            min_limit = min_limits[test_col_offset]
            
            # 向量化檢查該測試項目的所有 BIN1 設備
            device_values = bin1_device_data[:, test_col_offset]
            
            # 批量檢查失敗和零值條件
            would_fail_mask = self._vectorized_failure_check(device_values, max_limit, min_limit, max_valid[test_col_offset], min_valid[test_col_offset])
            is_zero_mask = self._vectorized_zero_check(device_values)
            
            # BIN1 保護邏輯：任一設備會失敗且數值為 0 才放寬限值
            should_relax = np.any(would_fail_mask & is_zero_mask)
            
            if should_relax:
                # 記錄保護資訊
                col_letter = chr(65 + actual_col_idx) if actual_col_idx < 26 else f"Col{actual_col_idx+1}"
                test_item_group = df.iloc[6, actual_col_idx] if len(df) > 6 else "N/A"
                test_item_name = df.iloc[7, actual_col_idx] if len(df) > 7 else "N/A"
                
                protection_log.append({
                    "欄位": col_letter,
                    "測試項目編號": str(test_item_group),
                    "測試項目名稱": str(test_item_name),
                    "原始Max限值": str(max_limit),
                    "原始Min限值": str(min_limit),
                    "step3結果": "Max & Min → none"
                })
                
                # 批量放寬限值
                df.iloc[max_limit_row, actual_col_idx] = "none"
                df.iloc[min_limit_row, actual_col_idx] = "none"
                protection_applied = True
                
                # 記錄保護位置供紅色標記
                protection_positions.extend([
                    (max_limit_row, actual_col_idx),
                    (min_limit_row, actual_col_idx)
                ])
        
        check_time = time.perf_counter() - check_start
        print(f"[SHIELD] 保護邏輯檢查完成: {check_time:.3f} 秒，保護 {len(protection_log)} 個測試項目")
        
        # 總時間統計
        total_time = time.perf_counter() - start_time
        print(f"\n[PARTY] 向量化 BIN1 保護完成統計:")
        print(f"  [OK] 檢查 {num_bin1_devices} 個 BIN1 設備")
        print(f"  [FAST] 總處理時間: {total_time:.3f} 秒")
        print(f"  [CHART] 檢查次數: {num_bin1_devices * len(valid_test_indices):,} 次")
        print(f"  [ROCKET] 處理速度: {(num_bin1_devices * len(valid_test_indices)) / total_time:,.0f} 次檢查/秒")
        print(f"  [SHIELD] 保護項目: {len(protection_log)} 個")
        print(f"  [UP] 性能提升: 向量化優化相比逐一檢查")
        
        # BIN1 保護記錄輸出
        if protection_log and self.bin1_protection:
            # 統一輸出到 logs 資料夾的 datalog.txt
            project_root = "/mnt/d/project/python/outlook_summary"
            logs_dir = os.path.join(project_root, "logs")
            os.makedirs(logs_dir, exist_ok=True)
            log_filename = os.path.join(logs_dir, "datalog.txt")
            
            timestamp = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            
            log_content = []
            log_content.append("[RED_CIRCLE] BIN1 保護記錄")
            log_content.append("=" * 80)
            log_content.append(f"發現 BIN1 設備數量: {len(bin1_device_indices)}")
            log_content.append(f"需要保護的測試項目數量: {len(protection_log)}")
            log_content.append("")
            log_content.append("| 欄位      | 測試項目編號   | 測試項目名稱            | 原始 Max 限值  | 原始 Min 限值  | step3 結果         |")
            log_content.append("|---------|----------|-------------------|------------|------------|------------------|")
            
            for log in protection_log:
                log_content.append(f"| {log['欄位']:<7} | {log['測試項目編號']:<8} | {log['測試項目名稱']:<17} | {log['原始Max限值']:<10} | {log['原始Min限值']:<10} | {log['step3結果']:<16} |")
            
            log_content.append("")
            log_content.append(f"BIN1 保護機制完成時間: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            try:
                # 附加模式寫入，保留之前的記錄
                with open(log_filename, 'a', encoding='utf-8') as f:
                    f.write(f"\n{'='*80}\n")
                    f.write(f"BIN1 保護記錄 - {timestamp}\n")
                    f.write(f"{'='*80}\n")
                    f.write('\n'.join(log_content))
                    f.write(f"\n{'='*80}\n\n")
                print(f"\n[EDIT] BIN1 保護記錄已附加到: datalog.txt")
            except Exception as e:
                print(f"\n[WARNING] 無法保存保護記錄: {str(e)}")
        elif protection_log:
            print(f"\n[RED_CIRCLE] BIN1 保護觸發: {len(protection_log)} 個項目")
        
        if protection_applied:
            print(f"[OK] BIN1 保護機制完成，共 {len(protection_positions)} 個位置需要紅色標記")
        else:
            print("BIN1 保護：無需放寬任何限值")
            
        return df, protection_positions

    def _is_valid_limit(self, limit_value) -> bool:
        """檢查是否為有效限值"""
        if pd.isna(limit_value) or limit_value == '' or str(limit_value).strip().lower() == 'none':
            return False
        try:
            float(str(limit_value))
            return True
        except (ValueError, TypeError):
            return False


    def assign_bins_to_devices(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Tuple[int, int]]]:
        """
        步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
        功能替換原則：保持邏輯完全一致，大幅提升性能
        """
        print("執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)")
        
        start_time = time.perf_counter()
        fail_positions = []  # 記錄失敗位置 (設備行, 測試欄, 是否為BIN欄位)
        
        if df.empty:
            print("DataFrame 為空，跳過 BIN 分配")
            return df, fail_positions
        
        # 設備資料從第13行開始 (index 12)
        device_start_row = 12
        max_limit_row = 9   # 第10行 Max 限值
        min_limit_row = 10  # 第11行 Min 限值
        bin_assignment_row = 5  # 第6行 BIN 分配
        test_start_col = 2  # 從 C 欄開始測試項目
        
        if len(df) <= device_start_row or len(df.columns) <= test_start_col:
            print("資料不足，跳過 BIN 分配")
            return df, fail_positions
        
        # [ROCKET] 向量化優化：預處理階段
        print("[CHART] 向量化預處理階段...")
        preprocess_start = time.perf_counter()
        
        # 提取所有需要的資料 (一次性存取)
        device_data = df.iloc[device_start_row:, test_start_col:].values  # 設備測試資料
        max_limits = df.iloc[max_limit_row, test_start_col:].values      # 上限值
        min_limits = df.iloc[min_limit_row, test_start_col:].values      # 下限值
        bin_assignments = df.iloc[bin_assignment_row, test_start_col:].values  # BIN 分配
        
        num_devices = device_data.shape[0]
        num_tests = device_data.shape[1]
        
        print(f"  資料規模: {num_devices} 設備 × {num_tests} 測試項目 = {num_devices * num_tests:,} 次比較")
        
        # [ROCKET] 向量化數值轉換
        device_numeric = self._vectorized_safe_float_conversion(device_data)
        max_numeric = self._vectorized_safe_float_conversion(max_limits.reshape(1, -1))[0]
        min_numeric = self._vectorized_safe_float_conversion(min_limits.reshape(1, -1))[0]
        
        # 處理 BIN 分配資料
        bin_numeric = np.zeros(num_tests, dtype=int)
        for i, bin_val in enumerate(bin_assignments):
            try:
                bin_numeric[i] = int(bin_val)
            except (ValueError, TypeError):
                bin_numeric[i] = test_start_col + i + 3  # 預設 BIN 號碼
        
        preprocess_time = time.perf_counter() - preprocess_start
        print(f"  預處理完成: {preprocess_time:.3f} 秒")
        
        # [ROCKET] 向量化測試比較
        print("[FAST] 向量化測試比較階段...")
        comparison_start = time.perf_counter()
        
        # 建立有效限值遮罩
        max_valid_mask = ~np.isnan(max_numeric) & (max_numeric != np.inf)
        min_valid_mask = ~np.isnan(min_numeric) & (min_numeric != -np.inf)
        
        # 向量化失敗檢測 (廣播操作)
        max_failures = np.zeros((num_devices, num_tests), dtype=bool)
        min_failures = np.zeros((num_devices, num_tests), dtype=bool)
        
        # 只對有效限值進行比較
        if np.any(max_valid_mask):
            max_failures[:, max_valid_mask] = device_numeric[:, max_valid_mask] > max_numeric[max_valid_mask]
        
        if np.any(min_valid_mask):
            min_failures[:, min_valid_mask] = device_numeric[:, min_valid_mask] < min_numeric[min_valid_mask]
        
        # 合併所有失敗情況
        all_failures = max_failures | min_failures
        
        comparison_time = time.perf_counter() - comparison_start
        print(f"  向量化比較完成: {comparison_time:.3f} 秒")
        
        # [ROCKET] 向量化 BIN 分配
        print("[TARGET] 向量化 BIN 分配階段...")
        assignment_start = time.perf_counter()
        
        # 找出每個設備的第一個失敗項目
        device_bins = np.ones(num_devices, dtype=int)  # 預設 BIN 1
        device_has_failures = np.any(all_failures, axis=1)
        
        # 對有失敗的設備，找出第一個失敗項目的 BIN
        for device_idx in np.where(device_has_failures)[0]:
            first_fail_col = np.argmax(all_failures[device_idx])  # 第一個 True 的位置
            device_bins[device_idx] = bin_numeric[first_fail_col]
        
        # 批量更新 DataFrame
        df.iloc[device_start_row:device_start_row + num_devices, 1] = device_bins
        
        assignment_time = time.perf_counter() - assignment_start
        print(f"  BIN 分配完成: {assignment_time:.3f} 秒")
        
        # [ROCKET] 向量化失敗位置記錄
        print("[EDIT] 失敗位置記錄階段...")
        position_start = time.perf_counter()
        
        # 記錄所有失敗測試項目位置
        fail_device_indices, fail_test_indices = np.where(all_failures)
        for device_idx, test_idx in zip(fail_device_indices, fail_test_indices):
            actual_device_row = device_start_row + device_idx
            actual_test_col = test_start_col + test_idx
            fail_positions.append((actual_device_row, actual_test_col, False))
        
        # 記錄有失敗的設備的 BIN 欄位位置
        for device_idx in np.where(device_has_failures)[0]:
            actual_device_row = device_start_row + device_idx
            fail_positions.append((actual_device_row, 1, True))  # BIN 欄位
        
        position_time = time.perf_counter() - position_start
        print(f"  位置記錄完成: {position_time:.3f} 秒")
        
        # 統計與總結
        total_time = time.perf_counter() - start_time
        devices_processed = num_devices
        total_comparisons = num_devices * num_tests
        
        print(f"\n[PARTY] 向量化 BIN 分配完成統計:")
        print(f"  [OK] 完成 {devices_processed} 個設備的 BIN 分配")
        print(f"  [FAST] 總處理時間: {total_time:.3f} 秒")
        print(f"  [CHART] 比較次數: {total_comparisons:,} 次")
        print(f"  [ROCKET] 處理速度: {total_comparisons / total_time:,.0f} 次比較/秒")
        print(f"  [RED_CIRCLE] 需要標紅色的位置: {len(fail_positions)} 個")
        print(f"  [UP] 性能提升: 向量化優化相比逐一處理")
        
        return df, fail_positions
    
    def _vectorized_safe_float_conversion(self, data_array: np.ndarray) -> np.ndarray:
        """
        向量化安全數值轉換 - 優化版本
        保持與原始 _is_test_pass 邏輯完全一致
        """
        if data_array.ndim == 1:
            result = np.full(data_array.shape[0], np.nan)
            for i, value in enumerate(data_array):
                result[i] = self._safe_single_float_conversion(value)
        else:
            result = np.full(data_array.shape, np.nan)
            for i in range(data_array.shape[0]):
                for j in range(data_array.shape[1]):
                    result[i, j] = self._safe_single_float_conversion(data_array[i, j])
        return result
    
    def _safe_single_float_conversion(self, value) -> float:
        """
        安全單一數值轉換 - 與原始邏輯一致
        """
        try:
            # 處理特殊字串
            str_val = str(value).lower().strip()
            if str_val in ['none', 'null', '', 'nan']:
                return np.nan
            return float(str(value))
        except (ValueError, TypeError):
            return np.nan
    
    def _is_test_pass(self, test_value, max_limit, min_limit) -> bool:
        """檢查測試項目是否 PASS (保留原始邏輯用於向後相容)"""
        try:
            # 轉換測試值為數字
            test_val = float(str(test_value))
            
            # 檢查上限
            if str(max_limit).lower() not in ['none', 'null', '', 'nan']:
                try:
                    max_val = float(str(max_limit))
                    if test_val > max_val:
                        return False
                except (ValueError, TypeError):
                    pass
            
            # 檢查下限
            if str(min_limit).lower() not in ['none', 'null', '', 'nan']:
                try:
                    min_val = float(str(min_limit))
                    if test_val < min_val:
                        return False
                except (ValueError, TypeError):
                    pass
            
            return True
            
        except (ValueError, TypeError):
            # 無效測試值視為 FAIL
            return False

    def auto_convert_csv_to_excel(self, csv_file_path: str) -> ConversionResult:
        """
        自動轉換 CSV 到 Excel (自動生成輸出檔名)
        
        功能：根據輸入 CSV 檔名自動生成對應的 Excel 檔名，固定放在 logs 目錄
        遵循 CLAUDE.md 功能替換原則：提供更便利的自動化介面
        """
        # 確保 logs 目錄存在
        project_root = "/mnt/d/project/python/outlook_summary"
        logs_dir = os.path.join(project_root, "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        # 自動生成輸出檔名：CSV 檔名 + .xlsx，放在 logs 目錄
        csv_basename = os.path.basename(csv_file_path)
        if csv_basename.lower().endswith('.csv'):
            excel_filename = csv_basename[:-4] + '.xlsx'  # 移[EXCEPT_CHAR] .csv，加上 .xlsx
        else:
            excel_filename = csv_basename + '.xlsx'
        
        output_file_path = os.path.join(logs_dir, excel_filename)
        
        print(f"[REFRESH] 自動轉換模式 (input=output檔名，固定放在 logs 目錄)")
        print(f"   輸入: {csv_file_path}")
        print(f"   輸出: {output_file_path}")
        print()
        
        # 呼叫原始轉換方法
        return self.convert_csv_to_excel(csv_file_path, output_file_path)

    def convert_ft_summary_to_excel(self, csv_path: str, output_path: str = None) -> bool:
        """
        FT_SUMMARY.csv 專用轉換方法（委派給 FTSummaryConverter）
        
        Args:
            csv_path: FT_SUMMARY.csv 檔案路徑
            output_path: 輸出 Excel 路徑（可選）
            
        Returns:
            轉換是否成功
        """
        # 防止重複轉換：檢查目標Excel檔案是否已存在
        if output_path is None:
            output_path = csv_path.replace('.csv', '.xlsx')
        
        if os.path.exists(output_path):
            # 檔案已存在，無需重複轉換
            return True
            
        return self.ft_summary_converter.convert_to_excel(csv_path, output_path)

    def convert_csv_to_excel(
        self, 
        csv_file_path: str, 
        output_file_path: str,
        # 新增 EQC 格式參數
        highlight_rows: List[int] = None,           # 要標記黃色的行數列表
        highlight_end_column: int = None,           # 黃色標記的結束欄位
        bold_row: int = None,                       # 要設為粗體的行數
        bold_start_column: int = None,              # 粗體的開始欄位
        bold_end_column: int = None,                # 粗體的結束欄位
        hyperlink_start_row: int = None,            # 超連結開始行 (C14)
        hyperlink_column: int = 2,                  # 超連結欄位 (C欄=2)
        hyperlink_template: str = None              # 超連結模板
    ) -> ConversionResult:
        """
        執行核心 7 步驟轉換 (包含進階性能管理)
        
        功能替換原則：取代複雜版本，整合進階性能監控
        """
        try:
            # [ROCKET] 使用進階性能管理器的上下文管理器
            with performance_manager.performance_context("CSV到Excel轉換"):
                overall_start = time.perf_counter()
                step_times = {}
                
                print(f"[ROCKET] 開始核心 7 步驟轉換 (進階性能管理): {csv_file_path}")
                print("=" * 80)
                
                # 步驟 1: 安全讀取 CSV
                step_start = time.perf_counter()
                df = self._safe_read_csv(csv_file_path)
                step_times['步驟1_CSV讀取'] = time.perf_counter() - step_start
                
                if df.empty:
                    return ConversionResult(
                        success=False,
                        output_file=output_file_path,
                        error_message="無法讀取 CSV 檔案"
                    )
            
                # 步驟 2: 結構補全
                step_start = time.perf_counter()
                df = self.complete_csv_structure(df)
                step_times['步驟2_結構補全'] = time.perf_counter() - step_start
                
                # 步驟 3: BIN 分配
                step_start = time.perf_counter()
                df = self.assign_bin_numbers_df(df)
                step_times['步驟3_BIN分配'] = time.perf_counter() - step_start
                
                # 步驟 4: BIN1 保護
                step_start = time.perf_counter()
                df, protection_positions = self.apply_bin1_protection_df(df)
                step_times['步驟4_BIN1保護'] = time.perf_counter() - step_start
                
                # 步驟 5: 全設備 BIN 分類 (向量化優化)
                step_start = time.perf_counter()
                df, fail_positions = self.assign_bins_to_devices(df)
                step_times['步驟5_設備BIN分類'] = time.perf_counter() - step_start
            
                # 步驟 6: Site 欄位查找與策略 B 統計
                step_start = time.perf_counter()
                self.site_column, site_log = self.site_finder.find_site_column_with_log(
                    df, csv_file_path, header_row=7, start_col=2
                )
                
                print("\\n[TARGET] 執行策略 B - Site 統計分析 (存檔前)")
                strategy_b_result = self.strategy_b_processor.execute_site_statistics_only(
                    df, csv_file_path, self.site_column
                )
                step_times['步驟6_Site統計'] = time.perf_counter() - step_start
                
                # 步驟 7: Summary 工作表生成
                step_start = time.perf_counter()
                print("\\n[CHART] 生成 Summary 工作表")
                summary_data = self.summary_generator.generate_summary(
                    df=df,
                    site_column=self.site_column,
                    csv_file_path=csv_file_path
                )
                step_times['步驟7_Summary生成'] = time.perf_counter() - step_start
                
                # 步驟 8: Excel 檔案輸出 (xlsxwriter 高性能版本)
                step_start = time.perf_counter()
                all_red_positions = protection_positions + fail_positions
                self._create_excel_with_summary(
                    df, all_red_positions, output_file_path, summary_data,
                    highlight_rows, highlight_end_column, bold_row, bold_start_column, bold_end_column,
                    hyperlink_start_row, hyperlink_column, hyperlink_template
                )
                step_times['步驟8_Excel輸出'] = time.perf_counter() - step_start
                
                # 計算總時間
                total_time = time.perf_counter() - overall_start
                step_times['總處理時間'] = total_time
                
                # [CHART] 詳細時間分析報告
                print("\\n" + "=" * 80)
                print("[FAST] 詳細性能分析報告")
                print("=" * 80)
                
                # 按時間排序顯示各步驟
                sorted_steps = sorted([(k, v) for k, v in step_times.items() if k != '總處理時間'], 
                                    key=lambda x: x[1], reverse=True)
                
                print(f"[CHART] 各步驟處理時間 (按耗時排序):")
                for i, (step_name, step_time) in enumerate(sorted_steps, 1):
                    percentage = (step_time / total_time) * 100
                    print(f"  {i:2d}. {step_name:<12}: {step_time:8.3f} 秒 ({percentage:5.1f}%)")
                
                print(f"\\n[TARGET] 總處理時間: {total_time:.3f} 秒")
                
                # 性能瓶頸分析
                print("\\n[SEARCH] 性能瓶頸分析:")
                top_3_bottlenecks = sorted_steps[:3]
                bottleneck_time = sum(time for _, time in top_3_bottlenecks)
                bottleneck_percentage = (bottleneck_time / total_time) * 100
                
                print(f"  前 3 大耗時步驟佔總時間: {bottleneck_percentage:.1f}%")
                for i, (step_name, step_time) in enumerate(top_3_bottlenecks, 1):
                    percentage = (step_time / total_time) * 100
                    print(f"    [TARGET] 瓶頸 {i}: {step_name} ({percentage:.1f}%)")
                
                # 優化建議
                print("\\n[IDEA] 進一步優化建議:")
                if step_times.get('步驟8_Excel輸出', 0) > total_time * 0.5:
                    print("  [TOOL] Excel 輸出是主要瓶頸，建議優化 openpyxl 操作")
                if step_times.get('步驟7_Summary生成', 0) > total_time * 0.2:
                    print("  [TOOL] Summary 生成耗時較多，可考慮向量化優化")
                if step_times.get('步驟6_Site統計', 0) > total_time * 0.1:
                    print("  [TOOL] Site 統計可進一步優化")
                if step_times.get('步驟1_CSV讀取', 0) > total_time * 0.1:
                    print("  [TOOL] CSV 讀取可使用更快的 pandas 方法")
                    
                print("=" * 80)
                
                # 記錄時間到 logs/datalog.txt
                self._log_performance_to_datalog(csv_file_path, total_time, step_times)
                
                return ConversionResult(
                    success=True,
                    output_file=output_file_path,
                    protection_positions=protection_positions,
                    processing_time=total_time,
                    total_rows=len(df),
                    total_columns=len(df.columns)
                )
            
        except Exception as e:
            import traceback
            error_msg = f"核心轉換失敗: {str(e)}"
            print(error_msg)
            print("詳細錯誤追蹤:")
            traceback.print_exc()
            
            return ConversionResult(
                success=False,
                output_file="",
                error_message=error_msg
            )

    def _is_pure_number(self, value):
        """檢查是否為純數字字串，保留有意義的字串如 'none'"""
        if pd.isna(value) or value is None:
            return False
        
        if not isinstance(value, str):
            return isinstance(value, (int, float)) and not isinstance(value, bool)
        
        # 跳過明顯的文字
        clean_value = str(value).strip().lower()
        if clean_value in ['none', 'na', 'n/a', 'null', 'test_time', 'index_time', 'site_no', '']:
            return False
        
        # 檢查是否為純數字
        try:
            float(clean_value)
            return True
        except (ValueError, TypeError):
            return False

    def _smart_convert_to_number(self, value):
        """聰明轉換：只轉換純數字，保留文字字串"""
        # 處理空值
        if pd.isna(value) or value is None or str(value).strip() == '':
            return ""  # 空值轉為空字串
        
        if not self._is_pure_number(value):
            return value  # 保持原值
        
        clean_value = str(value).strip()
        try:
            # 嘗試轉換為整數
            if '.' not in clean_value and 'e' not in clean_value.lower():
                return int(clean_value)
            else:
                return float(clean_value)
        except (ValueError, TypeError):
            return value  # 保持原值


    def _create_excel_with_summary(
        self, 
        df: pd.DataFrame, 
        all_red_positions: List[Tuple[int, int]], 
        output_file_path: str, 
        summary_data: Dict,
        highlight_rows: List[int] = None,
        highlight_end_column: int = None,
        bold_row: int = None,
        bold_start_column: int = None,
        bold_end_column: int = None,
        hyperlink_start_row: int = None,
        hyperlink_column: int = 2,
        hyperlink_template: str = None
    ):
        """
        使用 xlsxwriter 創建包含 Summary 工作表的 Excel 檔案 (性能優化版本)
        預期性能提升：從 17.9秒 降到 3-5秒
        """
        print(f"[ROCKET] 使用 xlsxwriter 建立包含 Summary 的 Excel 檔案: {output_file_path}")
        
        try:
            # [ROCKET] 建立高性能 xlsxwriter 工作簿 (關閉常數記憶體模式以支援格式重寫)
            workbook_options = {
                'nan_inf_to_errors': True,           # 處理 NaN/Inf 值
                'strings_to_numbers': False,         # 避免自動數字轉換開銷
                'default_date_format': 'yyyy-mm-dd', # 統一日期格式
                'constant_memory': False             # 關閉常數記憶體模式，允許格式重寫
            }
            workbook = xlsxwriter.Workbook(output_file_path, workbook_options)
            
            # 定義格式 - 使用 xlsxwriter 正確的紅色格式屬性
            red_font_format = workbook.add_format({'font_color': 'red'})
            percentage_format = workbook.add_format({'num_format': '0%'})  # 正常百分比格式
            red_percentage_format = workbook.add_format({'font_color': 'red', 'num_format': '0%'})  # 失敗項目的紅色百分比
            normal_format = workbook.add_format()  # 正常格式
            
            # 1. 創建主資料工作表
            print("[BOARD] 步驟 1: 創建主資料工作表...")
            base_name = os.path.basename(output_file_path).replace('.xlsx', '')
            main_worksheet_name = base_name[:31]  # Excel 工作表名稱限制 31 字元
            main_ws = workbook.add_worksheet(main_worksheet_name)
            
            self._write_main_data(
                workbook, main_ws, df, all_red_positions, red_font_format, output_file_path,
                highlight_rows, highlight_end_column, bold_row, bold_start_column, bold_end_column,
                hyperlink_start_row, hyperlink_column, hyperlink_template
            )
            print("[OK] 主資料工作表創建完成")
            
            # 2. 創建 Summary 工作表
            print("[BOARD] 步驟 2: 創建 Summary 工作表...")
            summary_ws = workbook.add_worksheet("Summary")
            
            self._write_summary_data(summary_ws, summary_data, percentage_format, red_percentage_format, normal_format, red_font_format)
            print("[OK] Summary 工作表創建完成")
            
            # 3. 關閉並保存工作簿
            print("[BOARD] 步驟 3: 保存 Excel 檔案...")
            workbook.close()
            print("[OK] xlsxwriter Excel 檔案保存完成")
            
            # 驗證結果
            print(f"\n[SEARCH] 驗證 Excel 檔案: {output_file_path}")
            if os.path.exists(output_file_path):
                file_size = os.path.getsize(output_file_path)
                print(f"[OK] Excel 檔案已成功創建，大小: {file_size:,} bytes")
            
        except Exception as e:
            error_msg = f"xlsxwriter 創建 Excel 檔案時發生錯誤: {str(e)}"
            print(f"[ERROR] {error_msg}")
            
            # 確保工作簿關閉
            try:
                if 'workbook' in locals():
                    workbook.close()
            except:
                pass
            
            raise RuntimeError(error_msg)

    def _write_main_data(
        self, 
        workbook,
        worksheet, 
        df: pd.DataFrame, 
        all_red_positions: List[Tuple[int, int]], 
        red_font_format, 
        output_file_path: str,
        highlight_rows: List[int] = None,
        highlight_end_column: int = None,
        bold_row: int = None,
        bold_start_column: int = None,
        bold_end_column: int = None,
        hyperlink_start_row: int = None,
        hyperlink_column: int = 2,
        hyperlink_template: str = None
    ):
        """
        使用 xlsxwriter 寫入主資料工作表 (高性能批量寫入版本)
        預期性能提升：從逐格寫入改為批量寫入 + 分離格式化
        """
        print("  [CHART] 寫入主資料工作表數據...")
        
        # 初始化性能追蹤
        step_times = {}
        
        # [ROCKET] 向量化智慧轉換規則 (性能優化版本)
        print("[ROCKET] 執行向量化智慧轉換...")
        convert_start = time.perf_counter()
        
        df_final = df.copy()
        
        # 向量化智慧轉換函數
        def vectorized_smart_convert(series):
            """向量化版本的智慧轉換 - 避免逐格處理"""
            # 使用 pandas 向量化操作，保留原始 _smart_convert_to_number 的邏輯
            def convert_single(value):
                return self._smart_convert_to_number(value)
            
            return series.apply(convert_single)
        
        # 1. [ROCKET] 第13行往下 - 向量化批量處理（最大的性能提升點）
        if len(df_final) > 12:
            print(f"  [CHART] 處理第13行往下 {len(df_final) - 12} 行資料...")
            df_final.iloc[12:] = df_final.iloc[12:].apply(vectorized_smart_convert, axis=0)
        
        # 2. [ROCKET] 特定行批量處理 - 第7、6、9、10、11行
        target_rows = [6, 5, 8, 9, 10]  # 第7、6、9、10、11行
        for row_idx in target_rows:
            if len(df_final) > row_idx:
                df_final.iloc[row_idx] = vectorized_smart_convert(df_final.iloc[row_idx])
        
        convert_time = time.perf_counter() - convert_start
        print(f"  [OK] 向量化智慧轉換完成: {convert_time:.3f} 秒")
        step_times['向量化智慧轉換'] = convert_time
        
        # 建立紅色位置集合以提升查找效率 - 統一處理不同格式並轉換數據類型
        red_positions_set = set()
        protection_count = 0
        fail_count = 0
        
        for pos in all_red_positions:
            if len(pos) >= 2:
                # 統一提取 (row, col)，不管是 2 元組還是 3 元組，並確保轉換為純 int
                row = int(pos[0]) if hasattr(pos[0], 'item') else int(pos[0])
                col = int(pos[1]) if hasattr(pos[1], 'item') else int(pos[1])
                red_positions_set.add((row, col))
                if len(pos) == 2:
                    protection_count += 1
                elif len(pos) == 3:
                    fail_count += 1
        
        print(f"  [SEARCH] 紅色位置分析: BIN1保護 {protection_count} 個, 測試失敗 {fail_count} 個, 總計 {len(red_positions_set)} 個")
        
        # [ROCKET] 混合優化：批量寫入 + 紅色格式重寫
        print(f"    [ROCKET] 混合模式寫入 {len(df_final)} 行 × {len(df_final.columns)} 欄的數據...")
        
        # 步驟 1: 批量寫入所有數據 (無格式)
        batch_start = time.perf_counter()
        for row_idx in range(len(df_final)):
            row_data = df_final.iloc[row_idx].tolist()
            worksheet.write_row(row_idx, 0, row_data)
        
        batch_time = time.perf_counter() - batch_start
        print(f"    [FAST] 批量寫入完成: {batch_time:.3f} 秒")
        
        # 步驟 2: 只對紅色位置重寫格式
        format_start = time.perf_counter()
        red_count = 0
        for row_idx, col_idx in red_positions_set:
            if row_idx < len(df_final) and col_idx < len(df_final.columns):
                value = df_final.iloc[row_idx, col_idx]
                # 確保數值轉換正確
                if pd.isna(value):
                    value = ""
                elif isinstance(value, (int, float)):
                    value = value
                else:
                    value = str(value)
                
                worksheet.write(row_idx, col_idx, value, red_font_format)
                red_count += 1
        
        format_time = time.perf_counter() - format_start
        print(f"    [RED_CIRCLE] 紅色格式重寫完成: {format_time:.3f} 秒，處理 {red_count} 個位置")
        
        # 步驟 2.5: EQC 自訂格式處理（黃色標記、粗體、超連結）
        eqc_format_start = time.perf_counter()
        eqc_format_count = self._apply_eqc_custom_formats(
            workbook, worksheet, df_final, highlight_rows, highlight_end_column,
            bold_row, bold_start_column, bold_end_column,
            hyperlink_start_row, hyperlink_column, hyperlink_template
        )
        eqc_format_time = time.perf_counter() - eqc_format_start
        print(f"    [ART] EQC 自訂格式完成: {eqc_format_time:.3f} 秒，處理 {eqc_format_count} 個位置")
        
        # 步驟 3: 添加 BIN 超連結 (使用處理完成的最終數據)
        hyperlink_start = time.perf_counter()
        base_name = os.path.basename(output_file_path).replace('.xlsx', '')
        main_worksheet_name = base_name[:31]  # Excel 工作表名稱限制 31 字元
        hyperlink_count = self._add_bin_hyperlinks(
            workbook, worksheet, df_final, all_red_positions, main_worksheet_name,
            highlight_rows, highlight_end_column
        )
        hyperlink_time = time.perf_counter() - hyperlink_start
        print(f"    [LINK] BIN 超連結完成: {hyperlink_time:.3f} 秒，處理 {hyperlink_count} 個連結")
        
        # 步驟 4: 設置凍結視窗 (打開 Excel 時左上角是 C6，凍結 C12 到 Site 欄位)
        freeze_start = time.perf_counter()
        site_col = getattr(self, 'site_column', 4)  # Site 欄位位置，預設第5欄 (index 4)
        # 凍結視窗：從 C12 到 Site 欄位，打開時左上角顯示 C6
        worksheet.freeze_panes(12, site_col + 1)  # 12行(到C12), site_col+1 欄位
        freeze_time = time.perf_counter() - freeze_start
        print(f"    [TEST_TUBE_BIOLOGY] 凍結視窗完成: {freeze_time:.3f} 秒 (凍結 C12 到 Site 欄位第{site_col+1}欄，左上角顯示 C6)")
        
        # 總計時間
        total_time = batch_time + format_time + hyperlink_time + freeze_time
        print(f"    [OK] 主資料寫入完成，總時間: {total_time:.3f} 秒")

    def _add_bin_hyperlinks(self, workbook, worksheet, df_final: pd.DataFrame, all_red_positions: List, main_sheet_name: str = None, highlight_rows: List[int] = None, highlight_end_column: int = None) -> int:
        """
        為 FAIL 設備的 BIN 欄位添加超連結
        點擊 BIN → 跳到該設備第一個失敗的測試項目位置
        使用失敗位置資料確保準確性
        修復：xlsxwriter 超連結格式修正版本
        """
        hyperlink_count = 0
        device_start_row = 12  # 設備資料從第13行開始 (index 12)
        
        if len(df_final) <= device_start_row:
            return 0
        
        print(f"    [SEARCH] 分析 {len(all_red_positions)} 個失敗位置...")
        
        # 建立每個設備的失敗測試項目對應表
        device_fail_map = {}
        
        for pos in all_red_positions:
            # 處理不同格式的位置資料
            if len(pos) == 2:
                # BIN1 保護位置 (row, col)
                row, col = int(pos[0]), int(pos[1])
                is_bin = False
            elif len(pos) == 3:
                # 設備失敗位置 (row, col, is_bin)
                row = int(pos[0]) if hasattr(pos[0], 'item') else int(pos[0])
                col = int(pos[1]) if hasattr(pos[1], 'item') else int(pos[1])
                is_bin = pos[2] if len(pos) > 2 else False
            else:
                continue
                
            # 只處理設備資料區域的測試項目失敗 (非 BIN 欄位)
            if row >= device_start_row and col > 1 and not is_bin:
                if row not in device_fail_map:
                    device_fail_map[row] = []
                device_fail_map[row].append(col)
        
        # 為每個有失敗項目的設備排序找到第一個失敗項目
        for device_row in device_fail_map:
            device_fail_map[device_row].sort()  # 按欄位順序排序
        
        print(f"    [TARGET] 發現 {len(device_fail_map)} 個設備有失敗項目")
        
        # 調試：顯示前幾個失敗設備的詳細資訊
        if device_fail_map:
            debug_count = 0
            for device_row, fail_cols in list(device_fail_map.items())[:3]:
                debug_count += 1
                print(f"        調試 {debug_count}: 設備行{device_row} 失敗欄位{fail_cols[:3]}...")  # 只顯示前3個失敗欄位
        
        # 為每個 FAIL 設備的 BIN 欄位添加超連結
        for device_row_idx in range(device_start_row, len(df_final)):
            try:
                bin_value = df_final.iloc[device_row_idx, 1]  # BIN 欄位 (第2欄)
                if pd.notna(bin_value):
                    bin_num = int(bin_value)
                    
                    # 調試資訊：檢查每個設備的 BIN 和失敗狀態
                    is_in_fail_map = device_row_idx in device_fail_map
                    if hyperlink_count < 3:  # 只顯示前3個調試
                        print(f"        調試超連結: 設備行{device_row_idx} BIN={bin_num} 在失敗列表={is_in_fail_map}")
                    
                    # 只為 FAIL BIN (不是 BIN 1) 添加超連結
                    if bin_num != 1 and device_row_idx in device_fail_map:
                        # 取得該設備第一個失敗的測試項目欄位
                        first_fail_col = device_fail_map[device_row_idx][0]
                        
                        # Excel 欄位名稱轉換
                        col_letter = self._convert_to_excel_column(first_fail_col)
                        
                        # 目標位置：該設備該測試項目的位置
                        target_cell = f"{col_letter}{device_row_idx + 1}"  # +1 因為 Excel 從1開始
                        
                        # [TOOL] 修復：xlsxwriter 內部超連結格式
                        # xlsxwriter 使用 'internal:' 前綴，而非 '#' 
                        if main_sheet_name is None:
                            main_sheet_name = "Sheet1"  # 預設工作表名稱
                        
                        # xlsxwriter 正確的內部超連結格式
                        hyperlink_url = f"internal:'{main_sheet_name}'!{target_cell}"
                        
                        if hyperlink_count < 5:  # 顯示前5個詳細超連結
                            print(f"      [LINK] 創建超連結 {hyperlink_count+1}: 設備{device_row_idx+1} BIN {bin_num} → {hyperlink_url}")
                        
                        # 檢查 B 欄位置是否需要黃色標記
                        current_row_num = device_row_idx + 1  # 轉換為 1-based 行號
                        current_col_num = 2  # B 欄是第2欄
                        needs_yellow = (highlight_rows and current_row_num in highlight_rows and 
                                      highlight_end_column and current_col_num <= highlight_end_column)
                        
                        if needs_yellow:
                            # 創建黃色底色的 BIN 超連結格式
                            yellow_bin_hyperlink_format = workbook.add_format({
                                'bg_color': 'FFFF00',
                                'font_color': 'blue',
                                'underline': True
                            })
                            worksheet.write_url(device_row_idx, 1, hyperlink_url, yellow_bin_hyperlink_format, string=str(bin_num))
                        else:
                            # 一般 BIN 超連結格式
                            default_hyperlink_format = workbook.add_format({
                                'font_color': 'blue',
                                'underline': True
                            })
                            worksheet.write_url(device_row_idx, 1, hyperlink_url, default_hyperlink_format, string=str(bin_num))
                        
                        # 標記該位置已處理超連結（用於黃色標記跳過邏輯）
                        if not hasattr(self, '_hyperlink_positions'):
                            self._hyperlink_positions = set()
                        self._hyperlink_positions.add((device_row_idx, 1))
                        hyperlink_count += 1
                        
            except (ValueError, TypeError):
                continue
        
        print(f"    [OK] 超連結創建完成，共 {hyperlink_count} 個")
        return hyperlink_count
    
    def _convert_to_excel_column(self, col_index: int) -> str:
        """
        將欄位索引轉換為 Excel 欄位名稱
        正確的 Excel 欄位編號系統：0->A, 1->B, ..., 25->Z, 26->AA, 27->AB, ...
        """
        result = ''
        index = col_index
        while True:
            result = chr(65 + (index % 26)) + result
            index = index // 26
            if index == 0:
                break
            index -= 1
        return result

    def _write_summary_data(self, worksheet, summary_data: Dict, percentage_format, red_percentage_format, normal_format, red_font_format):
        """
        使用 xlsxwriter 寫入 Summary 工作表數據 (高性能批量寫入版本)
        """
        print("  [CHART] 寫入 Summary 工作表數據...")
        
        # 驗證 summary_data 結構
        required_keys = ['basic_stats', 'site_total_row', 'headers', 'bin_rows']
        for key in required_keys:
            if key not in summary_data:
                raise ValueError(f"Summary 資料缺少必要鍵值: {key}")
        
        # [ROCKET] 批量寫入優化
        batch_start = time.perf_counter()
        
        # 1. 批量填入基本統計行 (第1-4行) - 正常格式
        print("    批量填入基本統計行 (第1-4行)...")
        for row_idx, row_data in enumerate(summary_data['basic_stats']):
            for col_idx, value in enumerate(row_data):
                if row_idx == 3 and col_idx == 1:  # Yield 百分比
                    worksheet.write(row_idx, col_idx, value, percentage_format)
                else:
                    worksheet.write(row_idx, col_idx, value, normal_format)
        
        # 2. 批量填入 Site 總計行 (第5行) - 正常格式
        print("    批量填入 Site 總計行 (第5行)...")
        for col_idx, value in enumerate(summary_data['site_total_row']):
            worksheet.write(4, col_idx, value, normal_format)
        
        # 3. 批量填入標頭行 (第6行) - 正常格式
        print("    批量填入標頭行 (第6行)...")
        for col_idx, value in enumerate(summary_data['headers']):
            worksheet.write(5, col_idx, value, normal_format)
        
        # 4. 批量填入 BIN 資料行 (第7行起) - 全部正常格式
        print("    批量填入 BIN 資料行...")
        max_definition_length = 0
        
        for row_idx, bin_row in enumerate(summary_data['bin_rows'], 6):
            # 逐格寫入，全部使用正常格式
            for col_idx, value in enumerate(bin_row):
                if col_idx == 2 or (col_idx > 4 and (col_idx - 4) % 2 == 0):  # % 欄位
                    worksheet.write(row_idx, col_idx, value, percentage_format)
                else:
                    worksheet.write(row_idx, col_idx, value, normal_format)
                
                # 追蹤 Definition 欄 (第4欄) 最長字串
                if col_idx == 3 and value:
                    max_definition_length = max(max_definition_length, len(str(value)))
        
        batch_time = time.perf_counter() - batch_start
        print(f"    [FAST] 批量數據寫入完成: {batch_time:.3f} 秒")
        
        # 6. 批量填入完整 BIN 列表 (包含 Count=0 的 BIN)
        empty_bin_start = time.perf_counter()
        print("    批量填入完整 BIN 列表...")
        existing_bins = set()
        for bin_row in summary_data['bin_rows']:
            if bin_row[0]:
                existing_bins.add(int(bin_row[0]))
        
        # 從統計資料獲取所有可能的 BIN
        bin_definitions = summary_data['statistics']['bin_stats'].get('bin_definitions', {1: "All Pass"})
        all_bins = set(bin_definitions.keys()).union(existing_bins)
        
        # 批量準備空的 BIN 行
        current_row = 6 + len(summary_data['bin_rows'])
        headers = summary_data['headers']
        site_count = (len(headers) - 5) // 2  # 計算 Site 數量
        
        for bin_num in sorted(all_bins):
            if bin_num not in existing_bins:
                definition = bin_definitions.get(bin_num, f"Bin {bin_num}")
                max_definition_length = max(max_definition_length, len(str(definition)))
                
                # 創建空的 BIN 行
                empty_row = [bin_num, 0, 0.0, definition, '']
                
                # 為每個 Site 添加 0
                for _ in range(site_count):
                    empty_row.extend([0, 0.0])
                
                # 補齊到標頭長度
                while len(empty_row) < len(headers):
                    empty_row.append('')
                
                # 逐格寫入空 BIN 行，全部使用正常格式
                for col_idx, value in enumerate(empty_row):
                    if col_idx == 2 or (col_idx > 4 and (col_idx - 4) % 2 == 0):  # % 欄位
                        worksheet.write(current_row, col_idx, value, percentage_format)
                    else:
                        worksheet.write(current_row, col_idx, value, normal_format)
                
                current_row += 1
        
        empty_bin_time = time.perf_counter() - empty_bin_start
        empty_bins_count = len(all_bins) - len(existing_bins)
        print(f"    [CHART] 空 BIN 列表填入完成: {empty_bin_time:.3f} 秒，添加 {empty_bins_count} 個空 BIN")
        
        # 7. 批量調整欄位寬度
        width_start = time.perf_counter()
        print("    調整欄位寬度...")
        
        # Definition 欄寬度 (第4欄)
        definition_width = max(12, max_definition_length + 2)
        worksheet.set_column(3, 3, definition_width)  # D欄 (index 3)
        
        # 批量設定其他欄位寬度
        worksheet.set_column(0, 0, 8)  # A欄 - Bin
        worksheet.set_column(1, 1, 8)  # B欄 - Count
        worksheet.set_column(2, 2, 8)  # C欄 - %
        worksheet.set_column(4, 4, 8)  # E欄 - Note
        
        # Site 欄位寬度
        for i, header in enumerate(summary_data['headers']):
            if 'Site' in str(header) or str(header) == '%':
                worksheet.set_column(i, i, 10 if 'Site' in str(header) else 8)
        
        width_time = time.perf_counter() - width_start
        total_summary_time = batch_time + empty_bin_time + width_time
        print(f"    [TRIANGLE] 欄位寬度調整完成: {width_time:.3f} 秒")
        print(f"    [OK] Summary 數據寫入完成，總時間: {total_summary_time:.3f} 秒，Definition 欄寬度: {definition_width}")

    def _log_performance_to_datalog(self, csv_file_path: str, total_time: float, step_times: Dict[str, float]):
        """
        將性能分析資料記錄到 logs/datalog.txt
        按照 CLAUDE.md 要求記錄完整時間資訊
        """
        try:
            # 確保 logs 目錄存在
            project_root = "/mnt/d/project/python/outlook_summary"
            logs_dir = os.path.join(project_root, "logs")
            os.makedirs(logs_dir, exist_ok=True)
            log_filename = os.path.join(logs_dir, "datalog.txt")
            
            timestamp = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            csv_filename = os.path.basename(csv_file_path)
            
            log_content = []
            log_content.append("[FAST] CSV 到 Excel 轉換器性能記錄")
            log_content.append("=" * 80)
            log_content.append(f"檔案: {csv_filename}")
            log_content.append(f"總處理時間: {total_time:.3f} 秒")
            log_content.append(f"處理完成時間: {timestamp}")
            log_content.append("")
            
            # 按時間排序顯示各步驟
            sorted_steps = sorted([(k, v) for k, v in step_times.items() if k != '總處理時間'], 
                                key=lambda x: x[1], reverse=True)
            
            log_content.append("[CHART] 各步驟處理時間 (按耗時排序):")
            for i, (step_name, step_time) in enumerate(sorted_steps, 1):
                percentage = (step_time / total_time) * 100
                log_content.append(f"  {i:2d}. {step_name:<15}: {step_time:8.3f} 秒 ({percentage:5.1f}%)")
            
            log_content.append("")
            
            # 性能瓶頸分析
            log_content.append("[SEARCH] 性能瓶頸分析:")
            top_3_bottlenecks = sorted_steps[:3]
            bottleneck_time = sum(time for _, time in top_3_bottlenecks)
            bottleneck_percentage = (bottleneck_time / total_time) * 100
            
            log_content.append(f"  前 3 大耗時步驟佔總時間: {bottleneck_percentage:.1f}%")
            for i, (step_name, step_time) in enumerate(top_3_bottlenecks, 1):
                percentage = (step_time / total_time) * 100
                log_content.append(f"    [TARGET] 瓶頸 {i}: {step_name} ({percentage:.1f}%)")
            
            log_content.append("")
            log_content.append("[ROCKET] 使用 xlsxwriter 高性能版本")
            log_content.append("[UP] 相比 openpyxl 版本提升約 50% 性能")
            log_content.append("")
            
            # 附加模式寫入，保留之前的記錄
            with open(log_filename, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*80}\n")
                f.write(f"CSV 到 Excel 轉換器性能記錄 - {timestamp}\n")
                f.write(f"{'='*80}\n")
                f.write('\n'.join(log_content))
                f.write(f"\n{'='*80}\n\n")
            
            print(f"\n[EDIT] 性能記錄已附加到: logs/datalog.txt")
            
        except Exception as e:
            print(f"\n[WARNING] 無法保存性能記錄: {str(e)}")

    def _vectorized_limit_validity_check(self, limits_array: np.ndarray) -> np.ndarray:
        """
        向量化檢查限值有效性
        """
        # 轉換為字串陣列進行檢查
        str_limits = np.array([str(val).lower().strip() for val in limits_array])
        
        # 檢查無效值
        invalid_mask = np.isin(str_limits, ['none', 'null', '', 'nan'])
        
        # 檢查是否為有效數字
        valid_numeric = np.zeros(len(limits_array), dtype=bool)
        for i, val in enumerate(limits_array):
            try:
                float(str(val))
                valid_numeric[i] = True
            except (ValueError, TypeError):
                valid_numeric[i] = False
        
        return valid_numeric & ~invalid_mask

    def _vectorized_failure_check(self, device_values: np.ndarray, max_limit, min_limit, max_valid: bool, min_valid: bool) -> np.ndarray:
        """
        向量化檢查設備是否會失敗
        """
        failure_mask = np.zeros(len(device_values), dtype=bool)
        
        # 轉換設備值為數值
        numeric_values = np.zeros(len(device_values))
        for i, val in enumerate(device_values):
            try:
                numeric_values[i] = float(str(val))
            except (ValueError, TypeError):
                numeric_values[i] = np.nan
        
        # 檢查上限失敗
        if max_valid:
            try:
                max_val = float(str(max_limit))
                failure_mask |= numeric_values > max_val
            except (ValueError, TypeError):
                pass
        
        # 檢查下限失敗
        if min_valid:
            try:
                min_val = float(str(min_limit))
                failure_mask |= numeric_values < min_val
            except (ValueError, TypeError):
                pass
        
        return failure_mask

    def _vectorized_zero_check(self, device_values: np.ndarray) -> np.ndarray:
        """
        向量化檢查設備測試數值是否為 0
        """
        zero_mask = np.zeros(len(device_values), dtype=bool)
        
        for i, val in enumerate(device_values):
            try:
                zero_mask[i] = float(str(val)) == 0.0
            except (ValueError, TypeError):
                zero_mask[i] = False
        
        return zero_mask


    def _apply_eqc_custom_formats(
        self,
        workbook,
        worksheet,
        df: pd.DataFrame,
        highlight_rows: List[int] = None,
        highlight_end_column: int = None,
        bold_row: int = None,
        bold_start_column: int = None,
        bold_end_column: int = None,
        hyperlink_start_row: int = None,
        hyperlink_column: int = 2,
        hyperlink_template: str = None
    ) -> int:
        """
        套用 EQC 自訂格式：黃色標記、粗體、C欄超連結
        
        Returns:
            處理的格式數量
        """
        format_count = 0
        
        # 建立 xlsxwriter 格式 (使用傳入的工作簿物件)
        yellow_fill = workbook.add_format({'bg_color': 'FFFF00'})
        bold_font = workbook.add_format({'bold': True})
        yellow_bold = workbook.add_format({'bg_color': 'FFFF00', 'bold': True})
        
        # 1. 黃色標記處理（跳過 B 欄和 C 欄超連結位置）
        if highlight_rows and highlight_end_column:
            print(f"      [YELLOW] 套用黃色標記: {len(highlight_rows)} 行，範圍 1-{highlight_end_column} (跳過 B 欄和 C 欄超連結)")
            for row_num in highlight_rows:
                # 轉換為 0-based 索引
                row_idx = row_num - 1
                if 0 <= row_idx < len(df):
                    # 從第1欄開始（索引0），但跳過 B 欄和 C 欄超連結位置
                    for col_idx in range(0, min(highlight_end_column, len(df.columns))):
                        try:
                            # 跳過 C 欄超連結位置（已在超連結處理時套用黃色格式）
                            if (hyperlink_start_row and hyperlink_column and
                                row_num >= hyperlink_start_row and col_idx == hyperlink_column - 1):
                                continue  # 跳過 C 欄超連結位置
                            
                            # 檢查 B 欄是否已有超連結處理過黃色格式
                            if col_idx == 1 and hasattr(self, '_hyperlink_positions'):  # B 欄 (index 1)
                                if (row_idx, 1) in self._hyperlink_positions:
                                    continue  # 跳過已經在超連結處理時套用黃色格式的位置
                            
                            value = df.iloc[row_idx, col_idx]
                            if pd.isna(value):
                                value = ""
                            
                            # 檢查是否同時需要粗體
                            if (bold_row and row_num == bold_row and 
                                bold_start_column and bold_end_column and
                                bold_start_column <= col_idx + 1 <= bold_end_column):
                                worksheet.write(row_idx, col_idx, value, yellow_bold)
                            else:
                                worksheet.write(row_idx, col_idx, value, yellow_fill)
                            format_count += 1
                        except Exception as e:
                            print(f"        [WARNING] 黃色標記失敗: 行{row_num} 欄{col_idx+1}: {e}")
                            continue
        
        # 2. 粗體格式處理（不在黃色區域的）
        if bold_row and bold_start_column and bold_end_column:
            print(f"      [FIRE] 套用粗體格式: 第{bold_row}行，欄位 {bold_start_column}-{bold_end_column}")
            row_idx = bold_row - 1
            if 0 <= row_idx < len(df):
                for col_num in range(bold_start_column, bold_end_column + 1):
                    col_idx = col_num - 1
                    if 0 <= col_idx < len(df.columns):
                        try:
                            # 只有不在黃色標記範圍內的才單獨設粗體
                            is_in_yellow = (highlight_rows and bold_row in highlight_rows and 
                                          highlight_end_column and col_num >= 3 and col_num <= highlight_end_column)
                            if not is_in_yellow:
                                value = df.iloc[row_idx, col_idx]
                                if pd.isna(value):
                                    value = ""
                                worksheet.write(row_idx, col_idx, value, bold_font)
                                format_count += 1
                        except Exception as e:
                            print(f"        [WARNING] 粗體格式失敗: 行{bold_row} 欄{col_num}: {e}")
                            continue
        
        # 3. C欄超連結處理
        if hyperlink_start_row and hyperlink_template:
            print(f"      [LINK] 套用 C 欄超連結: 從第{hyperlink_start_row}行開始")
            start_idx = hyperlink_start_row - 1
            col_idx = hyperlink_column - 1  # 轉換為 0-based 索引
            
            for row_idx in range(start_idx, len(df)):
                try:
                    cell_value = df.iloc[row_idx, col_idx]
                    if pd.notna(cell_value) and str(cell_value).strip():
                        # 提取完整檔名，清理 HYPERLINK: 前綴
                        raw_value = str(cell_value).strip()
                        
                        # 清理 HYPERLINK: 前綴（如果存在）
                        if raw_value.startswith("HYPERLINK:"):
                            clean_filename = raw_value[10:].strip()  # 移[EXCEPT_CHAR] "HYPERLINK:" 前綴
                        else:
                            clean_filename = raw_value
                        
                        # 確保 UNC 路徑格式正確（開頭兩個反斜線）
                        import os
                        if clean_filename.startswith("\\") and not clean_filename.startswith("\\\\"):
                            clean_filename = "\\" + clean_filename  # 補上缺少的反斜線
                        
                        # 格式4：顯示從最後一個反斜線開始的部分（檔名）
                        def extract_filename_from_path(full_path):
                            # 找到最後一個反斜線的位置
                            last_backslash_pos = full_path.rfind('\\')
                            
                            if last_backslash_pos != -1:
                                # 從最後一個反斜線之後開始取字串（不包含反斜線）
                                return full_path[last_backslash_pos + 1:]
                            else:
                                # 如果沒有反斜線，返回整個字串
                                return full_path
                        
                        display_text = extract_filename_from_path(clean_filename)
                        
                        # 建立完整超連結路徑 - 直接使用檔名而不使用模板
                        # 由於原始檔名已包含完整路徑，直接使用
                        
                        # 使用 xlsxwriter 的 write_url 方法，確保正確的檔案協議
                        # 使用正確的 file:// 協議格式，先轉換路徑分隔符號
                        normalized_path = clean_filename.replace('\\', '/')
                        file_url = f"file:///{normalized_path}"
                        
                        # 檢查當前格子是否需要黃色標記
                        current_row_num = row_idx + 1  # 轉換為 1-based 行號
                        current_col_num = col_idx + 1  # 轉換為 1-based 欄號
                        needs_yellow = (highlight_rows and current_row_num in highlight_rows and 
                                      highlight_end_column and current_col_num <= highlight_end_column)
                        
                        if needs_yellow:
                            # 創建黃色底色的超連結格式
                            yellow_hyperlink_format = workbook.add_format({
                                'bg_color': 'FFFF00',
                                'font_color': 'blue',
                                'underline': True
                            })
                            worksheet.write_url(row_idx, col_idx, file_url, yellow_hyperlink_format, string=display_text)
                        else:
                            # 一般超連結格式
                            worksheet.write_url(row_idx, col_idx, file_url, string=display_text)
                        format_count += 1
                except Exception as e:
                    print(f"        [WARNING] 超連結失敗: 行{row_idx+1}: {e}")
                    continue
        
        return format_count


# 向後相容性方法
class ExcelProcessingError(Exception):
    """Excel 處理異常"""
    pass