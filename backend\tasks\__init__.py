"""
任務模組 - 純 Dramatiq 任務管理

🎯 功能：
  - 使用 Dramatiq 作為唯一任務隊列系統
  - 提供統一的任務接口
  - 簡化的系統架構

📦 模組結構：
  - dramatiq_integration.py - Dramatiq 任務整合
  - unified_task_interface.py - 純 Dramatiq 任務接口
"""

# 導入純 Dramatiq 任務接口
try:
    from .unified_task_interface import (
        get_task_manager,
        submit_eqc_workflow,
        submit_product_search,
        get_system_status,
        TaskResult,
        TaskManager
    )
    
    # 導入 Dramatiq 整合
    from .dramatiq_integration import (
        get_dramatiq_tasks,
        get_dramatiq_task,
        is_dramatiq_available,
        get_task_status
    )
    
    # 導入管道任務
    from .pipeline_tasks import (
        process_vendor_files_task,
        pipeline_completion_task,
        create_vendor_processing_pipeline,
        create_sequential_vendor_pipeline,
        create_full_processing_pipeline,
        get_pipeline_status,
        cancel_pipeline
    )
    
    # 導入管道工具
    from .pipeline_utils import (
        get_pipeline_manager,
        PipelineManager,
        PipelineStatus,
        PipelineContext,
        create_pipeline_with_context
    )
    
    __all__ = [
        # 統一任務接口
        'get_task_manager',
        'submit_eqc_workflow',
        'submit_product_search',
        'get_system_status',
        'get_task_status',
        'TaskResult',
        'TaskManager',
        
        # Dramatiq 整合
        'get_dramatiq_tasks',
        'get_dramatiq_task',
        'is_dramatiq_available',
        
        # 管道任務
        'process_vendor_files_task',
        'pipeline_completion_task',
        'create_vendor_processing_pipeline',
        'create_sequential_vendor_pipeline',
        'create_full_processing_pipeline',
        'get_pipeline_status',
        'cancel_pipeline',
        
        # 管道工具
        'get_pipeline_manager',
        'PipelineManager',
        'PipelineStatus',
        'PipelineContext',
        'create_pipeline_with_context'
    ]
    
except ImportError as e:
    # 如果導入失敗，提供基本接口
    __all__ = []
    
    def get_task_manager():
        raise ImportError(f"任務管理器不可用: {e}")