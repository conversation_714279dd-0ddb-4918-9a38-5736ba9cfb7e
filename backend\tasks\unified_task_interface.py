"""
純 Dramatiq 任務接口

🎯 功能：
  - 提供統一的任務提交接口
  - 使用 Dramatiq 作為唯一任務隊列系統
  - 簡化的任務管理

🔧 使用方式：
  from backend.tasks.unified_task_interface import TaskManager
  
  task_manager = TaskManager()
  result = task_manager.submit_eqc_workflow(folder_path, session_id, options)
"""

import os
from typing import Dict, Any, Optional
from loguru import logger


class TaskResult:
    """任務結果"""
    
    def __init__(self, task_id: str, raw_result: Any = None):
        self.task_id = task_id
        self.raw_result = raw_result
    
    def get_status(self) -> Dict[str, Any]:
        """獲取任務狀態"""
        try:
            from .dramatiq_integration import get_task_status
            status = get_task_status(self.task_id)
            status['queue_type'] = 'dramatiq'
            return status
        except Exception as e:
            return {'status': 'error', 'error': str(e), 'queue_type': 'dramatiq'}

    def get_result(self, timeout: Optional[int] = None) -> Any:
        """獲取任務結果"""
        try:
            from dramatiq_config import get_result_backend
            result_backend = get_result_backend()

            # 獲取結果，可選擇是否阻塞等待
            if timeout:
                result = result_backend.get_result(self.task_id, block=True, timeout=timeout)
            else:
                result = result_backend.get_result(self.task_id, block=False)

            return result
        except Exception as e:
            logger.error(f"❌ 獲取任務結果失敗: {e}")
            return None

    def is_completed(self) -> bool:
        """檢查任務是否完成"""
        status = self.get_status()
        return status.get('status') in ['completed', 'failed']

    def is_successful(self) -> bool:
        """檢查任務是否成功完成"""
        status = self.get_status()
        return status.get('status') == 'completed'


class TaskManager:
    """純 Dramatiq 任務管理器"""
    
    def __init__(self):
        self._dramatiq_available = None
    
    def _check_dramatiq_availability(self) -> bool:
        """檢查 Dramatiq 是否可用"""
        if self._dramatiq_available is not None:
            return self._dramatiq_available
        
        try:
            from .dramatiq_integration import is_dramatiq_available
            self._dramatiq_available = is_dramatiq_available()
            if self._dramatiq_available:
                logger.info("✅ Dramatiq 任務隊列可用")
            else:
                logger.error("❌ Dramatiq 任務隊列不可用")
            return self._dramatiq_available
        except Exception as e:
            self._dramatiq_available = False
            logger.error(f"❌ Dramatiq 任務隊列檢查失敗: {e}")
            return False
    
    def submit_eqc_workflow(self, folder_path: str, user_session_id: str = None, options: Optional[Dict[str, Any]] = None) -> TaskResult:
        """提交 EQC 工作流程任務"""
        if not self._check_dramatiq_availability():
            raise RuntimeError("Dramatiq 任務隊列不可用")
        
        from .dramatiq_integration import submit_eqc_workflow_task
        result = submit_eqc_workflow_task(folder_path, user_session_id, options)
        task_id = result.message_id
        logger.info(f"📊 EQC 工作流程任務已提交到 Dramatiq: {task_id}")
        
        return TaskResult(task_id, result)
    
    def submit_product_search(self, product_name: str, search_paths: list = None, max_results: int = 1000, search_filters: dict = None) -> TaskResult:
        """提交產品搜索任務"""
        if not self._check_dramatiq_availability():
            raise RuntimeError("Dramatiq 任務隊列不可用")
        
        from .dramatiq_integration import submit_product_search_task
        result = submit_product_search_task(product_name, search_paths, max_results, search_filters)
        task_id = result.message_id
        logger.info(f"🔍 產品搜索任務已提交到 Dramatiq: {task_id}")
        
        return TaskResult(task_id, result)
    
    def submit_csv_summary(self, input_path: str) -> TaskResult:
        """提交 CSV 摘要任務"""
        if not self._check_dramatiq_availability():
            raise RuntimeError("Dramatiq 任務隊列不可用")
        
        from .dramatiq_integration import submit_csv_summary_task
        result = submit_csv_summary_task(input_path)
        task_id = result.message_id
        logger.info(f"📄 CSV 摘要任務已提交到 Dramatiq: {task_id}")
        
        return TaskResult(task_id, result)
    
    def submit_code_comparison(self, input_path: str) -> TaskResult:
        """提交代碼比較任務"""
        if not self._check_dramatiq_availability():
            raise RuntimeError("Dramatiq 任務隊列不可用")
        
        from .dramatiq_integration import submit_code_comparison_task
        result = submit_code_comparison_task(input_path)
        task_id = result.message_id
        logger.info(f"🔧 代碼比較任務已提交到 Dramatiq: {task_id}")
        
        return TaskResult(task_id, result)
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """獲取任務狀態"""
        result = TaskResult(task_id)
        return result.get_status()
    
    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        return {
            'dramatiq_available': self._check_dramatiq_availability(),
            'queue_type': 'dramatiq'
        }

    def submit_health_check(self) -> TaskResult:
        """提交健康檢查任務"""
        if not self._check_dramatiq_availability():
            raise RuntimeError("Dramatiq 任務隊列不可用")

        from .dramatiq_integration import submit_health_check_task
        result = submit_health_check_task()
        task_id = result.message_id
        logger.info(f"❤️ 健康檢查任務已提交到 Dramatiq: {task_id}")

        return TaskResult(task_id, result)


# 全域任務管理器實例
_task_manager = TaskManager()


def get_task_manager() -> TaskManager:
    """獲取全域任務管理器"""
    return _task_manager


# 便捷函數
def submit_eqc_workflow(folder_path: str, user_session_id: str = None, options: Optional[Dict[str, Any]] = None) -> TaskResult:
    """提交 EQC 工作流程任務"""
    return _task_manager.submit_eqc_workflow(folder_path, user_session_id, options)


def submit_product_search(product_name: str, search_paths: list = None, max_results: int = 1000, search_filters: dict = None) -> TaskResult:
    """提交產品搜索任務"""
    return _task_manager.submit_product_search(product_name, search_paths, max_results, search_filters)


def get_system_status() -> Dict[str, Any]:
    """獲取系統狀態"""
    return _task_manager.get_system_status()


# 導出所有公共接口
__all__ = [
    'TaskResult', 
    'TaskManager',
    'get_task_manager',
    'submit_eqc_workflow',
    'submit_product_search',
    'get_system_status'
]