"""
Unit tests for Dashboard Trend Analyzer

Tests the trend analysis, load prediction, and anomaly detection functionality
of the unified monitoring dashboard.

Requirements covered: 10 (Historical trend analysis and predictive warnings)

Author: Dashboard Monitoring System
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import statistics
import json

from backend.monitoring.core.dashboard_trend_analyzer import (
    DashboardTrendAnalyzer, TrendPeriod, AnomalyType, AnomalySeverity,
    TrendDataPoint, TrendAnalysis, LoadPrediction, Anomaly
)
from backend.monitoring.models.dashboard_metrics_models import (
    DashboardMetrics, EmailMetrics, CeleryMetrics, SystemMetrics, 
    FileMetrics, BusinessMetrics
)


class TestDashboardTrendAnalyzer:
    """Test suite for Dashboard Trend Analyzer"""
    
    @pytest.fixture
    def trend_analyzer(self):
        """Create a trend analyzer instance for testing"""
        return DashboardTrendAnalyzer()
    
    @pytest.fixture
    def sample_historical_data(self):
        """Create sample historical data for testing"""
        base_time = datetime.now() - timedelta(hours=48)
        data = []
        
        for i in range(48):  # 48 hours of data
            timestamp = base_time + timedelta(hours=i)
            # Create trending data with some noise
            value = 10 + i * 0.5 + (i % 6) * 2  # Upward trend with cyclical pattern
            
            data.append({
                "timestamp": timestamp.isoformat(),
                "value": value,
                "metric_type": "email_pending_count",
                "tags": {},
                "unit": "count"
            })
        
        return data
    
    @pytest.fixture
    def sample_stable_data(self):
        """Create sample stable data for testing"""
        base_time = datetime.now() - timedelta(hours=24)
        data = []
        
        for i in range(24):
            timestamp = base_time + timedelta(hours=i)
            # Stable data with minimal variation
            value = 15 + (i % 3) * 0.5  # Very stable with small variations
            
            data.append({
                "timestamp": timestamp.isoformat(),
                "value": value,
                "metric_type": "system_cpu_percent",
                "tags": {},
                "unit": "percent"
            })
        
        return data
    
    @pytest.fixture
    def sample_anomalous_data(self):
        """Create sample data with anomalies for testing"""
        base_time = datetime.now() - timedelta(hours=12)
        data = []
        
        for i in range(12):
            timestamp = base_time + timedelta(hours=i)
            # Normal data with a spike at hour 6
            if i == 6:
                value = 100  # Anomalous spike
            else:
                value = 20 + (i % 3) * 2  # Normal variation
            
            data.append({
                "timestamp": timestamp.isoformat(),
                "value": value,
                "metric_type": "celery_total_pending",
                "tags": {},
                "unit": "count"
            })
        
        return data
    
    @pytest.fixture
    def sample_current_metrics(self):
        """Create sample current metrics for testing"""
        return DashboardMetrics(
            email_metrics=EmailMetrics(
                pending_count=25,
                processing_count=5,
                completed_count=100,
                failed_count=2,
                avg_processing_time_seconds=45.5,
                throughput_per_hour=12.5
            ),
            celery_metrics=CeleryMetrics(
                total_active=8,
                total_pending=15,
                total_completed=200,
                total_failed=5
            ),
            system_metrics=SystemMetrics(
                cpu_percent=75.5,
                memory_percent=68.2,
                disk_percent=45.0
            ),
            file_metrics=FileMetrics(
                attachments_downloaded=50,
                attachments_pending=3,
                attachments_failed=1
            ),
            business_metrics=BusinessMetrics(
                mo_processed_today=25,
                lot_processed_today=75
            )
        )
    
    # Test trend analysis functionality
    @pytest.mark.asyncio
    async def test_analyze_trends_increasing(self, trend_analyzer, sample_historical_data):
        """Test trend analysis with increasing trend data"""
        result = await trend_analyzer.analyze_trends(
            "email_pending_count", 
            TrendPeriod.DAY_1, 
            sample_historical_data
        )
        
        assert isinstance(result, TrendAnalysis)
        assert result.metric_type == "email_pending_count"
        assert result.time_period == TrendPeriod.DAY_1
        assert result.trend_direction == "increasing"
        assert result.trend_strength > 0.5  # Should detect strong upward trend
        assert result.percent_change > 0  # Should show positive change
        assert len(result.data_points) == len(sample_historical_data)
    
    @pytest.mark.asyncio
    async def test_analyze_trends_stable(self, trend_analyzer, sample_stable_data):
        """Test trend analysis with stable data"""
        result = await trend_analyzer.analyze_trends(
            "system_cpu_percent",
            TrendPeriod.DAY_1,
            sample_stable_data
        )
        
        assert result.trend_direction == "stable"
        assert result.trend_strength < 0.3  # Should detect weak trend
        assert abs(result.percent_change) < 10  # Should show minimal change
    
    @pytest.mark.asyncio
    async def test_analyze_trends_insufficient_data(self, trend_analyzer):
        """Test trend analysis with insufficient data"""
        insufficient_data = [
            {
                "timestamp": datetime.now().isoformat(),
                "value": 10,
                "metric_type": "test_metric",
                "tags": {},
                "unit": "count"
            }
        ]
        
        result = await trend_analyzer.analyze_trends(
            "test_metric",
            TrendPeriod.DAY_1,
            insufficient_data
        )
        
        assert result.trend_direction == "stable"
        assert result.trend_strength == 0.0
        assert len(result.data_points) == 0
    
    @pytest.mark.asyncio
    async def test_pattern_detection(self, trend_analyzer):
        """Test pattern detection in trend analysis"""
        # Create data with clear daily pattern
        base_time = datetime.now() - timedelta(hours=48)
        pattern_data = []
        
        for i in range(48):
            timestamp = base_time + timedelta(hours=i)
            # Create daily pattern (high during day, low at night)
            hour = timestamp.hour
            if 9 <= hour <= 17:  # Business hours
                value = 30 + (i % 3) * 2
            else:  # Off hours
                value = 10 + (i % 3) * 1
            
            pattern_data.append({
                "timestamp": timestamp.isoformat(),
                "value": value,
                "metric_type": "email_volume",
                "tags": {},
                "unit": "count"
            })
        
        result = await trend_analyzer.analyze_trends(
            "email_volume",
            TrendPeriod.DAY_1,
            pattern_data
        )
        
        assert result.has_pattern
        assert "pattern detected" in result.pattern_description.lower()
    
    # Test load prediction functionality
    @pytest.mark.asyncio
    async def test_predict_load_linear(self, trend_analyzer, sample_historical_data):
        """Test load prediction with linear trend"""
        result = await trend_analyzer.predict_load(
            "email_pending_count",
            24,
            sample_historical_data
        )
        
        assert isinstance(result, LoadPrediction)
        assert result.metric_type == "email_pending_count"
        assert result.prediction_horizon_hours == 24
        assert len(result.predicted_values) == 24
        assert len(result.confidence_interval) == 24
        assert result.prediction_method in ["linear", "seasonal", "moving_average"]
        assert 0 <= result.confidence_score <= 1
    
    @pytest.mark.asyncio
    async def test_predict_load_insufficient_data(self, trend_analyzer):
        """Test load prediction with insufficient data"""
        insufficient_data = [
            {
                "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                "value": 10,
                "metric_type": "test_metric",
                "tags": {},
                "unit": "count"
            }
            for i in range(5)  # Only 5 hours of data
        ]
        
        result = await trend_analyzer.predict_load(
            "test_metric",
            24,
            insufficient_data
        )
        
        assert result.prediction_method == "insufficient_data"
        assert len(result.predicted_values) == 0
        assert "Insufficient" in result.capacity_warnings[0]
    
    @pytest.mark.asyncio
    async def test_capacity_warning_detection(self, trend_analyzer):
        """Test capacity warning detection in predictions"""
        # Create data that will exceed capacity threshold
        base_time = datetime.now() - timedelta(hours=24)
        high_trend_data = []
        
        for i in range(24):
            timestamp = base_time + timedelta(hours=i)
            value = 40 + i * 2  # Rapidly increasing trend
            
            high_trend_data.append({
                "timestamp": timestamp.isoformat(),
                "value": value,
                "metric_type": "email_queue_pending",
                "tags": {},
                "unit": "count"
            })
        
        result = await trend_analyzer.predict_load(
            "email_queue_pending",
            24,
            high_trend_data
        )
        
        # Should detect capacity warnings
        assert len(result.capacity_warnings) > 0
        assert any("capacity" in warning.lower() for warning in result.capacity_warnings)
    
    # Test anomaly detection functionality
    @pytest.mark.asyncio
    async def test_detect_email_anomalies(self, trend_analyzer, sample_current_metrics):
        """Test email anomaly detection"""
        # Create historical data with normal values
        historical_data = []
        base_time = datetime.now() - timedelta(hours=24)
        
        for i in range(24):
            historical_data.append({
                "metric_type": "email",
                "avg_processing_time_seconds": 30.0,  # Normal processing time
                "pending_count": 10,  # Normal queue size
                "completed_count": 50,
                "failed_count": 1,
                "timestamp": (base_time + timedelta(hours=i)).isoformat()
            })
        
        # Current metrics show anomalous processing time (45.5s vs normal 30s)
        anomalies = await trend_analyzer.detect_anomalies(
            sample_current_metrics,
            historical_data
        )
        
        # Should detect processing time anomaly
        processing_time_anomalies = [
            a for a in anomalies 
            if a.anomaly_type == AnomalyType.PROCESSING_TIME_SPIKE
        ]
        
        assert len(processing_time_anomalies) > 0
        anomaly = processing_time_anomalies[0]
        assert anomaly.metric_name == "email_avg_processing_time_seconds"
        assert anomaly.actual_value == 45.5
        assert anomaly.expected_value == 30.0
    
    @pytest.mark.asyncio
    async def test_detect_system_anomalies(self, trend_analyzer, sample_current_metrics):
        """Test system resource anomaly detection"""
        # Create historical data with normal CPU usage
        historical_data = []
        base_time = datetime.now() - timedelta(hours=12)
        
        for i in range(12):
            historical_data.append({
                "metric_type": "system",
                "cpu_percent": 45.0,  # Normal CPU usage
                "memory_percent": 50.0,
                "timestamp": (base_time + timedelta(hours=i)).isoformat()
            })
        
        # Current metrics show high CPU usage (75.5% vs normal 45%)
        anomalies = await trend_analyzer.detect_anomalies(
            sample_current_metrics,
            historical_data
        )
        
        # Should detect CPU usage anomaly
        cpu_anomalies = [
            a for a in anomalies 
            if a.anomaly_type == AnomalyType.RESOURCE_USAGE_SPIKE and "cpu" in a.metric_name
        ]
        
        assert len(cpu_anomalies) > 0
        anomaly = cpu_anomalies[0]
        assert anomaly.metric_name == "system_cpu_percent"
        assert anomaly.actual_value == 75.5
    
    @pytest.mark.asyncio
    async def test_anomaly_severity_calculation(self, trend_analyzer):
        """Test anomaly severity calculation"""
        # Test different deviation levels
        test_cases = [
            (100, 50, 10, AnomalySeverity.CRITICAL),  # 5 std devs
            (80, 50, 10, AnomalySeverity.HIGH),       # 3 std devs
            (70, 50, 10, AnomalySeverity.MEDIUM),     # 2 std devs
            (60, 50, 10, AnomalySeverity.LOW)         # 1 std dev
        ]
        
        for actual, expected, std_dev, expected_severity in test_cases:
            severity = trend_analyzer._calculate_anomaly_severity(actual, expected, std_dev)
            assert severity == expected_severity
    
    # Test root cause analysis functionality
    @pytest.mark.asyncio
    async def test_root_cause_analysis(self, trend_analyzer):
        """Test root cause analysis functionality"""
        # Add some historical anomalies
        historical_anomaly = Anomaly(
            anomaly_id="test_anomaly_1",
            anomaly_type=AnomalyType.PROCESSING_TIME_SPIKE,
            severity=AnomalySeverity.HIGH,
            metric_name="email_avg_processing_time_seconds",
            detected_at=datetime.now() - timedelta(hours=24),
            affected_timespan=(datetime.now() - timedelta(hours=25), datetime.now() - timedelta(hours=23)),
            expected_value=30.0,
            actual_value=60.0,
            deviation_percentage=100.0,
            description="Processing time spike detected",
            possible_causes=["High email volume", "Database performance issues"]
        )
        
        trend_analyzer._anomaly_history.append(historical_anomaly)
        
        result = await trend_analyzer.get_root_cause_analysis(
            AnomalyType.PROCESSING_TIME_SPIKE,
            "email_avg_processing_time_seconds"
        )
        
        assert "anomaly_type" in result
        assert result["anomaly_type"] == AnomalyType.PROCESSING_TIME_SPIKE.value
        assert "analysis" in result
        assert "recommendations" in result
        assert result["analysis"]["similar_incidents_count"] == 1
    
    # Test helper methods
    def test_convert_to_trend_points(self, trend_analyzer, sample_historical_data):
        """Test conversion of historical data to trend points"""
        trend_points = trend_analyzer._convert_to_trend_points(
            sample_historical_data, 
            "email_pending_count"
        )
        
        assert len(trend_points) == len(sample_historical_data)
        assert all(isinstance(tp, TrendDataPoint) for tp in trend_points)
        assert all(tp.metric_name == "email_pending_count" for tp in trend_points)
        
        # Check that points are sorted by timestamp
        timestamps = [tp.timestamp for tp in trend_points]
        assert timestamps == sorted(timestamps)
    
    def test_calculate_trend_direction(self, trend_analyzer):
        """Test trend direction calculation"""
        # Test increasing trend
        increasing_data = [
            TrendDataPoint(datetime.now() + timedelta(hours=i), i * 2, "test_metric")
            for i in range(10)
        ]
        
        direction, strength = trend_analyzer._calculate_trend_direction(increasing_data)
        assert direction == "increasing"
        assert strength > 0.8  # Should be strong correlation
        
        # Test stable trend
        stable_data = [
            TrendDataPoint(datetime.now() + timedelta(hours=i), 10, "test_metric")
            for i in range(10)
        ]
        
        direction, strength = trend_analyzer._calculate_trend_direction(stable_data)
        assert direction == "stable"
        assert strength < 0.1  # Should be weak correlation
    
    def test_calculate_percent_change(self, trend_analyzer):
        """Test percent change calculation"""
        # Test positive change
        values = [10, 15, 20]
        change = trend_analyzer._calculate_percent_change(values)
        assert change == 100.0  # 100% increase from 10 to 20
        
        # Test negative change
        values = [20, 15, 10]
        change = trend_analyzer._calculate_percent_change(values)
        assert change == -50.0  # 50% decrease from 20 to 10
        
        # Test no change
        values = [10, 10, 10]
        change = trend_analyzer._calculate_percent_change(values)
        assert change == 0.0
    
    def test_prediction_method_selection(self, trend_analyzer):
        """Test prediction method selection logic"""
        # Test with insufficient data (should use moving average)
        short_data = [
            TrendDataPoint(datetime.now() + timedelta(hours=i), 10, "test_metric")
            for i in range(10)
        ]
        
        method = trend_analyzer._select_prediction_method(short_data)
        assert method == "moving_average"
        
        # Test with sufficient data and strong trend (should use linear)
        trend_data = [
            TrendDataPoint(datetime.now() + timedelta(hours=i), i * 2, "test_metric")
            for i in range(50)
        ]
        
        method = trend_analyzer._select_prediction_method(trend_data)
        assert method in ["linear", "seasonal"]  # Could be either depending on pattern detection
    
    # Test error handling
    @pytest.mark.asyncio
    async def test_error_handling_invalid_data(self, trend_analyzer):
        """Test error handling with invalid data"""
        invalid_data = [
            {"invalid": "data"},
            {"timestamp": "invalid_timestamp", "value": "invalid_value"}
        ]
        
        result = await trend_analyzer.analyze_trends(
            "test_metric",
            TrendPeriod.DAY_1,
            invalid_data
        )
        
        # Should return empty analysis without crashing
        assert result.trend_direction == "stable"
        assert len(result.data_points) == 0
    
    @pytest.mark.asyncio
    async def test_error_handling_empty_data(self, trend_analyzer):
        """Test error handling with empty data"""
        result = await trend_analyzer.analyze_trends(
            "test_metric",
            TrendPeriod.DAY_1,
            []
        )
        
        assert result.trend_direction == "stable"
        assert len(result.data_points) == 0
    
    # Test configuration and thresholds
    def test_capacity_thresholds_configuration(self, trend_analyzer):
        """Test capacity thresholds configuration"""
        assert "email_queue_pending" in trend_analyzer.capacity_thresholds
        assert "celery_total_pending" in trend_analyzer.capacity_thresholds
        assert "cpu_percent" in trend_analyzer.capacity_thresholds
        
        # Test threshold values are reasonable
        assert trend_analyzer.capacity_thresholds["email_queue_pending"] > 0
        assert trend_analyzer.capacity_thresholds["cpu_percent"] <= 100
    
    def test_anomaly_detection_sensitivity(self, trend_analyzer):
        """Test anomaly detection sensitivity configuration"""
        assert trend_analyzer.anomaly_detection_sensitivity > 0
        assert trend_analyzer.anomaly_detection_sensitivity <= 5  # Reasonable range
    
    # Integration tests
    @pytest.mark.asyncio
    async def test_full_analysis_workflow(self, trend_analyzer, sample_historical_data, sample_current_metrics):
        """Test complete analysis workflow"""
        # 1. Analyze trends
        trend_result = await trend_analyzer.analyze_trends(
            "email_pending_count",
            TrendPeriod.DAY_1,
            sample_historical_data
        )
        
        assert isinstance(trend_result, TrendAnalysis)
        
        # 2. Predict load
        prediction_result = await trend_analyzer.predict_load(
            "email_pending_count",
            24,
            sample_historical_data
        )
        
        assert isinstance(prediction_result, LoadPrediction)
        
        # 3. Detect anomalies
        historical_for_anomaly = [
            {
                "metric_type": "email",
                "avg_processing_time_seconds": 30.0,
                "pending_count": 10,
                "timestamp": datetime.now().isoformat()
            }
        ] * 20
        
        anomaly_results = await trend_analyzer.detect_anomalies(
            sample_current_metrics,
            historical_for_anomaly
        )
        
        assert isinstance(anomaly_results, list)
        
        # All components should work together without errors
        assert True  # If we get here, the workflow completed successfully


# Test fixtures for pytest
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Performance tests
class TestTrendAnalyzerPerformance:
    """Performance tests for trend analyzer"""
    
    @pytest.mark.asyncio
    async def test_large_dataset_performance(self):
        """Test performance with large datasets"""
        analyzer = DashboardTrendAnalyzer()
        
        # Create large dataset (1 week of hourly data)
        base_time = datetime.now() - timedelta(hours=168)
        large_dataset = []
        
        for i in range(168):
            timestamp = base_time + timedelta(hours=i)
            value = 50 + i * 0.1 + (i % 24) * 2  # Trend with daily pattern
            
            large_dataset.append({
                "timestamp": timestamp.isoformat(),
                "value": value,
                "metric_type": "performance_test",
                "tags": {},
                "unit": "count"
            })
        
        # Measure analysis time
        start_time = datetime.now()
        
        result = await analyzer.analyze_trends(
            "performance_test",
            TrendPeriod.DAY_7,
            large_dataset
        )
        
        end_time = datetime.now()
        analysis_duration = (end_time - start_time).total_seconds()
        
        # Should complete within reasonable time (< 5 seconds)
        assert analysis_duration < 5.0
        assert len(result.data_points) == 168
        
        print(f"Large dataset analysis completed in {analysis_duration:.2f} seconds")
    
    @pytest.mark.asyncio
    async def test_concurrent_analysis_performance(self):
        """Test performance with concurrent analyses"""
        analyzer = DashboardTrendAnalyzer()
        
        # Create multiple datasets
        datasets = []
        for dataset_id in range(5):
            base_time = datetime.now() - timedelta(hours=48)
            dataset = []
            
            for i in range(48):
                timestamp = base_time + timedelta(hours=i)
                value = 20 + dataset_id * 10 + i * 0.5
                
                dataset.append({
                    "timestamp": timestamp.isoformat(),
                    "value": value,
                    "metric_type": f"concurrent_test_{dataset_id}",
                    "tags": {},
                    "unit": "count"
                })
            
            datasets.append((f"concurrent_test_{dataset_id}", dataset))
        
        # Run concurrent analyses
        start_time = datetime.now()
        
        tasks = [
            analyzer.analyze_trends(metric_name, TrendPeriod.DAY_1, data)
            for metric_name, data in datasets
        ]
        
        results = await asyncio.gather(*tasks)
        
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        # Should complete all analyses efficiently
        assert len(results) == 5
        assert all(isinstance(result, TrendAnalysis) for result in results)
        assert total_duration < 10.0  # Should be faster than sequential
        
        print(f"Concurrent analysis of {len(datasets)} datasets completed in {total_duration:.2f} seconds")


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])