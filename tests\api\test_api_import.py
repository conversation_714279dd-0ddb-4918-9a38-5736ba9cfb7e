#!/usr/bin/env python3
"""
測試 API 模組導入和基本功能
"""

def test_eqc_async_api():
    """測試 EQC 異步 API"""
    try:
        from frontend.api.eqc_async_api import router
        print('✅ EQC Async API router imported successfully')
        print('Available routes:')
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                method = list(route.methods)[0] if route.methods else "GET"
                print(f'  {method} {route.path}')
        return True
    except Exception as e:
        print(f'❌ EQC Async API import failed: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_eqc_session_manager():
    """測試 EQC 會話管理器"""
    try:
        from backend.eqc.services.eqc_session_manager import get_eqc_session_manager
        session_manager = get_eqc_session_manager()
        
        # 測試創建會話
        session_id = session_manager.create_session(
            folder_path="D:/test_folder",
            user_id="test_user"
        )
        print(f'✅ Session created: {session_id}')
        
        # 測試獲取會話
        session = session_manager.get_session(session_id)
        if session:
            print(f'✅ Session retrieved: {session.session_id}')
        
        # 測試統計
        stats = session_manager.get_session_stats()
        print(f'✅ Session stats: {stats}')
        
        return True
    except Exception as e:
        print(f'❌ EQC Session Manager test failed: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_celery_task():
    """測試 Celery 任務定義"""
    try:
        from backend.tasks import process_complete_eqc_workflow_task
        print('✅ EQC Celery task imported successfully')
        print(f'Task name: {process_complete_eqc_workflow_task.name}')
        return True
    except Exception as e:
        print(f'❌ Celery task import failed: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_monitoring_api():
    """測試監控 API"""
    try:
        from backend.monitoring.api.eqc_monitoring_api import router
        print('✅ EQC Monitoring API router imported successfully')
        print('Available monitoring routes:')
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                method = list(route.methods)[0] if route.methods else "GET"
                print(f'  {method} {route.path}')
        return True
    except Exception as e:
        print(f'❌ EQC Monitoring API import failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 開始 API 功能測試")
    print("=" * 50)
    
    tests = [
        ("EQC Async API", test_eqc_async_api),
        ("EQC Session Manager", test_eqc_session_manager),
        ("Celery Task", test_celery_task),
        ("Monitoring API", test_monitoring_api)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 測試 {test_name}:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 測試結果: {passed}/{total} 通過")
    if passed == total:
        print("🎉 所有測試通過！")
    else:
        print("⚠️ 部分測試失敗")
