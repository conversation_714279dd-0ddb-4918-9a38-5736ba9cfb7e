"""
統一監控儀表板 - 告警管理 API 整合測試

Task 19: 測試告警管理 API 的整合功能
- 告警生命週期測試
- 告警規則觸發測試
- 通知系統整合測試
- 資料持久化測試
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI

from backend.monitoring.api.dashboard_alert_api import router
from backend.monitoring.core.dashboard_alert_service import DashboardAlertService
from backend.monitoring.models.dashboard_alert_models import (
    DashboardAlert,
    AlertLevel,
    AlertStatus,
    AlertType,
    AlertRule,
    NotificationChannel
)
from backend.monitoring.config.dashboard_config import DashboardConfig


@pytest.fixture
def app():
    """創建測試應用"""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
def client(app):
    """創建測試客戶端"""
    return TestClient(app)


@pytest.fixture
def alert_service():
    """創建真實的告警服務實例"""
    return DashboardAlertService()


@pytest.fixture
def sample_metrics_data():
    """創建測試指標資料"""
    return {
        "email_queue": {
            "pending_count": 15,
            "processing_count": 3,
            "failed_count": 2
        },
        "dramatiq_task": {
            "total_active": 8,
            "total_pending": 25,
            "failure_rate": 0.15
        },
        "system_resource": {
            "cpu_percent": 85.5,
            "memory_percent": 92.0,
            "disk_percent": 78.0
        }
    }


class TestAlertLifecycle:
    """告警生命週期整合測試"""
    
    def test_alert_creation_and_acknowledgment_flow(self, alert_service, sample_metrics_data):
        """測試告警創建和確認流程"""
        # 1. 評估告警規則並生成告警
        alerts = asyncio.run(alert_service.evaluate_alerts(sample_metrics_data))
        
        # 驗證告警生成
        assert len(alerts) >= 0  # 可能沒有觸發告警，取決於規則配置
        
        # 2. 手動創建一個測試告警
        test_alert = DashboardAlert(
            id="integration-test-alert",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="整合測試告警",
            message="這是一個整合測試告警",
            source="integration_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE,
            threshold_value=10.0,
            current_value=15.0
        )
        
        # 添加到活躍告警中
        alert_service.active_alerts[test_alert.id] = test_alert
        
        # 3. 驗證告警處於活躍狀態
        active_alerts = alert_service.get_active_alerts()
        assert any(alert.id == test_alert.id for alert in active_alerts)
        
        # 4. 確認告警
        success = asyncio.run(alert_service.acknowledge_alert(test_alert.id, "integration_test"))
        assert success == True
        
        # 5. 驗證告警狀態變更
        updated_alert = asyncio.run(alert_service.get_alert_by_id(test_alert.id))
        assert updated_alert.status == AlertStatus.ACKNOWLEDGED
        assert updated_alert.acknowledged_at is not None
        
        # 6. 解決告警
        success = asyncio.run(alert_service.resolve_alert(test_alert.id, "integration_test", "測試完成"))
        assert success == True
        
        # 7. 驗證告警已解決
        resolved_alert = asyncio.run(alert_service.get_alert_by_id(test_alert.id))
        assert resolved_alert.status == AlertStatus.RESOLVED
        assert resolved_alert.resolved_at is not None
    
    def test_alert_escalation_flow(self, alert_service):
        """測試告警升級流程"""
        # 創建測試告警
        test_alert = DashboardAlert(
            id="escalation-test-alert",
            alert_type=AlertType.RESOURCE_HIGH,
            level=AlertLevel.WARNING,
            title="升級測試告警",
            message="測試告警升級機制",
            source="escalation_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE
        )
        
        alert_service.active_alerts[test_alert.id] = test_alert
        
        # 模擬多次觸發相同告警
        for i in range(3):
            test_alert.increment_occurrence()
            test_alert.escalate()
        
        # 驗證告警已升級
        assert test_alert.escalation_level == 3
        assert test_alert.occurrence_count == 4  # 初始1次 + 3次增加
        
        # 驗證優先級分數增加
        priority_score = test_alert.get_priority_score()
        assert priority_score > 100  # 基礎分數 + 升級獎勵 + 發生次數獎勵
    
    def test_alert_merging_and_deduplication(self, alert_service):
        """測試告警合併和去重"""
        # 創建多個相似告警
        base_time = datetime.now()
        similar_alerts = []
        
        for i in range(3):
            alert = DashboardAlert(
                id=f"merge-test-alert-{i}",
                alert_type=AlertType.QUEUE_OVERFLOW,
                level=AlertLevel.WARNING,
                title="佇列過載告警",
                message="郵件佇列待處理數量過多",
                source="email_monitor",
                triggered_at=base_time + timedelta(minutes=i),
                status=AlertStatus.ACTIVE,
                rule_id="queue-overflow-rule"
            )
            similar_alerts.append(alert)
        
        # 測試合併功能
        merged_alerts = alert_service._merge_and_deduplicate_alerts(similar_alerts)
        
        # 驗證合併結果
        assert len(merged_alerts) <= len(similar_alerts)  # 合併後數量應該減少或相等
        
        # 如果有合併，驗證合併後的告警包含正確資訊
        if len(merged_alerts) < len(similar_alerts):
            merged_alert = merged_alerts[0]
            assert "合併" in merged_alert.title
            assert merged_alert.occurrence_count >= 2


class TestAlertRuleIntegration:
    """告警規則整合測試"""
    
    def test_alert_rule_evaluation_and_triggering(self, alert_service, sample_metrics_data):
        """測試告警規則評估和觸發"""
        # 創建測試規則
        test_rule = AlertRule(
            id="integration-test-rule",
            name="CPU 使用率過高測試規則",
            description="測試 CPU 使用率告警",
            metric_type="system_resource",
            metric_name="cpu_percent",
            threshold_value=80.0,
            threshold_operator=">",
            alert_type=AlertType.RESOURCE_HIGH,
            alert_level=AlertLevel.WARNING,
            alert_title_template="CPU 使用率過高: {current_value:.1f}%",
            alert_message_template="當前 CPU 使用率為 {current_value:.1f}%，超過閾值 {threshold_value}%",
            enabled=True,
            cooldown_seconds=300,
            notification_channels=[NotificationChannel.SYSTEM_NOTIFICATION]
        )
        
        # 測試規則評估
        current_cpu = sample_metrics_data["system_resource"]["cpu_percent"]  # 85.5%
        should_trigger = test_rule.evaluate(current_cpu)
        
        # 驗證規則應該觸發（85.5 > 80.0）
        assert should_trigger == True
        
        # 測試創建告警
        alert = test_rule.create_alert(current_cpu, "system_monitor")
        
        # 驗證告警內容
        assert alert.alert_type == AlertType.RESOURCE_HIGH
        assert alert.level == AlertLevel.WARNING
        assert alert.current_value == current_cpu
        assert alert.threshold_value == 80.0
        assert alert.rule_id == test_rule.id
        assert "85.5%" in alert.title
        assert "85.5%" in alert.message
    
    def test_alert_rule_cooldown_mechanism(self, alert_service):
        """測試告警規則冷卻機制"""
        # 創建帶冷卻期的規則
        test_rule = AlertRule(
            id="cooldown-test-rule",
            name="冷卻測試規則",
            metric_type="test_metric",
            metric_name="test_value",
            threshold_value=50.0,
            threshold_operator=">",
            alert_type=AlertType.SYSTEM_ERROR,
            alert_level=AlertLevel.ERROR,
            alert_title_template="測試告警",
            alert_message_template="測試告警訊息",
            enabled=True,
            cooldown_seconds=60  # 1分鐘冷卻期
        )
        
        # 第一次評估應該觸發
        first_evaluation = test_rule.evaluate(60.0)
        assert first_evaluation == True
        
        # 立即第二次評估應該被冷卻期阻止
        second_evaluation = test_rule.evaluate(60.0)
        assert second_evaluation == False
        
        # 模擬時間過去（實際測試中可能需要 mock datetime）
        # 這裡我們直接重置 last_triggered 來模擬冷卻期結束
        test_rule.last_triggered = datetime.now() - timedelta(seconds=120)
        
        # 冷卻期結束後應該可以再次觸發
        third_evaluation = test_rule.evaluate(60.0)
        assert third_evaluation == True


class TestNotificationIntegration:
    """通知系統整合測試"""
    
    @patch('smtplib.SMTP')
    def test_email_notification_integration(self, mock_smtp, alert_service):
        """測試郵件通知整合"""
        # 創建測試告警
        test_alert = DashboardAlert(
            id="email-test-alert",
            alert_type=AlertType.CRITICAL,
            level=AlertLevel.CRITICAL,
            title="嚴重告警測試",
            message="這是一個嚴重告警測試",
            source="notification_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE
        )
        
        # 模擬 SMTP 服務器
        mock_server = Mock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        
        # 測試發送告警
        success = asyncio.run(alert_service.send_alert(test_alert))
        
        # 驗證結果（取決於配置是否有郵件設定）
        # 如果沒有配置郵件，success 可能為 False 或 True（系統通知）
        assert isinstance(success, bool)
    
    def test_system_notification_integration(self, alert_service):
        """測試系統通知整合"""
        # 創建測試告警
        test_alert = DashboardAlert(
            id="system-notification-test",
            alert_type=AlertType.SERVICE_DOWN,
            level=AlertLevel.ERROR,
            title="服務停止告警",
            message="關鍵服務已停止運行",
            source="system_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE
        )
        
        # 測試系統通知
        success = asyncio.run(alert_service.send_alert(test_alert))
        
        # 系統通知應該總是成功（記錄到日誌）
        assert success == True
        
        # 驗證通知記錄
        assert len(test_alert.notification_channels) > 0


class TestAlertAPIIntegration:
    """告警 API 整合測試"""
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_complete_alert_management_workflow(self, mock_service_dep, client, alert_service):
        """測試完整的告警管理工作流程"""
        # 設定依賴注入
        mock_service_dep.return_value = alert_service
        
        # 1. 創建測試告警
        test_alert = DashboardAlert(
            id="workflow-test-alert",
            alert_type=AlertType.QUEUE_OVERFLOW,
            level=AlertLevel.WARNING,
            title="工作流程測試告警",
            message="測試完整工作流程",
            source="workflow_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE
        )
        
        alert_service.active_alerts[test_alert.id] = test_alert
        
        # 2. 測試獲取活躍告警
        response = client.get("/api/alerts/active")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        
        # 3. 測試獲取告警詳情
        response = client.get(f"/api/alerts/{test_alert.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["alert"]["id"] == test_alert.id
        
        # 4. 測試確認告警
        acknowledge_data = {
            "acknowledged_by": "integration_test",
            "note": "工作流程測試確認"
        }
        response = client.post(f"/api/alerts/{test_alert.id}/acknowledge", json=acknowledge_data)
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        
        # 5. 測試解決告警
        resolve_data = {
            "resolved_by": "integration_test",
            "resolution_note": "工作流程測試完成"
        }
        response = client.post(f"/api/alerts/{test_alert.id}/resolve", json=resolve_data)
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        
        # 6. 驗證告警狀態
        updated_alert = asyncio.run(alert_service.get_alert_by_id(test_alert.id))
        assert updated_alert.status == AlertStatus.RESOLVED
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_alert_statistics_integration(self, mock_service_dep, client, alert_service):
        """測試告警統計整合"""
        # 設定依賴注入
        mock_service_dep.return_value = alert_service
        
        # 創建多個測試告警
        test_alerts = []
        for i in range(5):
            alert = DashboardAlert(
                id=f"stats-test-alert-{i}",
                alert_type=AlertType.QUEUE_OVERFLOW,
                level=AlertLevel.WARNING if i < 3 else AlertLevel.ERROR,
                title=f"統計測試告警 {i}",
                message=f"統計測試告警訊息 {i}",
                source="stats_test",
                triggered_at=datetime.now() - timedelta(hours=i),
                status=AlertStatus.ACTIVE if i < 2 else AlertStatus.RESOLVED
            )
            test_alerts.append(alert)
            alert_service.active_alerts[alert.id] = alert
            alert_service.alert_history.append(alert)
        
        # 測試獲取告警統計摘要
        response = client.get("/api/alerts/statistics/summary")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "summary" in data["data"]
        assert "trends" in data["data"]
        
        # 測試獲取告警趨勢
        response = client.get("/api/alerts/statistics/trends?time_range=24h")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["time_range"] == "24h"
    
    @patch('src.dashboard_monitoring.api.dashboard_alert_api.AlertService')
    def test_alert_export_integration(self, mock_service_dep, client, alert_service):
        """測試告警匯出整合"""
        # 設定依賴注入
        mock_service_dep.return_value = alert_service
        
        # 創建測試告警
        test_alert = DashboardAlert(
            id="export-test-alert",
            alert_type=AlertType.SYSTEM_ERROR,
            level=AlertLevel.ERROR,
            title="匯出測試告警",
            message="測試告警匯出功能",
            source="export_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE
        )
        
        alert_service.alert_history.append(test_alert)
        
        # 測試 JSON 匯出
        response = client.get("/api/alerts/export?format=json")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["export_format"] == "json"
        
        # 測試 CSV 匯出
        response = client.get("/api/alerts/export?format=csv")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["export_format"] == "csv"


class TestAlertPersistenceIntegration:
    """告警持久化整合測試"""
    
    def test_alert_history_persistence(self, alert_service):
        """測試告警歷史持久化"""
        # 創建測試告警
        test_alert = DashboardAlert(
            id="persistence-test-alert",
            alert_type=AlertType.DATA_QUALITY,
            level=AlertLevel.INFO,
            title="持久化測試告警",
            message="測試告警歷史持久化",
            source="persistence_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE
        )
        
        # 添加到歷史記錄
        alert_service.alert_history.append(test_alert)
        
        # 測試獲取歷史記錄
        history = alert_service.get_alert_history(hours=24, limit=100)
        assert len(history) > 0
        assert any(alert.id == test_alert.id for alert in history)
        
        # 測試清理舊記錄
        initial_count = len(alert_service.alert_history)
        cleaned_count = alert_service.cleanup_old_alerts(days=0)  # 清理所有記錄
        
        # 驗證清理結果
        assert cleaned_count >= 0
        assert len(alert_service.alert_history) <= initial_count
    
    def test_alert_metadata_persistence(self, alert_service):
        """測試告警元資料持久化"""
        # 創建帶元資料的告警
        test_alert = DashboardAlert(
            id="metadata-test-alert",
            alert_type=AlertType.PERFORMANCE_DEGRADATION,
            level=AlertLevel.WARNING,
            title="元資料測試告警",
            message="測試告警元資料持久化",
            source="metadata_test",
            triggered_at=datetime.now(),
            status=AlertStatus.ACTIVE,
            metadata={
                "test_key": "test_value",
                "numeric_value": 123.45,
                "nested_data": {
                    "sub_key": "sub_value"
                }
            }
        )
        
        alert_service.active_alerts[test_alert.id] = test_alert
        
        # 添加備註
        success = asyncio.run(alert_service.add_alert_note(
            test_alert.id, 
            "這是一個測試備註", 
            "integration_test"
        ))
        assert success == True
        
        # 驗證元資料和備註
        retrieved_alert = asyncio.run(alert_service.get_alert_by_id(test_alert.id))
        assert retrieved_alert.metadata["test_key"] == "test_value"
        assert retrieved_alert.metadata["numeric_value"] == 123.45
        assert "notes" in retrieved_alert.metadata
        assert len(retrieved_alert.metadata["notes"]) == 1


class TestAlertPerformanceIntegration:
    """告警效能整合測試"""
    
    def test_large_scale_alert_handling(self, alert_service):
        """測試大規模告警處理"""
        # 創建大量測試告警
        large_alert_batch = []
        for i in range(100):
            alert = DashboardAlert(
                id=f"performance-test-alert-{i}",
                alert_type=AlertType.QUEUE_OVERFLOW,
                level=AlertLevel.WARNING,
                title=f"效能測試告警 {i}",
                message=f"效能測試告警訊息 {i}",
                source="performance_test",
                triggered_at=datetime.now() - timedelta(minutes=i),
                status=AlertStatus.ACTIVE
            )
            large_alert_batch.append(alert)
        
        # 測試批量處理
        start_time = datetime.now()
        
        # 添加到服務中
        for alert in large_alert_batch:
            alert_service.active_alerts[alert.id] = alert
            alert_service.alert_history.append(alert)
        
        # 測試查詢效能
        active_alerts = alert_service.get_active_alerts()
        history = alert_service.get_alert_history(hours=24, limit=1000)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 驗證處理時間合理（應該在幾秒內完成）
        assert processing_time < 10.0  # 10秒內完成
        assert len(active_alerts) == 100
        assert len(history) >= 100
        
        # 測試清理效能
        cleanup_start = datetime.now()
        cleaned_count = alert_service.cleanup_old_alerts(days=0)
        cleanup_end = datetime.now()
        cleanup_time = (cleanup_end - cleanup_start).total_seconds()
        
        # 驗證清理效能
        assert cleanup_time < 5.0  # 5秒內完成清理
        assert cleaned_count >= 100


if __name__ == "__main__":
    pytest.main([__file__, "-v"])