"""
Integration tests for Dashboard Trend Analysis System

Tests the integration between trend analyzer, repository, and data models
for the unified monitoring dashboard.

Requirements covered: 10 (Historical trend analysis and predictive warnings)

Author: Dashboard Monitoring System
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path

from backend.monitoring.core.dashboard_trend_analyzer import (
    DashboardTrendAnalyzer, TrendPeriod, AnomalyType
)
from backend.monitoring.repositories.dashboard_trend_repository import (
    DashboardTrendRepository
)
from backend.monitoring.models.dashboard_metrics_models import (
    DashboardMetrics, EmailMetrics, CeleryMetrics, SystemMetrics
)
from backend.monitoring.models.dashboard_trend_models import (
    EnhancedTrendAnalysis, EnhancedLoadPrediction, EnhancedAnomaly
)


class TestDashboardTrendIntegration:
    """Integration tests for trend analysis system"""
    
    @pytest.fixture
    async def temp_db_repository(self):
        """Create a temporary database repository for testing"""
        # Create temporary database file
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        repository = DashboardTrendRepository(temp_db.name)
        await repository._initialize_schema()
        
        yield repository
        
        # Cleanup
        try:
            os.unlink(temp_db.name)
        except OSError:
            pass
    
    @pytest.fixture
    def trend_analyzer(self):
        """Create trend analyzer instance"""
        return DashboardTrendAnalyzer()
    
    @pytest.fixture
    def sample_metrics_sequence(self):
        """Create a sequence of metrics for testing"""
        base_time = datetime.now() - timedelta(hours=48)
        metrics_sequence = []
        
        for i in range(48):  # 48 hours of metrics
            timestamp = base_time + timedelta(hours=i)
            
            # Create realistic metrics with trends
            email_pending = 10 + i * 0.5 + (i % 6) * 2  # Increasing with pattern
            cpu_usage = 45 + (i % 12) * 3  # Cyclical pattern
            celery_pending = max(0, 20 + (i % 8) * 5 - i * 0.2)  # Decreasing trend
            
            metrics = DashboardMetrics(
                email_metrics=EmailMetrics(
                    pending_count=int(email_pending),
                    processing_count=5,
                    completed_count=100 + i * 2,
                    failed_count=max(0, 2 + (i % 10) - 1),
                    avg_processing_time_seconds=30 + (i % 5) * 2,
                    throughput_per_hour=12 + (i % 3)
                ),
                celery_metrics=CeleryMetrics(
                    total_active=8,
                    total_pending=int(celery_pending),
                    total_completed=200 + i * 3,
                    total_failed=max(0, 5 + (i % 8) - 2)
                ),
                system_metrics=SystemMetrics(
                    cpu_percent=cpu_usage,
                    memory_percent=60 + (i % 4) * 2,
                    disk_percent=40 + i * 0.1
                ),
                timestamp=timestamp
            )
            
            metrics_sequence.append(metrics)
        
        return metrics_sequence
    
    @pytest.mark.asyncio
    async def test_end_to_end_trend_analysis(self, temp_db_repository, trend_analyzer, sample_metrics_sequence):
        """Test complete end-to-end trend analysis workflow"""
        
        # 1. Store historical metrics data
        for metrics in sample_metrics_sequence:
            success = await temp_db_repository.store_metrics_data(metrics)
            assert success, "Failed to store metrics data"
        
        # 2. Retrieve historical data for analysis
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=48)
        
        historical_data = await temp_db_repository.get_historical_data(
            "pending_count", start_time, end_time
        )
        
        assert len(historical_data) > 0, "No historical data retrieved"
        
        # 3. Perform trend analysis
        trend_analysis = await trend_analyzer.analyze_trends(
            "pending_count",
            TrendPeriod.DAY_1,
            historical_data
        )
        
        assert trend_analysis.metric_type == "pending_count"
        assert trend_analysis.trend_direction in ["increasing", "decreasing", "stable"]
        assert len(trend_analysis.data_points) > 0
        
        # 4. Perform load prediction
        load_prediction = await trend_analyzer.predict_load(
            "pending_count",
            24,
            historical_data
        )
        
        assert load_prediction.metric_type == "pending_count"
        assert load_prediction.prediction_horizon_hours == 24
        assert len(load_prediction.predicted_values) == 24
        
        # 5. Store analysis results
        # Note: This would require implementing enhanced models storage
        # For now, verify the analysis completed successfully
        assert trend_analysis is not None
        assert load_prediction is not None
        
        print(f"✅ End-to-end analysis completed:")
        print(f"   Trend: {trend_analysis.trend_direction} (strength: {trend_analysis.trend_strength:.2f})")
        print(f"   Prediction confidence: {load_prediction.confidence_score:.2f}")
        print(f"   Data points analyzed: {len(trend_analysis.data_points)}")
    
    @pytest.mark.asyncio
    async def test_anomaly_detection_integration(self, temp_db_repository, trend_analyzer, sample_metrics_sequence):
        """Test anomaly detection with stored historical data"""
        
        # Store historical data (normal patterns)
        for metrics in sample_metrics_sequence[:-1]:  # All but the last one
            await temp_db_repository.store_metrics_data(metrics)
        
        # Create anomalous current metrics
        anomalous_metrics = DashboardMetrics(
            email_metrics=EmailMetrics(
                pending_count=100,  # Much higher than normal
                processing_count=5,
                completed_count=200,
                failed_count=2,
                avg_processing_time_seconds=120,  # Much slower than normal
                throughput_per_hour=5  # Much lower than normal
            ),
            celery_metrics=CeleryMetrics(
                total_active=8,
                total_pending=200,  # Much higher than normal
                total_completed=400,
                total_failed=50  # Much higher than normal
            ),
            system_metrics=SystemMetrics(
                cpu_percent=95,  # Very high
                memory_percent=90,  # Very high
                disk_percent=85  # High
            )
        )
        
        # Get historical data for comparison
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=48)
        
        email_history = await temp_db_repository.get_metrics_by_category(
            "email", start_time, end_time
        )
        celery_history = await temp_db_repository.get_metrics_by_category(
            "celery", start_time, end_time
        )
        system_history = await temp_db_repository.get_metrics_by_category(
            "system", start_time, end_time
        )
        
        all_history = email_history + celery_history + system_history
        
        # Detect anomalies
        anomalies = await trend_analyzer.detect_anomalies(
            anomalous_metrics,
            all_history
        )
        
        # Should detect multiple anomalies
        assert len(anomalies) > 0, "No anomalies detected despite anomalous data"
        
        # Verify anomaly types
        anomaly_types = [a.anomaly_type for a in anomalies]
        assert AnomalyType.PROCESSING_TIME_SPIKE in anomaly_types or AnomalyType.QUEUE_SIZE_ANOMALY in anomaly_types
        
        print(f"✅ Anomaly detection completed:")
        print(f"   Anomalies detected: {len(anomalies)}")
        for anomaly in anomalies:
            print(f"   - {anomaly.anomaly_type.value}: {anomaly.metric_name} ({anomaly.severity.value})")
    
    @pytest.mark.asyncio
    async def test_multiple_metrics_analysis(self, temp_db_repository, trend_analyzer, sample_metrics_sequence):
        """Test analysis of multiple metrics simultaneously"""
        
        # Store all metrics data
        for metrics in sample_metrics_sequence:
            await temp_db_repository.store_metrics_data(metrics)
        
        # Analyze multiple metrics
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=48)
        
        metrics_to_analyze = [
            "pending_count",
            "cpu_percent", 
            "total_pending"
        ]
        
        analysis_results = {}
        prediction_results = {}
        
        for metric_name in metrics_to_analyze:
            # Get historical data
            historical_data = await temp_db_repository.get_historical_data(
                metric_name, start_time, end_time
            )
            
            if len(historical_data) > 10:  # Ensure sufficient data
                # Perform trend analysis
                trend_analysis = await trend_analyzer.analyze_trends(
                    metric_name,
                    TrendPeriod.DAY_1,
                    historical_data
                )
                analysis_results[metric_name] = trend_analysis
                
                # Perform load prediction
                load_prediction = await trend_analyzer.predict_load(
                    metric_name,
                    12,  # 12 hour prediction
                    historical_data
                )
                prediction_results[metric_name] = load_prediction
        
        # Verify all analyses completed
        assert len(analysis_results) > 0, "No trend analyses completed"
        assert len(prediction_results) > 0, "No load predictions completed"
        
        # Verify analysis quality
        for metric_name, analysis in analysis_results.items():
            assert analysis.metric_type == metric_name
            assert len(analysis.data_points) > 0
            assert analysis.trend_direction in ["increasing", "decreasing", "stable"]
        
        for metric_name, prediction in prediction_results.items():
            assert prediction.metric_type == metric_name
            assert len(prediction.predicted_values) == 12
            assert 0 <= prediction.confidence_score <= 1
        
        print(f"✅ Multiple metrics analysis completed:")
        print(f"   Metrics analyzed: {len(analysis_results)}")
        print(f"   Predictions generated: {len(prediction_results)}")
        
        for metric_name in analysis_results:
            trend = analysis_results[metric_name]
            pred = prediction_results[metric_name]
            print(f"   {metric_name}: {trend.trend_direction} trend, {pred.confidence_score:.2f} confidence")
    
    @pytest.mark.asyncio
    async def test_data_quality_and_cleanup(self, temp_db_repository, sample_metrics_sequence):
        """Test data quality checks and cleanup functionality"""
        
        # Store metrics data
        for metrics in sample_metrics_sequence:
            await temp_db_repository.store_metrics_data(metrics)
        
        # Get initial database stats
        initial_stats = await temp_db_repository.get_database_stats()
        assert initial_stats["dashboard_metrics_history_count"] > 0
        
        # Test data retrieval with different time ranges
        end_time = datetime.now()
        
        # Last 24 hours
        recent_data = await temp_db_repository.get_historical_data(
            "pending_count", 
            end_time - timedelta(hours=24), 
            end_time
        )
        
        # Last 48 hours
        extended_data = await temp_db_repository.get_historical_data(
            "pending_count",
            end_time - timedelta(hours=48),
            end_time
        )
        
        assert len(extended_data) >= len(recent_data)
        
        # Test cleanup functionality
        # Note: Using very short retention for testing
        cleanup_success = await temp_db_repository.cleanup_old_data(retention_days=0)
        assert cleanup_success
        
        # Verify cleanup worked
        final_stats = await temp_db_repository.get_database_stats()
        # After cleanup with 0 retention days, should have fewer records
        assert final_stats["dashboard_metrics_history_count"] <= initial_stats["dashboard_metrics_history_count"]
        
        print(f"✅ Data quality and cleanup test completed:")
        print(f"   Initial records: {initial_stats['dashboard_metrics_history_count']}")
        print(f"   Final records: {final_stats['dashboard_metrics_history_count']}")
        print(f"   Recent data points: {len(recent_data)}")
        print(f"   Extended data points: {len(extended_data)}")
    
    @pytest.mark.asyncio
    async def test_performance_with_large_dataset(self, temp_db_repository, trend_analyzer):
        """Test performance with larger datasets"""
        
        # Generate larger dataset (1 week of hourly data)
        base_time = datetime.now() - timedelta(hours=168)
        large_metrics_sequence = []
        
        for i in range(168):  # 1 week of hourly data
            timestamp = base_time + timedelta(hours=i)
            
            metrics = DashboardMetrics(
                email_metrics=EmailMetrics(
                    pending_count=10 + i * 0.1 + (i % 24) * 2,
                    processing_count=5,
                    completed_count=100 + i,
                    failed_count=max(0, (i % 20) - 2),
                    avg_processing_time_seconds=30 + (i % 10),
                    throughput_per_hour=12 + (i % 5)
                ),
                celery_metrics=CeleryMetrics(
                    total_active=8,
                    total_pending=20 + (i % 12) * 3,
                    total_completed=200 + i * 2,
                    total_failed=max(0, (i % 15) - 3)
                ),
                system_metrics=SystemMetrics(
                    cpu_percent=45 + (i % 24) * 2,
                    memory_percent=60 + (i % 8),
                    disk_percent=40 + i * 0.05
                ),
                timestamp=timestamp
            )
            
            large_metrics_sequence.append(metrics)
        
        # Store all data
        start_time = datetime.now()
        
        for metrics in large_metrics_sequence:
            await temp_db_repository.store_metrics_data(metrics)
        
        storage_time = (datetime.now() - start_time).total_seconds()
        
        # Perform analysis on large dataset
        end_time = datetime.now()
        start_time_query = end_time - timedelta(hours=168)
        
        historical_data = await temp_db_repository.get_historical_data(
            "pending_count", start_time_query, end_time, limit=200
        )
        
        analysis_start = datetime.now()
        
        trend_analysis = await trend_analyzer.analyze_trends(
            "pending_count",
            TrendPeriod.DAY_7,
            historical_data
        )
        
        analysis_time = (datetime.now() - analysis_start).total_seconds()
        
        # Performance assertions
        assert storage_time < 30.0, f"Storage took too long: {storage_time:.2f}s"
        assert analysis_time < 5.0, f"Analysis took too long: {analysis_time:.2f}s"
        assert len(historical_data) > 100, "Insufficient data retrieved"
        assert trend_analysis is not None
        
        print(f"✅ Performance test completed:")
        print(f"   Data points stored: {len(large_metrics_sequence)}")
        print(f"   Storage time: {storage_time:.2f}s")
        print(f"   Analysis time: {analysis_time:.2f}s")
        print(f"   Data points analyzed: {len(historical_data)}")
    
    @pytest.mark.asyncio
    async def test_error_recovery_and_resilience(self, temp_db_repository, trend_analyzer):
        """Test error recovery and system resilience"""
        
        # Test with corrupted/invalid data
        invalid_metrics = DashboardMetrics(
            email_metrics=EmailMetrics(
                pending_count=-1,  # Invalid negative value
                processing_count=5,
                completed_count=100,
                failed_count=2
            ),
            system_metrics=SystemMetrics(
                cpu_percent=150,  # Invalid percentage > 100
                memory_percent=60,
                disk_percent=40
            )
        )
        
        # Should handle invalid data gracefully
        storage_success = await temp_db_repository.store_metrics_data(invalid_metrics)
        # Storage might succeed (database allows it) but analysis should handle it
        
        # Test analysis with minimal data
        minimal_data = [
            {
                "timestamp": datetime.now().isoformat(),
                "value": 10,
                "metric_type": "test_metric",
                "tags": {},
                "unit": "count"
            }
        ]
        
        # Should not crash with minimal data
        trend_analysis = await trend_analyzer.analyze_trends(
            "test_metric",
            TrendPeriod.DAY_1,
            minimal_data
        )
        
        assert trend_analysis is not None
        assert trend_analysis.trend_direction == "stable"  # Default for insufficient data
        
        # Test with empty data
        empty_analysis = await trend_analyzer.analyze_trends(
            "empty_metric",
            TrendPeriod.DAY_1,
            []
        )
        
        assert empty_analysis is not None
        assert len(empty_analysis.data_points) == 0
        
        # Test database connection resilience
        stats = await temp_db_repository.get_database_stats()
        assert isinstance(stats, dict)  # Should return dict even if some queries fail
        
        print(f"✅ Error recovery test completed:")
        print(f"   Invalid data handled gracefully")
        print(f"   Minimal data analysis: {trend_analysis.trend_direction}")
        print(f"   Empty data analysis completed without errors")
        print(f"   Database stats retrieved: {len(stats)} fields")


# Test fixtures for pytest
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Utility functions for integration testing
async def create_test_data_sequence(hours: int, base_value: float = 10.0, 
                                  trend_factor: float = 0.1, 
                                  noise_factor: float = 2.0) -> list:
    """Create a sequence of test data with configurable characteristics"""
    base_time = datetime.now() - timedelta(hours=hours)
    data_sequence = []
    
    for i in range(hours):
        timestamp = base_time + timedelta(hours=i)
        # Create value with trend and noise
        value = base_value + i * trend_factor + (i % 6) * noise_factor
        
        data_sequence.append({
            "timestamp": timestamp.isoformat(),
            "value": max(0, value),  # Ensure non-negative
            "metric_type": "test_metric",
            "tags": {"test": "true"},
            "unit": "count"
        })
    
    return data_sequence


async def verify_analysis_quality(analysis_result, expected_direction=None, 
                                min_data_points=10) -> bool:
    """Verify the quality of analysis results"""
    if not analysis_result:
        return False
    
    # Check basic structure
    if len(analysis_result.data_points) < min_data_points:
        return False
    
    # Check trend direction if specified
    if expected_direction and analysis_result.trend_direction != expected_direction:
        return False
    
    # Check statistical measures are reasonable
    if analysis_result.average < 0:
        return False
    
    if analysis_result.std_deviation < 0:
        return False
    
    return True


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v", "--tb=short", "-s"])