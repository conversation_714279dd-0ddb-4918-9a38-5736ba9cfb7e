"""
統一監控儀表板快取整合測試
測試快取服務與監控系統的整合
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from backend.monitoring.core.dashboard_cache_manager import (
    DashboardCacheManager, get_cache_manager, initialize_cache_manager, shutdown_cache_manager
)
from backend.monitoring.config.dashboard_cache_config import (
    CacheConfig, CacheNamespace, get_default_cache_config
)
from backend.monitoring.models.dashboard_metrics_models import (
    EmailMetrics, CeleryMetrics, SystemMetrics, FileMetrics, BusinessMetrics, DashboardMetrics
)
from backend.monitoring.utils.dashboard_cache_utils import (
    cache_result, get_or_set_cache, invalidate_metrics_cache, CacheContext, CacheBatch
)


class TestCacheManagerIntegration:
    """快取管理器整合測試"""
    
    @pytest.fixture
    async def cache_manager(self):
        """快取管理器測試夾具"""
        config = CacheConfig(
            max_size=100,
            default_ttl=300,
            cleanup_interval=60,
            max_memory_mb=10
        )
        
        manager = DashboardCacheManager(config)
        await manager.start()
        
        yield manager
        
        await manager.stop()
    
    @pytest.mark.asyncio
    async def test_cache_manager_lifecycle(self):
        """測試快取管理器生命週期"""
        # 初始化
        manager = await initialize_cache_manager()
        assert manager is not None
        
        # 測試基本操作
        success = manager.cache_service.set("lifecycle_test", "test_value")
        assert success
        
        value = manager.cache_service.get("lifecycle_test")
        assert value == "test_value"
        
        # 關閉
        await shutdown_cache_manager()
    
    @pytest.mark.asyncio
    async def test_metrics_caching(self, cache_manager):
        """測試監控指標快取"""
        # 創建測試指標
        email_metrics = EmailMetrics(
            pending_count=10,
            processing_count=5,
            completed_count=100,
            failed_count=2,
            avg_processing_time_seconds=30.5,
            throughput_per_hour=120.0,
            vendor_queue_counts={"GTK": 5, "JCET": 3},
            vendor_success_rates={"GTK": 0.95, "JCET": 0.88},
            code_comparison_active=2,
            code_comparison_pending=8,
            code_comparison_avg_duration=45.2
        )
        
        # 快取指標
        success = await cache_manager.cache_email_metrics(email_metrics)
        assert success
        
        # 獲取快取的指標
        cached_metrics = await cache_manager.get_email_metrics()
        assert cached_metrics is not None
        assert cached_metrics.pending_count == 10
        assert cached_metrics.vendor_queue_counts["GTK"] == 5
    
    @pytest.mark.asyncio
    async def test_business_data_caching(self, cache_manager):
        """測試業務資料快取"""
        # 測試廠商統計快取
        vendor_stats = {
            "GTK": {"mo_count": 15, "lot_count": 45, "success_rate": 95},
            "JCET": {"mo_count": 8, "lot_count": 24, "success_rate": 88}
        }
        
        success = await cache_manager.cache_vendor_stats(vendor_stats)
        assert success
        
        cached_stats = await cache_manager.get_vendor_stats()
        assert cached_stats is not None
        assert cached_stats["GTK"]["mo_count"] == 15
        
        # 測試 MO/LOT 資料快取
        today = datetime.now().strftime("%Y-%m-%d")
        mo_lot_data = {
            "mo_processed": 50,
            "lot_processed": 150,
            "data_quality_score": 95.5
        }
        
        success = await cache_manager.cache_mo_lot_data(today, mo_lot_data)
        assert success
        
        cached_data = await cache_manager.get_mo_lot_data(today)
        assert cached_data is not None
        assert cached_data["mo_processed"] == 50
    
    @pytest.mark.asyncio
    async def test_trend_data_caching(self, cache_manager):
        """測試趨勢資料快取"""
        trend_data = [
            {"timestamp": "2024-01-01T10:00:00", "value": 100},
            {"timestamp": "2024-01-01T11:00:00", "value": 120},
            {"timestamp": "2024-01-01T12:00:00", "value": 110}
        ]
        
        success = await cache_manager.cache_trend_data("email_queue", "1h", trend_data)
        assert success
        
        cached_trend = await cache_manager.get_trend_data("email_queue", "1h")
        assert cached_trend is not None
        assert len(cached_trend) == 3
        assert cached_trend[0]["value"] == 100
    
    @pytest.mark.asyncio
    async def test_alert_caching(self, cache_manager):
        """測試告警快取"""
        alerts = [
            {
                "id": "alert_1",
                "type": "queue_overflow",
                "level": "warning",
                "message": "郵件佇列過多"
            },
            {
                "id": "alert_2",
                "type": "resource_high",
                "level": "critical",
                "message": "CPU 使用率過高"
            }
        ]
        
        success = await cache_manager.cache_active_alerts(alerts)
        assert success
        
        cached_alerts = await cache_manager.get_active_alerts()
        assert cached_alerts is not None
        assert len(cached_alerts) == 2
        assert cached_alerts[0]["id"] == "alert_1"
    
    @pytest.mark.asyncio
    async def test_cache_invalidation(self, cache_manager):
        """測試快取失效"""
        # 設定一些測試快取
        await cache_manager.cache_email_metrics(EmailMetrics(
            pending_count=5, processing_count=2, completed_count=50, failed_count=1,
            avg_processing_time_seconds=25.0, throughput_per_hour=100.0,
            vendor_queue_counts={}, vendor_success_rates={},
            code_comparison_active=0, code_comparison_pending=0, code_comparison_avg_duration=0.0
        ))
        
        await cache_manager.cache_vendor_stats({"GTK": {"count": 10}})
        
        # 確認快取存在
        assert await cache_manager.get_email_metrics() is not None
        assert await cache_manager.get_vendor_stats() is not None
        
        # 失效指標快取
        invalidated_count = await cache_manager.invalidate_namespace(CacheNamespace.METRICS)
        assert invalidated_count > 0
        
        # 確認指標快取已失效
        assert await cache_manager.get_email_metrics() is None
        
        # 業務快取應該仍然存在
        assert await cache_manager.get_vendor_stats() is not None
    
    @pytest.mark.asyncio
    async def test_cache_with_callback(self, cache_manager):
        """測試回調快取"""
        call_count = 0
        
        async def expensive_operation():
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.1)  # 模擬耗時操作
            return {"result": f"data_{call_count}"}
        
        # 首次調用
        result1 = await cache_manager.cache_with_callback(
            "expensive_op", expensive_operation, ttl=300
        )
        assert result1["result"] == "data_1"
        assert call_count == 1
        
        # 第二次調用應該使用快取
        result2 = await cache_manager.cache_with_callback(
            "expensive_op", expensive_operation, ttl=300
        )
        assert result2["result"] == "data_1"  # 快取的結果
        assert call_count == 1  # 沒有再次調用
        
        # 強制重新整理
        result3 = await cache_manager.cache_with_callback(
            "expensive_op", expensive_operation, ttl=300, force_refresh=True
        )
        assert result3["result"] == "data_2"
        assert call_count == 2


class TestCacheUtilsIntegration:
    """快取工具整合測試"""
    
    @pytest.fixture
    async def setup_cache(self):
        """設定快取環境"""
        await initialize_cache_manager()
        yield
        await shutdown_cache_manager()
    
    @pytest.mark.asyncio
    async def test_cache_decorator(self, setup_cache):
        """測試快取裝飾器"""
        call_count = 0
        
        @cache_result(ttl=300, namespace="test")
        async def cached_function(param1: str, param2: int):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.01)
            return f"{param1}_{param2}_{call_count}"
        
        # 首次調用
        result1 = await cached_function("test", 123)
        assert result1 == "test_123_1"
        assert call_count == 1
        
        # 第二次調用相同參數，應該使用快取
        result2 = await cached_function("test", 123)
        assert result2 == "test_123_1"  # 快取的結果
        assert call_count == 1  # 沒有再次調用
        
        # 不同參數應該執行函數
        result3 = await cached_function("test", 456)
        assert result3 == "test_456_2"
        assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_get_or_set_cache(self, setup_cache):
        """測試獲取或設定快取"""
        async def data_provider():
            return {"timestamp": datetime.now().isoformat(), "data": "test_data"}
        
        # 首次獲取
        result1 = await get_or_set_cache("test_key", data_provider, ttl=300)
        assert result1["data"] == "test_data"
        
        # 第二次獲取應該是快取的結果
        result2 = await get_or_set_cache("test_key", data_provider, ttl=300)
        assert result2["timestamp"] == result1["timestamp"]  # 時間戳相同，說明是快取
    
    @pytest.mark.asyncio
    async def test_cache_context(self, setup_cache):
        """測試快取上下文管理器"""
        async with CacheContext("test_context") as ctx:
            # 在上下文中快取資料
            await ctx.cache("key1", "value1", ttl=300)
            await ctx.cache("key2", "value2", ttl=300)
            
            # 從上下文獲取資料
            value1 = await ctx.get("key1")
            value2 = await ctx.get("key2")
            
            assert value1 == "value1"
            assert value2 == "value2"
    
    @pytest.mark.asyncio
    async def test_cache_batch_operations(self, setup_cache):
        """測試批量快取操作"""
        batch = CacheBatch()
        
        # 添加批量操作
        batch.add_set("batch_key1", "batch_value1", ttl=300)
        batch.add_set("batch_key2", "batch_value2", ttl=300)
        batch.add_set("batch_key3", "batch_value3", ttl=300)
        
        # 執行批量操作
        results = await batch.execute()
        assert results["success"] == 3
        assert results["failed"] == 0
        
        # 驗證快取設定成功
        cache_manager = get_cache_manager()
        assert cache_manager.cache_service.get("batch_key1") == "batch_value1"
        assert cache_manager.cache_service.get("batch_key2") == "batch_value2"
        assert cache_manager.cache_service.get("batch_key3") == "batch_value3"
    
    @pytest.mark.asyncio
    async def test_cache_invalidation_utils(self, setup_cache):
        """測試快取失效工具"""
        cache_manager = get_cache_manager()
        
        # 設定一些測試快取
        cache_manager.cache_service.set("metrics:email:current", "email_data")
        cache_manager.cache_service.set("metrics:celery:current", "celery_data")
        cache_manager.cache_service.set("business:vendor:stats", "vendor_data")
        
        # 失效指標快取
        invalidated_count = await invalidate_metrics_cache()
        assert invalidated_count >= 2  # 至少失效了 email 和 celery 指標
        
        # 確認指標快取已失效
        assert cache_manager.cache_service.get("metrics:email:current") is None
        assert cache_manager.cache_service.get("metrics:celery:current") is None
        
        # 業務快取應該仍然存在
        assert cache_manager.cache_service.get("business:vendor:stats") == "vendor_data"


class TestCachePerformance:
    """快取效能測試"""
    
    @pytest.fixture
    async def performance_cache(self):
        """效能測試快取夾具"""
        config = CacheConfig(
            max_size=10000,
            default_ttl=3600,
            max_memory_mb=100
        )
        
        manager = DashboardCacheManager(config)
        await manager.start()
        
        yield manager
        
        await manager.stop()
    
    @pytest.mark.asyncio
    async def test_cache_performance(self, performance_cache):
        """測試快取效能"""
        import time
        
        # 測試大量寫入
        start_time = time.time()
        for i in range(1000):
            performance_cache.cache_service.set(f"perf_key_{i}", f"value_{i}")
        write_time = time.time() - start_time
        
        print(f"寫入 1000 個項目耗時: {write_time:.3f} 秒")
        assert write_time < 1.0  # 應該在 1 秒內完成
        
        # 測試大量讀取
        start_time = time.time()
        for i in range(1000):
            value = performance_cache.cache_service.get(f"perf_key_{i}")
            assert value == f"value_{i}"
        read_time = time.time() - start_time
        
        print(f"讀取 1000 個項目耗時: {read_time:.3f} 秒")
        assert read_time < 0.5  # 應該在 0.5 秒內完成
        
        # 檢查統計資料
        stats = performance_cache.get_cache_statistics()
        assert stats['statistics']['total_entries'] == 1000
        assert stats['statistics']['hit_rate'] == 100.0  # 100% 命中率
    
    @pytest.mark.asyncio
    async def test_memory_usage(self, performance_cache):
        """測試記憶體使用"""
        # 創建大型資料
        large_data = "x" * 10000  # 10KB 資料
        
        # 設定多個大型快取項目
        for i in range(100):
            performance_cache.cache_service.set(f"large_key_{i}", large_data)
        
        # 檢查記憶體使用
        stats = performance_cache.get_cache_statistics()
        memory_usage_mb = stats['statistics']['total_size_mb']
        
        print(f"記憶體使用: {memory_usage_mb:.2f} MB")
        assert memory_usage_mb > 0
        assert memory_usage_mb < 100  # 不應該超過限制
    
    @pytest.mark.asyncio
    async def test_concurrent_access(self, performance_cache):
        """測試並發存取"""
        async def concurrent_operation(worker_id: int):
            """並發操作"""
            for i in range(100):
                key = f"concurrent_{worker_id}_{i}"
                value = f"value_{worker_id}_{i}"
                
                # 設定快取
                performance_cache.cache_service.set(key, value)
                
                # 立即讀取
                cached_value = performance_cache.cache_service.get(key)
                assert cached_value == value
        
        # 啟動多個並發任務
        tasks = [concurrent_operation(i) for i in range(10)]
        await asyncio.gather(*tasks)
        
        # 檢查最終狀態
        stats = performance_cache.get_cache_statistics()
        assert stats['statistics']['total_entries'] == 1000  # 10 個工作者 × 100 個項目


if __name__ == "__main__":
    pytest.main([__file__, "-v"])