"""
企業級任務後處理管理器
負責郵件任務完成後的綜合處理：成功判斷、狀態標記、網路上傳、本地清理、通知發送
"""

import os
import sys
import shutil
import json
import uuid
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future
import subprocess
import requests
from contextlib import contextmanager

# 動態添加 src 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.shared.infrastructure.adapters.notification.line_notification_service import LineNotificationService
from backend.shared.infrastructure.adapters.network_file_upload_service import NetworkFileUploadService, UploadResult, UploadProgress


class PostProcessingStatus(Enum):
    """後處理狀態枚舉"""
    PENDING = "pending"              # 等待處理
    ANALYZING = "analyzing"          # 分析結果中
    SUCCESS_VERIFIED = "success_verified"  # 成功驗證
    UPLOADING = "uploading"          # 上傳中
    CLEANING = "cleaning"            # 清理中
    COMPLETED = "completed"          # 處理完成
    FAILED = "failed"                # 處理失敗
    SKIPPED = "skipped"              # 跳過處理


class ProcessingTrigger(Enum):
    """處理觸發方式"""
    MANUAL = "manual"                # 手動觸發
    AUTO_SUCCESS = "auto_success"    # 任務成功自動觸發
    AUTO_FAILED = "auto_failed"      # 任務失敗自動觸發
    SCHEDULED = "scheduled"          # 定時觸發


@dataclass
class PostProcessingTask:
    """後處理任務資料類"""
    task_id: str
    email_task_id: Optional[str]     # 對應的郵件任務ID
    pd: str                          # 產品代碼
    mo: str                          # 製造訂單號
    local_path: str                  # 本地處理路徑
    status: PostProcessingStatus
    trigger: ProcessingTrigger
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    success_verified: bool = False
    upload_completed: bool = False
    cleanup_completed: bool = False
    notification_sent: bool = False
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    result_details: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        data = asdict(self)
        data['status'] = self.status.value
        data['trigger'] = self.trigger.value
        data['created_at'] = self.created_at.isoformat() if self.created_at else None
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        return data




class PostProcessingManager:
    """
    企業級任務後處理管理器
    
    核心功能：
    - 任務成功/失敗判斷
    - 郵件任務狀態標記
    - 網路上傳協調
    - 本地資源清理
    - LINE通知發送
    - 錯誤處理和重試
    """
    
    def __init__(self, 
                 max_workers: int = 2,
                 cleanup_delay_hours: int = 24,
                 enable_auto_processing: bool = True,
                 logger: Optional = None):
        """
        初始化後處理管理器
        
        Args:
            max_workers: 最大並發處理數
            cleanup_delay_hours: 清理延遲時間(小時)
            enable_auto_processing: 是否啟用自動處理
            logger: 自定義日誌記錄器
        """
        self.logger = logger or LoggerManager().get_logger("PostProcessingManager")
        self.max_workers = max_workers
        self.cleanup_delay_hours = cleanup_delay_hours
        self.enable_auto_processing = enable_auto_processing
        
        # 基礎路徑配置
        self.temp_base_path = Path(os.getenv('FILE_TEMP_BASE_PATH', r'D:\temp'))
        
        # 任務存儲
        self._tasks: Dict[str, PostProcessingTask] = {}
        self._task_lock = threading.RLock()
        
        # 執行緒池
        self._executor = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="PostProcessor"
        )
        
        # 服務初始化
        self._init_services()
        
        # 啟動背景處理執行緒
        if enable_auto_processing:
            self._start_background_processor()
        
        self.logger.info(f"[OK] 後處理管理器已啟動 - 工作者: {max_workers}, 清理延遲: {cleanup_delay_hours}小時")
    
    def _init_services(self):
        """初始化相關服務"""
        # LINE通知服務
        try:
            self._notification_service = LineNotificationService()
        except Exception as e:
            self.logger.warning(f"無法初始化LINE通知服務: {e}")
            self._notification_service = None
        
        # 企業級網路上傳服務
        try:
            self._upload_service = NetworkFileUploadService(
                max_concurrent_uploads=2,
                max_retries=3,
                enable_md5_verification=True,
                logger=self.logger
            )
        except Exception as e:
            self.logger.warning(f"無法初始化網路上傳服務: {e}")
            self._upload_service = None
    
    def submit_post_processing_task(self, 
                                  pd: str,
                                  mo: str,
                                  email_task_id: Optional[str] = None,
                                  trigger: ProcessingTrigger = ProcessingTrigger.MANUAL) -> str:
        """
        提交後處理任務
        
        Args:
            pd: 產品代碼
            mo: 製造訂單號
            email_task_id: 對應的郵件任務ID
            trigger: 觸發方式
            
        Returns:
            str: 後處理任務ID
        """
        # 生成任務ID
        task_id = str(uuid.uuid4())
        local_path = str(self.temp_base_path / pd / mo.upper())
        
        # 創建任務資訊
        task = PostProcessingTask(
            task_id=task_id,
            email_task_id=email_task_id,
            pd=pd,
            mo=mo,
            local_path=local_path,
            status=PostProcessingStatus.PENDING,
            trigger=trigger,
            created_at=datetime.now(),
            result_details={}
        )
        
        # 存儲任務
        with self._task_lock:
            self._tasks[task_id] = task
        
        # 提交到執行緒池處理
        self._executor.submit(self._process_task, task_id)
        
        self.logger.info(f"[SUBMIT] 已提交後處理任務: {task_id} (PD: {pd}, MO: {mo})")
        return task_id
    
    def _process_task(self, task_id: str):
        """
        處理單個後處理任務
        
        Args:
            task_id: 任務ID
        """
        task = self._tasks.get(task_id)
        if not task:
            self.logger.error(f"[ERROR] 任務不存在: {task_id}")
            return
        
        try:
            task.status = PostProcessingStatus.ANALYZING
            task.started_at = datetime.now()
            
            self.logger.info(f"[START] 開始後處理任務: {task_id}")
            
            # 步驟1: 驗證任務成功
            success, details = self._verify_task_success(task)
            task.success_verified = success
            task.result_details.update(details)
            
            if not success:
                task.status = PostProcessingStatus.FAILED
                task.error_message = details.get('message', '任務驗證失敗')
                self._send_failure_notification(task)
                return
            
            task.status = PostProcessingStatus.SUCCESS_VERIFIED
            self.logger.info(f"[OK] 任務成功驗證: {task_id}")
            
            # 步驟2: 網路上傳
            task.status = PostProcessingStatus.UPLOADING
            upload_success = self._handle_network_upload(task)
            task.upload_completed = upload_success
            
            # 檢查上傳是否需要失敗處理
            upload_enabled = self._upload_service and os.getenv('NETWORK_UPLOAD_ENABLED', 'true').lower() == 'true'
            if not upload_success and upload_enabled:
                # 如果上傳啟用但失敗，則標記為失敗
                task.status = PostProcessingStatus.FAILED
                task.error_message = "網路上傳失敗"
                self._send_failure_notification(task)
                return
            
            # 步驟3: 發送成功通知
            self._send_success_notification(task)
            task.notification_sent = True
            
            # 步驟4: 安排本地清理 (延遲執行)
            self._schedule_cleanup(task)
            
            # 標記完成
            task.status = PostProcessingStatus.COMPLETED
            task.completed_at = datetime.now()
            
            self.logger.info(f"[OK] 後處理任務完成: {task_id}")
            
        except Exception as e:
            task.status = PostProcessingStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            
            self.logger.error(f"[ERROR] 後處理任務失敗: {task_id} - {e}")
            
            # 重試邏輯
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                self.logger.info(f"[RETRY] 安排重試: {task_id} (第{task.retry_count}次)")
                # 延遲重試
                time.sleep(30 * task.retry_count)  # 指數退避
                self._executor.submit(self._process_task, task_id)
            else:
                self._send_failure_notification(task)
    
    def _verify_task_success(self, task: PostProcessingTask) -> Tuple[bool, Dict[str, Any]]:
        """
        檢查任務是否執行成功
        
        成功標準：存在檔案 FT_SUMMARY.xlsx
        
        Args:
            task: 後處理任務
            
        Returns:
            Tuple[bool, Dict]: (是否成功, 詳細資訊)
        """
        local_path = Path(task.local_path)
        ft_summary_path = local_path / "FT_SUMMARY.xlsx"
        
        if not local_path.exists():
            return False, {
                "message": f"本地處理路徑不存在: {local_path}",
                "path_not_exists": True
            }
        
        if not ft_summary_path.exists():
            # 檢查目錄內容以提供更多資訊
            csv_files = list(local_path.glob("*.csv"))
            xlsx_files = list(local_path.glob("*.xlsx"))
            
            return False, {
                "message": f"缺少成功標記檔案: FT_SUMMARY.xlsx",
                "missing_ft_summary": True,
                "csv_files_count": len(csv_files),
                "xlsx_files_count": len(xlsx_files),
                "directory_contents": [f.name for f in local_path.iterdir() if f.is_file()][:10]  # 最多顯示10個檔案
            }
        
        # 檢查 FT_SUMMARY.xlsx 檔案大小（確保不是空檔案）
        file_size = ft_summary_path.stat().st_size
        if file_size < 1024:  # 小於1KB可能是有問題的檔案
            return False, {
                "message": f"FT_SUMMARY.xlsx 檔案過小 ({file_size} bytes)，可能處理不完整",
                "file_size_too_small": True
            }
        
        # 檢查檔案的修改時間（確保是最近生成的）
        file_mtime = datetime.fromtimestamp(ft_summary_path.stat().st_mtime)
        time_diff = datetime.now() - file_mtime
        
        if time_diff.total_seconds() > 3600:  # 超過1小時
            self.logger.warning(f"[WARNING] FT_SUMMARY.xlsx 檔案較舊 ({time_diff})")
        
        return True, {
            "message": "任務執行成功",
            "ft_summary_exists": True,
            "file_size_kb": round(file_size / 1024, 2),
            "file_modified": file_mtime.isoformat(),
            "verification_time": datetime.now().isoformat()
        }
    
    def _handle_network_upload(self, task: PostProcessingTask) -> bool:
        """
        處理網路上傳
        
        Args:
            task: 後處理任務
            
        Returns:
            bool: 是否成功
        """
        if not self._upload_service:
            self.logger.info(f"[SKIP] 網路上傳服務未啟用: {task.task_id}")
            return True
        
        try:
            local_path = Path(task.local_path)
            
            # 定義進度回調
            def progress_callback(progress: UploadProgress):
                self.logger.info(f"[UPLOAD] {task.task_id} - {progress.status.value} - "
                               f"{progress.progress_percentage:.1f}% - "
                               f"{progress.files_completed}/{progress.files_total} 檔案")
            
            # 執行上傳
            upload_result: UploadResult = self._upload_service.upload_directory(
                source_path=local_path,
                pd=task.pd,
                mo=task.mo,
                progress_callback=progress_callback
            )
            
            # 保存上傳結果詳細資訊
            task.result_details['upload'] = upload_result.to_dict()
            
            if upload_result.success:
                self.logger.info(f"[OK] 網路上傳成功: {task.task_id} - "
                               f"{upload_result.files_uploaded}個檔案, "
                               f"{upload_result.total_size_mb:.2f}MB, "
                               f"平均速度: {upload_result.average_speed_mbps:.2f}MB/s")
                
                # 如果啟用了MD5驗證，記錄驗證結果
                if upload_result.md5_verification_results:
                    failed_files = [f for f, passed in upload_result.md5_verification_results.items() if not passed]
                    if failed_files:
                        self.logger.warning(f"[WARNING] MD5驗證失敗的檔案: {failed_files}")
                    else:
                        self.logger.info(f"[OK] 所有檔案MD5驗證通過: {task.task_id}")
            else:
                self.logger.error(f"[ERROR] 網路上傳失敗: {task.task_id} - {upload_result.error_message}")
            
            return upload_result.success
            
        except Exception as e:
            self.logger.error(f"[ERROR] 網路上傳異常: {task.task_id} - {e}")
            task.result_details['upload'] = {"error": str(e)}
            return False
    
    def _schedule_cleanup(self, task: PostProcessingTask):
        """
        安排本地清理（延遲執行）
        
        Args:
            task: 後處理任務
        """
        def cleanup():
            time.sleep(self.cleanup_delay_hours * 3600)  # 延遲指定小時數
            self._perform_cleanup(task)
        
        cleanup_thread = threading.Thread(target=cleanup, daemon=True)
        cleanup_thread.start()
        
        self.logger.info(f"[SCHEDULE] 已安排 {self.cleanup_delay_hours} 小時後清理: {task.task_id}")
    
    def _perform_cleanup(self, task: PostProcessingTask):
        """
        執行本地清理
        
        Args:
            task: 後處理任務
        """
        try:
            task.status = PostProcessingStatus.CLEANING
            local_path = Path(task.local_path)
            
            if not local_path.exists():
                self.logger.info(f"[SKIP] 清理路徑不存在: {local_path}")
                task.cleanup_completed = True
                return
            
            # 檢查是否有其他正在處理的任務使用相同路徑
            active_tasks_on_path = []
            with self._task_lock:
                for other_task in self._tasks.values():
                    if (other_task.task_id != task.task_id and 
                        other_task.local_path == task.local_path and
                        other_task.status in [PostProcessingStatus.PENDING, 
                                            PostProcessingStatus.ANALYZING,
                                            PostProcessingStatus.UPLOADING]):
                        active_tasks_on_path.append(other_task.task_id)
            
            if active_tasks_on_path:
                self.logger.info(f"[SKIP] 有其他任務正在使用路徑，延遲清理: {active_tasks_on_path}")
                # 延遲1小時後重試
                threading.Timer(3600, lambda: self._perform_cleanup(task)).start()
                return
            
            # 執行清理
            total_size = sum(f.stat().st_size for f in local_path.rglob('*') if f.is_file())
            shutil.rmtree(local_path, ignore_errors=True)
            
            task.cleanup_completed = True
            task.result_details['cleanup'] = {
                "cleaned_path": str(local_path),
                "cleaned_size_mb": round(total_size / 1024 / 1024, 2),
                "cleanup_time": datetime.now().isoformat()
            }
            
            self.logger.info(f"[OK] 本地清理完成: {task.task_id} ({round(total_size / 1024 / 1024, 2)} MB)")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 本地清理失敗: {task.task_id} - {e}")
            task.result_details['cleanup'] = {"error": str(e)}
    
    def _send_success_notification(self, task: PostProcessingTask):
        """發送成功通知"""
        if not self._notification_service:
            return
        
        try:
            message = f"""✅ 郵件任務處理完成

🔸 產品代碼: {task.pd}
🔸 製造訂單: {task.mo}
🔸 處理時間: {(task.completed_at or datetime.now() - task.started_at).total_seconds():.1f}秒
🔸 本地路徑: {task.local_path}

✅ 成功驗證: 已完成
{'✅ 網路上傳: 已完成' if task.upload_completed else '⚠️ 網路上傳: 已跳過'}
🕒 自動清理: {self.cleanup_delay_hours}小時後執行"""
            
            self._notification_service._send_message(message)
            self.logger.info(f"[OK] 成功通知已發送: {task.task_id}")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 發送成功通知失敗: {task.task_id} - {e}")
    
    def _send_failure_notification(self, task: PostProcessingTask):
        """發送失敗通知"""
        if not self._notification_service:
            return
        
        try:
            message = f"""❌ 郵件任務處理失敗

🔸 產品代碼: {task.pd}
🔸 製造訂單: {task.mo}
🔸 錯誤訊息: {task.error_message or '未知錯誤'}
🔸 重試次數: {task.retry_count}/{task.max_retries}
🔸 本地路徑: {task.local_path}

請檢查處理狀況並手動處理。"""
            
            self._notification_service._send_message(message)
            self.logger.info(f"[OK] 失敗通知已發送: {task.task_id}")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 發送失敗通知失敗: {task.task_id} - {e}")
    
    def _start_background_processor(self):
        """啟動背景處理執行緒"""
        def background_processor():
            while True:
                try:
                    time.sleep(60)  # 每分鐘檢查一次
                    self._cleanup_old_tasks()
                except Exception as e:
                    self.logger.error(f"[ERROR] 背景處理器錯誤: {e}")
        
        processor_thread = threading.Thread(target=background_processor, daemon=True)
        processor_thread.start()
        self.logger.info("[OK] 背景處理執行緒已啟動")
    
    def _cleanup_old_tasks(self):
        """清理舊任務記錄"""
        cutoff_time = datetime.now() - timedelta(days=7)  # 保留7天
        tasks_to_remove = []
        
        with self._task_lock:
            for task_id, task in self._tasks.items():
                if (task.status in [PostProcessingStatus.COMPLETED, PostProcessingStatus.FAILED] and
                    task.completed_at and task.completed_at < cutoff_time):
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self._tasks[task_id]
        
        if tasks_to_remove:
            self.logger.info(f"[CLEANUP] 清理了 {len(tasks_to_remove)} 個過期任務記錄")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        獲取任務狀態
        
        Args:
            task_id: 任務ID
            
        Returns:
            Dict: 任務狀態資訊
        """
        with self._task_lock:
            task = self._tasks.get(task_id)
            return task.to_dict() if task else None
    
    def list_active_tasks(self) -> List[Dict[str, Any]]:
        """列出活躍任務"""
        active_tasks = []
        with self._task_lock:
            for task in self._tasks.values():
                if task.status not in [PostProcessingStatus.COMPLETED, PostProcessingStatus.FAILED]:
                    active_tasks.append(task.to_dict())
        
        return sorted(active_tasks, key=lambda x: x['created_at'], reverse=True)
    
    def force_cleanup_path(self, pd: str, mo: str) -> bool:
        """
        強制清理指定路徑
        
        Args:
            pd: 產品代碼
            mo: 製造訂單號
            
        Returns:
            bool: 是否成功
        """
        try:
            local_path = self.temp_base_path / pd / mo.upper()
            if local_path.exists():
                shutil.rmtree(local_path, ignore_errors=True)
                self.logger.info(f"[OK] 強制清理完成: {local_path}")
                return True
            else:
                self.logger.info(f"[SKIP] 路徑不存在: {local_path}")
                return True
        except Exception as e:
            self.logger.error(f"[ERROR] 強制清理失敗: {e}")
            return False
    
    def shutdown(self, wait: bool = True):
        """
        關閉後處理管理器
        
        Args:
            wait: 是否等待所有任務完成
        """
        self.logger.info("[SHUTDOWN] 正在關閉後處理管理器...")
        
        # 關閉執行緒池
        self._executor.shutdown(wait=wait)
        
        self.logger.info("[OK] 後處理管理器已關閉")


# 全域實例（單例模式）
_post_processor_instance = None
_post_processor_lock = threading.Lock()

def get_post_processing_manager(**kwargs) -> PostProcessingManager:
    """
    獲取後處理管理器單例實例
    
    Returns:
        PostProcessingManager: 後處理管理器實例
    """
    global _post_processor_instance
    
    if _post_processor_instance is None:
        with _post_processor_lock:
            if _post_processor_instance is None:
                _post_processor_instance = PostProcessingManager(**kwargs)
    
    return _post_processor_instance


# 整合到現有 code_comparison.py 的輔助函式
def trigger_post_processing_for_task(pd: str, mo: str, email_task_id: Optional[str] = None) -> Optional[str]:
    """
    為完成的任務觸發後處理
    
    Args:
        pd: 產品代碼
        mo: 製造訂單號
        email_task_id: 郵件任務ID
        
    Returns:
        Optional[str]: 後處理任務ID，如果失敗則返回None
    """
    try:
        post_processor = get_post_processing_manager()
        task_id = post_processor.submit_post_processing_task(
            pd=pd,
            mo=mo,
            email_task_id=email_task_id,
            trigger=ProcessingTrigger.AUTO_SUCCESS
        )
        return task_id
    except Exception as e:
        logger = LoggerManager().get_logger("PostProcessingTrigger")
        logger.error(f"[ERROR] 觸發後處理失敗: {e}")
        return None