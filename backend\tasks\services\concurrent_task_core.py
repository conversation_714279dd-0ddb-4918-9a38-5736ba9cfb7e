"""
企業級並發任務管理器 - 核心模組
提供並發任務執行的基礎功能
"""

import os
import sys
import json
import uuid
import time
import logging
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
import threading
from contextlib import contextmanager

# 動態添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
src_path = os.path.join(project_root, 'src')
backend_path = os.path.join(project_root, 'backend')
if src_path not in sys.path:
    sys.path.insert(0, src_path)
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

try:
    from backend.shared.infrastructure.logging.logger_manager import LoggerManager
    from backend.shared.infrastructure.adapters.notification.line_notification_service import LineNotificationService
    from backend.shared.infrastructure.adapters.database.models import DatabaseEngine
except ImportError:
    # 如果无法导入，使用默认值
    DatabaseEngine = None


class TaskStatus(Enum):
    """任務狀態枚舉"""
    PENDING = "pending"         # 等待執行
    RUNNING = "running"         # 執行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 執行失敗
    CANCELLED = "cancelled"     # 已取消
    TIMEOUT = "timeout"         # 執行超時


class TaskPriority(Enum):
    """任務優先級枚舉"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class TaskInfo:
    """任務資訊"""
    task_id: str
    task_type: str
    status: TaskStatus
    priority: TaskPriority
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    input_params: Optional[Dict[str, Any]] = None
    result: Optional[Any] = None
    error_message: Optional[str] = None
    progress: float = 0.0
    estimated_duration: Optional[float] = None  # 秒
    actual_duration: Optional[float] = None     # 秒
    retry_count: int = 0
    max_retries: int = 3


class TaskExecutor:
    """任務執行器 - 核心執行邏輯"""
    
    def __init__(self, max_workers: int = 4, logger=None):
        self.max_workers = max_workers
        self.logger = logger or LoggerManager().get_logger("TaskExecutor")
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._shutdown = False
    
    def submit_task(self, task_func: Callable, *args, **kwargs) -> Future:
        """提交任務到執行器"""
        if self._shutdown:
            raise RuntimeError("Task executor is shutdown")
        return self._executor.submit(task_func, *args, **kwargs)
    
    def shutdown(self, wait: bool = True):
        """關閉執行器"""
        self._shutdown = True
        self._executor.shutdown(wait=wait)


class TaskRegistry:
    """任務註冊器"""
    
    def __init__(self):
        self._handlers: Dict[str, Callable] = {}
        self._lock = threading.RLock()
    
    def register_handler(self, task_type: str, handler: Callable):
        """註冊任務處理器"""
        with self._lock:
            self._handlers[task_type] = handler
    
    def get_handler(self, task_type: str) -> Optional[Callable]:
        """獲取任務處理器"""
        with self._lock:
            return self._handlers.get(task_type)
    
    def list_handlers(self) -> List[str]:
        """列出所有已註冊的處理器"""
        with self._lock:
            return list(self._handlers.keys())


class TaskStatusTracker:
    """任務狀態追蹤器"""
    
    def __init__(self):
        self._tasks: Dict[str, TaskInfo] = {}
        self._lock = threading.RLock()
    
    def add_task(self, task_info: TaskInfo):
        """添加任務"""
        with self._lock:
            self._tasks[task_info.task_id] = task_info
    
    def update_task(self, task_id: str, **updates):
        """更新任務狀態"""
        with self._lock:
            if task_id in self._tasks:
                task = self._tasks[task_id]
                for key, value in updates.items():
                    if hasattr(task, key):
                        setattr(task, key, value)
    
    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """獲取任務資訊"""
        with self._lock:
            return self._tasks.get(task_id)
    
    def list_tasks(self, status: Optional[TaskStatus] = None) -> List[TaskInfo]:
        """列出任務"""
        with self._lock:
            tasks = list(self._tasks.values())
            if status:
                tasks = [t for t in tasks if t.status == status]
            return sorted(tasks, key=lambda x: x.created_at, reverse=True)
    
    def remove_task(self, task_id: str):
        """移除任務"""
        with self._lock:
            self._tasks.pop(task_id, None)


def execute_code_comparison_task(input_path: str, **kwargs) -> Dict[str, Any]:
    """執行程式碼對比任務"""
    try:
        # 構建命令
        cmd = [sys.executable, "-m", "src.core.code_comparison", input_path]
        
        if kwargs.get('code_region'):
            cmd.extend(['--code-region', kwargs['code_region']])
        if kwargs.get('with_excel'):
            cmd.append('--with-excel')
        if kwargs.get('verbose'):
            cmd.append('--verbose')
        
        # 執行命令
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=3600,
            cwd=Path(__file__).parent.parent.parent
        )
        
        if result.returncode == 0:
            return {
                'status': 'success',
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode
            }
        else:
            return {
                'status': 'failed',
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode
            }
    except subprocess.TimeoutExpired:
        return {
            'status': 'timeout',
            'error': '任務執行超時（>1小時）'
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e)
        }


def execute_email_processing_task(email_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
    """執行郵件處理任務"""
    try:
        # 這裡實現郵件處理邏輯
        # 暫時返回模擬結果
        time.sleep(2)  # 模擬處理時間
        
        return {
            'status': 'success',
            'processed_emails': 1,
            'email_id': email_data.get('id'),
            'processing_time': 2.0
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e)
        }


# 預定義任務處理器
DEFAULT_HANDLERS = {
    'code_comparison': execute_code_comparison_task,
    'email_processing': execute_email_processing_task,
}