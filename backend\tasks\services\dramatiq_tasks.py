"""
Dramatiq 任務定義 - 生產級異步任務實現

🎯 任務類型：
  📊 EQC 工作流程任務 - 核心業務邏輯
  🔍 產品搜索任務 - 異步搜索功能
  📄 文件處理任務 - CSV 摘要和代碼比較
  ❤️ 健康檢查任務 - 系統監控

🚀 特性：
  - 原生異步支援，無需適配層
  - 自動重試和錯誤處理
  - 結果追蹤和狀態監控
  - 多隊列優先級管理
"""

import asyncio
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from pathlib import Path

import dramatiq
from dramatiq import actor
from loguru import logger

# 導入配置
from dramatiq_config import get_queue_config

# 導入現有服務 (修正路徑)
# EQC 處理服務將在任務內部動態導入以避免循環依賴


# ============================================================================
# 🎯 EQC 工作流程任務
# ============================================================================

@actor(
    queue_name="eqc_queue",
    max_retries=3,
    time_limit=1800000,
    store_results=True,  # 🔧 修復：啟用結果存儲
    retry_when=lambda retries_so_far, exception: retries_so_far < 3 and not isinstance(exception, (ValueError, FileNotFoundError))
)
async def process_complete_eqc_workflow_task(
    folder_path: str,
    user_session_id: str = None,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    處理完整的 EQC 工作流程 - Dramatiq 版本
    100% 重用現有 EQCProcessingService 的所有方法

    Args:
        folder_path: EQC 資料夾路徑
        user_session_id: 用戶會話ID，用於多人隔離
        options: 處理選項，包含 CODE 區間設定等

    Returns:
        Dict[str, Any]: 完整的 EQC 處理結果
    """
    import time
    start_time = time.time()

    # 🔧 獲取當前任務ID（使用 Dramatiq 的內建方法）
    task_id = None
    try:
        # 從當前消息獲取任務ID
        import dramatiq
        current_message = dramatiq.get_current_message()
        if current_message:
            task_id = current_message.message_id
            logger.debug(f"🎯 獲取到任務ID: {task_id}")
    except Exception as e:
        logger.debug(f"⚠️ 無法獲取任務ID: {e}")
        # 🔧 生成一個臨時任務ID
        import uuid
        task_id = str(uuid.uuid4())
        logger.debug(f"🎯 生成臨時任務ID: {task_id}")

    # 🔧 初始化資料庫狀態追蹤
    from backend.shared.infrastructure.database.task_status_db import get_task_status_db
    task_db = get_task_status_db()

    if task_id:
        task_db.start_task(task_id, user_session_id, folder_path)

    logger.info(f"[DRAMATIQ] 開始 EQC 工作流程: folder={folder_path}, session={user_session_id}, task_id={task_id}")

    try:
        # 導入服務和管理器
        from backend.eqc.services.eqc_processing_service import EQCProcessingService
        from backend.eqc.models.request_models import OnlineEQCProcessRequest
        from backend.eqc.services.eqc_session_manager import get_eqc_session_manager

        # 初始化服務和管理器
        eqc_service = EQCProcessingService()
        session_manager = get_eqc_session_manager()
        options = options or {}

        # Step 1: process_online_eqc (mode='1')
        logger.info(f"[DRAMATIQ] Step 1/4: 執行繁中資料夾處理 - EQCBin1FinalProcessor")
        session_manager.update_session_progress(user_session_id, 10, "Step 1/4: 執行資料夾處理...")
        # 🔧 資料庫狀態更新
        if task_id:
            task_db.update_step_status(task_id, user_session_id, "Step 1: 繁中資料夾處理", 1, 10, "processing", "開始執行繁中資料夾處理")

        step1_request = OnlineEQCProcessRequest(
            folder_path=folder_path,
            processing_mode='1'  # EQC BIN1統計
        )
        step1_result = await eqc_service.process_online_eqc(step1_request)
        session_manager.update_session_progress(user_session_id, 25, "Step 1 完成")
        # 🔧 資料庫狀態更新
        if task_id:
            task_db.update_step_status(task_id, user_session_id, "Step 1: 繁中資料夾處理", 1, 25, "completed", "繁中資料夾處理完成")
        logger.info(f"[DRAMATIQ] Step 1 完成")

        # Step 2: process_eqc_advanced
        logger.info(f"[DRAMATIQ] Step 2/4: 程式碼區間檢測與雙重搜尋 - StandardEQCProcessor")
        session_manager.update_session_progress(user_session_id, 35, "Step 2/4: 執行程式碼區間檢測與雙重搜尋...")
        # 🔧 資料庫狀態更新
        if task_id:
            task_db.update_step_status(task_id, user_session_id, "Step 2: 程式碼區間檢測", 2, 35, "processing", "開始執行程式碼區間檢測與雙重搜尋")

        step2_request = {
            'folder_path': folder_path,
            'include_step123': False,  # 跳過步驟1-3，直接從步驟2開始
            **options  # 包含 CODE 區間設定等
        }
        step2_result = await eqc_service.process_eqc_advanced(step2_request)
        session_manager.update_session_progress(user_session_id, 60, "Step 2 完成")
        # 🔧 資料庫狀態更新
        if task_id:
            task_db.update_step_status(task_id, user_session_id, "Step 2: 程式碼區間檢測", 2, 60, "completed", "程式碼區間檢測與雙重搜尋完成")
        logger.info(f"[DRAMATIQ] Step 2 完成")

        # Step 3: analyze_eqc_real_data
        logger.info(f"[DRAMATIQ] Step 3/4: 分析真實 EQCTOTALDATA.xlsx 資料")
        session_manager.update_session_progress(user_session_id, 75, "Step 3/4: 分析處理結果...")
        # 🔧 資料庫狀態更新
        if task_id:
            task_db.update_step_status(task_id, user_session_id, "Step 3: 分析真實資料", 3, 75, "processing", "開始分析真實 EQCTOTALDATA.xlsx 資料")

        step3_result = await eqc_service.analyze_real_data({"folder_path": folder_path})
        session_manager.update_session_progress(user_session_id, 90, "Step 3 完成")
        # 🔧 資料庫狀態更新
        if task_id:
            task_db.update_step_status(task_id, user_session_id, "Step 3: 分析真實資料", 3, 90, "completed", "真實資料分析完成")
        logger.info(f"[DRAMATIQ] Step 3 完成")

        # Step 4: 結果整理
        logger.info(f"[DRAMATIQ] Step 4/4: 整理結果完成")
        session_manager.update_session_progress(user_session_id, 95, "Step 4/4: 整理最終報告...")
        # 🔧 資料庫狀態更新
        if task_id:
            task_db.update_step_status(task_id, user_session_id, "Step 4: 結果整理", 4, 95, "processing", "開始整理最終報告")

        # 整合所有結果
        processing_time = time.time() - start_time
        final_result = {
            'status': 'completed',
            'folder_path': folder_path,
            'user_session_id': user_session_id,
            'step1_result': step1_result.dict() if hasattr(step1_result, 'dict') else step1_result,
            'step2_result': step2_result,
            'step3_result': step3_result,
            'processing_time': processing_time,
            'completed_at': time.time(),
            'steps_completed': 4,
            'success': True
        }

        # 🔧 資料庫狀態更新：任務完成
        if task_id:
            # 🔧 查找 EQCTOTALDATA.xlsx 檔案路徑
            eqc_file_path = None
            try:
                from pathlib import Path
                folder_path_obj = Path(folder_path)
                if folder_path_obj.exists():
                    # 查找 EQCTOTALDATA.xlsx 檔案
                    eqc_files = list(folder_path_obj.glob("**/EQCTOTALDATA.xlsx"))
                    if eqc_files:
                        eqc_file_path = str(eqc_files[0])
                        logger.info(f"📄 找到 EQC 檔案: {eqc_file_path}")
                    else:
                        # 查找其他可能的 EQC 檔案
                        eqc_files = list(folder_path_obj.glob("**/*EQC*.xlsx"))
                        if eqc_files:
                            eqc_file_path = str(eqc_files[0])
                            logger.info(f"📄 找到 EQC 相關檔案: {eqc_file_path}")
            except Exception as e:
                logger.debug(f"⚠️ 查找 EQC 檔案失敗: {e}")

            task_db.complete_task(task_id, user_session_id, final_result, processing_time, eqc_file_path)
            task_db.update_step_status(task_id, user_session_id, "Step 4: 結果整理", 4, 100, "completed", "EQC 工作流程執行完成", final_result)

        # 🔧 任務完成後的清理工作
        await _cleanup_task_completion(user_session_id, folder_path, final_result, task_id)

        logger.info(f"[DRAMATIQ] EQC 工作流程完成，耗時: {processing_time:.2f}秒")
        return final_result

    except FileNotFoundError as e:
        error_msg = f"EQC 工作流程任務失敗 - 文件未找到: {str(e)}"
        logger.error(f"[DRAMATIQ] {error_msg}")

        # 🔧 資料庫狀態更新：任務失敗
        if task_id:
            task_db.fail_task(task_id, user_session_id, error_msg)

        # 🔧 任務失敗後的清理工作
        await _cleanup_task_failure(user_session_id, folder_path, error_msg)

        # 文件未找到錯誤不重試
        return {
            'status': 'failed',
            'folder_path': folder_path,
            'user_session_id': user_session_id,
            'error': error_msg,
            'error_type': 'FileNotFoundError',
            'processing_time': time.time() - start_time,
            'failed_at': time.time(),
            'success': False,
            'retryable': False
        }
    except ValueError as e:
        error_msg = f"EQC 工作流程任務失敗 - 參數錯誤: {str(e)}"
        logger.error(f"[DRAMATIQ] {error_msg}")

        # 🔧 任務失敗後的清理工作
        await _cleanup_task_failure(user_session_id, folder_path, error_msg)

        # 參數錯誤不重試
        return {
            'status': 'failed',
            'folder_path': folder_path,
            'user_session_id': user_session_id,
            'error': error_msg,
            'error_type': 'ValueError',
            'processing_time': time.time() - start_time,
            'failed_at': time.time(),
            'success': False,
            'retryable': False
        }
    except Exception as e:
        error_msg = f"EQC 工作流程任務執行失敗: {str(e)}"
        logger.error(f"[DRAMATIQ] {error_msg}\n{traceback.format_exc()}")

        # 🔧 任務失敗後的清理工作
        await _cleanup_task_failure(user_session_id, folder_path, error_msg)

        # 其他錯誤可重試
        return {
            'status': 'failed',
            'folder_path': folder_path,
            'user_session_id': user_session_id,
            'error': error_msg,
            'error_type': type(e).__name__,
            'processing_time': time.time() - start_time,
            'failed_at': time.time(),
            'success': False,
            'retryable': True
        }


# ============================================================================
# 🔍 產品搜索任務
# ============================================================================

@actor(
    queue_name="search_queue",
    max_retries=3,
    time_limit=300000,
    retry_when=lambda retries_so_far, exception: retries_so_far < 3 and not isinstance(exception, ValueError)
)
async def search_product_task(
    product_name: str,
    search_paths: Optional[List[str]] = None,
    max_results: int = 1000,
    search_filters: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    背景執行產品搜尋的任務 - Dramatiq 版本

    Args:
        product_name: 產品名稱
        search_paths: 搜尋路徑列表
        max_results: 最大結果數量
        search_filters: 搜尋篩選條件字典

    Returns:
        Dict: 搜尋結果字典
    """
    import time
    start_time = time.time()
    logger.info(f"[DRAMATIQ] 開始產品搜尋: '{product_name}'")

    try:
        # 導入產品搜尋服務
        from backend.shared.infrastructure.adapters.product_search_service import ProductSearchService
        from backend.shared.domain.entities.file_search import SearchFilters, TimeRange
        from backend.shared.models.search_models import TimeRangeType
        from pathlib import Path

        # 初始化搜尋服務
        search_service = ProductSearchService()

        # 處理搜尋路徑
        if not search_paths:
            search_paths = [r"\\192.168.1.60\test_log"]

        # 建立時間範圍
        time_range = TimeRange.from_type(TimeRangeType.LAST_6_MONTHS)

        # 建立搜尋篩選器
        filters = None
        if search_filters:
            filters = SearchFilters(
                time_range=time_range,
                file_types=search_filters.get('file_types'),
                size_range=search_filters.get('size_range'),
                include_patterns=search_filters.get('include_patterns'),
                exclude_patterns=search_filters.get('exclude_patterns')
            )

        # 執行搜尋
        all_results = []
        for search_path in search_paths:
            base_path = Path(search_path)
            if base_path.exists():
                result = await search_service.search_product_folder(
                    product_name=product_name,
                    base_path=base_path,
                    time_range=time_range,
                    filters=filters
                )

                # 將結果轉換為字典格式
                if hasattr(result, 'dict'):
                    result_dict = result.dict()
                else:
                    # 手動序列化
                    result_dict = {
                        'status': result.status.value if hasattr(result.status, 'value') else str(result.status),
                        'product_name': result.product_name,
                        'product_folder': str(result.product_folder),
                        'matched_files': [
                            {
                                'name': file_info.name,
                                'path': str(file_info.path),
                                'size': file_info.size,
                                'modified_time': file_info.modified_time.isoformat() if file_info.modified_time else None,
                                'is_directory': file_info.is_directory
                            }
                            for file_info in result.matched_files[:max_results]
                        ],
                        'total_found': len(result.matched_files),
                        'search_duration': time.time() - start_time
                    }

                all_results.append(result_dict)

        execution_time = time.time() - start_time
        total_files = sum(len(r.get('matched_files', [])) for r in all_results)

        logger.info(f"[DRAMATIQ] 產品搜尋完成: 找到 {total_files} 個檔案, 耗時={execution_time:.2f}秒")

        return {
            'status': 'completed',
            'product_name': product_name,
            'search_paths': search_paths,
            'results': all_results,
            'total_found': total_files,
            'search_duration': execution_time,
            'timestamp': datetime.now().isoformat()
        }

    except Exception as e:
        error_msg = f"產品搜尋失敗: {str(e)}"
        logger.error(f"[DRAMATIQ] {error_msg}\n{traceback.format_exc()}")

        return {
            'status': 'failed',
            'product_name': product_name,
            'error': error_msg,
            'search_duration': time.time() - start_time,
            'timestamp': datetime.now().isoformat()
        }


# ============================================================================
# 📄 文件處理任務
# ============================================================================

@actor(queue_name="processing_queue", max_retries=2, time_limit=600000)
async def run_csv_summary_task(
    input_path: str
) -> Dict[str, Any]:
    """
    背景執行 CSV 摘要生成的任務 - Dramatiq 版本
    """
    import time
    start_time = time.time()
    logger.info(f"[DRAMATIQ] 開始 CSV 摘要任務: {input_path}")

    try:
        # 導入檔案處理服務
        from backend.shared.application.services.processing import get_file_processing_service
        file_processing_service = get_file_processing_service()

        # 導入處理工具枚舉
        from backend.shared.application.services.processing.models import ProcessingTool

        # 創建 CSV 摘要任務
        task_id = file_processing_service.create_task(
            tool=ProcessingTool.CSV_SUMMARY,
            input_path=input_path,
            use_staging=False  # 直接處理，不使用暫存
        )

        logger.info(f"[DRAMATIQ] 創建 CSV 摘要任務: {task_id}")

        # 執行 CSV 摘要處理
        result = await file_processing_service.execute_task(task_id)

        execution_time = time.time() - start_time
        logger.info(f"[DRAMATIQ] CSV 摘要任務完成，耗時: {execution_time:.2f}秒")

        # 檢查是否需要自動壓縮下載（d:\temp\ 下的處理結果）
        download_archive_path = None
        should_create_archive = False
        archive_task_id = None

        # 判斷是否為 d:\temp\ 下的處理（遠端資料夾或上傳檔案）
        normalized_path = input_path.lower().replace('/', '\\')
        if normalized_path.startswith('d:\\temp\\'):
            should_create_archive = True
            logger.info(f"[DRAMATIQ] 檢測到 d:\\temp\\ 處理，提交自動壓縮任務")

            try:
                from pathlib import Path

                # 創建結果壓縮檔名稱
                folder_name = Path(input_path).name
                archive_name = f"{folder_name}_summary_result"

                # 提交自動壓縮任務到 Dramatiq 隊列
                archive_task = create_download_archive_task.send(input_path, archive_name)
                archive_task_id = archive_task.message_id

                logger.info(f"[DRAMATIQ] 自動壓縮任務已提交: {archive_task_id}")

            except Exception as e:
                logger.error(f"[DRAMATIQ] 提交自動壓縮任務失敗: {e}")

        # 回傳結果
        return {
            'status': 'completed',
            'input_path': input_path,
            'result': result.dict() if hasattr(result, 'dict') else result,
            'execution_time': execution_time,
            'timestamp': datetime.now().isoformat(),
            'auto_archive_enabled': should_create_archive,
            'archive_task_id': archive_task_id,
            'archive_task_submitted': archive_task_id is not None
        }

    except Exception as e:
        error_msg = f"CSV 摘要任務執行失敗: {str(e)}"
        logger.error(f"[DRAMATIQ] {error_msg}\n{traceback.format_exc()}")

        return {
            'status': 'failed',
            'input_path': input_path,
            'error': error_msg,
            'execution_time': time.time() - start_time,
            'timestamp': datetime.now().isoformat()
        }


@actor(queue_name="processing_queue", max_retries=2, time_limit=600000, store_results=True)
async def run_code_comparison_task(
    input_data: Union[str, Dict[str, Any]]
) -> Dict[str, Any]:
    """
    背景執行程式碼比較的任務 - Dramatiq 版本 (支援管道輸入)
    
    Args:
        input_data: 輸入數據，可以是:
            - str: 直接的文件路徑 (向後兼容)
            - Dict: 來自管道的結果，包含 next_task_params
    """
    import time
    start_time = time.time()
    
    # 參數適配邏輯：支援管道輸入和直接字符串輸入
    if isinstance(input_data, dict):
        # 檢查是否為失敗的任務
        if not input_data.get('success', True):
            error_msg = f"跳過失敗任務的程式碼比較: {input_data.get('error', '未知錯誤')}"
            logger.warning(f"[DRAMATIQ] {error_msg}")
            logger.info(f"   廠商代碼: {input_data.get('vendor_code')}")
            logger.info(f"   MO編號: {input_data.get('mo')}")
            logger.info(f"   錯誤類型: {input_data.get('error_type')}")
            
            # 返回跳過結果而不是拋出異常
            return {
                'status': 'skipped',
                'success': False,
                'reason': 'upstream_task_failed',
                'error': error_msg,
                'vendor_code': input_data.get('vendor_code'),
                'mo': input_data.get('mo'),
                'pipeline_context': input_data.get('pipeline_context', {}),
                'skipped_at': time.time()
            }
        
        # 來自管道的輸入 (成功任務)
        input_path = input_data.get('next_task_params', {}).get('input_path')
        if not input_path:
            input_path = input_data.get('temp_path')
        
        pipeline_context = input_data.get('pipeline_context', {})
        vendor_code = input_data.get('vendor_code')
        mo = input_data.get('mo')
        
        logger.info(f"[DRAMATIQ] 開始程式碼比較任務 (管道模式): {input_path}")
        logger.info(f"   來源管道: {pipeline_context.get('pipeline_id', 'unknown')}")
        logger.info(f"   廠商代碼: {vendor_code}")
        logger.info(f"   MO編號: {mo}")
    else:
        # 直接字符串輸入 (向後兼容)
        input_path = input_data
        pipeline_context = {}
        vendor_code = None
        mo = None
        
        logger.info(f"[DRAMATIQ] 開始程式碼比較任務 (直接模式): {input_path}")
    
    if not input_path:
        error_msg = f"無效的輸入路徑 - vendor: {vendor_code}, mo: {mo}"
        logger.error(f"[DRAMATIQ] {error_msg}")
        logger.error(f"   輸入資料: {input_data}")
        raise ValueError(error_msg)

    try:
        # 導入檔案處理服務
        from backend.tasks.services.scheduler import get_file_processing_service
        file_processing_service = get_file_processing_service()

        # 執行程式碼比較處理
        result = await file_processing_service.execute_code_comparison(input_path)

        execution_time = time.time() - start_time
        logger.info(f"[DRAMATIQ] 程式碼比較任務完成，耗時: {execution_time:.2f}秒")

        # 回傳結果 (管道兼容格式)
        task_result = {
            'task_type': 'run_code_comparison',
            'status': 'completed',
            'success': True,
            'input_path': input_path,
            'vendor_code': vendor_code,
            'mo': mo,
            'result': result.dict() if hasattr(result, 'dict') else result,
            'execution_time': execution_time,
            'completed_at': datetime.now().isoformat(),
            'pipeline_context': pipeline_context
        }
        
        return task_result

    except Exception as e:
        error_msg = f"程式碼比較任務執行失敗: {str(e)}"
        logger.error(f"[DRAMATIQ] {error_msg}\n{traceback.format_exc()}")

        return {
            'task_type': 'run_code_comparison',
            'status': 'failed',
            'success': False,
            'input_path': input_path,
            'vendor_code': vendor_code,
            'mo': mo,
            'error': error_msg,
            'error_type': type(e).__name__,
            'execution_time': time.time() - start_time,
            'failed_at': datetime.now().isoformat(),
            'retryable': True,
            'pipeline_context': pipeline_context
        }


# ============================================================================
# ❤️ 健康檢查任務
# ============================================================================

@actor(queue_name="health_queue", max_retries=1, time_limit=30000)
async def health_check_task() -> Dict[str, Any]:
    """
    系統健康檢查任務
    """
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "dramatiq_version": dramatiq.__version__,
            "worker_status": "active"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


# ============================================================================
# 📋 任務註冊和導出
# ============================================================================

# ============================================================================
# 🔧 任務清理函數
# ============================================================================

async def _cleanup_task_completion(session_id: str, folder_path: str, result: Dict[str, Any], task_id: str = None):
    """任務完成後的清理工作"""
    try:
        logger.info(f"[CLEANUP] 開始清理任務完成後的資源: session={session_id}")

        # 🔧 0. 設置 Redis 完成標記（用於跨進程通信）
        if task_id:
            try:
                import redis
                import json
                r = redis.Redis(host="localhost", port=6379, db=0)

                completion_data = {
                    'status': 'completed',
                    'message': 'EQC 工作流程處理完成',
                    'result': result,
                    'completed_at': datetime.now().isoformat()
                }

                completion_key = f"task_completed:{task_id}"
                r.setex(completion_key, 300, json.dumps(completion_data))  # 5分鐘過期
                logger.info(f"[CLEANUP] ✅ Redis 完成標記已設置: {completion_key}")

            except Exception as e:
                logger.warning(f"[CLEANUP] ⚠️ 設置 Redis 完成標記失敗: {e}")

        # 1. 更新會話狀態為完成
        try:
            from backend.eqc.services.eqc_session_manager import get_eqc_session_manager
            session_manager = get_eqc_session_manager()
            session_manager.complete_session(session_id, result)
            logger.info(f"[CLEANUP] ✅ 會話狀態已更新為完成: {session_id}")
        except Exception as e:
            logger.warning(f"[CLEANUP] ⚠️ 更新會話狀態失敗: {e}")

        # 2. 釋放檔案鎖定
        try:
            from backend.shared.infrastructure.adapters.filesystem.file_lock_manager import get_file_lock_manager
            file_lock_manager = get_file_lock_manager()
            release_result = file_lock_manager.release_lock(folder_path, session_id)
            if release_result:
                logger.info(f"[CLEANUP] ✅ 檔案鎖定已釋放: {folder_path}")
            else:
                logger.warning(f"[CLEANUP] ⚠️ 檔案鎖定釋放失敗: {folder_path}")
        except Exception as e:
            logger.warning(f"[CLEANUP] ⚠️ 釋放檔案鎖定失敗: {e}")

        logger.success(f"[CLEANUP] ✅ 任務完成清理完成: session={session_id}")

    except Exception as e:
        logger.error(f"[CLEANUP] ❌ 任務完成清理失敗: {e}")


async def _cleanup_task_failure(session_id: str, folder_path: str, error_message: str):
    """任務失敗後的清理工作"""
    try:
        logger.info(f"[CLEANUP] 開始清理任務失敗後的資源: session={session_id}")

        # 1. 更新會話狀態為失敗
        try:
            from backend.eqc.services.eqc_session_manager import get_eqc_session_manager
            session_manager = get_eqc_session_manager()
            session_manager.fail_session(session_id, error_message)
            logger.info(f"[CLEANUP] ✅ 會話狀態已更新為失敗: {session_id}")
        except Exception as e:
            logger.warning(f"[CLEANUP] ⚠️ 更新會話狀態失敗: {e}")

        # 2. 釋放檔案鎖定
        try:
            from backend.shared.infrastructure.adapters.filesystem.file_lock_manager import get_file_lock_manager
            file_lock_manager = get_file_lock_manager()
            release_result = file_lock_manager.release_lock(folder_path, session_id)
            if release_result:
                logger.info(f"[CLEANUP] ✅ 檔案鎖定已釋放: {folder_path}")
            else:
                logger.warning(f"[CLEANUP] ⚠️ 檔案鎖定釋放失敗: {folder_path}")
        except Exception as e:
            logger.warning(f"[CLEANUP] ⚠️ 釋放檔案鎖定失敗: {e}")

        logger.info(f"[CLEANUP] ✅ 任務失敗清理完成: session={session_id}")

    except Exception as e:
        logger.error(f"[CLEANUP] ❌ 任務失敗清理失敗: {e}")


# 自動壓縮任務
@dramatiq.actor(store_results=True, max_retries=3, min_backoff=1000, max_backoff=30000)
async def create_download_archive_task(source_path: str, archive_name: str = None) -> Dict[str, Any]:
    """
    創建下載用壓縮檔任務

    Args:
        source_path: 要壓縮的源路徑
        archive_name: 壓縮檔名稱（不含副檔名）

    Returns:
        Dict[str, Any]: 壓縮結果
    """
    import time
    import traceback
    from datetime import datetime
    from pathlib import Path

    start_time = time.time()
    logger.info(f"[DRAMATIQ] 開始自動壓縮任務: {source_path}")

    try:
        # 檢查源路徑是否存在
        if not Path(source_path).exists():
            error_msg = f"源路徑不存在: {source_path}"
            logger.error(f"[DRAMATIQ] {error_msg}")
            return {
                'status': 'failed',
                'source_path': source_path,
                'error': error_msg,
                'execution_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }

        # 導入遠端路徑處理器
        from backend.shared.utils.remote_path_processor import create_result_archive

        # 如果沒有提供壓縮檔名稱，自動生成
        if not archive_name:
            folder_name = Path(source_path).name
            archive_name = f"{folder_name}_summary_result"

        logger.info(f"[DRAMATIQ] 創建壓縮檔: {archive_name}")

        # 創建壓縮檔
        archive_path, success = create_result_archive(source_path, archive_name)

        execution_time = time.time() - start_time

        if success:
            logger.info(f"[DRAMATIQ] 自動壓縮完成: {archive_path}, 耗時: {execution_time:.2f}秒")

            return {
                'status': 'completed',
                'source_path': source_path,
                'archive_path': archive_path,
                'archive_name': archive_name,
                'archive_filename': Path(archive_path).name,
                'execution_time': execution_time,
                'timestamp': datetime.now().isoformat(),
                'download_ready': True
            }
        else:
            error_msg = "壓縮檔創建失敗"
            logger.error(f"[DRAMATIQ] {error_msg}")

            return {
                'status': 'failed',
                'source_path': source_path,
                'error': error_msg,
                'execution_time': execution_time,
                'timestamp': datetime.now().isoformat()
            }

    except Exception as e:
        error_msg = f"自動壓縮任務執行失敗: {str(e)}"
        logger.error(f"[DRAMATIQ] {error_msg}\n{traceback.format_exc()}")

        return {
            'status': 'failed',
            'source_path': source_path,
            'error': error_msg,
            'execution_time': time.time() - start_time,
            'timestamp': datetime.now().isoformat()
        }


# 導出所有任務
# 從解壓縮模組導入任務
from backend.tasks.archive_pipeline_tasks import extract_archive_task

__all__ = [
    'extract_archive_task',  # 從 archive_pipeline_tasks 導入
    'create_download_archive_task',  # 自動壓縮任務
    'process_complete_eqc_workflow_task',
    'search_product_task',
    'run_csv_summary_task',
    'run_code_comparison_task',
    'health_check_task',
    'process_vendor_files_task',
    'pipeline_completion_task'
]
