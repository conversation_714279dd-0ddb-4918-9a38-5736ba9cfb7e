"""
Dramatiq API 整合測試

🎯 測試目標：
  - 驗證前端 API 能正確調用 Dramatiq 任務
  - 測試完整的前端到後端流程
  - 確認任務提交和狀態追蹤
"""

import asyncio
import time
import os
import sys
from pathlib import Path
from loguru import logger

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 設置環境變數使用內存代理進行測試
os.environ['USE_MEMORY_BROKER'] = 'true'

async def test_eqc_api_integration():
    """測試 EQC API 整合"""
    logger.info("🧪 開始 EQC API 整合測試")
    
    try:
        # 導入 API 模組
        from frontend.api.eqc_async_api import start_eqc_async_processing, EQCAsyncStartRequest
        
        # 創建測試資料夾
        test_folder = r"D:\project\python\outlook_summary\test_data\eqc_api_test"
        Path(test_folder).mkdir(parents=True, exist_ok=True)
        
        # 創建基本測試文件
        test_files = ["EQCTOTALDATA.csv", "FT_data.csv", "EQC_data.csv"]
        for file_name in test_files:
            test_file = Path(test_folder) / file_name
            test_file.write_text("test,data\n1,2\n", encoding='utf-8')
        
        # 創建測試請求
        test_request = EQCAsyncStartRequest(
            folder_path=test_folder,
            options={
                'main_start': 'A1',
                'main_end': 'Z100',
                'backup_start': 'AA1',
                'backup_end': 'ZZ100'
            }
        )
        
        logger.info(f"📋 測試請求: {test_request.folder_path}")
        
        # 調用 API 端點
        start_time = time.time()
        response = await start_eqc_async_processing(test_request)
        api_time = time.time() - start_time
        
        logger.info("📊 API 響應結果:")
        logger.info(f"  - 狀態: {response.status}")
        logger.info(f"  - 會話ID: {response.session_id}")
        logger.info(f"  - 任務ID: {response.task_id}")
        logger.info(f"  - 消息: {response.message}")
        logger.info(f"  - API 響應時間: {api_time:.2f}秒")
        
        # 驗證響應
        if response.status == "submitted" and response.task_id:
            logger.success("✅ EQC API 整合測試通過!")
            logger.info("🎯 測試結論:")
            logger.info("  - ✅ API 端點正確調用 Dramatiq 任務")
            logger.info("  - ✅ 任務ID 正確生成")
            logger.info("  - ✅ 會話管理正常工作")
            logger.info("  - ✅ 響應格式符合預期")
            return True
        else:
            logger.error("❌ API 響應異常")
            logger.error(f"  - 狀態: {response.status}")
            logger.error(f"  - 任務ID: {response.task_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ EQC API 整合測試失敗: {str(e)}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def test_search_api_integration():
    """測試搜索 API 整合"""
    logger.info("🧪 開始搜索 API 整合測試")
    
    try:
        # 導入搜索 API
        from frontend.api.search_routes import search_product_submit
        from backend.shared.models.search_models import ProductSearchRequest
        from frontend.api.dependencies import APIState
        
        # 創建測試請求
        test_request = ProductSearchRequest(
            product_name="test_product",
            search_paths=[r"D:\project\python\outlook_summary\test_data"],
            max_results=100
        )
        
        # 創建 API 狀態
        api_state = APIState()
        
        logger.info(f"📋 測試搜索請求: {test_request.product_name}")
        
        # 調用 API 端點
        start_time = time.time()
        response = await search_product_submit(test_request, api_state)
        api_time = time.time() - start_time
        
        logger.info("📊 搜索 API 響應結果:")
        logger.info(f"  - 狀態: {response.status}")
        logger.info(f"  - 任務ID: {response.task_id}")
        logger.info(f"  - 產品名稱: {response.product_name}")
        logger.info(f"  - API 響應時間: {api_time:.2f}秒")
        
        # 驗證響應
        if response.status == "submitted" and response.task_id:
            logger.success("✅ 搜索 API 整合測試通過!")
            return True
        else:
            logger.error("❌ 搜索 API 響應異常")
            return False
            
    except Exception as e:
        logger.error(f"❌ 搜索 API 整合測試失敗: {str(e)}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def test_processing_api_integration():
    """測試處理 API 整合"""
    logger.info("🧪 開始處理 API 整合測試")
    
    try:
        # 導入處理 API
        from frontend.api.processing_routes import (
            process_csv_summary_submit,
            process_code_comparison_submit
        )
        from frontend.api.dependencies import APIState
        from backend.shared.infrastructure.adapters.processing import get_file_processing_service
        
        # 創建測試文件
        test_file = r"D:\project\python\outlook_summary\test_data\test.csv"
        Path(test_file).parent.mkdir(parents=True, exist_ok=True)
        Path(test_file).write_text("test,data\n1,2\n", encoding='utf-8')
        
        # 創建 API 狀態和服務
        api_state = APIState()
        processing_service = get_file_processing_service()
        
        logger.info(f"📋 測試處理文件: {test_file}")
        
        # 測試 CSV 摘要 API
        start_time = time.time()
        csv_response = await process_csv_summary_submit(
            input_path=test_file,
            processing_service=processing_service,
            api_state=api_state
        )
        csv_time = time.time() - start_time
        
        logger.info("📊 CSV 摘要 API 響應:")
        logger.info(f"  - 狀態: {csv_response.get('status', 'N/A')}")
        logger.info(f"  - 任務ID: {csv_response.get('task_id', 'N/A')}")
        logger.info(f"  - 響應時間: {csv_time:.2f}秒")
        
        # 測試代碼比較 API
        start_time = time.time()
        code_response = await process_code_comparison_submit(
            input_path=test_file,
            processing_service=processing_service,
            api_state=api_state
        )
        code_time = time.time() - start_time
        
        logger.info("📊 代碼比較 API 響應:")
        logger.info(f"  - 狀態: {code_response.get('status', 'N/A')}")
        logger.info(f"  - 任務ID: {code_response.get('task_id', 'N/A')}")
        logger.info(f"  - 響應時間: {code_time:.2f}秒")
        
        # 驗證響應
        csv_ok = csv_response.get('status') == 'submitted'
        code_ok = code_response.get('status') == 'submitted'
        
        if csv_ok and code_ok:
            logger.success("✅ 處理 API 整合測試通過!")
            return True
        else:
            logger.error("❌ 部分處理 API 測試失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 處理 API 整合測試失敗: {str(e)}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def main():
    """主測試函數"""
    logger.info("🎯 Dramatiq API 整合測試套件開始")
    
    # 測試 1: EQC API 整合
    eqc_test_passed = await test_eqc_api_integration()
    
    # 測試 2: 搜索 API 整合
    search_test_passed = await test_search_api_integration()
    
    # 測試 3: 處理 API 整合
    processing_test_passed = await test_processing_api_integration()
    
    # 總結
    logger.info("📋 API 整合測試總結:")
    logger.info(f"  - EQC API 測試: {'✅ 通過' if eqc_test_passed else '❌ 失敗'}")
    logger.info(f"  - 搜索 API 測試: {'✅ 通過' if search_test_passed else '❌ 失敗'}")
    logger.info(f"  - 處理 API 測試: {'✅ 通過' if processing_test_passed else '❌ 失敗'}")
    
    all_passed = eqc_test_passed and search_test_passed and processing_test_passed
    
    if all_passed:
        logger.success("🎉 所有 API 整合測試通過! Dramatiq 系統準備就緒")
        logger.info("🚀 下一步: 可以開始多人多工併發測試和生產部署")
        return True
    else:
        logger.error("❌ 部分 API 整合測試失敗，需要進一步調試")
        return False


if __name__ == "__main__":
    asyncio.run(main())
