"""
EQC 任務狀態資料庫操作類
解決跨進程狀態同步問題
"""

import sqlite3
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger


class TaskStatusDB:
    """EQC 任務狀態資料庫管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_path: str = None):
        """單例模式確保全局唯一實例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, db_path: str = None):
        if hasattr(self, '_initialized'):
            return
            
        self.db_path = db_path or "data/eqc_task_status.db"
        self._local = threading.local()
        self._ensure_db_exists()
        self._initialized = True
        logger.info(f"✅ TaskStatusDB 初始化完成: {self.db_path}")
    
    def _ensure_db_exists(self):
        """確保資料庫和表存在"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        with self._get_connection() as conn:
            # 讀取並執行 schema
            schema_path = Path(__file__).parent / "task_status_schema.sql"
            if schema_path.exists():
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                conn.executescript(schema_sql)
            else:
                # 備用 schema
                self._create_tables(conn)
    
    def _create_tables(self, conn: sqlite3.Connection):
        """創建表結構（備用方案）"""
        conn.executescript("""
            CREATE TABLE IF NOT EXISTS eqc_task_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(50) NOT NULL,
                session_id VARCHAR(50) NOT NULL,
                step_name VARCHAR(100) NOT NULL,
                step_number INTEGER NOT NULL,
                progress INTEGER NOT NULL DEFAULT 0,
                status VARCHAR(20) NOT NULL DEFAULT 'processing',
                message TEXT,
                details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE IF NOT EXISTS eqc_task_execution (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(50) NOT NULL UNIQUE,
                session_id VARCHAR(50) NOT NULL,
                folder_path TEXT NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'started',
                total_steps INTEGER NOT NULL DEFAULT 4,
                current_step INTEGER NOT NULL DEFAULT 0,
                progress INTEGER NOT NULL DEFAULT 0,
                error_message TEXT,
                result TEXT,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                processing_time_seconds REAL
            );
            
            CREATE INDEX IF NOT EXISTS idx_task_id ON eqc_task_status(task_id);
            CREATE INDEX IF NOT EXISTS idx_session_id ON eqc_task_status(session_id);
            CREATE INDEX IF NOT EXISTS idx_execution_task_id ON eqc_task_execution(task_id);
        """)
    
    def _get_connection(self) -> sqlite3.Connection:
        """獲取線程本地資料庫連接"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
        return self._local.connection
    
    def start_task(self, task_id: str, session_id: str, folder_path: str) -> bool:
        """開始任務執行"""
        try:
            with self._get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO eqc_task_execution 
                    (task_id, session_id, folder_path, status, started_at)
                    VALUES (?, ?, ?, 'started', ?)
                """, (task_id, session_id, folder_path, datetime.now().isoformat()))
                
                # 記錄第一個狀態
                self.update_step_status(task_id, session_id, "任務開始", 0, 0, "processing", "EQC 工作流程開始執行")
                
            logger.info(f"📊 任務開始記錄已保存: {task_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 保存任務開始記錄失敗: {e}")
            return False
    
    def update_step_status(self, task_id: str, session_id: str, step_name: str, 
                          step_number: int, progress: int, status: str = "processing", 
                          message: str = None, details: Dict = None) -> bool:
        """更新步驟狀態"""
        try:
            with self._get_connection() as conn:
                # 插入步驟狀態記錄
                conn.execute("""
                    INSERT INTO eqc_task_status 
                    (task_id, session_id, step_name, step_number, progress, status, message, details, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_id, session_id, step_name, step_number, progress, status, 
                    message, json.dumps(details) if details else None, datetime.now().isoformat()
                ))
                
                # 更新執行記錄
                conn.execute("""
                    UPDATE eqc_task_execution
                    SET current_step = ?, progress = ?, status = ?
                    WHERE task_id = ?
                """, (step_number, progress, status, task_id))
                
            logger.debug(f"📊 步驟狀態已更新: {task_id} - {step_name} ({progress}%)")
            return True
        except Exception as e:
            logger.error(f"❌ 更新步驟狀態失敗: {e}")
            return False
    
    def complete_task(self, task_id: str, session_id: str, result: Dict = None,
                     processing_time: float = None, eqc_file_path: str = None) -> bool:
        """完成任務"""
        try:
            with self._get_connection() as conn:
                # 解析結果數據
                summary_data = None
                detail_data = None
                eqc_file_size = None

                if result:
                    # 提取摘要數據
                    summary_data = {
                        'status': result.get('status'),
                        'steps_completed': result.get('steps_completed', 0),
                        'success': result.get('success', False),
                        'processing_time': processing_time
                    }

                    # 提取詳細數據
                    detail_data = result

                # 獲取檔案大小
                if eqc_file_path:
                    try:
                        from pathlib import Path
                        if Path(eqc_file_path).exists():
                            eqc_file_size = Path(eqc_file_path).stat().st_size
                    except Exception:
                        pass

                # 更新執行記錄
                conn.execute("""
                    UPDATE eqc_task_execution
                    SET status = 'completed', progress = 100, current_step = 4,
                        completed_at = ?, processing_time_seconds = ?, result = ?,
                        eqc_file_path = ?, eqc_file_size = ?,
                        summary_data = ?, detail_data = ?
                    WHERE task_id = ?
                """, (
                    datetime.now().isoformat(),
                    processing_time,
                    json.dumps(result) if result else None,
                    eqc_file_path,
                    eqc_file_size,
                    json.dumps(summary_data) if summary_data else None,
                    json.dumps(detail_data) if detail_data else None,
                    task_id
                ))

                # 記錄完成狀態
                self.update_step_status(task_id, session_id, "任務完成", 4, 100, "completed", "EQC 工作流程執行完成", result)

                # 🔧 新增：保存到處理歷史記錄
                self._save_processing_history(task_id, session_id, result, processing_time, eqc_file_path)

            logger.info(f"✅ 任務完成記錄已保存: {task_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 保存任務完成記錄失敗: {e}")
            return False
    
    def fail_task(self, task_id: str, session_id: str, error_message: str) -> bool:
        """任務失敗"""
        try:
            with self._get_connection() as conn:
                # 更新執行記錄
                conn.execute("""
                    UPDATE eqc_task_execution 
                    SET status = 'failed', error_message = ?, completed_at = ?
                    WHERE task_id = ?
                """, (error_message, datetime.now().isoformat(), task_id))
                
                # 記錄失敗狀態
                self.update_step_status(task_id, session_id, "任務失敗", -1, 0, "failed", error_message)
                
            logger.warning(f"❌ 任務失敗記錄已保存: {task_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 保存任務失敗記錄失敗: {e}")
            return False
    
    def get_task_status(self, task_id: str = None, session_id: str = None) -> Optional[Dict]:
        """獲取任務狀態"""
        try:
            with self._get_connection() as conn:
                if task_id:
                    row = conn.execute("""
                        SELECT * FROM eqc_task_execution WHERE task_id = ?
                    """, (task_id,)).fetchone()
                elif session_id:
                    row = conn.execute("""
                        SELECT * FROM eqc_task_execution WHERE session_id = ?
                        ORDER BY started_at DESC LIMIT 1
                    """, (session_id,)).fetchone()
                else:
                    return None
                
                if row:
                    result = dict(row)
                    # 解析 JSON 字段
                    if result.get('result'):
                        try:
                            result['result'] = json.loads(result['result'])
                        except:
                            pass
                    return result
                return None
        except Exception as e:
            logger.error(f"❌ 獲取任務狀態失敗: {e}")
            return None
    
    def get_latest_step_status(self, task_id: str) -> Optional[Dict]:
        """獲取最新步驟狀態"""
        try:
            with self._get_connection() as conn:
                row = conn.execute("""
                    SELECT * FROM eqc_task_status 
                    WHERE task_id = ? 
                    ORDER BY created_at DESC LIMIT 1
                """, (task_id,)).fetchone()
                
                if row:
                    result = dict(row)
                    # 解析 JSON 字段
                    if result.get('details'):
                        try:
                            result['details'] = json.loads(result['details'])
                        except:
                            pass
                    return result
                return None
        except Exception as e:
            logger.error(f"❌ 獲取最新步驟狀態失敗: {e}")
            return None

    def _save_processing_history(self, task_id: str, session_id: str, result: Dict = None,
                               processing_time: float = None, eqc_file_path: str = None) -> bool:
        """保存處理歷史記錄"""
        try:
            with self._get_connection() as conn:
                # 獲取任務基本信息
                task_info = self.get_task_status(task_id=task_id)
                if not task_info:
                    return False

                folder_path = task_info.get('folder_path', '')
                folder_name = folder_path.split('\\')[-1] if folder_path else 'Unknown'

                # 解析處理結果
                files_processed = 0
                total_records = 0
                success_records = 0
                error_records = 0
                summary_info = {}

                if result:
                    # 從結果中提取統計信息
                    for step_key in ['step1_result', 'step2_result', 'step3_result']:
                        step_result = result.get(step_key, {})
                        if isinstance(step_result, dict):
                            files_processed += step_result.get('files_processed', 0)
                            total_records += step_result.get('total_records', 0)
                            success_records += step_result.get('success_records', 0)
                            error_records += step_result.get('error_records', 0)

                    summary_info = {
                        'processing_time': processing_time,
                        'steps_completed': result.get('steps_completed', 0),
                        'success': result.get('success', False),
                        'folder_path': folder_path
                    }

                # 插入歷史記錄
                conn.execute("""
                    INSERT INTO eqc_processing_history
                    (task_id, session_id, folder_path, folder_name, status,
                     processing_time_seconds, files_processed, total_records,
                     success_records, error_records, eqc_file_path, summary_info,
                     completed_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_id, session_id, folder_path, folder_name, 'completed',
                    processing_time, files_processed, total_records,
                    success_records, error_records, eqc_file_path,
                    json.dumps(summary_info), datetime.now().isoformat()
                ))

            logger.info(f"📊 處理歷史記錄已保存: {task_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 保存處理歷史記錄失敗: {e}")
            return False

    def get_today_processing_history(self, limit: int = 50) -> List[Dict]:
        """獲取今日處理記錄"""
        try:
            with self._get_connection() as conn:
                rows = conn.execute("""
                    SELECT * FROM eqc_processing_history
                    WHERE DATE(created_at) = DATE('now', 'localtime')
                    ORDER BY created_at DESC
                    LIMIT ?
                """, (limit,)).fetchall()

                results = []
                for row in rows:
                    record = dict(row)
                    # 解析 JSON 字段
                    if record.get('summary_info'):
                        try:
                            record['summary_info'] = json.loads(record['summary_info'])
                        except:
                            pass
                    results.append(record)

                return results
        except Exception as e:
            logger.error(f"❌ 獲取今日處理記錄失敗: {e}")
            return []

    def get_processing_history(self, days: int = 7, limit: int = 100) -> List[Dict]:
        """獲取處理歷史記錄"""
        try:
            with self._get_connection() as conn:
                rows = conn.execute("""
                    SELECT * FROM eqc_processing_history
                    WHERE created_at >= datetime('now', '-{} days', 'localtime')
                    ORDER BY created_at DESC
                    LIMIT ?
                """.format(days), (limit,)).fetchall()

                results = []
                for row in rows:
                    record = dict(row)
                    # 解析 JSON 字段
                    if record.get('summary_info'):
                        try:
                            record['summary_info'] = json.loads(record['summary_info'])
                        except:
                            pass
                    results.append(record)

                return results
        except Exception as e:
            logger.error(f"❌ 獲取處理歷史記錄失敗: {e}")
            return []

    def update_download_count(self, task_id: str) -> bool:
        """更新下載次數"""
        try:
            with self._get_connection() as conn:
                conn.execute("""
                    UPDATE eqc_task_execution
                    SET download_count = download_count + 1,
                        last_downloaded_at = ?
                    WHERE task_id = ?
                """, (datetime.now().isoformat(), task_id))

            logger.debug(f"📥 下載次數已更新: {task_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 更新下載次數失敗: {e}")
            return False

    def get_eqc_file_info(self, task_id: str = None, session_id: str = None) -> Optional[Dict]:
        """獲取 EQC 檔案信息"""
        try:
            with self._get_connection() as conn:
                if task_id:
                    row = conn.execute("""
                        SELECT task_id, session_id, eqc_file_path, eqc_file_size,
                               download_count, last_downloaded_at, completed_at
                        FROM eqc_task_execution
                        WHERE task_id = ? AND eqc_file_path IS NOT NULL
                    """, (task_id,)).fetchone()
                elif session_id:
                    row = conn.execute("""
                        SELECT task_id, session_id, eqc_file_path, eqc_file_size,
                               download_count, last_downloaded_at, completed_at
                        FROM eqc_task_execution
                        WHERE session_id = ? AND eqc_file_path IS NOT NULL
                        ORDER BY completed_at DESC LIMIT 1
                    """, (session_id,)).fetchone()
                else:
                    return None

                if row:
                    return dict(row)
                return None
        except Exception as e:
            logger.error(f"❌ 獲取 EQC 檔案信息失敗: {e}")
            return None


# 全局實例
_task_status_db = None

def get_task_status_db() -> TaskStatusDB:
    """獲取全局 TaskStatusDB 實例"""
    global _task_status_db
    if _task_status_db is None:
        _task_status_db = TaskStatusDB()
    return _task_status_db
