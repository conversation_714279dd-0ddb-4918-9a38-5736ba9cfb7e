"""
Dramatiq 多人多工併發測試

🎯 測試目標：
  - 驗證真實的多人多工併發處理能力
  - 測試會話隔離和檔案鎖定機制
  - 確認 Dramatiq 的併發處理效果
  - 模擬生產環境的多用戶場景

🔧 測試場景：
  - 同時 3-5 個用戶處理不同 EQC 資料夾
  - 每個用戶有獨立的會話和資料夾
  - 驗證任務並行執行而非序列執行
  - 檢查資源競爭和隔離效果
"""

import asyncio
import time
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 設置環境變數使用內存代理進行測試
os.environ['USE_MEMORY_BROKER'] = 'true'

class ConcurrentUser:
    """併發用戶模擬器"""
    
    def __init__(self, user_id: str, folder_path: str):
        self.user_id = user_id
        self.folder_path = folder_path
        self.session_id = None
        self.task_id = None
        self.start_time = None
        self.end_time = None
        self.result = None
        self.error = None
    
    async def setup_test_data(self):
        """設置測試資料"""
        try:
            # 創建用戶專屬資料夾
            Path(self.folder_path).mkdir(parents=True, exist_ok=True)
            
            # 創建測試文件
            test_files = [
                "EQCTOTALDATA.csv",
                "FT_data.csv", 
                "EQC_data.csv",
                f"user_{self.user_id}_data.csv"
            ]
            
            for file_name in test_files:
                test_file = Path(self.folder_path) / file_name
                test_content = f"user,{self.user_id},test,data\n1,2,3,4\n"
                test_file.write_text(test_content, encoding='utf-8')
            
            logger.info(f"👤 用戶 {self.user_id} 測試資料準備完成: {self.folder_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 用戶 {self.user_id} 測試資料準備失敗: {e}")
            return False
    
    async def execute_eqc_workflow(self):
        """執行 EQC 工作流程"""
        try:
            self.start_time = time.time()
            logger.info(f"🚀 用戶 {self.user_id} 開始 EQC 工作流程")
            
            # 導入 Dramatiq 任務
            from backend.tasks.services.dramatiq_tasks import process_complete_eqc_workflow_task
            
            # 生成會話ID
            self.session_id = f"user_{self.user_id}_session_{int(time.time())}"
            
            # 執行任務 (直接調用函數進行測試)
            task_function = process_complete_eqc_workflow_task.fn
            
            self.result = await task_function(
                folder_path=self.folder_path,
                user_session_id=self.session_id,
                options={
                    'main_start': 'A1',
                    'main_end': 'Z100',
                    'test_mode': True,
                    'user_id': self.user_id
                }
            )
            
            self.end_time = time.time()
            execution_time = self.end_time - self.start_time
            
            logger.info(f"✅ 用戶 {self.user_id} EQC 工作流程完成，耗時: {execution_time:.2f}秒")
            return True
            
        except Exception as e:
            self.error = str(e)
            self.end_time = time.time()
            logger.error(f"❌ 用戶 {self.user_id} EQC 工作流程失敗: {e}")
            return False
    
    def get_execution_time(self) -> float:
        """獲取執行時間"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0
    
    def is_successful(self) -> bool:
        """檢查是否成功"""
        return self.result is not None and self.result.get('status') == 'completed'


async def test_concurrent_eqc_processing():
    """測試併發 EQC 處理"""
    logger.info("🧪 開始多人多工併發 EQC 處理測試")
    
    # 創建多個併發用戶
    num_users = 5
    users = []
    
    for i in range(num_users):
        user_id = f"user_{i+1}"
        folder_path = f"D:\\project\\python\\outlook_summary\\test_data\\concurrent_{user_id}"
        user = ConcurrentUser(user_id, folder_path)
        users.append(user)
    
    try:
        # 階段 1: 準備測試資料
        logger.info("📋 階段 1: 準備測試資料")
        setup_tasks = [user.setup_test_data() for user in users]
        setup_results = await asyncio.gather(*setup_tasks)
        
        if not all(setup_results):
            logger.error("❌ 部分用戶測試資料準備失敗")
            return False
        
        logger.info(f"✅ {num_users} 個用戶測試資料準備完成")
        
        # 階段 2: 併發執行 EQC 工作流程
        logger.info("🚀 階段 2: 併發執行 EQC 工作流程")
        
        # 記錄總開始時間
        total_start_time = time.time()
        
        # 同時啟動所有用戶的 EQC 工作流程
        execution_tasks = [user.execute_eqc_workflow() for user in users]
        execution_results = await asyncio.gather(*execution_tasks, return_exceptions=True)
        
        # 記錄總結束時間
        total_end_time = time.time()
        total_execution_time = total_end_time - total_start_time
        
        # 階段 3: 分析結果
        logger.info("📊 階段 3: 分析併發測試結果")
        
        successful_users = 0
        failed_users = 0
        total_individual_time = 0.0
        
        for i, user in enumerate(users):
            execution_time = user.get_execution_time()
            total_individual_time += execution_time
            
            if user.is_successful():
                successful_users += 1
                logger.info(f"✅ 用戶 {user.user_id}: 成功 ({execution_time:.2f}秒)")
            else:
                failed_users += 1
                logger.error(f"❌ 用戶 {user.user_id}: 失敗 ({execution_time:.2f}秒) - {user.error}")
        
        # 計算併發效率
        avg_individual_time = total_individual_time / num_users
        concurrency_efficiency = (avg_individual_time / total_execution_time) if total_execution_time > 0 else 0
        
        # 輸出詳細分析
        logger.info("📈 併發測試分析結果:")
        logger.info(f"  - 總用戶數: {num_users}")
        logger.info(f"  - 成功用戶: {successful_users}")
        logger.info(f"  - 失敗用戶: {failed_users}")
        logger.info(f"  - 總執行時間: {total_execution_time:.2f}秒")
        logger.info(f"  - 平均個別時間: {avg_individual_time:.2f}秒")
        logger.info(f"  - 併發效率: {concurrency_efficiency:.2f}x")
        
        # 判斷併發效果
        if concurrency_efficiency > 0.8:  # 80% 以上效率認為是真正併發
            logger.success("🎉 真正併發處理驗證成功!")
            logger.info("🎯 結論:")
            logger.info("  - ✅ Dramatiq 支援真正的多人多工併發")
            logger.info("  - ✅ 任務並行執行而非序列執行")
            logger.info("  - ✅ 會話隔離機制正常工作")
            logger.info("  - ✅ 系統具備生產級併發能力")
            return True
        else:
            logger.warning("⚠️ 併發效率較低，可能仍為序列處理")
            logger.info("🔧 建議:")
            logger.info("  - 檢查 Dramatiq Worker 配置")
            logger.info("  - 確認 Redis 連接狀態")
            logger.info("  - 驗證任務隊列設置")
            return False
            
    except Exception as e:
        logger.error(f"❌ 併發測試執行異常: {str(e)}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        return False


async def test_session_isolation():
    """測試會話隔離"""
    logger.info("🧪 開始會話隔離測試")
    
    try:
        # 導入會話管理器
        from backend.eqc.services.eqc_session_manager import get_eqc_session_manager
        
        session_manager = get_eqc_session_manager()
        
        # 創建多個會話
        sessions = []
        for i in range(3):
            session_id = f"isolation_test_session_{i}"
            user_id = f"isolation_user_{i}"
            folder_path = f"D:\\project\\python\\outlook_summary\\test_data\\isolation_{i}"
            
            # 創建會話
            session = session_manager.create_session(session_id, user_id, folder_path)
            sessions.append(session)
            
            logger.info(f"📋 創建會話: {session_id}")
        
        # 驗證會話隔離
        for session in sessions:
            retrieved_session = session_manager.get_session(session.session_id)
            
            if retrieved_session and retrieved_session.user_id == session.user_id:
                logger.info(f"✅ 會話 {session.session_id} 隔離正常")
            else:
                logger.error(f"❌ 會話 {session.session_id} 隔離失敗")
                return False
        
        logger.success("✅ 會話隔離測試通過")
        return True
        
    except Exception as e:
        logger.error(f"❌ 會話隔離測試失敗: {e}")
        return False


async def main():
    """主測試函數"""
    logger.info("🎯 Dramatiq 多人多工併發測試套件開始")
    
    # 測試 1: 併發 EQC 處理
    concurrent_test_passed = await test_concurrent_eqc_processing()
    
    # 測試 2: 會話隔離
    isolation_test_passed = await test_session_isolation()
    
    # 總結
    logger.info("📋 併發測試總結:")
    logger.info(f"  - 併發處理測試: {'✅ 通過' if concurrent_test_passed else '❌ 失敗'}")
    logger.info(f"  - 會話隔離測試: {'✅ 通過' if isolation_test_passed else '❌ 失敗'}")
    
    all_passed = concurrent_test_passed and isolation_test_passed
    
    if all_passed:
        logger.success("🎉 所有併發測試通過! Dramatiq 多人多工系統準備就緒")
        logger.info("🚀 系統已具備生產級多人多工併發處理能力")
        logger.info("📈 建議進行生產環境部署和效能調優")
        return True
    else:
        logger.error("❌ 部分併發測試失敗，需要進一步優化")
        return False


if __name__ == "__main__":
    asyncio.run(main())
