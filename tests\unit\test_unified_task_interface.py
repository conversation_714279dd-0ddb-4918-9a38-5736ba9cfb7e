"""
統一任務接口單元測試

🎯 測試目標：
  - 驗證純 Dramatiq 任務管理器功能
  - 測試任務提交和狀態查詢
  - 確認錯誤處理機制
  - 驗證系統狀態檢查

🔧 測試範圍：
  - TaskResult 類
  - TaskManager 類
  - 便捷函數
  - 異常處理
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

# 測試目標模組
from backend.tasks.unified_task_interface import (
    TaskResult,
    TaskManager,
    get_task_manager,
    submit_eqc_workflow,
    submit_product_search,
    get_system_status
)


class TestTaskResult:
    """測試 TaskResult 類"""
    
    def test_task_result_initialization(self):
        """測試 TaskResult 初始化"""
        task_id = "test_task_123"
        raw_result = {"status": "success"}
        
        result = TaskResult(task_id, raw_result)
        
        assert result.task_id == task_id
        assert result.raw_result == raw_result
    
    @patch('src.tasks.dramatiq_integration.get_task_status')
    def test_get_status_success(self, mock_get_status):
        """測試成功獲取任務狀態"""
        mock_status = {
            'status': 'SUCCESS',
            'message': 'Task completed',
            'queue_type': 'dramatiq'
        }
        mock_get_status.return_value = mock_status

        result = TaskResult("test_task_123")
        status = result.get_status()

        assert status == mock_status
        assert status['queue_type'] == 'dramatiq'
        mock_get_status.assert_called_once_with("test_task_123")
    
    @patch('src.tasks.dramatiq_integration.get_task_status')
    def test_get_status_error(self, mock_get_status):
        """測試獲取任務狀態時發生錯誤"""
        mock_get_status.side_effect = Exception("Status check failed")

        result = TaskResult("test_task_123")
        status = result.get_status()

        assert status['status'] == 'ERROR'
        assert 'error' in status
        assert status['queue_type'] == 'dramatiq'


class TestTaskManager:
    """測試 TaskManager 類"""
    
    def test_task_manager_initialization(self):
        """測試 TaskManager 初始化"""
        manager = TaskManager()
        
        assert manager._dramatiq_available is None
    
    @patch('src.tasks.dramatiq_integration.is_dramatiq_available')
    def test_check_dramatiq_availability_success(self, mock_is_available):
        """測試 Dramatiq 可用性檢查成功"""
        mock_is_available.return_value = True

        manager = TaskManager()
        result = manager._check_dramatiq_availability()

        assert result == True
        assert manager._dramatiq_available == True
        mock_is_available.assert_called_once()
    
    @patch('src.tasks.dramatiq_integration.is_dramatiq_available')
    def test_check_dramatiq_availability_failure(self, mock_is_available):
        """測試 Dramatiq 可用性檢查失敗"""
        mock_is_available.return_value = False

        manager = TaskManager()
        result = manager._check_dramatiq_availability()

        assert result == False
        assert manager._dramatiq_available == False

    @patch('src.tasks.dramatiq_integration.is_dramatiq_available')
    def test_check_dramatiq_availability_exception(self, mock_is_available):
        """測試 Dramatiq 可用性檢查異常"""
        mock_is_available.side_effect = Exception("Import error")

        manager = TaskManager()
        result = manager._check_dramatiq_availability()

        assert result == False
        assert manager._dramatiq_available == False
    
    @patch('src.tasks.dramatiq_integration.submit_eqc_workflow_task')
    def test_submit_eqc_workflow_success(self, mock_submit):
        """測試成功提交 EQC 工作流程"""
        mock_result = Mock()
        mock_result.message_id = "task_123"
        mock_submit.return_value = mock_result

        manager = TaskManager()

        with patch.object(manager, '_check_dramatiq_availability', return_value=True):
            result = manager.submit_eqc_workflow("test_folder", "test_session", {"option": "value"})

        assert isinstance(result, TaskResult)
        assert result.task_id == "task_123"
        assert result.raw_result == mock_result
        mock_submit.assert_called_once_with("test_folder", "test_session", {"option": "value"})
    
    def test_submit_eqc_workflow_unavailable(self):
        """測試 Dramatiq 不可用時提交 EQC 工作流程"""
        manager = TaskManager()
        
        with patch.object(manager, '_check_dramatiq_availability', return_value=False):
            with pytest.raises(RuntimeError, match="Dramatiq 任務隊列不可用"):
                manager.submit_eqc_workflow("test_folder")
    
    @patch('src.tasks.dramatiq_integration.submit_product_search_task')
    def test_submit_product_search_success(self, mock_submit):
        """測試成功提交產品搜索"""
        mock_result = Mock()
        mock_result.message_id = "search_123"
        mock_submit.return_value = mock_result

        manager = TaskManager()

        with patch.object(manager, '_check_dramatiq_availability', return_value=True):
            result = manager.submit_product_search("test_product", ["path1"], 100, {"filter": "value"})

        assert isinstance(result, TaskResult)
        assert result.task_id == "search_123"
        mock_submit.assert_called_once_with("test_product", ["path1"], 100, {"filter": "value"})

    @patch('src.tasks.dramatiq_integration.submit_csv_summary_task')
    def test_submit_csv_summary_success(self, mock_submit):
        """測試成功提交 CSV 摘要"""
        mock_result = Mock()
        mock_result.message_id = "csv_123"
        mock_submit.return_value = mock_result

        manager = TaskManager()

        with patch.object(manager, '_check_dramatiq_availability', return_value=True):
            result = manager.submit_csv_summary("test_path")

        assert isinstance(result, TaskResult)
        assert result.task_id == "csv_123"
        mock_submit.assert_called_once_with("test_path")

    @patch('src.tasks.dramatiq_integration.submit_code_comparison_task')
    def test_submit_code_comparison_success(self, mock_submit):
        """測試成功提交代碼比較"""
        mock_result = Mock()
        mock_result.message_id = "code_123"
        mock_submit.return_value = mock_result

        manager = TaskManager()

        with patch.object(manager, '_check_dramatiq_availability', return_value=True):
            result = manager.submit_code_comparison("test_path")

        assert isinstance(result, TaskResult)
        assert result.task_id == "code_123"
        mock_submit.assert_called_once_with("test_path")
    
    def test_get_task_status(self):
        """測試獲取任務狀態"""
        manager = TaskManager()
        
        with patch('src.tasks.unified_task_interface.TaskResult') as mock_task_result:
            mock_result_instance = Mock()
            mock_status = {'status': 'SUCCESS'}
            mock_result_instance.get_status.return_value = mock_status
            mock_task_result.return_value = mock_result_instance
            
            result = manager.get_task_status("test_task_123")
            
            assert result == mock_status
            mock_task_result.assert_called_once_with("test_task_123")
            mock_result_instance.get_status.assert_called_once()
    
    def test_get_system_status(self):
        """測試獲取系統狀態"""
        manager = TaskManager()
        
        with patch.object(manager, '_check_dramatiq_availability', return_value=True):
            status = manager.get_system_status()
        
        assert status['dramatiq_available'] == True
        assert status['queue_type'] == 'dramatiq'


class TestGlobalFunctions:
    """測試全域函數"""
    
    @patch('src.tasks.unified_task_interface._task_manager')
    def test_get_task_manager(self, mock_manager):
        """測試獲取全域任務管理器"""
        result = get_task_manager()
        
        assert result == mock_manager
    
    @patch('src.tasks.unified_task_interface._task_manager')
    def test_submit_eqc_workflow_global(self, mock_manager):
        """測試全域 EQC 工作流程提交函數"""
        mock_result = Mock()
        mock_manager.submit_eqc_workflow.return_value = mock_result
        
        result = submit_eqc_workflow("test_folder", "test_session", {"option": "value"})
        
        assert result == mock_result
        mock_manager.submit_eqc_workflow.assert_called_once_with("test_folder", "test_session", {"option": "value"})
    
    @patch('src.tasks.unified_task_interface._task_manager')
    def test_submit_product_search_global(self, mock_manager):
        """測試全域產品搜索提交函數"""
        mock_result = Mock()
        mock_manager.submit_product_search.return_value = mock_result
        
        result = submit_product_search("test_product", ["path1"], 100, {"filter": "value"})
        
        assert result == mock_result
        mock_manager.submit_product_search.assert_called_once_with("test_product", ["path1"], 100, {"filter": "value"})
    
    @patch('src.tasks.unified_task_interface._task_manager')
    def test_get_system_status_global(self, mock_manager):
        """測試全域系統狀態函數"""
        mock_status = {'dramatiq_available': True, 'queue_type': 'dramatiq'}
        mock_manager.get_system_status.return_value = mock_status
        
        result = get_system_status()
        
        assert result == mock_status
        mock_manager.get_system_status.assert_called_once()


class TestErrorHandling:
    """測試錯誤處理"""
    
    def test_task_manager_submit_with_exception(self):
        """測試任務提交時發生異常"""
        manager = TaskManager()
        
        with patch.object(manager, '_check_dramatiq_availability', side_effect=Exception("Unexpected error")):
            with pytest.raises(Exception, match="Unexpected error"):
                manager.submit_eqc_workflow("test_folder")
    
    def test_task_result_with_invalid_task_id(self):
        """測試無效任務ID的 TaskResult"""
        result = TaskResult("")
        
        with patch('src.tasks.dramatiq_integration.get_task_status', side_effect=Exception("Invalid task ID")):
            status = result.get_status()

            assert status['status'] == 'ERROR'
            assert 'error' in status


@pytest.mark.integration
class TestTaskManagerIntegration:
    """測試任務管理器整合功能"""
    
    @patch('src.tasks.dramatiq_integration.is_dramatiq_available')
    @patch('src.tasks.dramatiq_integration.submit_eqc_workflow_task')
    def test_full_workflow_integration(self, mock_submit, mock_available):
        """測試完整工作流程整合"""
        # 設置模擬
        mock_available.return_value = True
        mock_result = Mock()
        mock_result.message_id = "integration_test_123"
        mock_submit.return_value = mock_result

        # 執行測試
        manager = TaskManager()
        result = manager.submit_eqc_workflow("integration_test_folder")

        # 驗證結果
        assert isinstance(result, TaskResult)
        assert result.task_id == "integration_test_123"

        # 驗證系統狀態
        status = manager.get_system_status()
        assert status['dramatiq_available'] == True
        assert status['queue_type'] == 'dramatiq'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
