"""
日誌系統測試
TASK_003: 建立結構化日誌系統
特殊要求：支援 info, debug, error, warning, 效能 等級別，不同顏色，包含檔案和函式名稱
遵循 TDD 原則 - 先寫失敗的測試
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, MagicMock
from datetime import datetime

from backend.shared.infrastructure.logging.logger_manager import (
    LoggerManager,
    LogLevel,
    LogFormat,
    PerformanceLogger,
    StructuredLogger,
)


class TestLoggerManager:
    """測試日誌管理器"""

    def test_logger_manager_initialization(self):
        """測試日誌管理器初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir)
            
            assert logger_manager.log_dir == log_dir
            assert logger_manager.default_level == LogLevel.INFO
            assert logger_manager.formatters is not None

    def test_logger_creation_with_colors(self):
        """測試建立帶顏色的日誌器"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir, enable_colors=True)
            
            logger = logger_manager.get_logger("test_module")
            
            assert logger is not None
            assert logger.name == "test_module"
            assert logger_manager.enable_colors is True

    def test_log_levels_and_colors(self):
        """測試不同日誌級別和顏色"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir, enable_colors=True)
            logger = logger_manager.get_logger("color_test")
            
            # 測試不同級別的日誌
            test_messages = [
                (LogLevel.DEBUG, "[EXCEPT_CHAR]錯訊息", "藍色"),
                (LogLevel.INFO, "資訊訊息", "綠色"),
                (LogLevel.WARNING, "警告訊息", "黃色"),
                (LogLevel.ERROR, "錯誤訊息", "紅色"),
                (LogLevel.CRITICAL, "嚴重錯誤", "背景紅色"),
            ]
            
            for level, message, expected_color in test_messages:
                # 模擬日誌輸出並檢查顏色
                with patch('sys.stdout') as mock_stdout:
                    logger_manager.log(logger, level, message)
                    # 檢查是否包含 ANSI 顏色代碼
                    assert mock_stdout.write.called

    def test_performance_logging(self):
        """測試效能日誌功能"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir)
            perf_logger = logger_manager.get_performance_logger()
            
            # 測試效能計時
            with perf_logger.timer("email_processing"):
                # 模擬處理時間
                import time
                time.sleep(0.01)
            
            # 檢查效能日誌是否記錄
            perf_log_file = log_dir / "performance.log"
            assert perf_log_file.exists()

    def test_structured_logging(self):
        """測試結構化日誌（JSON 格式）"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(
                log_dir=log_dir, 
                log_format=LogFormat.JSON
            )
            logger = logger_manager.get_logger("structured_test")
            
            # 記錄結構化日誌
            context = {
                "email_id": "test_email_123",
                "vendor": "GTK",
                "processing_step": "parsing"
            }
            
            logger_manager.log_with_context(
                logger, 
                LogLevel.INFO, 
                "郵件處理開始", 
                context
            )
            
            # 檢查 JSON 日誌格式
            log_file = log_dir / "application.log"
            assert log_file.exists()
            
            with open(log_file, 'r', encoding='utf-8') as f:
                log_line = f.readline().strip()
                log_data = json.loads(log_line)
                
                assert log_data["message"] == "郵件處理開始"
                assert log_data["context"]["email_id"] == "test_email_123"
                assert log_data["context"]["vendor"] == "GTK"

    def test_file_and_function_name_inclusion(self):
        """測試包含檔案名稱和函式名稱"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(
                log_dir=log_dir,
                include_caller_info=True
            )
            logger = logger_manager.get_logger("caller_test")
            
            # 記錄日誌
            logger_manager.log(logger, LogLevel.INFO, "測試呼叫者資訊")
            
            # 檢查日誌是否包含檔案和函式資訊
            log_file = log_dir / "application.log"
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.read()
                
                # 應該包含檔案名稱
                assert "test_logger.py" in log_content
                # 應該包含函式名稱
                assert "test_file_and_function_name_inclusion" in log_content

    def test_log_rotation(self):
        """測試日誌輪轉功能"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(
                log_dir=log_dir,
                max_log_size_mb=0.001,  # 很小的大小來觸發輪轉
                backup_count=3
            )
            logger = logger_manager.get_logger("rotation_test")
            
            # 寫入大量日誌來觸發輪轉
            for i in range(100):
                logger_manager.log(
                    logger, 
                    LogLevel.INFO, 
                    f"測試日誌輪轉 - 訊息 {i} " + "x" * 100
                )
            
            # 檢查是否產生了備份檔案
            log_files = list(log_dir.glob("application.log*"))
            assert len(log_files) > 1  # 應該有主檔案和備份檔案

    def test_error_logging_with_exception(self):
        """測試例外錯誤日誌"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir)
            logger = logger_manager.get_logger("error_test")
            
            # 模擬例外
            try:
                raise ValueError("測試例外錯誤")
            except Exception as e:
                logger_manager.log_exception(
                    logger, 
                    e, 
                    context={"operation": "email_parsing"}
                )
            
            # 檢查錯誤日誌
            error_log_file = log_dir / "error.log"
            assert error_log_file.exists()
            
            with open(error_log_file, 'r', encoding='utf-8') as f:
                error_content = f.read()
                assert "ValueError" in error_content
                assert "測試例外錯誤" in error_content
                assert "Traceback" in error_content

    def test_performance_metrics_logging(self):
        """測試效能指標日誌"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir)
            
            # 記錄效能指標
            metrics = {
                "email_processing_time": 1.25,
                "file_size_mb": 15.5,
                "memory_usage_mb": 256.8,
                "cpu_usage_percent": 45.2
            }
            
            logger_manager.log_performance_metrics(metrics)
            
            # 檢查效能日誌
            perf_log_file = log_dir / "performance.log"
            assert perf_log_file.exists()

    def test_email_processing_logging(self):
        """測試郵件處理專用日誌"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir)
            
            # 模擬郵件資料
            email_data = {
                "id": "email_123",
                "subject": "GTK FT HOLD MO:F123456",
                "sender": "<EMAIL>",
                "vendor": "GTK",
                "received_time": datetime.now().isoformat()
            }
            
            logger_manager.log_email_processing(
                email_data, 
                "processing_started",
                additional_info={"parser": "GTKParser"}
            )
            
            # 檢查郵件處理日誌
            email_log_file = log_dir / "email_processing.log"
            assert email_log_file.exists()

    def test_logger_configuration_from_config(self):
        """測試從配置檔案建立日誌器"""
        config = {
            "log_level": "DEBUG",
            "enable_colors": True,
            "log_format": "JSON",
            "include_caller_info": True,
            "max_log_size_mb": 10,
            "backup_count": 5
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager.from_config(config, log_dir=log_dir)
            
            assert logger_manager.default_level == LogLevel.DEBUG
            assert logger_manager.enable_colors is True
            assert logger_manager.log_format == LogFormat.JSON
            assert logger_manager.include_caller_info is True

    def test_async_logging(self):
        """測試非同步日誌"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(
                log_dir=log_dir,
                async_logging=True
            )
            logger = logger_manager.get_logger("async_test")
            
            # 記錄多個非同步日誌
            for i in range(10):
                logger_manager.log_async(
                    logger, 
                    LogLevel.INFO, 
                    f"非同步日誌訊息 {i}"
                )
            
            # 等待非同步處理完成
            logger_manager.flush_async_logs()
            
            # 檢查日誌檔案
            log_file = log_dir / "application.log"
            assert log_file.exists()

    def test_chinese_character_support(self):
        """測試中文字元支援"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            logger_manager = LoggerManager(log_dir=log_dir)
            logger = logger_manager.get_logger("中文測試")
            
            # 記錄包含中文的日誌
            logger_manager.log(
                logger, 
                LogLevel.INFO, 
                "測試中文日誌：郵件處理成功，廠商GTK，產品編號F123456"
            )
            
            # 檢查中文是否正確記錄
            log_file = log_dir / "application.log"
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                assert "測試中文日誌" in content
                assert "郵件處理成功" in content
                assert "廠商GTK" in content