"""
統一的 EQC DEBUG LOG 記錄器
解決 eqc_inseqcrtdata2_processor.py 中的日誌重複問題
"""

import logging
import os
from datetime import datetime
from typing import Optional


class EQCDebugLogger:
    """統一的 EQC DEBUG 日誌記錄器"""
    
    def __init__(self, logger_name: str = __name__):
        """
        初始化 EQC DEBUG 日誌記錄器
        
        Args:
            logger_name: 日誌記錄器名稱
        """
        self.logger = logging.getLogger(logger_name)
        self.debug_log_lines = []  # 存儲 DEBUG LOG 到內存
        self.log_file_path: Optional[str] = None  # 日誌檔案路徑
        self.step_name: str = "EQC"  # 步驟名稱，預設為 EQC
    
    def set_step_name(self, step_name: str):
        """設定步驟名稱（如 Step3, Step4 等）"""
        self.step_name = step_name
    
    def set_log_file(self, log_file_path: str):
        """設定日誌檔案路徑"""
        self.log_file_path = log_file_path
    
    def log_debug(self, message: str):
        """
        記錄 DEBUG LOG 到終端和檔案
        
        Args:
            message: 要記錄的訊息
        """
        # 記錄到終端
        self.logger.info(message)
        
        # 記錄到內存
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        log_line = f"[{timestamp}] {message}"
        self.debug_log_lines.append(log_line)
        
        # 如果有檔案路徑，同時寫入檔案
        if self.log_file_path:
            try:
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    f.write(log_line + '\n')
            except Exception as e:
                self.logger.warning(f"無法寫入 {self.step_name} DEBUG LOG檔案 {self.log_file_path}: {e}")
    
    def save_debug_log(self, base_dir: str) -> Optional[str]:
        """
        完成 DEBUG LOG 檔案
        
        Args:
            base_dir: 基礎目錄路徑
            
        Returns:
            成功時返回日誌檔案路徑，失敗時返回 None
        """
        try:
            if self.log_file_path and os.path.exists(self.log_file_path):
                # 在檔案末尾添加結束標記
                end_lines = [
                    "",
                    "=" * 60,
                    f"處理完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    f"{self.step_name} DEBUG LOG 結束",
                    "=" * 60
                ]
                
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    for line in end_lines:
                        f.write(line + '\n')
                
                log_filename = os.path.basename(self.log_file_path)
                self.logger.info(f"   [OK] {self.step_name} DEBUG LOG 已完成: {log_filename}")
                return self.log_file_path
            else:
                self.logger.warning(f"[WARNING] {self.step_name} DEBUG LOG 檔案不存在，無法完成")
                return None
                
        except Exception as e:
            self.logger.error(f"[ERROR] 完成 {self.step_name} DEBUG LOG 失敗: {e}")
            return None
    
    def clear_memory_logs(self):
        """清除內存中的日誌記錄"""
        self.debug_log_lines.clear()
    
    def get_memory_logs(self) -> list:
        """獲取內存中的日誌記錄"""
        return self.debug_log_lines.copy()
    
    def has_log_file(self) -> bool:
        """檢查是否設定了日誌檔案"""
        return self.log_file_path is not None and os.path.exists(self.log_file_path)


class Step3DebugLogger(EQCDebugLogger):
    """Step 3 專用的 DEBUG 日誌記錄器"""
    
    def __init__(self, logger_name: str = __name__):
        super().__init__(logger_name)
        self.set_step_name("Step 3 - Online EQC FAIL 檢測")


class Step4DebugLogger(EQCDebugLogger):
    """Step 4 專用的 DEBUG 日誌記錄器"""
    
    def __init__(self, logger_name: str = __name__):
        super().__init__(logger_name)
        self.set_step_name("Step 4 - CODE 區間匹配搜尋")