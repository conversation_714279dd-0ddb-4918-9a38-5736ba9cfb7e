"""Phase 4: Performance Validation Testing

This module tests that the dependency injection refactoring has not
negatively impacted system performance.
"""

import pytest
import time
import asyncio
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any

from .conftest import API_ENDPOINTS, TEST_TASK_ID

# Import dependencies for testing
try:
    from frontend.api.dependencies import (
        get_api_state, get_staging_service, get_processing_service
    )
except ImportError:
    # Handle case where dependencies are not available
    get_api_state = None
    get_staging_service = None
    get_processing_service = None


class TestResponseTimePerformance:
    """Test response time performance of refactored endpoints."""
    
    def test_staging_endpoint_response_time(self, test_client, sample_staging_request):
        """Test that staging endpoint responds within acceptable time."""
        start_time = time.time()
        
        response = test_client.post(
            API_ENDPOINTS["STAGING_CREATE"],
            json=sample_staging_request
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Should respond within 5 seconds (generous limit for dependency injection overhead)
        assert response_time < 5.0, f"Staging endpoint too slow: {response_time:.2f}s"
        
        # Response should be valid
        assert response.status_code in [200, 201, 422]
    
    def test_search_endpoint_response_time(self, test_client, sample_search_request):
        """Test that search endpoint responds within acceptable time."""
        start_time = time.time()
        
        response = test_client.post(
            API_ENDPOINTS["SEARCH_PRODUCT"],
            json=sample_search_request
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Should respond within 3 seconds
        assert response_time < 3.0, f"Search endpoint too slow: {response_time:.2f}s"
        
        # Response should be valid
        assert response.status_code in [200, 201, 422]
    
    def test_health_check_response_time(self, test_client):
        """Test that health check endpoint responds quickly."""
        start_time = time.time()
        
        response = test_client.get(API_ENDPOINTS["CELERY_HEALTH"])
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Health checks should be very fast
        assert response_time < 1.0, f"Health check too slow: {response_time:.2f}s"
        
        # Response should be valid
        assert response.status_code in [200, 503]
    
    def test_dashboard_response_time(self, test_client):
        """Test that dashboard endpoint responds within acceptable time."""
        start_time = time.time()
        
        response = test_client.get(API_ENDPOINTS["DASHBOARD"])
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Dashboard should load within 2 seconds
        assert response_time < 2.0, f"Dashboard too slow: {response_time:.2f}s"
        
        # Response should be valid (500 might be OK for missing templates)
        assert response.status_code in [200, 500]


class TestConcurrentPerformance:
    """Test performance under concurrent load."""
    
    def test_concurrent_health_checks(self, test_client):
        """Test multiple concurrent health check requests."""
        def make_health_request():
            start_time = time.time()
            response = test_client.get(API_ENDPOINTS["CELERY_HEALTH"])
            end_time = time.time()
            return {
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code in [200, 503]
            }
        
        # Run 10 concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_health_request) for _ in range(10)]
            results = [future.result() for future in as_completed(futures)]
        
        # All requests should complete
        assert len(results) == 10, "Not all concurrent requests completed"
        
        # All should be successful
        successful_requests = [r for r in results if r['success']]
        assert len(successful_requests) >= 8, "Too many failed concurrent requests"
        
        # Average response time should be reasonable
        response_times = [r['response_time'] for r in successful_requests]
        avg_response_time = statistics.mean(response_times)
        assert avg_response_time < 2.0, f"Average concurrent response time too slow: {avg_response_time:.2f}s"
    
    def test_concurrent_search_requests(self, test_client, sample_search_request):
        """Test multiple concurrent search requests."""
        def make_search_request():
            start_time = time.time()
            response = test_client.post(
                API_ENDPOINTS["SEARCH_PRODUCT"],
                json=sample_search_request
            )
            end_time = time.time()
            return {
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code in [200, 201, 422]
            }
        
        # Run 5 concurrent search requests (fewer due to potentially higher load)
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_search_request) for _ in range(5)]
            results = [future.result() for future in as_completed(futures)]
        
        # All requests should complete
        assert len(results) == 5, "Not all concurrent search requests completed"
        
        # Most should be successful
        successful_requests = [r for r in results if r['success']]
        assert len(successful_requests) >= 3, "Too many failed concurrent search requests"
        
        # Response times should be reasonable
        response_times = [r['response_time'] for r in successful_requests]
        if response_times:
            max_response_time = max(response_times)
            assert max_response_time < 10.0, f"Max concurrent search response time too slow: {max_response_time:.2f}s"


@pytest.mark.asyncio
class TestAsyncPerformance:
    """Test asynchronous performance characteristics."""
    
    async def test_async_endpoint_performance(self, async_client, sample_search_request):
        """Test async endpoint performance."""
        start_time = time.time()
        
        response = await async_client.post(
            API_ENDPOINTS["SEARCH_PRODUCT"],
            json=sample_search_request
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Async endpoints should be reasonably fast
        assert response_time < 5.0, f"Async endpoint too slow: {response_time:.2f}s"
        
        # Response should be valid
        assert response.status_code in [200, 201, 422]
    
    async def test_concurrent_async_requests(self, async_client):
        """Test concurrent async requests."""
        async def make_async_request():
            start_time = time.time()
            response = await async_client.get(API_ENDPOINTS["CELERY_HEALTH"])
            end_time = time.time()
            return {
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code in [200, 503]
            }
        
        # Run 10 concurrent async requests
        tasks = [make_async_request() for _ in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        successful_results = [r for r in results if isinstance(r, dict) and r.get('success')]
        
        # Should have some successful results
        assert len(successful_results) >= 5, "Too few successful async requests"
        
        # Response times should be reasonable
        response_times = [r['response_time'] for r in successful_results]
        if response_times:
            avg_response_time = statistics.mean(response_times)
            assert avg_response_time < 3.0, f"Average async response time too slow: {avg_response_time:.2f}s"


class TestMemoryPerformance:
    """Test memory usage characteristics."""
    
    def test_dependency_injection_memory_overhead(self, test_client):
        """Test that dependency injection doesn't cause excessive memory overhead."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Make multiple requests to trigger dependency injection
        for _ in range(50):
            test_client.get(API_ENDPOINTS["CELERY_HEALTH"])
        
        # Get final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 50MB for 50 requests)
        assert memory_increase < 50, f"Excessive memory usage: {memory_increase:.2f}MB increase"
    
    def test_no_memory_leaks_in_dependencies(self, test_client):
        """Test that dependency injection doesn't cause memory leaks."""
        import gc
        
        # Force garbage collection
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # Make many requests
        for _ in range(100):
            test_client.get(API_ENDPOINTS["TEST_SIMPLE"])
        
        # Force garbage collection again
        gc.collect()
        final_objects = len(gc.get_objects())
        
        # Object count shouldn't grow excessively
        object_increase = final_objects - initial_objects
        assert object_increase < 1000, f"Potential memory leak: {object_increase} new objects"


class TestThroughputPerformance:
    """Test system throughput characteristics."""
    
    def test_requests_per_second_baseline(self, test_client):
        """Test baseline requests per second for simple endpoints."""
        start_time = time.time()
        request_count = 0
        
        # Make requests for 5 seconds
        while time.time() - start_time < 5.0:
            response = test_client.get(API_ENDPOINTS["TEST_SIMPLE"])
            if response.status_code in [200, 404]:  # Count successful responses
                request_count += 1
        
        elapsed_time = time.time() - start_time
        requests_per_second = request_count / elapsed_time
        
        # Should handle at least 10 requests per second for simple endpoints
        assert requests_per_second >= 10, f"Low throughput: {requests_per_second:.2f} req/s"
    
    def test_complex_endpoint_throughput(self, test_client, sample_search_request):
        """Test throughput for more complex endpoints."""
        start_time = time.time()
        request_count = 0
        
        # Make requests for 10 seconds (longer for complex endpoints)
        while time.time() - start_time < 10.0:
            response = test_client.post(
                API_ENDPOINTS["SEARCH_PRODUCT"],
                json=sample_search_request
            )
            if response.status_code in [200, 201, 422]:  # Count valid responses
                request_count += 1
        
        elapsed_time = time.time() - start_time
        requests_per_second = request_count / elapsed_time
        
        # Should handle at least 2 requests per second for complex endpoints
        assert requests_per_second >= 2, f"Low complex endpoint throughput: {requests_per_second:.2f} req/s"


class TestDependencyInjectionOverhead:
    """Test the overhead introduced by dependency injection."""
    
    def test_dependency_resolution_time(self, test_client):
        """Test that dependency resolution doesn't add significant overhead."""
        # Test a simple endpoint that uses dependency injection
        times = []
        
        for _ in range(20):
            start_time = time.time()
            response = test_client.get(API_ENDPOINTS["CELERY_HEALTH"])
            end_time = time.time()
            
            if response.status_code in [200, 503]:
                times.append(end_time - start_time)
        
        # Should have some successful measurements
        assert len(times) >= 10, "Not enough successful requests for timing"
        
        # Average time should be very low for simple endpoints
        avg_time = statistics.mean(times)
        assert avg_time < 0.5, f"Dependency injection overhead too high: {avg_time:.3f}s"
        
        # Times should be consistent (low standard deviation)
        if len(times) > 1:
            std_dev = statistics.stdev(times)
            assert std_dev < 0.2, f"Inconsistent response times: {std_dev:.3f}s std dev"
