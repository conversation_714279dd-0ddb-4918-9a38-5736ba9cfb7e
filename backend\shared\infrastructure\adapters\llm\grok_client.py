"""
Grok LLM API 客戶端
提供與 Grok API 的通信接口
"""

import os
import json
import requests
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import time

from backend.shared.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class GrokRequest:
    """Grok API 請求資料結構"""
    prompt: str
    temperature: float = 0.1
    max_tokens: int = 1000
    top_p: float = 0.9
    # 注意：Grok API 不支援 frequency_penalty 和 presence_penalty


@dataclass
class GrokResponse:
    """Grok API 回應資料結構"""
    success: bool
    content: str
    usage: Dict[str, Any]
    error: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None


class GrokClient:
    """
    Grok LLM API 客戶端 - 單例模式
    處理與 Grok API 的所有通信
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GrokClient, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化 Grok 客戶端"""
        # 避免重複初始化
        if self._initialized:
            return
            
        self.logger = LoggerManager().get_logger("GrokClient")
        
        # 載入設定
        self.api_key = os.getenv('GROK_API_KEY')
        self.api_url = os.getenv('GROK_API_URL', 'https://api.x.ai/v1')
        self.model = os.getenv('GROK_MODEL', 'grok-3-mini')
        self.timeout = int(os.getenv('GROK_TIMEOUT', '60'))
        self.max_retries = int(os.getenv('GROK_MAX_RETRIES', '3'))
        self.enabled = os.getenv('GROK_PARSING_ENABLED', 'true').lower() == 'true'
        
        # 驗證設定
        self._validate_config()
        
        # 設定 headers
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'EmailParser/1.0'
        }
        
        self.logger.info(f"Grok 客戶端已初始化 - 模型: {self.model}, 啟用: {self.enabled}")
        self._initialized = True
    
    def _validate_config(self):
        """驗證 Grok 設定"""
        if not self.api_key:
            self.logger.warning("GROK_API_KEY 環境變數未設定，Grok 服務將不可用")
            self.enabled = False
            return
        
        if not self.api_url:
            self.logger.warning("GROK_API_URL 環境變數未設定，Grok 服務將不可用")
            self.enabled = False
            return
        
        if not self.model:
            self.logger.warning("GROK_MODEL 環境變數未設定，Grok 服務將不可用")
            self.enabled = False
            return
        
        self.logger.info("Grok 設定驗證通過")
    
    def is_enabled(self) -> bool:
        """檢查 Grok 服務是否啟用"""
        return self.enabled
    
    def send_request(self, request: GrokRequest) -> GrokResponse:
        """
        發送請求到 Grok API
        
        Args:
            request: Grok 請求物件
            
        Returns:
            Grok 回應物件
        """
        if not self.enabled:
            return GrokResponse(
                success=False,
                content="",
                usage={},
                error="Grok 服務未啟用"
            )
        
        for attempt in range(self.max_retries):
            try:
                # 提取請求目的（從 prompt 中）
                request_purpose = "未知"
                if hasattr(request, 'prompt') and request.prompt:
                    if "廠商識別" in request.prompt:
                        request_purpose = "廠商識別"
                    elif "解析方式分類" in request.prompt:
                        request_purpose = "解析方式分類"
                    elif "產品資訊提取" in request.prompt:
                        request_purpose = "產品資訊提取"
                    elif "MO" in request.prompt or "LOT" in request.prompt:
                        request_purpose = "MO/LOT識別"
                    elif "良率" in request.prompt or "yield" in request.prompt:
                        request_purpose = "良率提取"
                    elif "驗證" in request.prompt:
                        request_purpose = "結果驗證"
                    elif "分析" in request.prompt:
                        request_purpose = "內容分析"

                self.logger.info(f"發送 Grok 請求 - {request_purpose} (嘗試 {attempt + 1}/{self.max_retries})")
                
                # 構建請求資料
                payload = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "user",
                            "content": request.prompt
                        }
                    ],
                    "temperature": request.temperature,
                    "max_tokens": request.max_tokens,
                    "top_p": request.top_p
                    # 注意：Grok API 不支援 frequency_penalty 和 presence_penalty
                }
                
                # 發送請求
                response = requests.post(
                    f"{self.api_url}/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=self.timeout
                )
                
                # 檢查回應狀態
                if response.status_code == 200:
                    response_data = response.json()
                    self.logger.info(f"Grok API 回應狀態: 200, 有 {len(response_data.get('choices', []))} 個選項")
                    
                    # 提取回應內容
                    content = ""
                    usage = {}
                    
                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        choice = response_data["choices"][0]
                        if "message" in choice:
                            message = choice["message"]
                            # 嘗試提取 content 或 reasoning_content
                            content = message.get("content", "")
                            if not content and "reasoning_content" in message:
                                content = message["reasoning_content"]
                            
                            if not content:
                                self.logger.warning(f"回應內容為空，可能是 token 限制: {choice}")
                        else:
                            self.logger.error(f"回應結構異常: {choice}")
                    else:
                        self.logger.error(f"回應中沒有 choices: {response_data}")
                    
                    if "usage" in response_data:
                        usage = response_data["usage"]
                    
                    self.logger.info(f"Grok 請求成功 - 回應長度: {len(content)}")
                    
                    return GrokResponse(
                        success=True,
                        content=content,
                        usage=usage,
                        raw_response=response_data
                    )
                
                else:
                    error_msg = f"Grok API 錯誤: {response.status_code} - {response.text}"
                    self.logger.error(error_msg)
                    
                    # 如果是 rate limit 錯誤，等待後重試
                    if response.status_code == 429:
                        wait_time = 2 ** attempt
                        self.logger.warning(f"Rate limit 錯誤，等待 {wait_time} 秒後重試")
                        time.sleep(wait_time)
                        continue
                    
                    # 其他錯誤
                    if attempt == self.max_retries - 1:
                        return GrokResponse(
                            success=False,
                            content="",
                            usage={},
                            error=error_msg
                        )
                
            except requests.exceptions.Timeout:
                error_msg = f"Grok API 請求超時 (嘗試 {attempt + 1}/{self.max_retries})"
                self.logger.error(error_msg)
                
                if attempt == self.max_retries - 1:
                    return GrokResponse(
                        success=False,
                        content="",
                        usage={},
                        error="請求超時"
                    )
                
                # 等待後重試
                time.sleep(2 ** attempt)
                
            except requests.exceptions.RequestException as e:
                error_msg = f"Grok API 請求錯誤: {str(e)}"
                self.logger.error(error_msg)
                
                if attempt == self.max_retries - 1:
                    return GrokResponse(
                        success=False,
                        content="",
                        usage={},
                        error=error_msg
                    )
                
                # 等待後重試
                time.sleep(2 ** attempt)
                
            except Exception as e:
                error_msg = f"Grok API 未知錯誤: {str(e)}"
                self.logger.error(error_msg)
                
                if attempt == self.max_retries - 1:
                    return GrokResponse(
                        success=False,
                        content="",
                        usage={},
                        error=error_msg
                    )
                
                # 等待後重試
                time.sleep(2 ** attempt)
        
        return GrokResponse(
            success=False,
            content="",
            usage={},
            error="所有重試都失敗"
        )
    
    def test_connection(self) -> bool:
        """
        測試與 Grok API 的連接
        
        Returns:
            bool: 連接是否成功
        """
        try:
            test_request = GrokRequest(
                prompt="Hello, please respond with 'OK' to test the connection.",
                temperature=0.1,
                max_tokens=50
            )
            
            response = self.send_request(test_request)
            
            if response.success and "OK" in response.content.upper():
                self.logger.info("Grok API 連接測試成功")
                return True
            else:
                self.logger.error(f"Grok API 連接測試失敗: {response.error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Grok API 連接測試異常: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        獲取 Grok 客戶端狀態
        
        Returns:
            Dict: 狀態資訊
        """
        return {
            "enabled": self.enabled,
            "model": self.model,
            "api_url": self.api_url,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "api_key_configured": bool(self.api_key)
        }