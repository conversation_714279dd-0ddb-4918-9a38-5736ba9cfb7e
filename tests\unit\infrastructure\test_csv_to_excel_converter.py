"""
CSV/Excel 轉換器測試模組 - TDD 先行開發

基於真實測試檔案 KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv 
對照 VBA Device2BinControl 函數的完整功能實作測試
"""

import pytest
import pandas as pd
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch

from backend.shared.infrastructure.adapters.excel.csv_to_excel_converter import (
    CsvToExcelConverter,
    BinAnalysisResult,
    StatisticsResult,
    ConversionResult,
    ExcelProcessingError
)


class TestCsvToExcelConverter:
    """CSV/Excel 轉換器完整測試套件 - 對照 VBA Device2BinControl 功能"""
    
    @pytest.fixture
    def sample_test_data(self):
        """建立模擬實際測試資料"""
        return {
            'header_data': {
                'test_program': 'GMT_G2726AC_AX2_F1_01ENG04 (QC) No Version#',
                'lot_id': 'F2550176A_EQC1',
                'operator': 'OP2A',
                'computer': 'ASLX-06',
                'date': '05/23/25 02:33:15'
            },
            'device_data': [
                {
                    'Index_Time': 8682.045898,
                    'Site_No': 1,
                    'Bin#': 1,
                    'slotNo': 10,
                    'Temperature': 26.0225372,
                    'Test_Item_1': 170,  # 通過測試
                    'Test_Item_2': 85,   # 通過測試
                    'Test_Item_3': 192   # 失敗測試
                },
                {
                    'Index_Time': 8661.087891,
                    'Site_No': 1,
                    'Bin#': 1,
                    'slotNo': 12,
                    'Temperature': 26.0044327,
                    'Test_Item_1': 170,  # 通過測試
                    'Test_Item_2': 85,   # 通過測試
                    'Test_Item_3': 190   # 通過測試
                }
            ],
            'test_limits': {
                'Test_Item_1': {'min': 160, 'max': 180},
                'Test_Item_2': {'min': 80, 'max': 90},
                'Test_Item_3': {'min': 185, 'max': 195}
            }
        }
    
    @pytest.fixture
    def temp_csv_file(self, sample_test_data):
        """建立臨時 CSV 測試檔案"""
        temp_dir = tempfile.mkdtemp()
        csv_path = Path(temp_dir) / "test_data.csv"
        
        # 建立符合實際格式的 CSV 檔案，包含 Serial# 和 Bin# 標頭
        header_rows = [
            "Spreadsheet,Format," + ",".join([""] * 100),
            f"Test Program:,{sample_test_data['header_data']['test_program']}," + ",".join([""] * 100),
            f"Lot ID:,{sample_test_data['header_data']['lot_id']}," + ",".join([""] * 100),
            f"Operator:,{sample_test_data['header_data']['operator']}," + ",".join([""] * 100),
            f"Computer:,{sample_test_data['header_data']['computer']}," + ",".join([""] * 100),
            f"Date:,{sample_test_data['header_data']['date']}," + ",".join([""] * 100),
            ",,," + ",".join([f"1.01.{i:02d}" for i in range(1, 21)]),  # 測試項目標頭
            ",,,Index_Time,Site_No,Bin#,slotNo,Temperature,Test_Item_1,Test_Item_2,Test_Item_3",
            ",,,none,none,20,20,20,20,20,20,none,0",  # 限制值行
            "Serial#,Bin#,Time (mS),Index_Time,Site_No,Temperature,Test_Item_1,Test_Item_2,Test_Item_3"  # 實際資料標頭
        ]
        
        # 建立資料行
        data_rows = []
        for i, device in enumerate(sample_test_data['device_data'], 1):
            row = f"{i},{device['Bin#']},{device['Index_Time']},{device['Index_Time']},{device['Site_No']},{device['Temperature']},{device['Test_Item_1']},{device['Test_Item_2']},{device['Test_Item_3']}"
            data_rows.append(row)
        
        all_rows = header_rows + data_rows
        
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(all_rows))
        
        yield csv_path
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def converter(self):
        """建立 CSV 轉換器實例"""
        return CsvToExcelConverter()

    def test_converter_initialization(self, converter):
        """測試轉換器初始化"""
        # 測試基本屬性
        assert converter is not None
        assert hasattr(converter, 'max_devices')
        assert hasattr(converter, 'max_test_items')
        assert converter.max_devices > 0
        assert converter.max_test_items > 0

    def test_csv_file_validation_success(self, converter, temp_csv_file):
        """測試 CSV 檔案格式驗證 - 成功案例"""
        # 測試有效的 CSV 檔案
        result = converter.validate_csv_format(temp_csv_file)
        assert result is True

    def test_csv_file_validation_failure(self, converter):
        """測試 CSV 檔案格式驗證 - 失敗案例"""
        # 測試無效檔案
        with pytest.raises(ExcelProcessingError):
            converter.validate_csv_format("nonexistent_file.csv")
        
        # 測試空檔案
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        temp_file.close()
        
        with pytest.raises(ExcelProcessingError):
            converter.validate_csv_format(temp_file.name)

    def test_header_data_extraction(self, converter, temp_csv_file, sample_test_data):
        """測試表頭資料提取功能"""
        header_data = converter.extract_header_data(temp_csv_file)
        
        expected = sample_test_data['header_data']
        assert header_data['test_program'] == expected['test_program']
        assert header_data['lot_id'] == expected['lot_id']
        assert header_data['operator'] == expected['operator']
        assert header_data['computer'] == expected['computer']
        assert header_data['date'] == expected['date']

    def test_test_limits_extraction(self, converter, temp_csv_file):
        """測試項目限制值提取"""
        limits = converter.extract_test_limits(temp_csv_file)
        
        # 檢查是否正確識別測試項目
        assert 'Test_Item_1' in limits
        assert 'Test_Item_2' in limits
        assert 'Test_Item_3' in limits
        
        # 檢查限制值結構
        for item_name, limit in limits.items():
            assert 'min' in limit
            assert 'max' in limit
            assert isinstance(limit['min'], (int, float))
            assert isinstance(limit['max'], (int, float))

    def test_device_data_parsing(self, converter, temp_csv_file, sample_test_data):
        """測試設備資料解析"""
        devices = converter.parse_device_data(temp_csv_file)
        
        # 檢查設備數量
        expected_count = len(sample_test_data['device_data'])
        assert len(devices) == expected_count
        
        # 檢查第一個設備資料
        first_device = devices[0]
        expected_first = sample_test_data['device_data'][0]
        
        assert first_device['Index_Time'] == expected_first['Index_Time']
        assert first_device['Site_No'] == expected_first['Site_No']
        assert first_device['Bin#'] == expected_first['Bin#']
        assert first_device['slotNo'] == expected_first['slotNo']

    def test_bin_analysis_calculation(self, converter, sample_test_data):
        """測試 Bin 分析計算邏輯 - 對照 VBA 邏輯"""
        devices = sample_test_data['device_data']
        limits = sample_test_data['test_limits']
        
        result = converter.calculate_bin_analysis(devices, limits)
        
        # 檢查基本統計
        assert isinstance(result, BinAnalysisResult)
        assert result.total_devices == len(devices)
        assert result.pass_devices >= 0
        assert result.fail_devices >= 0
        assert result.total_devices == result.pass_devices + result.fail_devices
        
        # 檢查良率計算
        expected_yield = (result.pass_devices / result.total_devices) * 100
        assert abs(result.yield_percentage - expected_yield) < 0.01
        
        # 檢查 Bin 分布
        assert isinstance(result.bin_distribution, dict)
        total_bin_count = sum(result.bin_distribution.values())
        assert total_bin_count == result.total_devices

    def test_bin_classification_logic(self, converter):
        """測試 Bin 分類邏輯 - 對照 VBA myDeviceBinN 陣列邏輯"""
        # 測試全通過的設備 (應該是 Bin 1)
        device_pass = {
            'Test_Item_1': 170,  # 在 160-180 範圍內
            'Test_Item_2': 85,   # 在 80-90 範圍內
            'Test_Item_3': 190   # 在 185-195 範圍內
        }
        limits = {
            'Test_Item_1': {'min': 160, 'max': 180},
            'Test_Item_2': {'min': 80, 'max': 90},
            'Test_Item_3': {'min': 185, 'max': 195}
        }
        
        bin_number = converter.classify_device_bin(device_pass, limits)
        assert bin_number == 1  # All Pass
        
        # 測試失敗的設備 (應該是 Bin > 1)
        device_fail = {
            'Test_Item_1': 200,  # 超出最大值
            'Test_Item_2': 85,   # 正常
            'Test_Item_3': 190   # 正常
        }
        
        bin_number_fail = converter.classify_device_bin(device_fail, limits)
        assert bin_number_fail > 1  # 失敗 Bin

    def test_statistics_calculation(self, converter, sample_test_data):
        """測試統計計算功能"""
        devices = sample_test_data['device_data']
        
        stats = converter.calculate_statistics(devices)
        
        assert isinstance(stats, StatisticsResult)
        assert stats.total_devices == len(devices)
        assert stats.avg_test_time > 0
        assert stats.min_test_time <= stats.avg_test_time <= stats.max_test_time
        assert len(stats.site_statistics) > 0

    def test_site_analysis(self, converter, sample_test_data):
        """測試多 Site 分析功能 - 對照 VBA mySiteBinArray 邏輯"""
        devices = sample_test_data['device_data']
        
        site_stats = converter.analyze_sites(devices)
        
        # 檢查 Site 統計結構
        assert isinstance(site_stats, dict)
        
        # 檢查每個 Site 的統計
        for site_num, stats in site_stats.items():
            assert 'total_devices' in stats
            assert 'bin_distribution' in stats
            assert 'yield_percentage' in stats
            assert stats['total_devices'] > 0
            assert 0 <= stats['yield_percentage'] <= 100

    def test_excel_workbook_creation(self, converter, temp_csv_file):
        """測試 Excel 工作簿建立"""
        output_dir = tempfile.mkdtemp()
        output_path = Path(output_dir) / "test_output.xlsx"
        
        result = converter.convert_csv_to_excel(temp_csv_file, str(output_path))
        
        # 檢查檔案是否建立
        assert output_path.exists()
        assert result.success is True
        assert result.output_file == str(output_path)
        
        shutil.rmtree(output_dir)

    def test_summary_worksheet_creation(self, converter, sample_test_data):
        """測試 Summary 工作表建立 - 對照 VBA Summary Sheet 邏輯"""
        # 模擬 Bin 分析結果
        bin_result = BinAnalysisResult(
            total_devices=10,
            pass_devices=8,
            fail_devices=2,
            yield_percentage=80.0,
            bin_distribution={1: 8, 2: 1, 3: 1},
            failed_items=['Test_Item_1', 'Test_Item_3']
        )
        
        summary_data = converter.create_summary_worksheet_data(bin_result)
        
        # 檢查 Summary 資料結構
        assert 'headers' in summary_data
        assert 'bin_rows' in summary_data
        assert 'statistics' in summary_data
        
        # 檢查統計資料
        stats = summary_data['statistics']
        assert stats['total'] == 10
        assert stats['pass'] == 8
        assert stats['fail'] == 2
        assert stats['yield'] == '80.0%'

    def test_worksheet_reordering(self, converter):
        """測試工作表重新排序 - 對照 VBA QAData 移到前面的邏輯"""
        # 模擬工作表名稱清單
        worksheet_names = ['Datalog', 'Sheet1', 'QAData', 'Summary']
        
        reordered = converter.reorder_worksheets(worksheet_names)
        
        # QAData 應該移到前面 (但在 Summary 之後)
        qa_index = reordered.index('QAData')
        datalog_index = reordered.index('Datalog')
        assert qa_index < datalog_index

    def test_chinese_content_support(self, converter):
        """測試中文內容支援"""
        # 測試中文檔名
        chinese_filename = "測試檔案_F123456.csv"
        result = converter.validate_filename(chinese_filename)
        assert result is True
        
        # 測試中文測試項目名稱
        chinese_limits = {
            '電壓測試': {'min': 3.0, 'max': 3.6},
            '電流測試': {'min': 0.8, 'max': 1.2},
            '溫度測試': {'min': 20, 'max': 30}
        }
        
        result = converter.validate_test_limits(chinese_limits)
        assert result is True

    def test_error_handling_invalid_data(self, converter):
        """測試錯誤處理 - 無效資料"""
        # 測試空的設備資料
        with pytest.raises(ExcelProcessingError):
            converter.calculate_bin_analysis([], {})
        
        # 測試無效的限制值
        invalid_limits = {
            'Test_Item_1': {'min': 200, 'max': 100}  # min > max
        }
        
        with pytest.raises(ExcelProcessingError):
            converter.validate_test_limits(invalid_limits)

    def test_performance_with_large_dataset(self, converter):
        """測試大數據集效能 - 對照 VBA 的效能最佳化"""
        # 建立大型測試資料集 (1000 設備)
        large_dataset = []
        for i in range(1000):
            device = {
                'Index_Time': 8000 + i,
                'Site_No': (i % 4) + 1,
                'Bin#': 1 if i % 10 != 0 else 2,  # 90% pass rate
                'slotNo': i + 10,
                'Test_Item_1': 170 + (i % 10),
                'Test_Item_2': 85 + (i % 5),
                'Test_Item_3': 190 + (i % 8)
            }
            large_dataset.append(device)
        
        limits = {
            'Test_Item_1': {'min': 160, 'max': 180},
            'Test_Item_2': {'min': 80, 'max': 90},
            'Test_Item_3': {'min': 185, 'max': 195}
        }
        
        # 執行分析 - 應該在合理時間內完成
        import time
        start_time = time.time()
        result = converter.calculate_bin_analysis(large_dataset, limits)
        end_time = time.time()
        
        # 檢查效能 - 應該少於 5 秒
        processing_time = end_time - start_time
        assert processing_time < 5.0
        
        # 檢查結果正確性
        assert result.total_devices == 1000
        assert result.pass_devices > 800  # 預期高良率

    def test_complete_conversion_workflow(self, converter, temp_csv_file):
        """測試完整轉換工作流程 - 端到端測試"""
        output_dir = tempfile.mkdtemp()
        output_path = Path(output_dir) / "complete_test.xlsx"
        
        # 執行完整轉換
        result = converter.convert_csv_to_excel(
            csv_file_path=str(temp_csv_file),
            output_file_path=str(output_path),
            create_summary=True,
            analyze_sites=True
        )
        
        # 檢查轉換結果
        assert isinstance(result, ConversionResult)
        assert result.success is True
        assert result.output_file == str(output_path)
        assert output_path.exists()
        
        # 檢查包含的分析結果
        assert result.bin_analysis is not None
        assert result.statistics is not None
        assert len(result.worksheets_created) > 0
        
        # 檢查是否包含 Summary 工作表
        assert 'Summary' in result.worksheets_created
        
        shutil.rmtree(output_dir)

    def test_vba_compatibility_verification(self, converter, sample_test_data):
        """測試與 VBA 版本的相容性驗證"""
        # 使用相同的測試資料，驗證結果是否與 VBA 版本一致
        devices = sample_test_data['device_data']
        limits = sample_test_data['test_limits']
        
        result = converter.calculate_bin_analysis(devices, limits)
        
        # 根據範例資料，預期結果：
        # - 2個設備，第一個有1項失敗，第二個全通過
        # - 應該有 1 個 Bin 1 設備，1 個失敗 Bin 設備
        assert result.total_devices == 2
        
        # 檢查 Bin 分布符合預期
        bin_1_count = result.bin_distribution.get(1, 0)
        assert bin_1_count >= 1  # 至少有一個全通過設備


# 測試資料模型類別定義
class MockBinAnalysisResult:
    """模擬 BinAnalysisResult 類別用於測試"""
    def __init__(self, total_devices=0, pass_devices=0, fail_devices=0, 
                 yield_percentage=0.0, bin_distribution=None, failed_items=None):
        self.total_devices = total_devices
        self.pass_devices = pass_devices
        self.fail_devices = fail_devices
        self.yield_percentage = yield_percentage
        self.bin_distribution = bin_distribution or {}
        self.failed_items = failed_items or []


class MockStatisticsResult:
    """模擬 StatisticsResult 類別用於測試"""
    def __init__(self, total_devices=0, avg_test_time=0.0, 
                 min_test_time=0.0, max_test_time=0.0, site_statistics=None):
        self.total_devices = total_devices
        self.avg_test_time = avg_test_time
        self.min_test_time = min_test_time
        self.max_test_time = max_test_time
        self.site_statistics = site_statistics or {}


class MockConversionResult:
    """模擬 ConversionResult 類別用於測試"""
    def __init__(self, success=False, output_file="", bin_analysis=None, 
                 statistics=None, worksheets_created=None):
        self.success = success
        self.output_file = output_file
        self.bin_analysis = bin_analysis
        self.statistics = statistics
        self.worksheets_created = worksheets_created or []


class MockExcelProcessingError(Exception):
    """模擬 ExcelProcessingError 異常用於測試"""
    pass


# 將 Mock 類別注入到測試中
@pytest.fixture(autouse=True)
def mock_classes(monkeypatch):
    """自動注入 Mock 類別到測試環境"""
    monkeypatch.setattr('tests.unit.infrastructure.test_csv_to_excel_converter.BinAnalysisResult', MockBinAnalysisResult)
    monkeypatch.setattr('tests.unit.infrastructure.test_csv_to_excel_converter.StatisticsResult', MockStatisticsResult)
    monkeypatch.setattr('tests.unit.infrastructure.test_csv_to_excel_converter.ConversionResult', MockConversionResult)
    monkeypatch.setattr('tests.unit.infrastructure.test_csv_to_excel_converter.ExcelProcessingError', MockExcelProcessingError)