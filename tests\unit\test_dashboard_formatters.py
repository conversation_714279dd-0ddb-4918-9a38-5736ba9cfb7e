"""
測試儀表板資料格式化工具模組
"""

import pytest
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, Any

from backend.monitoring.utils.dashboard_formatters import (
    format_metrics_data,
    format_alert_data,
    format_trend_data,
    format_dashboard_response,
    format_json_safe,
    validate_metrics_data,
    validate_alert_data
)


@dataclass
class MockMetrics:
    """模擬指標資料類別"""
    cpu_percent: float
    memory_usage: int
    timestamp: datetime
    vendor_counts: Dict[str, int]


@dataclass
class MockAlert:
    """模擬告警資料類別"""
    id: str
    level: str
    title: str
    message: str
    triggered_at: datetime
    status: str = "active"


class TestMetricsDataFormatting:
    """測試指標資料格式化"""
    
    def test_format_metrics_data_dict(self):
        """測試格式化字典形式的指標資料"""
        metrics = {
            "cpu_percent": 75.5,
            "memory_usage": 1024000000,
            "timestamp": datetime(2023, 12, 25, 15, 30, 45),
            "vendor_counts": {"GTK": 5, "JCET": 3}
        }
        
        result = format_metrics_data(metrics)
        
        assert "cpuPercent" in result
        assert result["cpuPercent"] == 75.5
        assert "memoryUsage" in result
        assert "timestamp" in result
        assert "vendorCounts" in result
        assert "_metadata" in result
    
    def test_format_metrics_data_dataclass(self):
        """測試格式化 dataclass 形式的指標資料"""
        metrics = MockMetrics(
            cpu_percent=80.0,
            memory_usage=2048000000,
            timestamp=datetime(2023, 12, 25, 16, 0, 0),
            vendor_counts={"ETD": 2, "LINGSEN": 4}
        )
        
        result = format_metrics_data(metrics)
        
        assert "cpuPercent" in result
        assert result["cpuPercent"] == 80.0
        assert "memoryUsage" in result
        assert "vendorCounts" in result
        assert "_metadata" in result
        assert result["_metadata"]["data_type"] == "MockMetrics"
    
    def test_format_metrics_data_without_metadata(self):
        """測試不包含元資料的格式化"""
        metrics = {"cpu_percent": 60.0}
        
        result = format_metrics_data(metrics, include_metadata=False)
        
        assert "cpuPercent" in result
        assert "_metadata" not in result
    
    def test_format_metrics_data_invalid_input(self):
        """測試無效輸入的處理"""
        result = format_metrics_data("invalid_input")
        
        assert "rawData" in result
        assert result["rawData"] == "invalid_input"


class TestAlertDataFormatting:
    """測試告警資料格式化"""
    
    def test_format_alert_data_single_dict(self):
        """測試格式化單個字典形式的告警"""
        alert = {
            "id": "alert_001",
            "alert_level": "warning",
            "title": "High CPU Usage",
            "message": "CPU usage is above 80%",
            "triggered_at": datetime(2023, 12, 25, 15, 30, 45),
            "status": "active"
        }
        
        result = format_alert_data(alert)
        
        assert result["count"] == 1
        assert len(result["alerts"]) == 1
        
        formatted_alert = result["alerts"][0]
        assert formatted_alert["id"] == "alert_001"
        assert formatted_alert["level"] == "warning"
        assert formatted_alert["title"] == "High CPU Usage"
        assert formatted_alert["isActive"] is True
        assert "ageMinutes" in formatted_alert
        assert "severity" in formatted_alert
    
    def test_format_alert_data_list(self):
        """測試格式化告警列表"""
        alerts = [
            {
                "id": "alert_001",
                "level": "warning",
                "title": "High CPU",
                "message": "CPU high",
                "triggered_at": datetime(2023, 12, 25, 15, 30, 45)
            },
            {
                "id": "alert_002",
                "level": "critical",
                "title": "Memory Full",
                "message": "Memory full",
                "triggered_at": datetime(2023, 12, 25, 16, 0, 0)
            }
        ]
        
        result = format_alert_data(alerts, group_by_level=True, include_summary=True)
        
        assert result["count"] == 2
        assert len(result["alerts"]) == 2
        assert "groupedByLevel" in result
        assert "warning" in result["groupedByLevel"]
        assert "critical" in result["groupedByLevel"]
        assert "summary" in result
        assert result["summary"]["total"] == 2
        assert result["summary"]["criticalCount"] == 1
        assert result["summary"]["warningCount"] == 1
    
    def test_format_alert_data_dataclass(self):
        """測試格式化 dataclass 形式的告警"""
        alert = MockAlert(
            id="alert_003",
            level="error",
            title="Database Error",
            message="Database connection failed",
            triggered_at=datetime(2023, 12, 25, 17, 0, 0)
        )
        
        result = format_alert_data(alert)
        
        assert result["count"] == 1
        formatted_alert = result["alerts"][0]
        assert formatted_alert["id"] == "alert_003"
        assert formatted_alert["level"] == "error"
        assert formatted_alert["severity"] == 3  # error level
    
    def test_format_alert_data_empty_list(self):
        """測試格式化空告警列表"""
        result = format_alert_data([])
        
        assert result["count"] == 0
        assert len(result["alerts"]) == 0
        assert result["summary"]["total"] == 0


class TestTrendDataFormatting:
    """測試趨勢資料格式化"""
    
    def test_format_trend_data_list(self):
        """測試格式化趨勢資料列表"""
        trend_data = [
            {
                "timestamp": datetime(2023, 12, 25, 15, 0, 0),
                "value": 75.5,
                "metric_type": "cpu_usage"
            },
            {
                "timestamp": datetime(2023, 12, 25, 15, 5, 0),
                "value": 80.2,
                "metric_type": "cpu_usage"
            },
            {
                "timestamp": datetime(2023, 12, 25, 15, 10, 0),
                "value": 72.8,
                "metric_type": "cpu_usage"
            }
        ]
        
        result = format_trend_data(trend_data, time_range="15m", include_statistics=True)
        
        assert result["timeRange"] == "15m"
        assert result["dataPoints"] == 3
        assert len(result["data"]) == 3
        assert "statistics" in result
        
        stats = result["statistics"]
        assert stats["count"] == 3
        assert stats["min"] == 72.8
        assert stats["max"] == 80.2
        assert "avg" in stats
        assert "trend" in stats
    
    def test_format_trend_data_dict_with_data_key(self):
        """測試格式化包含 data 鍵的字典"""
        trend_data = {
            "data": [
                {"timestamp": datetime(2023, 12, 25, 15, 0, 0), "value": 50.0},
                {"timestamp": datetime(2023, 12, 25, 15, 5, 0), "value": 60.0}
            ]
        }
        
        result = format_trend_data(trend_data)
        
        assert result["dataPoints"] == 2
        assert len(result["data"]) == 2
    
    def test_format_trend_data_max_data_points(self):
        """測試資料點數量限制"""
        # 創建 150 個資料點
        trend_data = [
            {
                "timestamp": datetime(2023, 12, 25, 15, i % 60, 0),
                "value": i * 0.5
            }
            for i in range(150)
        ]
        
        result = format_trend_data(trend_data, max_data_points=100)
        
        # 應該被採樣到 100 個點以內
        assert result["dataPoints"] <= 100
        assert len(result["data"]) <= 100
    
    def test_format_trend_data_empty(self):
        """測試格式化空趨勢資料"""
        result = format_trend_data([])
        
        assert result["dataPoints"] == 0
        assert len(result["data"]) == 0
        assert result["startTime"] is None
        assert result["endTime"] is None


class TestUtilityFormatting:
    """測試通用格式化函數"""
    
    def test_format_dashboard_response_success(self):
        """測試格式化成功回應"""
        data = {"metric": "value"}
        
        result = format_dashboard_response(
            data,
            status="success",
            message="操作成功",
            include_timestamp=True,
            include_metadata=True
        )
        
        assert result["status"] == "success"
        assert result["data"] == data
        assert result["message"] == "操作成功"
        assert "timestamp" in result
        assert "metadata" in result
    
    def test_format_dashboard_response_minimal(self):
        """測試最小格式化回應"""
        data = "simple_data"
        
        result = format_dashboard_response(
            data,
            include_timestamp=False,
            include_metadata=False
        )
        
        assert result["status"] == "success"
        assert result["data"] == data
        assert "timestamp" not in result
        assert "metadata" not in result
    
    def test_format_json_safe_normal(self):
        """測試安全 JSON 格式化 - 正常情況"""
        data = {"key": "value", "number": 123}
        
        result = format_json_safe(data, indent=2)
        
        parsed = json.loads(result)
        assert parsed["key"] == "value"
        assert parsed["number"] == 123
    
    def test_format_json_safe_datetime(self):
        """測試安全 JSON 格式化 - 包含 datetime"""
        data = {
            "timestamp": datetime(2023, 12, 25, 15, 30, 45),
            "duration": timedelta(hours=1, minutes=30)
        }
        
        result = format_json_safe(data)
        
        parsed = json.loads(result)
        assert "2023-12-25T15:30:45" in parsed["timestamp"]
        assert parsed["duration"] == 5400.0  # 1.5 hours in seconds
    
    def test_format_json_safe_unserializable(self):
        """測試安全 JSON 格式化 - 不可序列化物件"""
        class CustomObject:
            def __init__(self):
                self.value = "test"
        
        data = {"custom": CustomObject()}
        
        result = format_json_safe(data)
        
        # 應該不會拋出異常，而是返回字串表示
        assert isinstance(result, str)
        parsed = json.loads(result)
        assert "custom" in parsed


class TestDataValidation:
    """測試資料驗證函數"""
    
    def test_validate_metrics_data_valid_dict(self):
        """測試驗證有效的指標資料字典"""
        data = {
            "cpu_percent": 75.5,
            "memory_usage": 1024,
            "status": "running"
        }
        
        assert validate_metrics_data(data) is True
    
    def test_validate_metrics_data_valid_dataclass(self):
        """測試驗證有效的指標資料 dataclass"""
        data = MockMetrics(
            cpu_percent=80.0,
            memory_usage=2048,
            timestamp=datetime.now(),
            vendor_counts={}
        )
        
        assert validate_metrics_data(data) is True
    
    def test_validate_metrics_data_invalid(self):
        """測試驗證無效的指標資料"""
        # 沒有數值欄位的字典
        data = {"status": "running", "name": "test"}
        
        assert validate_metrics_data(data) is False
    
    def test_validate_metrics_data_none(self):
        """測試驗證 None 值"""
        assert validate_metrics_data(None) is False
    
    def test_validate_alert_data_valid_dict(self):
        """測試驗證有效的告警資料字典"""
        data = {
            "level": "warning",
            "message": "Test alert",
            "title": "Test"
        }
        
        assert validate_alert_data(data) is True
    
    def test_validate_alert_data_valid_list(self):
        """測試驗證有效的告警資料列表"""
        data = [
            {"level": "warning", "message": "Alert 1"},
            {"level": "error", "message": "Alert 2"}
        ]
        
        assert validate_alert_data(data) is True
    
    def test_validate_alert_data_empty_list(self):
        """測試驗證空告警列表"""
        assert validate_alert_data([]) is True
    
    def test_validate_alert_data_invalid_dict(self):
        """測試驗證無效的告警資料字典"""
        data = {"title": "Test"}  # 缺少必要欄位
        
        assert validate_alert_data(data) is False
    
    def test_validate_alert_data_valid_dataclass(self):
        """測試驗證有效的告警資料 dataclass"""
        data = MockAlert(
            id="test",
            level="warning",
            title="Test",
            message="Test message",
            triggered_at=datetime.now()
        )
        
        assert validate_alert_data(data) is True
    
    def test_validate_alert_data_none(self):
        """測試驗證 None 值"""
        assert validate_alert_data(None) is False


if __name__ == "__main__":
    pytest.main([__file__])