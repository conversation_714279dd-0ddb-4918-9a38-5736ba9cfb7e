#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
遠端路徑處理工具模組
處理雙反斜線（\\）網路路徑的複製和管理功能
"""

import os
import shutil
import zipfile
import tempfile
from pathlib import Path
from typing import Tuple, Optional, List
from loguru import logger
import time


class RemotePathProcessor:
    """遠端路徑處理器"""
    
    def __init__(self, temp_base_dir: str = r"d:\temp"):
        """
        初始化遠端路徑處理器
        
        Args:
            temp_base_dir: 臨時目錄基礎路徑
        """
        self.temp_base_dir = Path(temp_base_dir)
        self.temp_base_dir.mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def is_remote_path(path: str) -> bool:
        """
        檢測是否為遠端路徑（雙反斜線開頭）
        
        Args:
            path: 要檢測的路徑
            
        Returns:
            bool: 是否為遠端路徑
        """
        return path.startswith(r"\\") or path.startswith("//")
    
    @staticmethod
    def is_archive_file(path: str) -> bool:
        """
        檢測是否為壓縮檔
        
        Args:
            path: 要檢測的路徑
            
        Returns:
            bool: 是否為壓縮檔
        """
        archive_extensions = {'.zip', '.7z', '.rar', '.tar', '.gz', '.tgz', '.bz2'}
        return Path(path).suffix.lower() in archive_extensions
    
    def copy_remote_path_to_temp(self, remote_path: str) -> Tuple[str, bool]:
        """
        複製遠端路徑到臨時目錄
        
        Args:
            remote_path: 遠端路徑
            
        Returns:
            Tuple[str, bool]: (本地路徑, 是否成功)
        """
        try:
            logger.info(f"開始複製遠端路徑: {remote_path}")
            
            # 檢查遠端路徑是否存在
            if not os.path.exists(remote_path):
                logger.error(f"遠端路徑不存在: {remote_path}")
                return "", False
            
            # 決定目標路徑
            if os.path.isfile(remote_path):
                # 如果是檔案
                if self.is_archive_file(remote_path):
                    # 壓縮檔：複製到 d:\temp\壓縮檔檔名\壓縮檔
                    archive_name = Path(remote_path).stem
                    target_dir = self.temp_base_dir / archive_name
                    target_dir.mkdir(parents=True, exist_ok=True)
                    target_path = target_dir / Path(remote_path).name
                else:
                    # 一般檔案：直接複製到 d:\temp
                    target_path = self.temp_base_dir / Path(remote_path).name
            else:
                # 如果是資料夾：複製到 d:\temp\資料夾名稱
                folder_name = Path(remote_path).name
                target_path = self.temp_base_dir / folder_name
            
            # 執行複製
            if os.path.isfile(remote_path):
                # 複製檔案
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(remote_path, target_path)
                logger.info(f"檔案複製完成: {remote_path} -> {target_path}")
            else:
                # 複製資料夾
                if target_path.exists():
                    shutil.rmtree(target_path)
                shutil.copytree(remote_path, target_path)
                logger.info(f"資料夾複製完成: {remote_path} -> {target_path}")
            
            return str(target_path), True
            
        except Exception as e:
            logger.error(f"複製遠端路徑失敗: {e}")
            return "", False
    
    def create_download_archive(self, source_path: str, archive_name: str = None) -> Tuple[str, bool]:
        """
        創建下載用的壓縮檔
        
        Args:
            source_path: 要壓縮的源路徑
            archive_name: 壓縮檔名稱（不含副檔名）
            
        Returns:
            Tuple[str, bool]: (壓縮檔路徑, 是否成功)
        """
        try:
            source_path = Path(source_path)
            
            if not source_path.exists():
                logger.error(f"源路徑不存在: {source_path}")
                return "", False
            
            # 決定壓縮檔名稱
            if archive_name is None:
                archive_name = f"{source_path.name}_processed_{int(time.time())}"
            
            # 創建壓縮檔路徑
            archive_path = self.temp_base_dir / f"{archive_name}.zip"
            
            # 創建 ZIP 壓縮檔
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                if source_path.is_file():
                    # 單個檔案
                    zipf.write(source_path, source_path.name)
                else:
                    # 資料夾
                    for file_path in source_path.rglob('*'):
                        if file_path.is_file():
                            # 計算相對路徑
                            relative_path = file_path.relative_to(source_path)
                            zipf.write(file_path, relative_path)
            
            logger.info(f"壓縮檔創建完成: {archive_path}")
            return str(archive_path), True
            
        except Exception as e:
            logger.error(f"創建壓縮檔失敗: {e}")
            return "", False
    
    def cleanup_temp_files(self, paths: List[str]) -> bool:
        """
        清理臨時文件
        
        Args:
            paths: 要清理的路徑列表
            
        Returns:
            bool: 是否成功
        """
        try:
            for path in paths:
                path_obj = Path(path)
                if path_obj.exists():
                    if path_obj.is_file():
                        path_obj.unlink()
                        logger.info(f"已刪除檔案: {path}")
                    else:
                        shutil.rmtree(path_obj)
                        logger.info(f"已刪除資料夾: {path}")
            
            return True
            
        except Exception as e:
            logger.error(f"清理臨時文件失敗: {e}")
            return False
    
    def get_temp_path_for_upload(self, filename: str) -> str:
        """
        為上傳的檔案獲取臨時路徑
        
        Args:
            filename: 檔案名稱
            
        Returns:
            str: 臨時路徑
        """
        if self.is_archive_file(filename):
            # 壓縮檔：創建 d:\temp\檔名\檔案 結構
            archive_name = Path(filename).stem
            target_dir = self.temp_base_dir / archive_name
            target_dir.mkdir(parents=True, exist_ok=True)
            return str(target_dir / filename)
        else:
            # 一般檔案：直接放在 d:\temp
            return str(self.temp_base_dir / filename)


# 全局實例
remote_processor = RemotePathProcessor()


def process_input_path(input_path: str) -> Tuple[str, str, bool]:
    """
    處理輸入路徑（遠端路徑或本地路徑）
    
    Args:
        input_path: 輸入路徑
        
    Returns:
        Tuple[str, str, bool]: (處理後的本地路徑, 處理類型, 是否成功)
        處理類型: 'remote_copy', 'local', 'error'
    """
    if remote_processor.is_remote_path(input_path):
        # 遠端路徑處理
        local_path, success = remote_processor.copy_remote_path_to_temp(input_path)
        if success:
            return local_path, 'remote_copy', True
        else:
            return input_path, 'error', False
    else:
        # 本地路徑直接返回
        return input_path, 'local', True


def create_result_archive(source_path: str, archive_name: str = None) -> Tuple[str, bool]:
    """
    創建結果壓縮檔
    
    Args:
        source_path: 源路徑
        archive_name: 壓縮檔名稱
        
    Returns:
        Tuple[str, bool]: (壓縮檔路徑, 是否成功)
    """
    return remote_processor.create_download_archive(source_path, archive_name)


def cleanup_paths(paths: List[str]) -> bool:
    """
    清理路徑列表
    
    Args:
        paths: 路徑列表
        
    Returns:
        bool: 是否成功
    """
    return remote_processor.cleanup_temp_files(paths)
