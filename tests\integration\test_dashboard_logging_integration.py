"""
測試儀表板日誌和錯誤處理系統整合
"""

import pytest
import asyncio
import time
from datetime import datetime
from unittest.mock import patch

from backend.monitoring.utils.dashboard_helpers import (
    dashboard_monitor,
    dashboard_async_monitor,
    DashboardPerformanceTracker,
    get_dashboard_logger_manager,
    get_dashboard_performance_logger
)
from backend.monitoring.utils.dashboard_formatters import (
    format_metrics_data,
    format_alert_data,
    format_dashboard_response
)


class TestDashboardLoggingIntegration:
    """測試儀表板日誌系統整合"""
    
    def test_logger_manager_initialization(self):
        """測試日誌管理器初始化"""
        logger_manager = get_dashboard_logger_manager()
        
        assert logger_manager is not None
        assert logger_manager.log_dir.name == "dashboard_monitoring"
        
        # 測試獲取日誌器
        logger = logger_manager.get_logger("test_logger")
        assert logger is not None
    
    def test_performance_logger_initialization(self):
        """測試效能日誌器初始化"""
        performance_logger = get_dashboard_performance_logger()
        
        assert performance_logger is not None
        
        # 測試效能計時器
        with performance_logger.timer("test_operation"):
            time.sleep(0.1)
    
    def test_error_handling_with_logging(self):
        """測試錯誤處理與日誌記錄整合"""
        @dashboard_monitor(
            fallback_value="fallback_result",
            operation_name="test_integration_operation",
            suppress_exceptions=True
        )
        def test_function_with_error():
            raise ValueError("Integration test error")
        
        result = test_function_with_error()
        assert result == "fallback_result"
    
    @pytest.mark.asyncio
    async def test_async_error_handling_with_logging(self):
        """測試非同步錯誤處理與日誌記錄整合"""
        @dashboard_async_monitor(
            fallback_value="async_fallback_result",
            operation_name="test_async_integration_operation",
            suppress_exceptions=True
        )
        async def test_async_function_with_error():
            await asyncio.sleep(0.1)
            raise ValueError("Async integration test error")
        
        result = await test_async_function_with_error()
        assert result == "async_fallback_result"
    
    def test_performance_tracking_integration(self):
        """測試效能追蹤整合"""
        tracker = DashboardPerformanceTracker("integration_test_operation")
        
        with tracker:
            time.sleep(0.1)
            tracker.add_metric("test_metric", "test_value")
        
        assert tracker.metrics["operation"] == "integration_test_operation"
        assert tracker.metrics["test_metric"] == "test_value"
        assert tracker.metrics["duration_seconds"] > 0
    
    def test_formatters_with_error_handling(self):
        """測試格式化器與錯誤處理整合"""
        # 測試正常資料格式化
        valid_metrics = {
            "cpu_percent": 75.5,
            "memory_usage": 1024000000,
            "timestamp": datetime.now()
        }
        
        result = format_metrics_data(valid_metrics)
        assert "cpuPercent" in result
        assert "_metadata" in result
        
        # 測試無效資料的錯誤處理
        invalid_metrics = None
        result = format_metrics_data(invalid_metrics)
        assert isinstance(result, dict)  # 應該返回預設值而不是拋出異常
    
    def test_alert_formatting_integration(self):
        """測試告警格式化整合"""
        alerts = [
            {
                "id": "alert_001",
                "level": "warning",
                "title": "Integration Test Alert",
                "message": "This is a test alert",
                "triggered_at": datetime.now(),
                "status": "active"
            }
        ]
        
        result = format_alert_data(alerts, include_summary=True, group_by_level=True)
        
        assert result["count"] == 1
        assert "summary" in result
        assert "groupedByLevel" in result
        assert "warning" in result["groupedByLevel"]
    
    def test_dashboard_response_formatting(self):
        """測試儀表板回應格式化"""
        test_data = {
            "metrics": {"cpu": 75.5, "memory": 80.2},
            "alerts": [{"level": "warning", "message": "Test alert"}]
        }
        
        response = format_dashboard_response(
            test_data,
            status="success",
            message="Integration test successful",
            include_timestamp=True,
            include_metadata=True
        )
        
        assert response["status"] == "success"
        assert response["data"] == test_data
        assert response["message"] == "Integration test successful"
        assert "timestamp" in response
        assert "metadata" in response
    
    def test_end_to_end_monitoring_workflow(self):
        """測試端到端監控工作流程"""
        @dashboard_monitor(
            fallback_value={"error": "operation_failed"},
            operation_name="end_to_end_test",
            log_performance=True,
            slow_threshold_seconds=0.05
        )
        def monitored_operation():
            # 模擬一些處理
            time.sleep(0.1)  # 超過慢操作閾值
            
            # 返回一些指標資料
            return {
                "processed_items": 100,
                "processing_time": 0.1,
                "timestamp": datetime.now()
            }
        
        # 執行被監控的操作
        result = monitored_operation()
        
        # 驗證結果
        assert "processed_items" in result
        assert result["processed_items"] == 100
        
        # 格式化結果
        formatted_result = format_metrics_data(result)
        assert "processedItems" in formatted_result
        
        # 創建儀表板回應
        dashboard_response = format_dashboard_response(
            formatted_result,
            status="success",
            message="End-to-end test completed"
        )
        
        assert dashboard_response["status"] == "success"
        assert "processedItems" in dashboard_response["data"]


class TestErrorIsolation:
    """測試錯誤隔離功能"""
    
    def test_formatter_error_isolation(self):
        """測試格式化器錯誤隔離"""
        # 即使格式化失敗，也不應該影響主要功能
        class UnserializableObject:
            def __str__(self):
                raise Exception("Cannot convert to string")
        
        problematic_data = {
            "normal_field": "normal_value",
            "problematic_field": UnserializableObject()
        }
        
        # 格式化器應該處理錯誤並返回可用的結果
        result = format_metrics_data(problematic_data)
        assert isinstance(result, dict)
        assert "normalField" in result
    
    def test_logging_error_isolation(self):
        """測試日誌錯誤隔離"""
        @dashboard_monitor(
            fallback_value="isolated_result",
            suppress_exceptions=True
        )
        def function_with_logging_issues():
            # 即使日誌系統有問題，主要功能也應該正常工作
            return "main_result"
        
        result = function_with_logging_issues()
        assert result == "main_result"
    
    @pytest.mark.asyncio
    async def test_async_error_isolation(self):
        """測試非同步錯誤隔離"""
        @dashboard_async_monitor(
            fallback_value="async_isolated_result",
            suppress_exceptions=True
        )
        async def async_function_with_issues():
            await asyncio.sleep(0.01)
            raise RuntimeError("Simulated async error")
        
        result = await async_function_with_issues()
        assert result == "async_isolated_result"


if __name__ == "__main__":
    pytest.main([__file__])