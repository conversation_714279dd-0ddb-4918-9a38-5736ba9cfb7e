"""
增強任務排程器 - 精簡版
符合 CLAUDE.md 500行限制要求
"""

import os
import sys
import json
import uuid
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from contextlib import contextmanager

# APScheduler imports
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR

# 導入核心模組
from .enhanced_scheduler_core import (
    ScheduledTaskStatus, VendorType, ScheduledTask, SchedulingError,
    GTKSchedulingLogic, VendorClassifier, TaskPriorityCalculator,
    ScheduleCalculator, create_scheduled_task, get_vendor_info
)

# 動態添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
src_path = os.path.join(project_root, 'src')
backend_path = os.path.join(project_root, 'backend')
if src_path not in sys.path:
    sys.path.insert(0, src_path)
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.shared.infrastructure.adapters.notification.line_notification_service import LineNotificationService
from backend.tasks.services.concurrent_task_manager import get_task_manager, TaskPriority


class EnhancedTaskScheduler:
    """
    企業級增強任務排程器 - 精簡版
    
    核心功能：
    - GTK廠商特殊排程邏輯
    - 高精度時間觸發
    - 與現有系統整合
    - 企業級錯誤處理
    """
    
    def __init__(self, 
                 enable_notifications: bool = True,
                 max_workers: int = 4,
                 logger=None):
        """初始化增強任務排程器"""
        self.logger = logger or LoggerManager().get_logger("EnhancedTaskScheduler")
        
        # 初始化排程器
        self._scheduler = BackgroundScheduler(
            jobstores={'default': MemoryJobStore()},
            executors={'default': ThreadPoolExecutor(max_workers)},
            job_defaults={'coalesce': False, 'max_instances': 3}
        )
        
        # 任務追蹤
        self._scheduled_tasks: Dict[str, ScheduledTask] = {}
        self._task_lock = threading.RLock()
        
        # 服務整合
        self._task_manager = get_task_manager()
        
        # 通知服務
        self._notification_service = None
        if enable_notifications:
            try:
                self._notification_service = LineNotificationService()
            except Exception as e:
                self.logger.warning(f"[WARNING] 通知服務初始化失敗: {e}")
        
        # 統計資料
        self._stats = {
            'total_scheduled': 0,
            'total_executed': 0,
            'total_failed': 0,
            'gtk_tasks': 0,
            'immediate_tasks': 0
        }
        
        # 設置事件監聽器
        self._setup_event_listeners()
        
        # 啟動排程器
        self._scheduler.start()
        self.logger.info("[OK] 增強任務排程器已初始化並啟動")
    
    def _setup_event_listeners(self):
        """設置排程器事件監聽器"""
        self._scheduler.add_listener(self._on_job_executed, EVENT_JOB_EXECUTED)
        self._scheduler.add_listener(self._on_job_error, EVENT_JOB_ERROR)
    
    def _on_job_executed(self, event):
        """任務執行成功回調"""
        job_id = event.job_id
        self.logger.info(f"[EXECUTED] 排程任務執行成功: {job_id}")
        self._stats['total_executed'] += 1
    
    def _on_job_error(self, event):
        """任務執行錯誤回調"""
        job_id = event.job_id
        exception = event.exception
        self.logger.error(f"[ERROR] 排程任務執行失敗: {job_id} - {exception}")
        self._stats['total_failed'] += 1
    
    def schedule_email_processing(self,
                                vendor_code: str,
                                email_data: Dict[str, Any],
                                processing_params: Dict[str, Any] = None,
                                received_time: Optional[datetime] = None) -> str:
        """
        排程郵件處理任務
        
        Args:
            vendor_code: 廠商代碼
            email_data: 郵件資料
            processing_params: 處理參數
            received_time: 接收時間
            
        Returns:
            str: 任務ID
        """
        try:
            # 創建排程任務
            scheduled_task = create_scheduled_task(
                vendor_code=vendor_code,
                email_data=email_data,
                processing_params=processing_params or {},
                received_time=received_time
            )
            
            # 存儲任務
            with self._task_lock:
                self._scheduled_tasks[scheduled_task.task_id] = scheduled_task
            
            # 決定處理策略
            if scheduled_task.vendor_type == VendorType.GTK:
                return self._schedule_gtk_task(scheduled_task)
            else:
                return self._schedule_immediate_task(scheduled_task)
                
        except Exception as e:
            self.logger.error(f"[ERROR] 排程郵件處理任務失敗: {e}")
            raise SchedulingError(f"排程失敗: {e}")
    
    def _schedule_gtk_task(self, task: ScheduledTask) -> str:
        """排程GTK任務"""
        try:
            # 添加到APScheduler
            self._scheduler.add_job(
                func=self._execute_scheduled_task,
                args=[task.task_id],
                trigger=DateTrigger(run_date=task.scheduled_time),
                id=task.task_id,
                name=f"GTK_Task_{task.vendor_name}_{task.task_id[:8]}",
                misfire_grace_time=300  # 5分鐘寬限時間
            )
            
            # 更新狀態
            with self._task_lock:
                task.status = ScheduledTaskStatus.SCHEDULED
            
            # 發送通知
            self._send_notification(
                f"📅 GTK任務已排程\n"
                f"廠商: {task.vendor_name}\n"
                f"執行時間: {task.scheduled_time.strftime('%H:%M:%S')}\n"
                f"任務ID: {task.task_id[:8]}"
            )
            
            self._stats['total_scheduled'] += 1
            self._stats['gtk_tasks'] += 1
            
            self.logger.info(
                f"[SCHEDULED] GTK任務已排程: {task.task_id} "
                f"(執行時間: {task.scheduled_time})"
            )
            
            return task.task_id
            
        except Exception as e:
            self.logger.error(f"[ERROR] GTK任務排程失敗: {e}")
            raise
    
    def _schedule_immediate_task(self, task: ScheduledTask) -> str:
        """排程立即執行任務"""
        try:
            # 直接提交到任務管理器
            task_id = self._task_manager.submit_task(
                task_type='email_processing',
                task_params={
                    'email_data': task.email_data,
                    **task.processing_params
                },
                priority=task.priority
            )
            
            # 更新狀態
            with self._task_lock:
                task.status = ScheduledTaskStatus.EXECUTING
                task.executed_time = datetime.now()
            
            self._stats['total_scheduled'] += 1
            self._stats['immediate_tasks'] += 1
            
            self.logger.info(f"[IMMEDIATE] 立即任務已提交: {task.task_id}")
            
            return task.task_id
            
        except Exception as e:
            self.logger.error(f"[ERROR] 立即任務提交失敗: {e}")
            raise
    
    def _execute_scheduled_task(self, task_id: str):
        """執行排程任務"""
        try:
            with self._task_lock:
                task = self._scheduled_tasks.get(task_id)
                if not task:
                    self.logger.warning(f"[WARNING] 找不到排程任務: {task_id}")
                    return
                
                # 更新狀態
                task.status = ScheduledTaskStatus.EXECUTING
                task.executed_time = datetime.now()
            
            # 提交到任務管理器執行
            concurrent_task_id = self._task_manager.submit_task(
                task_type='email_processing',
                task_params={
                    'email_data': task.email_data,
                    **task.processing_params
                },
                priority=task.priority
            )
            
            # 發送執行通知
            self._send_notification(
                f"🚀 排程任務開始執行\n"
                f"廠商: {task.vendor_name}\n"
                f"任務ID: {task_id[:8]}\n"
                f"執行ID: {concurrent_task_id[:8]}"
            )
            
            self.logger.info(
                f"[EXECUTING] 排程任務開始執行: {task_id} -> {concurrent_task_id}"
            )
            
        except Exception as e:
            self._handle_task_error(task_id, str(e))
    
    def _handle_task_error(self, task_id: str, error_message: str):
        """處理任務錯誤"""
        with self._task_lock:
            task = self._scheduled_tasks.get(task_id)
            if task:
                task.status = ScheduledTaskStatus.FAILED
                task.error_message = error_message
                task.completion_time = datetime.now()
        
        # 發送錯誤通知
        self._send_notification(
            f"❌ 排程任務執行失敗\n"
            f"任務ID: {task_id[:8]}\n"
            f"錯誤: {error_message}"
        )
        
        self.logger.error(f"[FAILED] 排程任務失敗: {task_id} - {error_message}")
    
    def _send_notification(self, message: str):
        """發送通知"""
        if self._notification_service:
            try:
                self._notification_service.send_notification(message)
            except Exception as e:
                self.logger.warning(f"[WARNING] 通知發送失敗: {e}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務狀態"""
        with self._task_lock:
            task = self._scheduled_tasks.get(task_id)
            if not task:
                return None
            return task.to_dict()
    
    def list_scheduled_tasks(self, 
                           status: Optional[str] = None,
                           vendor_type: Optional[str] = None,
                           limit: int = 50) -> List[Dict[str, Any]]:
        """列出排程任務"""
        with self._task_lock:
            tasks = list(self._scheduled_tasks.values())
        
        # 狀態過濾
        if status:
            status_enum = ScheduledTaskStatus(status)
            tasks = [t for t in tasks if t.status == status_enum]
        
        # 廠商類型過濾
        if vendor_type:
            vendor_enum = VendorType(vendor_type.upper())
            tasks = [t for t in tasks if t.vendor_type == vendor_enum]
        
        # 排序和限制
        tasks.sort(key=lambda x: x.created_time, reverse=True)
        tasks = tasks[:limit]
        
        return [task.to_dict() for task in tasks]
    
    def cancel_scheduled_task(self, task_id: str) -> bool:
        """取消排程任務"""
        try:
            with self._task_lock:
                task = self._scheduled_tasks.get(task_id)
                if not task:
                    return False
                
                if task.status in [ScheduledTaskStatus.EXECUTING, 
                                 ScheduledTaskStatus.COMPLETED,
                                 ScheduledTaskStatus.FAILED]:
                    return False  # 無法取消已執行或已完成的任務
                
                # 從排程器移除
                if task.status == ScheduledTaskStatus.SCHEDULED:
                    self._scheduler.remove_job(task_id)
                
                # 更新狀態
                task.status = ScheduledTaskStatus.CANCELLED
                task.completion_time = datetime.now()
            
            self.logger.info(f"[CANCELLED] 排程任務已取消: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] 取消排程任務失敗: {task_id} - {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計資料"""
        with self._task_lock:
            active_tasks = len([t for t in self._scheduled_tasks.values() 
                              if t.status in [ScheduledTaskStatus.PENDING, 
                                            ScheduledTaskStatus.SCHEDULED,
                                            ScheduledTaskStatus.EXECUTING]])
        
        return {
            **self._stats,
            'active_tasks': active_tasks,
            'scheduler_running': self._scheduler.running,
            'pending_jobs': len(self._scheduler.get_jobs())
        }
    
    def shutdown(self, wait: bool = True):
        """關閉排程器"""
        self.logger.info("[SHUTDOWN] 正在關閉增強任務排程器...")
        
        try:
            if self._scheduler.running:
                self._scheduler.shutdown(wait=wait)
        except Exception as e:
            self.logger.error(f"[ERROR] 關閉排程器時出錯: {e}")
        
        self.logger.info("[OK] 增強任務排程器已關閉")


# 全域實例（單例模式）
_scheduler_instance = None
_scheduler_lock = threading.Lock()

def get_enhanced_scheduler(**kwargs) -> EnhancedTaskScheduler:
    """獲取增強任務排程器單例實例"""
    global _scheduler_instance
    
    if _scheduler_instance is None:
        with _scheduler_lock:
            if _scheduler_instance is None:
                _scheduler_instance = EnhancedTaskScheduler(**kwargs)
    
    return _scheduler_instance


# 便利函數
def schedule_gtk_email(vendor_code: str, email_data: Dict[str, Any], **kwargs) -> str:
    """排程GTK郵件處理的便利函數"""
    return get_enhanced_scheduler().schedule_email_processing(
        vendor_code=vendor_code,
        email_data=email_data,
        **kwargs
    )


def schedule_immediate_email(vendor_code: str, email_data: Dict[str, Any], **kwargs) -> str:
    """立即處理郵件的便利函數"""
    return get_enhanced_scheduler().schedule_email_processing(
        vendor_code=vendor_code,
        email_data=email_data,
        **kwargs
    )