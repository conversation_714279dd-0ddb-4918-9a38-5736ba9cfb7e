"""Phase 4: 真實 API 端點測試

這個模組測試真實的 API 端點，使用實際的 FastAPI 應用和依賴注入。
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch
import asyncio

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 導入實際的應用和依賴
try:
    from frontend.api.ft_eqc_api import app
    from frontend.api.dependencies import (
        get_api_state, get_staging_service, get_processing_service
    )
    from fastapi import FastAPI
    from httpx import AsyncClient
    
    APP_AVAILABLE = True
except ImportError as e:
    print(f"Warning: App not available: {e}")
    APP_AVAILABLE = False
    app = None


class TestRealAPIEndpoints:
    """測試真實的 API 端點"""
    
    @pytest.fixture
    def test_app(self):
        """提供測試用的 FastAPI 應用"""
        if not APP_AVAILABLE:
            pytest.skip("App not available")
        
        # 使用真實的應用，但覆蓋依賴以避免副作用
        test_app = app
        
        # 創建 Mock 依賴
        mock_api_state = Mock()
        mock_api_state.increment_request_count = Mock()
        mock_api_state.get_request_count = Mock(return_value=100)
        mock_api_state.is_healthy = Mock(return_value=True)
        
        mock_staging_service = Mock()
        mock_staging_service.service_id = "test_staging"
        mock_staging_service.is_healthy = Mock(return_value=True)
        mock_staging_service.get_status = Mock(return_value={"status": "ready"})
        
        mock_processing_service = Mock()
        mock_processing_service.service_id = "test_processing"
        mock_processing_service.is_healthy = Mock(return_value=True)
        mock_processing_service.get_status = Mock(return_value={"status": "ready"})
        
        # 覆蓋依賴
        test_app.dependency_overrides[get_api_state] = lambda: mock_api_state
        test_app.dependency_overrides[get_staging_service] = lambda: mock_staging_service
        test_app.dependency_overrides[get_processing_service] = lambda: mock_processing_service
        
        yield test_app
        
        # 清理
        test_app.dependency_overrides.clear()
    
    @pytest.mark.asyncio
    async def test_root_endpoint(self, test_app):
        """測試根端點"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get("/")
            
            assert response.status_code == 200
            data = response.json()
            
            # 驗證響應結構
            assert "message" in data
            assert "version" in data
            assert "endpoints" in data
            
            # 驗證基本內容
            assert isinstance(data["endpoints"], dict)
            assert len(data["endpoints"]) > 0
            
            print(f"✅ Root endpoint test passed - version: {data.get('version', 'N/A')}")
    
    @pytest.mark.asyncio
    async def test_health_endpoint(self, test_app):
        """測試健康檢查端點"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get("/api/health")
            
            # 健康檢查應該返回 200 或 503
            assert response.status_code in [200, 503]
            
            data = response.json()
            assert "status" in data
            
            print(f"✅ Health endpoint test passed - status: {data.get('status', 'N/A')}")
    
    @pytest.mark.asyncio
    async def test_get_7zip_path_endpoint(self, test_app):
        """測試 7zip 路徑端點"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get("/api/get_7zip_path")
            
            assert response.status_code == 200
            data = response.json()
            
            # 驗證響應結構
            assert "status" in data
            assert "path" in data
            
            print(f"✅ 7zip path endpoint test passed - status: {data.get('status', 'N/A')}")
    
    @pytest.mark.asyncio
    async def test_test_7zip_path_endpoint(self, test_app):
        """測試 7zip 路徑驗證端點"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 測試空路徑
            response = await client.post("/api/test_7zip_path", json={"path": ""})
            
            assert response.status_code == 200
            data = response.json()
            
            # 空路徑應該返回錯誤
            assert "status" in data
            assert data["status"] == "error"
            
            print(f"✅ 7zip path test endpoint passed - message: {data.get('message', 'N/A')}")
    
    @pytest.mark.asyncio
    async def test_dependency_injection_in_endpoints(self, test_app):
        """測試端點中的依賴注入"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 調用使用依賴注入的端點
            response = await client.get("/api/health")
            
            assert response.status_code in [200, 503]
            
            # 驗證 Mock 依賴被調用
            # 注意：由於我們使用的是真實應用，Mock 可能不會被調用
            # 這裡主要驗證端點能正常響應
            
            print("✅ Dependency injection in endpoints test passed")


class TestRealAPIPerformance:
    """測試真實 API 的性能"""
    
    @pytest.fixture
    def test_app(self):
        """提供測試用的 FastAPI 應用"""
        if not APP_AVAILABLE:
            pytest.skip("App not available")
        return app
    
    @pytest.mark.asyncio
    async def test_root_endpoint_performance(self, test_app):
        """測試根端點性能"""
        import time
        
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 預熱
            await client.get("/")
            
            # 測量響應時間
            start_time = time.time()
            response = await client.get("/")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # 真實的基準：應該在 1 秒內
            assert response_time < 1.0, f"Root endpoint too slow: {response_time:.3f}s"
            assert response.status_code == 200
            
            print(f"✅ Root endpoint performance: {response_time:.3f}s")
    
    @pytest.mark.asyncio
    async def test_health_endpoint_performance(self, test_app):
        """測試健康檢查端點性能"""
        import time
        
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 預熱
            await client.get("/api/health")
            
            # 測量響應時間
            start_time = time.time()
            response = await client.get("/api/health")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # 健康檢查應該很快
            assert response_time < 0.5, f"Health endpoint too slow: {response_time:.3f}s"
            assert response.status_code in [200, 503]
            
            print(f"✅ Health endpoint performance: {response_time:.3f}s")
    
    @pytest.mark.asyncio
    async def test_concurrent_requests_performance(self, test_app):
        """測試並發請求性能"""
        import time
        
        async def make_request(client):
            start_time = time.time()
            response = await client.get("/")
            end_time = time.time()
            return {
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code == 200
            }
        
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 創建 5 個並發請求
            tasks = [make_request(client) for _ in range(5)]
            
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # 過濾出成功的結果
            successful_results = [r for r in results if isinstance(r, dict) and r.get('success')]
            
            # 驗證結果
            assert len(successful_results) >= 3, "At least 60% requests should succeed"
            
            if successful_results:
                avg_response_time = sum(r['response_time'] for r in successful_results) / len(successful_results)
                assert avg_response_time < 2.0, f"Average response time too slow: {avg_response_time:.3f}s"
            
            assert total_time < 5.0, f"Total concurrent time too slow: {total_time:.3f}s"
            
            print(f"✅ Concurrent requests: {len(successful_results)}/5 success, total: {total_time:.3f}s")


class TestRealAPIErrorHandling:
    """測試真實 API 的錯誤處理"""
    
    @pytest.fixture
    def test_app(self):
        """提供測試用的 FastAPI 應用"""
        if not APP_AVAILABLE:
            pytest.skip("App not available")
        return app
    
    @pytest.mark.asyncio
    async def test_404_error_handling(self, test_app):
        """測試 404 錯誤處理"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get("/api/nonexistent")
            
            assert response.status_code == 404
            
            print("✅ 404 error handling test passed")
    
    @pytest.mark.asyncio
    async def test_invalid_json_handling(self, test_app):
        """測試無效 JSON 處理"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 發送無效的 JSON
            response = await client.post(
                "/api/test_7zip_path",
                content="invalid json",
                headers={"Content-Type": "application/json"}
            )
            
            # 應該返回 400 或 422
            assert response.status_code in [400, 422]
            
            print("✅ Invalid JSON handling test passed")
    
    @pytest.mark.asyncio
    async def test_missing_required_fields(self, test_app):
        """測試缺少必需字段的處理"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 發送空的 JSON 對象
            response = await client.post("/api/test_7zip_path", json={})
            
            # 應該正常處理並返回錯誤信息
            assert response.status_code == 200
            
            data = response.json()
            assert data.get("status") == "error"
            
            print("✅ Missing required fields handling test passed")


class TestRealAPICoverage:
    """測試真實 API 的覆蓋率"""
    
    @pytest.fixture
    def test_app(self):
        """提供測試用的 FastAPI 應用"""
        if not APP_AVAILABLE:
            pytest.skip("App not available")
        return app
    
    @pytest.mark.asyncio
    async def test_documented_endpoints_coverage(self, test_app):
        """測試文檔化端點的覆蓋率"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 獲取根端點的端點列表
            response = await client.get("/")
            assert response.status_code == 200
            
            data = response.json()
            if "endpoints" in data:
                endpoints = data["endpoints"]
                
                # 測試一些關鍵端點
                key_endpoints = ["/", "/api/health", "/api/get_7zip_path"]
                
                tested_endpoints = 0
                for endpoint in key_endpoints:
                    try:
                        test_response = await client.get(endpoint)
                        if test_response.status_code in [200, 404, 405, 503]:
                            tested_endpoints += 1
                    except:
                        pass
                
                coverage_rate = tested_endpoints / len(key_endpoints)
                assert coverage_rate >= 0.8, f"Endpoint coverage too low: {coverage_rate:.1%}"
                
                print(f"✅ Endpoint coverage: {coverage_rate:.1%} ({tested_endpoints}/{len(key_endpoints)})")
            else:
                print("⚠️ No endpoints list found in root response")
    
    @pytest.mark.asyncio
    async def test_dependency_injection_coverage(self, test_app):
        """測試依賴注入覆蓋率"""
        # 檢查依賴注入是否在應用中正確配置
        
        # 驗證依賴函數存在
        assert callable(get_api_state), "get_api_state should be callable"
        assert callable(get_staging_service), "get_staging_service should be callable"
        assert callable(get_processing_service), "get_processing_service should be callable"
        
        # 驗證應用有依賴覆蓋機制
        assert hasattr(test_app, 'dependency_overrides'), "App should have dependency_overrides"
        
        print("✅ Dependency injection coverage verified")


class TestRealAPIIntegration:
    """測試真實 API 的集成功能"""
    
    @pytest.fixture
    def test_app(self):
        """提供測試用的 FastAPI 應用"""
        if not APP_AVAILABLE:
            pytest.skip("App not available")
        return app
    
    @pytest.mark.asyncio
    async def test_api_workflow(self, test_app):
        """測試 API 工作流程"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 1. 檢查根端點
            root_response = await client.get("/")
            assert root_response.status_code == 200
            
            # 2. 檢查健康狀態
            health_response = await client.get("/api/health")
            assert health_response.status_code in [200, 503]
            
            # 3. 檢查 7zip 路徑
            path_response = await client.get("/api/get_7zip_path")
            assert path_response.status_code == 200
            
            # 4. 測試 7zip 路徑驗證
            test_response = await client.post("/api/test_7zip_path", json={"path": ""})
            assert test_response.status_code == 200
            
            print("✅ API workflow test completed successfully")
    
    @pytest.mark.asyncio
    async def test_api_consistency(self, test_app):
        """測試 API 一致性"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            # 多次調用同一端點，驗證一致性
            responses = []
            for _ in range(3):
                response = await client.get("/api/health")
                responses.append(response)
            
            # 所有響應應該有相同的狀態碼
            status_codes = [r.status_code for r in responses]
            assert len(set(status_codes)) <= 2, "Status codes should be consistent"  # 允許 200/503 變化
            
            # 所有響應應該有相同的結構
            for response in responses:
                if response.status_code == 200:
                    data = response.json()
                    assert "status" in data, "Response structure should be consistent"
            
            print("✅ API consistency test passed")
