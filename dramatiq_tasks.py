"""
Dramatiq 任務模組 - 根目錄入口點

🎯 功能：
  - 為 Dramatiq Worker 提供任務入口點
  - 導入並暴露所有 Dramatiq 任務
  - 確保 dramatiq CLI 可以正確發現任務

🔧 使用方式：
  dramatiq dramatiq_tasks --processes 4 --threads 8

📋 包含的任務：
  - EQC 工作流程任務
  - 產品搜索任務  
  - CSV 摘要任務
  - 代碼比較任務
  - 健康檢查任務
  - 管道處理任務
  - 解壓縮任務
"""

import os
import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 設置環境變數
os.environ.setdefault('PYTHONPATH', str(project_root))
os.environ.setdefault('USE_MEMORY_BROKER', 'false')  # 預設使用 Redis

# 導入 Dramatiq 配置 (必須在任務導入之前)
import dramatiq_config

# 導入所有 Dramatiq 任務
try:
    # 從 backend.tasks.services.dramatiq_tasks 導入核心任務
    from backend.tasks.services.dramatiq_tasks import (
        process_complete_eqc_workflow_task,
        search_product_task,
        run_csv_summary_task,
        run_code_comparison_task,
        health_check_task,
        create_download_archive_task
    )
    
    # 從 backend.tasks.pipeline_tasks 導入管道任務
    from backend.tasks.pipeline_tasks import (
        process_vendor_files_task,
        pipeline_completion_task
    )
    
    # 從 backend.tasks.archive_pipeline_tasks 導入解壓縮任務
    from backend.tasks.archive_pipeline_tasks import (
        extract_archive_task
    )
    
    print("✅ Dramatiq 任務導入成功")
    print(f"   - EQC 工作流程任務: {process_complete_eqc_workflow_task.actor_name}")
    print(f"   - 產品搜索任務: {search_product_task.actor_name}")
    print(f"   - CSV 摘要任務: {run_csv_summary_task.actor_name}")
    print(f"   - 代碼比較任務: {run_code_comparison_task.actor_name}")
    print(f"   - 健康檢查任務: {health_check_task.actor_name}")
    print(f"   - 管道處理任務: {process_vendor_files_task.actor_name}")
    print(f"   - 管道完成任務: {pipeline_completion_task.actor_name}")
    print(f"   - 解壓縮任務: {extract_archive_task.actor_name}")
    print(f"   - 自動壓縮任務: {create_download_archive_task.actor_name}")
    
except ImportError as e:
    print(f"❌ Dramatiq 任務導入失敗: {e}")
    print("請檢查以下項目：")
    print("1. 虛擬環境是否正確激活")
    print("2. 所有依賴是否已安裝")
    print("3. Python 路徑是否正確設置")
    print("4. 後端模組是否存在")
    raise

# 導出 broker 供 Dramatiq CLI 使用
from dramatiq_config import broker

# 導出所有任務供外部使用
__all__ = [
    'broker',
    'process_complete_eqc_workflow_task',
    'search_product_task', 
    'run_csv_summary_task',
    'run_code_comparison_task',
    'health_check_task',
    'process_vendor_files_task',
    'pipeline_completion_task',
    'extract_archive_task'
]
