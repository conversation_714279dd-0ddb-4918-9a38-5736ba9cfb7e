"""
檔案上傳重複和目錄鎖定修復測試模組

遵循 CLAUDE.md 後端程式碼強制測試要求
"""

import asyncio
import os
import shutil
import tempfile
import time
import uuid
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import pytest
from fastapi import UploadFile

from backend.shared.infrastructure.adapters.file_upload.temp_file_manager import TempFileManager
from backend.shared.infrastructure.adapters.file_upload.archive_extractor import ArchiveExtractor
from backend.shared.infrastructure.adapters.file_upload.upload_processor import UploadProcessor


class TestFileUploadDuplicatePrevention:
    """檔案上傳重複防護測試類"""
    
    def setup_method(self):
        """每個測試方法前的設置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.test_files = []
    
    def teardown_method(self):
        """每個測試方法後的清理"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
            for file_path in self.test_files:
                if Path(file_path).exists():
                    Path(file_path).unlink()
        except Exception:
            pass
    
    @pytest.mark.asyncio
    async def test_prevent_duplicate_upload_same_file(self):
        """測試防止同一檔案重複上傳"""
        # 建立測試檔案
        test_content = b"test content for duplicate prevention"
        mock_file1 = Mock(spec=UploadFile)
        mock_file1.filename = "test.zip"
        mock_file1.size = len(test_content)
        mock_file1.read = AsyncMock(return_value=test_content)
        
        mock_file2 = Mock(spec=UploadFile)
        mock_file2.filename = "test.zip"
        mock_file2.size = len(test_content)
        mock_file2.read = AsyncMock(return_value=test_content)
        
        processor = UploadProcessor()
        
        # 第一次上傳應該成功
        result1 = await processor.process_upload(mock_file1)
        assert result1['success'] is True
        first_upload_path = result1['upload_path']
        
        # 第二次上傳相同檔案應該被檢測為重複
        result2 = await processor.process_upload(mock_file2)
        
        # 驗證重複檢測邏輯
        if processor.has_duplicate_prevention:
            assert result2['success'] is False
            assert "重複" in result2['message'] or "duplicate" in result2['message'].lower()
        else:
            # 如果還沒實現重複檢測，這個測試應該失敗，提醒我們需要實現
            pytest.fail("需要實現檔案上傳重複檢測功能")
        
        # 清理
        if Path(first_upload_path).exists():
            Path(first_upload_path).unlink()
    
    @pytest.mark.asyncio
    async def test_upload_status_locking_mechanism(self):
        """測試上傳狀態鎖定機制"""
        processor = UploadProcessor()
        
        # 檢查是否有上傳狀態管理
        assert hasattr(processor, 'is_uploading') or hasattr(processor, '_upload_lock'), \
            "UploadProcessor 需要實現上傳狀態鎖定機制"
        
        # 模擬同時上傳
        test_content = b"test content"
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "test.zip"
        mock_file.size = len(test_content)
        mock_file.read = AsyncMock(return_value=test_content)
        
        # 啟動第一個上傳任務
        upload_task1 = asyncio.create_task(processor.process_upload(mock_file))
        
        # 立即啟動第二個上傳任務（模擬快速重複點擊）
        upload_task2 = asyncio.create_task(processor.process_upload(mock_file))
        
        result1, result2 = await asyncio.gather(upload_task1, upload_task2, return_exceptions=True)
        
        # 至少其中一個應該被鎖定機制阻止
        success_count = sum(1 for r in [result1, result2] 
                          if isinstance(r, dict) and r.get('success') is True)
        
        assert success_count <= 1, "上傳狀態鎖定機制應該防止同時上傳"


class TestDirectoryCleanupFixes:
    """目錄清理和檔案鎖定修復測試類"""
    
    def setup_method(self):
        """每個測試方法前的設置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.test_dirs = []
    
    def teardown_method(self):
        """每個測試方法後的清理"""
        try:
            for test_dir in self.test_dirs:
                if Path(test_dir).exists():
                    shutil.rmtree(test_dir, ignore_errors=True)
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception:
            pass
    
    def test_delayed_directory_removal(self):
        """測試延遲目錄移[EXCEPT_CHAR]機制"""
        manager = TempFileManager()
        
        # 建立測試目錄
        test_dir = self.temp_dir / "test_extract_dir"
        test_dir.mkdir(parents=True)
        test_file = test_dir / "test_file.txt"
        test_file.write_text("test content")
        
        # 模擬檔案被鎖定的情況
        with test_file.open('r') as locked_file:
            # 嘗試移[EXCEPT_CHAR]目錄（應該會失敗，但有重試機制）
            result = manager.remove_file_or_dir(test_dir)
            
            # 在某些系統上可能會成功，在某些系統上可能會失敗
            # 重點是要有適當的錯誤處理
            assert isinstance(result, bool), "remove_file_or_dir 應該返回布林值"
    
    def test_enhanced_cleanup_with_retry(self):
        """測試強化的清理機制（包含重試）"""
        manager = TempFileManager()
        
        # 建立深層次的目錄結構
        deep_dir = self.temp_dir / "level1" / "level2" / "level3"
        deep_dir.mkdir(parents=True)
        
        # 建立多個檔案
        for i in range(5):
            test_file = deep_dir / f"test_file_{i}.txt"
            test_file.write_text(f"test content {i}")
        
        # 測試清理
        result = manager.remove_file_or_dir(self.temp_dir / "level1")
        
        # 驗證清理結果
        assert isinstance(result, bool), "清理方法應該返回結果狀態"
        
        # 如果清理成功，目錄應該不存在
        if result:
            assert not (self.temp_dir / "level1").exists(), "清理成功後目錄應該不存在"
    
    def test_archive_extraction_process_completion(self):
        """測試壓縮檔解壓縮進程完成狀態"""
        extractor = ArchiveExtractor()
        
        # 建立簡單的測試ZIP檔案
        import zipfile
        test_zip = self.temp_dir / "test.zip"
        with zipfile.ZipFile(test_zip, 'w') as zf:
            zf.writestr("test_file.txt", "test content")
        
        # 執行解壓縮
        try:
            result = extractor.extract_archive(str(test_zip))
            
            if result['success']:
                extract_dir = Path(result['extract_dir'])
                
                # 驗證解壓縮結果
                assert extract_dir.exists(), "解壓縮目錄應該存在"
                assert (extract_dir / "test_file.txt").exists(), "解壓縮的檔案應該存在"
                
                # 測試清理（這應該成功，因為沒有7z進程鎖定）
                cleanup_result = extractor.cleanup_extracted_dir(str(extract_dir))
                assert cleanup_result is True, "清理解壓縮目錄應該成功"
                
        except Exception as e:
            pytest.skip(f"跳過解壓縮測試，因為環境問題: {e}")
    
    @patch('subprocess.run')
    def test_7z_process_completion_and_cleanup(self, mock_subprocess):
        """測試7z進程完成和清理"""
        # 模擬7z命令成功執行
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stderr = ""
        
        extractor = ArchiveExtractor()
        
        # 建立測試7z檔案（模擬）
        test_7z = self.temp_dir / "test.7z"
        test_7z.write_bytes(b"fake 7z content")
        
        # 建立預期的解壓縮目錄
        extract_dir = self.temp_dir / "extract_test"
        extract_dir.mkdir(parents=True)
        (extract_dir / "test_file.txt").write_text("extracted content")
        
        # 模擬解壓縮流程
        try:
            # 這會調用_extract_7z方法
            result = extractor._extract_7z(test_7z, extract_dir)
            
            # 驗證subprocess被正確調用
            mock_subprocess.assert_called_once()
            call_args = mock_subprocess.call_args[0][0]
            assert '7z' in call_args, "應該調用7z命令"
            assert 'x' in call_args, "應該使用解壓縮選項"
            
            # 驗證返回結果
            assert isinstance(result, list), "應該返回檔案列表"
            
        except Exception as e:
            # 如果7z不可用或其他問題，這是預期的
            assert "7zip" in str(e) or "未找到" in str(e), f"預期的7z錯誤: {e}"


class TestUploadStateManagement:
    """上傳狀態管理測試類"""
    
    @pytest.mark.asyncio
    async def test_upload_processor_state_tracking(self):
        """測試上傳處理器狀態追蹤"""
        processor = UploadProcessor()
        
        # 檢查是否有狀態追蹤屬性
        assert hasattr(processor, 'get_upload_state') or hasattr(processor, 'is_uploading'), \
            "UploadProcessor 需要實現狀態追蹤功能"
    
    def test_concurrent_upload_prevention(self):
        """測試並發上傳防護"""
        # 這個測試應該確保同時多個上傳請求時的行為
        processor = UploadProcessor()
        
        # 如果實現了並發防護，應該有相關方法
        has_concurrency_control = (
            hasattr(processor, '_upload_lock') or 
            hasattr(processor, 'max_concurrent_uploads') or
            hasattr(processor, 'active_uploads')
        )
        
        if not has_concurrency_control:
            pytest.fail("需要實現並發上傳控制機制")


if __name__ == "__main__":
    # 執行測試
    pytest.main([__file__, "-v"])