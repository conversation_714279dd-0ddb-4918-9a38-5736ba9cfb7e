"""檔案處理服務與暫存功能整合測試"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

from backend.shared.infrastructure.adapters.processing import FileProcessingService, ProcessingTool
from backend.shared.infrastructure.adapters.file_staging_service import FileStagingService


@pytest.fixture
def temp_source_files():
    """建立臨時來源檔案"""
    temp_dir = tempfile.mkdtemp()
    
    # 建立測試檔案
    test_files = []
    for i in range(2):
        file_path = Path(temp_dir) / f"integration_test_{i}.csv"
        # 建立一些 CSV 內容
        csv_content = f"Product,Lot,Yield\nAAA,LOT{i},95.{i}\nBBB,LOT{i+1},97.{i}"
        file_path.write_text(csv_content)
        test_files.append(file_path)
    
    yield test_files
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def temp_staging_dir():
    """建立臨時暫存目錄"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def processing_service():
    """建立檔案處理服務實例"""
    return FileProcessingService()


@pytest.fixture
def staging_service(temp_staging_dir):
    """建立檔案暫存服務實例"""
    return FileStagingService(
        base_staging_path=temp_staging_dir,
        max_workers=2,
        verify_integrity=True
    )


class TestFileProcessingWithStaging:
    """檔案處理服務與暫存功能整合測試"""
    
    def test_create_task_with_staging(self, processing_service, temp_source_files):
        """測試建立帶有暫存功能的處理任務"""
        product_name = "TEST_PRODUCT"
        
        task_id = processing_service.create_task_with_staging(
            tool=ProcessingTool.CSV_SUMMARY,
            source_files=temp_source_files,
            product_name=product_name,
            preserve_structure=True
        )
        
        assert task_id is not None
        
        # 檢查任務是否正確建立
        task = processing_service.get_task_status(task_id)
        assert task is not None
        assert task.use_staging is True
        assert task.tool == ProcessingTool.CSV_SUMMARY
        assert "多個檔案" in task.input_path
    
    @pytest.mark.asyncio
    async def test_execute_with_staging_success(self, processing_service, temp_source_files, temp_staging_dir):
        """測試成功執行帶有暫存功能的處理任務"""
        product_name = "TEST_PRODUCT"
        
        # 建立任務
        task_id = processing_service.create_task_with_staging(
            tool=ProcessingTool.CSV_SUMMARY,
            source_files=temp_source_files,
            product_name=product_name,
            preserve_structure=True
        )
        
        # 模擬工具執行成功
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            # 設定模擬的子程序
            mock_process = MagicMock()
            mock_process.returncode = 0
            
            # 建立一個 async mock 方法
            async def mock_communicate():
                return (b"Processing completed", b"")
            
            mock_process.communicate = mock_communicate
            mock_subprocess.return_value = mock_process
            
            # 模擬輸出檔案的發現
            with patch.object(processing_service, '_find_output_files') as mock_find_files:
                mock_output_file = Path(temp_staging_dir) / product_name / "summary_output.xlsx"
                mock_output_file.parent.mkdir(parents=True, exist_ok=True)
                mock_output_file.write_text("Mock output file")
                mock_find_files.return_value = [mock_output_file]
                
                # 執行任務
                result = await processing_service.execute_with_staging(
                    task_id=task_id,
                    source_files=temp_source_files,
                    product_name=product_name,
                    preserve_structure=True
                )
        
        # 驗證結果
        assert result.success is True
        assert result.task_id == task_id
        assert len(result.output_files) > 0
        assert result.processing_time > 0
        
        # 檢查任務狀態
        task = processing_service.get_task_status(task_id)
        assert task.status.value == "completed"
        assert task.progress == 100.0
        assert task.staging_task_id is not None
        assert task.staged_path is not None
    
    @pytest.mark.asyncio
    async def test_execute_with_staging_tool_failure(self, processing_service, temp_source_files):
        """測試工具執行失敗的情況"""
        product_name = "TEST_PRODUCT"
        
        # 建立任務
        task_id = processing_service.create_task_with_staging(
            tool=ProcessingTool.CSV_SUMMARY,
            source_files=temp_source_files,
            product_name=product_name,
            preserve_structure=True
        )
        
        # 模擬工具執行失敗
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            # 設定模擬的子程序失敗
            mock_process = MagicMock()
            mock_process.returncode = 1  # 失敗的返回碼
            
            # 建立一個 async mock 方法
            async def mock_communicate():
                return (b"", b"Tool execution failed")
            
            mock_process.communicate = mock_communicate
            mock_subprocess.return_value = mock_process
            
            # 執行任務
            result = await processing_service.execute_with_staging(
                task_id=task_id,
                source_files=temp_source_files,
                product_name=product_name,
                preserve_structure=True
            )
        
        # 驗證結果
        assert result.success is False
        assert result.error_message is not None
        assert "Tool execution failed" in result.error_message or "工具執行失敗" in result.error_message
        
        # 檢查任務狀態
        task = processing_service.get_task_status(task_id)
        assert task.status.value == "failed"
        assert task.error_message is not None
    
    @pytest.mark.asyncio
    async def test_execute_with_staging_staging_failure(self, processing_service, temp_source_files):
        """測試暫存失敗的情況"""
        product_name = "TEST_PRODUCT"
        
        # 建立任務
        task_id = processing_service.create_task_with_staging(
            tool=ProcessingTool.CSV_SUMMARY,
            source_files=temp_source_files,
            product_name=product_name,
            preserve_structure=True
        )
        
        # 模擬磁碟空間不足導致暫存失敗
        with patch('shutil.disk_usage') as mock_disk_usage:
            mock_disk_usage.return_value = MagicMock(free=100)  # 只有100位元組可用
            
            # 執行任務
            result = await processing_service.execute_with_staging(
                task_id=task_id,
                source_files=temp_source_files,
                product_name=product_name,
                preserve_structure=True
            )
        
        # 驗證結果
        assert result.success is False
        assert result.error_message is not None
        assert "磁碟空間不足" in result.error_message
        
        # 檢查任務狀態
        task = processing_service.get_task_status(task_id)
        assert task.status.value == "failed"
    
    def test_create_task_without_staging(self, processing_service):
        """測試建立不使用暫存功能的處理任務"""
        input_path = "/path/to/input"
        
        task_id = processing_service.create_task(
            tool=ProcessingTool.CSV_SUMMARY,
            input_path=input_path,
            use_staging=False
        )
        
        assert task_id is not None
        
        # 檢查任務是否正確建立
        task = processing_service.get_task_status(task_id)
        assert task is not None
        assert task.use_staging is False
        assert task.input_path == input_path
        assert task.staging_task_id is None
        assert task.staged_path is None
    
    @pytest.mark.asyncio
    async def test_task_progress_with_staging(self, processing_service, temp_source_files):
        """測試帶有暫存功能的任務進度追蹤"""
        product_name = "TEST_PRODUCT"
        
        # 建立任務
        task_id = processing_service.create_task_with_staging(
            tool=ProcessingTool.CODE_COMPARISON,
            source_files=temp_source_files,
            product_name=product_name,
            preserve_structure=True
        )
        
        # 檢查初始進度
        progress = await processing_service.get_task_progress(task_id)
        
        assert progress["task_id"] == task_id
        assert progress["status"] == "pending"
        assert progress["progress"] == 0.0
        assert progress["tool"] == "code_comparison"
        assert "多個檔案" in progress["input_path"]
        assert progress["staging_task_id"] is None
        assert progress["staged_path"] is None


@pytest.mark.asyncio
async def test_full_staging_workflow_integration():
    """完整的暫存工作流程整合測試"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # 建立測試檔案
        source_dir = Path(temp_dir) / "source"
        source_dir.mkdir()
        
        test_files = []
        for i in range(2):
            file_path = source_dir / f"workflow_test_{i}.csv"
            csv_content = f"Product,Lot,Yield\nWORKFLOW_TEST,LOT{i},98.{i}"
            file_path.write_text(csv_content)
            test_files.append(file_path)
        
        # 建立暫存目錄
        staging_dir = Path(temp_dir) / "staging"
        
        # 建立服務實例
        processing_service = FileProcessingService()
        
        try:
            # 建立帶暫存的處理任務
            task_id = processing_service.create_task_with_staging(
                tool=ProcessingTool.CSV_SUMMARY,
                source_files=test_files,
                product_name="WORKFLOW_TEST",
                preserve_structure=True
            )
            
            # 檢查初始狀態
            initial_progress = await processing_service.get_task_progress(task_id)
            assert initial_progress["status"] == "pending"
            assert initial_progress["progress"] == 0.0
            
            # 模擬成功的工具執行
            with patch('asyncio.create_subprocess_exec') as mock_subprocess:
                mock_process = MagicMock()
                mock_process.returncode = 0
                
                # 建立一個 async mock 方法
                async def mock_communicate():
                    return (b"Workflow test completed", b"")
                
                mock_process.communicate = mock_communicate
                mock_subprocess.return_value = mock_process
                
                # 模擬輸出檔案
                with patch.object(processing_service, '_find_output_files') as mock_find_files:
                    mock_output_file = Path(temp_dir) / "workflow_output.xlsx"
                    mock_output_file.write_text("Mock workflow output")
                    mock_find_files.return_value = [mock_output_file]
                    
                    # 執行完整工作流程
                    result = await processing_service.execute_with_staging(
                        task_id=task_id,
                        source_files=test_files,
                        product_name="WORKFLOW_TEST",
                        preserve_structure=True
                    )
            
            # 驗證最終結果
            assert result.success is True
            assert result.task_id == task_id
            assert len(result.output_files) > 0
            
            # 檢查最終狀態
            final_progress = await processing_service.get_task_progress(task_id)
            assert final_progress["status"] == "completed"
            assert final_progress["progress"] == 100.0
            assert final_progress["staging_task_id"] is not None
            assert final_progress["staged_path"] is not None
            
        finally:
            # 清理資源
            if hasattr(processing_service, 'executor'):
                processing_service.executor.shutdown(wait=True)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])