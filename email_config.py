#!/usr/bin/env python3
"""
郵件配置載入模組
支援從 .env 文件和環境變數載入郵件伺服器設定
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path
from dotenv import load_dotenv


@dataclass
class POP3Config:
    """POP3 配置"""
    server: str
    port: int
    use_ssl: bool
    timeout: int = 30


@dataclass
class SMTPConfig:
    """SMTP 配置"""
    server: str
    port: int
    use_auth: bool
    use_tls: bool
    timeout: int = 30


@dataclass
class EmailAccount:
    """郵件帳號配置"""
    email_address: str
    password: str
    display_name: Optional[str] = None


@dataclass
class EmailProcessingConfig:
    """郵件處理配置"""
    check_interval: int = 60
    max_fetch_count: int = 50
    delete_after_read: bool = False
    enable_monitoring: bool = True


class EmailConfigManager:
    """郵件配置管理器"""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            env_file: .env 檔案路徑，None 表示使用預設路徑
        """
        self._load_env_file(env_file)
        self._pop3_config = None
        self._smtp_config = None
        self._email_account = None
        self._processing_config = None
    
    def _load_env_file(self, env_file: Optional[str]):
        """載入 .env 檔案"""
        try:
            if env_file:
                env_path = Path(env_file)
            else:
                # 尋找 .env 檔案
                current_dir = Path.cwd()
                env_path = current_dir / '.env'
                
                # 如果當前目錄沒有，嘗試上級目錄
                if not env_path.exists():
                    env_path = current_dir.parent / '.env'
            
            if env_path.exists():
                load_dotenv(env_path)
                print(f"[OK] 已載入配置檔案: {env_path}")
            else:
                print("[WARNING] 未找到 .env 檔案，將使用環境變數")
                
        except Exception as e:
            print(f"[WARNING] 載入 .env 檔案時發生錯誤: {e}")
    
    def _get_env_value(self, key: str, default: Any = None, var_type: type = str) -> Any:
        """取得環境變數值並轉換類型"""
        value = os.getenv(key, default)
        
        if value is None:
            return default
        
        try:
            if var_type == bool:
                return str(value).lower() in ('true', '1', 'yes', 'on')
            elif var_type == int:
                return int(value)
            elif var_type == float:
                return float(value)
            else:
                return str(value)
        except (ValueError, TypeError):
            print(f"[WARNING] 無法轉換環境變數 {key}={value} 為 {var_type.__name__}，使用預設值")
            return default
    
    def get_pop3_config(self) -> POP3Config:
        """取得 POP3 配置"""
        if self._pop3_config is None:
            self._pop3_config = POP3Config(
                server=self._get_env_value('POP3_SERVER', 'hcmail.gmt.com.tw'),
                port=self._get_env_value('POP3_PORT', 1100, int),
                use_ssl=self._get_env_value('POP3_USE_SSL', True, bool),
                timeout=self._get_env_value('POP3_TIMEOUT', 30, int)
            )
        return self._pop3_config
    
    def get_smtp_config(self) -> SMTPConfig:
        """取得 SMTP 配置"""
        if self._smtp_config is None:
            self._smtp_config = SMTPConfig(
                server=self._get_env_value('SMTP_SERVER', 'hcmail.gmt.com.tw'),
                port=self._get_env_value('SMTP_PORT', 2500, int),
                use_auth=self._get_env_value('SMTP_USE_AUTH', True, bool),
                use_tls=self._get_env_value('SMTP_USE_TLS', True, bool),
                timeout=self._get_env_value('SMTP_TIMEOUT', 30, int)
            )
        return self._smtp_config
    
    def get_email_account(self) -> EmailAccount:
        """取得郵件帳號配置"""
        if self._email_account is None:
            email_address = self._get_env_value('EMAIL_ADDRESS')
            password = self._get_env_value('EMAIL_PASSWORD')
            display_name = self._get_env_value('EMAIL_DISPLAY_NAME')
            
            if not email_address or email_address in ['<EMAIL>', 'your_account']:
                raise ValueError("EMAIL_ADDRESS 環境變數未設定或使用預設值")
            
            if not password or password == 'your_password':
                raise ValueError("EMAIL_PASSWORD 環境變數未設定或使用預設值")
            
            self._email_account = EmailAccount(
                email_address=email_address,
                password=password,
                display_name=display_name
            )
        return self._email_account
    
    def get_processing_config(self) -> EmailProcessingConfig:
        """取得郵件處理配置"""
        if self._processing_config is None:
            self._processing_config = EmailProcessingConfig(
                check_interval=self._get_env_value('EMAIL_CHECK_INTERVAL', 60, int),
                max_fetch_count=self._get_env_value('EMAIL_MAX_FETCH_COUNT', 50, int),
                delete_after_read=self._get_env_value('EMAIL_DELETE_AFTER_READ', False, bool),
                enable_monitoring=self._get_env_value('EMAIL_ENABLE_MONITORING', True, bool)
            )
        return self._processing_config
    
    def validate_config(self) -> Dict[str, Any]:
        """驗證配置完整性"""
        errors = []
        warnings = []
        
        try:
            # 驗證 POP3 配置
            pop3_config = self.get_pop3_config()
            if not pop3_config.server:
                errors.append("POP3 伺服器未設定")
            if pop3_config.port <= 0 or pop3_config.port > 65535:
                errors.append(f"POP3 端口無效: {pop3_config.port}")
        except Exception as e:
            errors.append(f"POP3 配置錯誤: {e}")
        
        try:
            # 驗證 SMTP 配置
            smtp_config = self.get_smtp_config()
            if not smtp_config.server:
                errors.append("SMTP 伺服器未設定")
            if smtp_config.port <= 0 or smtp_config.port > 65535:
                errors.append(f"SMTP 端口無效: {smtp_config.port}")
        except Exception as e:
            errors.append(f"SMTP 配置錯誤: {e}")
        
        try:
            # 驗證帳號配置
            account = self.get_email_account()
            # 允許純帳號名稱或完整郵件地址
            if len(account.email_address) < 1:
                errors.append("帳號不能為空")
            if len(account.password) < 1:
                errors.append("密碼不能為空")
        except Exception as e:
            errors.append(f"帳號配置錯誤: {e}")
        
        try:
            # 驗證處理配置
            processing = self.get_processing_config()
            if processing.check_interval < 10:
                warnings.append("檢查間隔過短，可能造成伺服器負擔")
            if processing.max_fetch_count > 100:
                warnings.append("一次取得郵件數量過多，可能影響效能")
        except Exception as e:
            errors.append(f"處理配置錯誤: {e}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def get_all_configs(self) -> Dict[str, Any]:
        """取得所有配置"""
        return {
            'pop3': self.get_pop3_config(),
            'smtp': self.get_smtp_config(),
            'account': self.get_email_account(),
            'processing': self.get_processing_config()
        }
    
    def print_config_summary(self):
        """列印配置摘要"""
        try:
            configs = self.get_all_configs()
            
            print("=== 郵件配置摘要 ===")
            print()
            
            print("[POP3] 收信設定:")
            pop3 = configs['pop3']
            print(f"   伺服器: {pop3.server}:{pop3.port}")
            print(f"   SSL: {'是' if pop3.use_ssl else '否'}")
            print(f"   超時: {pop3.timeout} 秒")
            print()
            
            print("[SMTP] 發信設定:")
            smtp = configs['smtp']
            print(f"   伺服器: {smtp.server}:{smtp.port}")
            print(f"   驗證: {'是' if smtp.use_auth else '否'}")
            print(f"   TLS: {'是' if smtp.use_tls else '否'}")
            print(f"   超時: {smtp.timeout} 秒")
            print()
            
            print("[ACCOUNT] 帳號設定:")
            account = configs['account']
            print(f"   郵件地址: {account.email_address}")
            print(f"   顯示名稱: {account.display_name or '未設定'}")
            print(f"   密碼: {'已設定' if account.password else '未設定'}")
            print()
            
            print("[CONFIG] 處理設定:")
            processing = configs['processing']
            print(f"   檢查間隔: {processing.check_interval} 秒")
            print(f"   最大取得數量: {processing.max_fetch_count}")
            print(f"   讀取後刪[EXCEPT_CHAR]: {'是' if processing.delete_after_read else '否'}")
            print(f"   啟用監控: {'是' if processing.enable_monitoring else '否'}")
            
        except Exception as e:
            print(f"[ERROR] 無法顯示配置摘要: {e}")
    
    def export_config_template(self, file_path: str):
        """匯出配置範本檔案"""
        template = """# 郵件伺服器設定範本
# ================

# 公司郵件伺服器
EMAIL_SERVER=hcmail.gmt.com.tw

# POP3 收信設定
POP3_SERVER=hcmail.gmt.com.tw
POP3_PORT=1100
POP3_USE_SSL=true
POP3_TIMEOUT=30

# SMTP 發信設定
SMTP_SERVER=hcmail.gmt.com.tw
SMTP_PORT=2500
SMTP_USE_AUTH=true
SMTP_USE_TLS=true
SMTP_TIMEOUT=30

# 郵件帳號設定（請修改為您的實際帳號）
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_password
EMAIL_DISPLAY_NAME=您的顯示名稱

# 郵件處理設定
EMAIL_CHECK_INTERVAL=60
EMAIL_MAX_FETCH_COUNT=50
EMAIL_DELETE_AFTER_READ=false
EMAIL_ENABLE_MONITORING=true
"""
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(template)
            print(f"[OK] 配置範本已匯出到: {file_path}")
        except Exception as e:
            print(f"[ERROR] 匯出配置範本失敗: {e}")


def main():
    """主函數，用於測試配置載入"""
    print("=== 郵件配置載入測試 ===")
    print()
    
    try:
        # 建立配置管理器
        config_manager = EmailConfigManager()
        
        # 顯示配置摘要
        config_manager.print_config_summary()
        print()
        
        # 驗證配置
        validation = config_manager.validate_config()
        
        if validation['valid']:
            print("[OK] 配置驗證通過")
        else:
            print("[ERROR] 配置驗證失敗")
            for error in validation['errors']:
                print(f"   錯誤: {error}")
        
        if validation['warnings']:
            print("[WARNING] 配置警告:")
            for warning in validation['warnings']:
                print(f"   警告: {warning}")
        
        return 0 if validation['valid'] else 1
        
    except Exception as e:
        print(f"[ERROR] 配置載入失敗: {e}")
        return 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)