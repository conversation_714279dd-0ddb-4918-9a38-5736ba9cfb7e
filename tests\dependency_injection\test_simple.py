"""簡化的依賴函數測試
不依賴 FastAPI TestClient，直接測試依賴函數的行為
"""

import pytest
from unittest.mock import Mock, patch


def test_fixed_dependency_function_behavior():
    """測試修正後的依賴函數行為

    這個測試驗證修正後的正確行為：
    require_staging_service() 直接返回服務實例
    """
    
    # 導入當前的依賴函數
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from frontend.api.dependencies import require_staging_service
    
    # 修正後的實現應該返回服務實例
    result = require_staging_service()

    # 驗證修正後的行為
    print(f"require_staging_service() 返回的類型: {type(result)}")
    print(f"是否為可調用對象: {callable(result)}")

    # 修正後的正確行為：返回的是服務實例，不是函數
    assert not callable(result), "修正後應該返回服務實例，不是函數"

    # 檢查實際的服務實例有什麼方法
    print(f"服務實例的方法: {[method for method in dir(result) if not method.startswith('_')]}")

    # 檢查是否是正確的服務類型
    assert hasattr(result, 'create_staging_task'), "應該返回具有 create_staging_task 方法的暫存服務實例"


def test_expected_dependency_function_behavior():
    """測試期望的依賴函數行為
    
    這個測試定義了我們期望的正確行為：
    require_staging_service() 應該直接返回服務實例或拋出異常
    """
    
    with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
        # 測試場景1：服務可用
        mock_service = Mock()
        mock_service.service_id = "test_service"
        mock_get.return_value = mock_service
        
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
        from frontend.api.dependencies import require_staging_service
        
        # 期望：直接返回服務實例
        # 修正後的實現應該正確工作
        try:
            result = require_staging_service()
            # 結果應該是服務實例
            assert hasattr(result, 'service_id'), "應該返回服務實例"
            assert result.service_id == "test_service"
            print("✅ 測試通過：正確返回服務實例")
        except Exception as e:
            print(f"❌ 測試失敗：{e}")
            raise


def test_dependency_function_with_unavailable_service():
    """測試服務不可用時的行為"""
    
    with patch('src.presentation.api.dependencies.get_staging_service') as mock_get:
        mock_get.return_value = None
        
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
        from frontend.api.dependencies import require_staging_service
        
        # 期望：拋出 HTTPException
        try:
            result = require_staging_service()
            print(f"❌ 應該拋出異常，但返回了: {result}")
            assert False, "服務不可用時應該拋出 HTTPException"

        except Exception as e:
            print(f"✅ 正確拋出異常: {type(e).__name__}: {e}")
            # 檢查是否是 HTTPException
            from fastapi import HTTPException
            if isinstance(e, HTTPException):
                assert e.status_code == 503
                assert "不可用" in str(e.detail)
            else:
                print(f"⚠️  異常類型不正確，期望 HTTPException，實際: {type(e)}")
                raise


if __name__ == "__main__":
    print("🧪 測試當前依賴函數實現的問題")
    print("=" * 50)
    
    print("\n1. 測試修正後的實現:")
    test_fixed_dependency_function_behavior()
    
    print("\n2. 測試期望的行為:")
    test_expected_dependency_function_behavior()
    
    print("\n3. 測試服務不可用的情況:")
    test_dependency_function_with_unavailable_service()
    
    print("\n🎯 測試完成！")
