#!/usr/bin/env python3
"""
初始化 EQC 任務狀態資料庫
解決前端與後端狀態同步問題
"""

import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.shared.infrastructure.database.task_status_db import get_task_status_db
from loguru import logger


def main():
    """初始化資料庫"""
    try:
        logger.info("🚀 開始初始化 EQC 任務狀態資料庫...")
        
        # 初始化資料庫
        task_db = get_task_status_db()
        
        # 測試資料庫連接
        test_task_id = "test_init_12345"
        test_session_id = "test_session_12345"
        
        # 測試寫入
        success = task_db.start_task(test_task_id, test_session_id, "/test/path")
        if success:
            logger.info("✅ 資料庫寫入測試成功")
            
            # 測試讀取
            status = task_db.get_task_status(task_id=test_task_id)
            if status:
                logger.info(f"✅ 資料庫讀取測試成功: {status}")
                
                # 清理測試數據
                task_db.fail_task(test_task_id, test_session_id, "測試完成，清理數據")
                logger.info("🧹 測試數據已清理")
            else:
                logger.error("❌ 資料庫讀取測試失敗")
        else:
            logger.error("❌ 資料庫寫入測試失敗")
        
        logger.success("🎉 EQC 任務狀態資料庫初始化完成！")
        
    except Exception as e:
        logger.error(f"❌ 資料庫初始化失敗: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
