#!/usr/bin/env python3
"""
最簡單的生產模式測試
"""

import os
import sys
import time
from pathlib import Path

# 設定生產模式
os.environ['USE_MEMORY_BROKER'] = 'false'

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_submission():
    """測試任務提交"""
    print("📤 測試任務提交")
    print("=" * 50)
    
    try:
        from backend.tasks import debug_task, celery_app
        
        print(f"🔧 Broker: {celery_app.conf.broker_url}")
        print(f"⚡ Eager: {celery_app.conf.task_always_eager}")
        
        # 提交任務
        print("📤 提交任務...")
        task = debug_task.delay()
        print(f"✅ 任務提交成功，ID: {task.id}")
        print(f"📊 初始狀態: {task.status}")
        
        # 檢查任務狀態變化
        for i in range(10):
            time.sleep(1)
            status = task.status
            print(f"⏱️  {i+1}秒後狀態: {status}")
            
            if status == 'SUCCESS':
                result = task.get()
                print(f"✅ 任務成功完成: {result}")
                return True
            elif status == 'FAILURE':
                print(f"❌ 任務失敗")
                return False
        
        print("⏰ 任務仍在處理中...")
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_redis_queue():
    """檢查 Redis 隊列"""
    print("\n🔴 檢查 Redis 隊列")
    print("=" * 50)
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        # 檢查隊列
        queues = ['celery', 'default']
        for queue in queues:
            length = r.llen(queue)
            print(f"📋 隊列 {queue}: {length} 個任務")
            
            if length > 0:
                # 查看隊列內容
                items = r.lrange(queue, 0, 2)
                for i, item in enumerate(items):
                    print(f"   任務 {i+1}: {item[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis 隊列檢查失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 簡單生產模式測試")
    print("=" * 60)
    
    # 檢查 Redis 隊列
    check_redis_queue()
    
    # 測試任務提交
    result = test_task_submission()
    
    # 再次檢查隊列
    check_redis_queue()
    
    if result:
        print("\n✅ 生產模式基本功能正常")
    else:
        print("\n❌ 生產模式存在問題")
    
    return result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
