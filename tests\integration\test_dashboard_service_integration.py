"""
統一監控儀表板 - 服務整合測試

測試監控儀表板與主程式的整合功能，
包括初始化、啟動、停止和健康檢查。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from fastapi import FastAPI

from backend.monitoring.integration.dashboard_service_integrator import (
    DashboardServiceIntegrator,
    get_dashboard_integrator,
    initialize_dashboard_service,
    get_dashboard_service_status,
    get_dashboard_health_check
)


class TestDashboardServiceIntegrator:
    """監控儀表板服務整合器測試"""
    
    @pytest.fixture
    def app(self):
        """創建測試用 FastAPI 應用"""
        return FastAPI(title="Test App")
    
    @pytest.fixture
    def integrator(self):
        """創建測試用整合器"""
        return DashboardServiceIntegrator()
    
    def test_integrator_initialization(self, integrator):
        """測試整合器初始化"""
        assert integrator is not None
        assert not integrator.is_initialized
        assert not integrator.is_running
        assert integrator.coordinator is None
        assert integrator.config is None
        assert integrator.initialization_error is None
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, integrator, app):
        """測試成功初始化"""
        with patch('src.dashboard_monitoring.integration.dashboard_service_integrator.DashboardConfig') as mock_config, \
             patch('src.dashboard_monitoring.integration.dashboard_service_integrator.DashboardMonitoringCoordinator') as mock_coordinator:
            
            # 模擬配置
            mock_config.return_value = Mock()
            
            # 模擬協調器
            mock_coordinator_instance = Mock()
            mock_coordinator_instance.register_collector = Mock()
            mock_coordinator.return_value = mock_coordinator_instance
            
            # 執行初始化
            result = await integrator.initialize(app)
            
            # 驗證結果
            assert result is True
            assert integrator.is_initialized is True
            assert integrator.config is not None
            assert integrator.coordinator is not None
            assert integrator.initialization_error is None
    
    @pytest.mark.asyncio
    async def test_initialize_failure(self, integrator, app):
        """測試初始化失敗"""
        with patch('src.dashboard_monitoring.integration.dashboard_service_integrator.DashboardConfig') as mock_config:
            # 模擬配置初始化失敗
            mock_config.side_effect = Exception("配置初始化失敗")
            
            # 執行初始化
            result = await integrator.initialize(app)
            
            # 驗證結果
            assert result is False
            assert integrator.is_initialized is False
            assert integrator.initialization_error is not None
    
    @pytest.mark.asyncio
    async def test_start_monitoring_success(self, integrator):
        """測試成功啟動監控"""
        # 模擬已初始化狀態
        integrator.is_initialized = True
        integrator.coordinator = Mock()
        integrator.coordinator.start_monitoring = AsyncMock()
        
        # 執行啟動
        result = await integrator.start_monitoring()
        
        # 驗證結果
        assert result is True
        assert integrator.is_running is True
        integrator.coordinator.start_monitoring.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_monitoring_not_initialized(self, integrator):
        """測試未初始化時啟動監控"""
        # 執行啟動
        result = await integrator.start_monitoring()
        
        # 驗證結果
        assert result is False
        assert integrator.is_running is False
    
    @pytest.mark.asyncio
    async def test_stop_monitoring_success(self, integrator):
        """測試成功停止監控"""
        # 模擬運行狀態
        integrator.is_running = True
        integrator.coordinator = Mock()
        integrator.coordinator.stop_monitoring = AsyncMock()
        
        # 執行停止
        result = await integrator.stop_monitoring()
        
        # 驗證結果
        assert result is True
        assert integrator.is_running is False
        integrator.coordinator.stop_monitoring.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_stop_monitoring_not_running(self, integrator):
        """測試未運行時停止監控"""
        # 執行停止
        result = await integrator.stop_monitoring()
        
        # 驗證結果
        assert result is True
        assert integrator.is_running is False
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, integrator):
        """測試健康檢查 - 健康狀態"""
        # 模擬健康狀態
        integrator.is_initialized = True
        integrator.is_running = True
        integrator.coordinator = Mock()
        integrator.coordinator.is_healthy = Mock(return_value=True)
        
        # 執行健康檢查
        health_status = await integrator._perform_health_check()
        
        # 驗證結果
        assert health_status == 'healthy'
    
    @pytest.mark.asyncio
    async def test_health_check_uninitialized(self, integrator):
        """測試健康檢查 - 未初始化狀態"""
        # 執行健康檢查
        health_status = await integrator._perform_health_check()
        
        # 驗證結果
        assert health_status == 'uninitialized'
    
    @pytest.mark.asyncio
    async def test_health_check_stopped(self, integrator):
        """測試健康檢查 - 已停止狀態"""
        # 模擬已初始化但未運行狀態
        integrator.is_initialized = True
        integrator.is_running = False
        
        # 執行健康檢查
        health_status = await integrator._perform_health_check()
        
        # 驗證結果
        assert health_status == 'stopped'
    
    @pytest.mark.asyncio
    async def test_get_service_status(self, integrator):
        """測試獲取服務狀態"""
        # 模擬狀態
        integrator.is_initialized = True
        integrator.is_running = True
        integrator.coordinator = Mock()
        integrator.coordinator.get_current_status = AsyncMock(return_value={'test': 'data'})
        
        # 執行獲取狀態
        status = await integrator.get_service_status()
        
        # 驗證結果
        assert status is not None
        assert status['service_name'] == '統一監控儀表板'
        assert status['is_initialized'] is True
        assert status['is_running'] is True
        assert 'coordinator_status' in status
        assert 'statistics' in status
        assert 'endpoints' in status
    
    @pytest.mark.asyncio
    async def test_get_health_check_result(self, integrator):
        """測試獲取健康檢查結果"""
        # 模擬狀態
        integrator.is_initialized = True
        integrator.is_running = True
        integrator.coordinator = Mock()
        integrator.coordinator.get_health_status = Mock(return_value='healthy')
        integrator.coordinator.is_running = True
        integrator.coordinator.collectors = {'test': Mock()}
        
        # 執行健康檢查
        health_result = await integrator.get_health_check()
        
        # 驗證結果
        assert health_result is not None
        assert health_result['overall_status'] == 'healthy'
        assert 'components' in health_result
        assert 'integrator' in health_result['components']
        assert 'coordinator' in health_result['components']
        assert 'config' in health_result['components']
    
    def test_get_service_info(self, integrator):
        """測試獲取服務基本資訊"""
        # 模擬運行狀態
        integrator.is_running = True
        integrator.stats['health_status'] = 'healthy'
        
        # 執行獲取資訊
        info = integrator.get_service_info()
        
        # 驗證結果
        assert info is not None
        assert info['name'] == '統一監控儀表板'
        assert info['version'] == '1.0.0'
        assert info['status'] == 'running'
        assert info['health'] == 'healthy'
        assert 'features' in info
        assert 'endpoints' in info
        assert len(info['features']) > 0
        assert len(info['endpoints']) > 0


class TestGlobalFunctions:
    """全域函數測試"""
    
    def test_get_dashboard_integrator_singleton(self):
        """測試整合器單例模式"""
        integrator1 = get_dashboard_integrator()
        integrator2 = get_dashboard_integrator()
        
        assert integrator1 is integrator2
        assert isinstance(integrator1, DashboardServiceIntegrator)
    
    @pytest.mark.asyncio
    async def test_initialize_dashboard_service(self):
        """測試初始化服務便利函數"""
        app = FastAPI(title="Test App")
        
        with patch.object(DashboardServiceIntegrator, 'initialize', new_callable=AsyncMock) as mock_init:
            mock_init.return_value = True
            
            result = await initialize_dashboard_service(app)
            
            assert result is True
            mock_init.assert_called_once_with(app)
    
    @pytest.mark.asyncio
    async def test_get_dashboard_service_status_function(self):
        """測試獲取服務狀態便利函數"""
        expected_status = {'service_name': '統一監控儀表板', 'status': 'running'}
        
        with patch.object(DashboardServiceIntegrator, 'get_service_status', new_callable=AsyncMock) as mock_status:
            mock_status.return_value = expected_status
            
            result = await get_dashboard_service_status()
            
            assert result == expected_status
            mock_status.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_dashboard_health_check_function(self):
        """測試獲取健康檢查便利函數"""
        expected_health = {'overall_status': 'healthy'}
        
        with patch.object(DashboardServiceIntegrator, 'get_health_check', new_callable=AsyncMock) as mock_health:
            mock_health.return_value = expected_health
            
            result = await get_dashboard_health_check()
            
            assert result == expected_health
            mock_health.assert_called_once()


class TestErrorHandling:
    """錯誤處理測試"""
    
    @pytest.mark.asyncio
    async def test_initialize_with_config_error(self):
        """測試配置錯誤時的初始化"""
        integrator = DashboardServiceIntegrator()
        app = FastAPI(title="Test App")
        
        with patch('src.dashboard_monitoring.integration.dashboard_service_integrator.DashboardConfig') as mock_config:
            mock_config.side_effect = Exception("配置載入失敗")
            
            result = await integrator.initialize(app)
            
            assert result is False
            assert integrator.initialization_error is not None
            assert "配置載入失敗" in integrator.initialization_error
    
    @pytest.mark.asyncio
    async def test_initialize_with_coordinator_error(self):
        """測試協調器錯誤時的初始化"""
        integrator = DashboardServiceIntegrator()
        app = FastAPI(title="Test App")
        
        with patch('src.dashboard_monitoring.integration.dashboard_service_integrator.DashboardConfig') as mock_config, \
             patch('src.dashboard_monitoring.integration.dashboard_service_integrator.DashboardMonitoringCoordinator') as mock_coordinator:
            
            mock_config.return_value = Mock()
            mock_coordinator.side_effect = Exception("協調器初始化失敗")
            
            result = await integrator.initialize(app)
            
            assert result is False
            assert integrator.initialization_error is not None
    
    @pytest.mark.asyncio
    async def test_start_monitoring_with_coordinator_error(self):
        """測試協調器啟動錯誤"""
        integrator = DashboardServiceIntegrator()
        integrator.is_initialized = True
        integrator.coordinator = Mock()
        integrator.coordinator.start_monitoring = AsyncMock(side_effect=Exception("啟動失敗"))
        
        result = await integrator.start_monitoring()
        
        assert result is False
        assert integrator.is_running is False
    
    @pytest.mark.asyncio
    async def test_health_check_with_exception(self):
        """測試健康檢查異常處理"""
        integrator = DashboardServiceIntegrator()
        integrator.is_initialized = True
        integrator.is_running = True
        integrator.coordinator = Mock()
        integrator.coordinator.is_healthy = Mock(side_effect=Exception("健康檢查失敗"))
        
        health_status = await integrator._perform_health_check()
        
        assert health_status == 'unhealthy'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])