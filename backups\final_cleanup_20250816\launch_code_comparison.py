#!/usr/bin/env python3
"""
啟動 code_comparison.py 的簡單腳本
用於 FT-EQC 處理功能
"""

import sys
import subprocess
import os
from pathlib import Path

def main():
    """啟動 code_comparison.py"""
    try:
        # 獲取專案根目錄
        project_root = Path(__file__).parent
        
        # code_comparison.py 的路徑
        code_comparison_path = project_root / "code_comparison.py"
        
        if not code_comparison_path.exists():
            print(f"❌ 找不到 code_comparison.py 檔案: {code_comparison_path}")
            return 1
        
        print(f"🚀 啟動 FT-EQC 處理工具...")
        print(f"📁 檔案路徑: {code_comparison_path}")
        
        # 啟動 code_comparison.py
        result = subprocess.run([
            sys.executable, 
            str(code_comparison_path)
        ], cwd=str(project_root))
        
        return result.returncode
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
