"""
測試 Online EQC 失敗檔案處理器
基於 TDD 原則，測試 FindOnlieEQCFAILFiles 功能
"""

import os
import tempfile
import pytest
from pathlib import Path
from backend.shared.infrastructure.adapters.excel.ft_eqc_grouping_processor import OnlineEQCFailProcessor


class TestOnlineEQCFailProcessor:
    """Online EQC 失敗檔案處理器測試"""
    
    def setup_method(self):
        """設置測試環境"""
        self.processor = OnlineEQCFailProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """清理測試環境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_test_csv(self, filename: str, content: str) -> str:
        """創建測試用的CSV檔案"""
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_find_first_non_one_row_with_failure(self):
        """測試檢測失敗行 - 有失敗資料的情況"""
        # 創建測試CSV - 包含失敗資料 (BIN != 1)
        csv_content = """頭部行1
頭部行2
頭部行3
頭部行4
頭部行5
頭部行6
頭部行7
頭部行8
頭部行9
頭部行10
頭部行11
頭部行12
1001,1,pass
1002,1,pass
1003,2,fail
1004,1,pass"""
        
        csv_file = self._create_test_csv("test_eqc_with_fail.csv", csv_content)
        
        # 執行測試
        result = self.processor.find_first_non_one_row(csv_file)
        
        # 驗證結果 - 應該返回第一個失敗行的A欄值
        assert result == 1003
    
    def test_find_first_non_one_row_no_failure(self):
        """測試檢測失敗行 - 沒有失敗資料的情況"""
        # 創建測試CSV - 所有行都是BIN=1
        csv_content = """頭部行1
頭部行2
頭部行3
頭部行4
頭部行5
頭部行6
頭部行7
頭部行8
頭部行9
頭部行10
頭部行11
頭部行12
1001,1,pass
1002,1,pass
1003,1,pass
1004,1,pass"""
        
        csv_file = self._create_test_csv("test_eqc_no_fail.csv", csv_content)
        
        # 執行測試
        result = self.processor.find_first_non_one_row(csv_file)
        
        # 驗證結果 - 應該返回0（沒有失敗）
        assert result == 0
    
    def test_find_first_non_one_row_empty_file(self):
        """測試檢測失敗行 - 空檔案的情況"""
        csv_file = self._create_test_csv("test_empty.csv", "")
        
        # 執行測試
        result = self.processor.find_first_non_one_row(csv_file)
        
        # 驗證結果 - 應該返回0
        assert result == 0
    
    def test_find_first_non_one_row_invalid_format(self):
        """測試檢測失敗行 - 格式錯誤的情況"""
        csv_content = """頭部行1
頭部行2
頭部行3
頭部行4
頭部行5
頭部行6
頭部行7
頭部行8
頭部行9
頭部行10
頭部行11
頭部行12
1001,abc,invalid
1002,1,pass"""
        
        csv_file = self._create_test_csv("test_invalid.csv", csv_content)
        
        # 執行測試
        result = self.processor.find_first_non_one_row(csv_file)
        
        # 驗證結果 - 應該跳過無效行，返回0（沒有有效失敗）
        assert result == 0
    
    def test_find_online_eqc_fail_files_with_failures(self):
        """測試找出Online EQC失敗檔案 - 有失敗的情況"""
        # 創建FT檔案
        ft_content = """FT頭部行1
FT頭部行2
FT頭部行3
FT頭部行4
FT頭部行5
FT頭部行6
FT頭部行7
FT頭部行8
FT頭部行9
FT頭部行10
FT頭部行11
FT頭部行12
1001,1,pass
1002,1,pass
1003,1,pass"""
        
        # 創建有失敗的EQC檔案
        eqc_fail_content = """EQC頭部行1
EQC頭部行2
EQC頭部行3
EQC頭部行4
EQC頭部行5
EQC頭部行6
EQC頭部行7
EQC頭部行8
EQC頭部行9
EQC頭部行10
EQC頭部行11
EQC頭部行12
1001,1,pass
1002,2,fail
1003,1,pass"""
        
        # 創建沒有失敗的EQC檔案
        eqc_pass_content = """EQC頭部行1
EQC頭部行2
EQC頭部行3
EQC頭部行4
EQC頭部行5
EQC頭部行6
EQC頭部行7
EQC頭部行8
EQC頭部行9
EQC頭部行10
EQC頭部行11
EQC頭部行12
1001,1,pass
1002,1,pass
1003,1,pass"""
        
        ft_file = self._create_test_csv("test_ft.csv", ft_content)
        eqc_fail_file = self._create_test_csv("test_eqc_fail.csv", eqc_fail_content)
        eqc_pass_file = self._create_test_csv("test_eqc_pass.csv", eqc_pass_content)
        
        # 配對列表
        matched_pairs = [
            (ft_file, eqc_fail_file),  # 有失敗的配對
            (ft_file, eqc_pass_file)   # 沒失敗的配對
        ]
        
        # 執行測試
        result = self.processor.find_online_eqc_fail_files(matched_pairs)
        
        # 驗證結果
        assert result['fail_count'] == 1
        assert len(result['fail_files']) == 1
        assert eqc_fail_file in result['fail_files']
        assert eqc_pass_file not in result['fail_files']
        assert len(result['analysis_files']) == 1
        assert 'processing_timestamp' in result
    
    def test_find_online_eqc_fail_files_no_failures(self):
        """測試找出Online EQC失敗檔案 - 沒有失敗的情況"""
        # 創建所有都通過的EQC檔案
        eqc_content = """EQC頭部行1
EQC頭部行2
EQC頭部行3
EQC頭部行4
EQC頭部行5
EQC頭部行6
EQC頭部行7
EQC頭部行8
EQC頭部行9
EQC頭部行10
EQC頭部行11
EQC頭部行12
1001,1,pass
1002,1,pass
1003,1,pass"""
        
        ft_file = self._create_test_csv("test_ft.csv", "FT檔案內容")
        eqc_file = self._create_test_csv("test_eqc.csv", eqc_content)
        
        matched_pairs = [(ft_file, eqc_file)]
        
        # 執行測試
        result = self.processor.find_online_eqc_fail_files(matched_pairs)
        
        # 驗證結果
        assert result['fail_count'] == 0
        assert len(result['fail_files']) == 0
        assert len(result['analysis_files']) == 0
    
    def test_copy_rows_to_new_file(self):
        """測試複製失敗行到新檔案"""
        # 創建有失敗資料的EQC檔案
        eqc_content = """EQC頭部行1
EQC頭部行2
EQC頭部行3
EQC頭部行4
EQC頭部行5
EQC頭部行6
EQC頭部行7
EQC頭部行8
EQC頭部行9
EQC頭部行10
EQC頭部行11
EQC頭部行12
1001,1,pass,data1
1002,2,fail,data2
1003,1,pass,data3
1004,3,fail,data4"""
        
        ft_file = self._create_test_csv("test_ft.csv", "FT檔案內容")
        eqc_file = self._create_test_csv("test_eqc.csv", eqc_content)
        
        # 執行測試
        result_file = self.processor._copy_rows_to_new_file(ft_file, eqc_file)
        
        # 驗證結果
        assert result_file is not None
        assert result_file.endswith("_EQCFAILDATA.csv")
        assert os.path.exists(result_file)
        
        # 檢查輸出檔案內容
        with open(result_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 應該包含標頭行和失敗行
        assert "EQC頭部行1" in content
        assert "1002,2,fail,data2" in content
        assert "1004,3,fail,data4" in content
        # 不應該包含通過的行
        assert "1001,1,pass" not in content
        assert "1003,1,pass" not in content
    
    def test_copy_rows_to_new_file_no_failures(self):
        """測試複製失敗行到新檔案 - 沒有失敗的情況"""
        # 創建沒有失敗資料的EQC檔案
        eqc_content = """EQC頭部行1
EQC頭部行2
EQC頭部行3
EQC頭部行4
EQC頭部行5
EQC頭部行6
EQC頭部行7
EQC頭部行8
EQC頭部行9
EQC頭部行10
EQC頭部行11
EQC頭部行12
1001,1,pass
1002,1,pass
1003,1,pass"""
        
        ft_file = self._create_test_csv("test_ft.csv", "FT檔案內容")
        eqc_file = self._create_test_csv("test_eqc.csv", eqc_content)
        
        # 執行測試
        result_file = self.processor._copy_rows_to_new_file(ft_file, eqc_file)
        
        # 驗證結果 - 沒有失敗行時應該返回None
        assert result_file is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])