"""
郵件處理引擎測試
基於 TDD 開發，支援 Outlook 2019+ 版本，整合現代郵件處理最佳實踐

測試覆蓋範圍：
- 郵件處理引擎核心功能
- Outlook COM API 整合
- 非同步處理和佇列機制
- 版本相容性
- 錯誤處理和恢復
"""

import pytest
import asyncio
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import List, Optional

from backend.email.models.email_models import EmailData, EmailParsingResult, ProcessingStatus, EmailAttachment
from backend.shared.application.use_cases.email_processor import EmailProcessor, EmailProcessingConfig
from backend.shared.application.interfaces.email_reader import EmailReader
from backend.shared.application.interfaces.task_queue import TaskQueue
from backend.email.adapters.outlook.outlook_adapter import OutlookAdapter


class TestEmailProcessor:
    """郵件處理引擎測試"""

    def setup_method(self):
        """每個測試方法前的設置"""
        # 模擬依賴
        self.mock_email_reader = Mock(spec=EmailReader)
        self.mock_task_queue = Mock(spec=TaskQueue)
        self.mock_outlook_adapter = Mock(spec=OutlookAdapter)
        
        # 配置
        self.config = EmailProcessingConfig(
            max_concurrent_processing=5,
            retry_attempts=3,
            processing_timeout=300,
            enable_monitoring=True,
            outlook_version="2019"
        )
        
        # 建立處理器
        self.processor = EmailProcessor(
            email_reader=self.mock_email_reader,
            task_queue=self.mock_task_queue,
            outlook_adapter=self.mock_outlook_adapter,
            config=self.config
        )

    def test_email_processor_initialization(self):
        """測試郵件處理器初始化"""
        assert self.processor.config.max_concurrent_processing == 5
        assert self.processor.config.outlook_version == "2019"
        assert self.processor.config.enable_monitoring is True
        assert self.processor.is_running is False
        assert self.processor.processed_count == 0

    @pytest.mark.asyncio
    async def test_process_single_email_success(self):
        """測試單一郵件處理成功"""
        # 準備測試數據
        email_data = EmailData(
            message_id="test-001",
            subject="GTK FT HOLD MO:F123456 LOT:ABC123",
            sender="<EMAIL>",
            body="GTK testing notification",
            received_time=datetime.now()
        )
        
        # 模擬成功解析
        expected_result = EmailParsingResult(
            vendor_code="GTK",
            mo_number="F123456",
            lot_number="ABC123",
            is_success=True,
            extracted_data={"vendor_name": "GTK"}
        )
        
        # 模擬 ParserFactory 的行為
        from backend.email.models.email_models import VendorIdentificationResult
        from backend.email.parsers.base_parser import BaseParser
        
        # 模擬解析器
        mock_parser = Mock(spec=BaseParser)
        mock_parser.vendor_code = "GTK"
        mock_parser.parse_email.return_value = expected_result
        
        # 模擬廠商識別結果
        vendor_result = VendorIdentificationResult(
            vendor_code="GTK",
            vendor_name="GTK",
            confidence_score=0.9,
            matching_patterns=["GTK"],
            is_identified=True
        )
        
        # 配置模擬行為
        self.processor.parser_factory.identify_vendor = Mock(return_value=(mock_parser, vendor_result))
        
        # 執行測試
        result = await self.processor.process_email(email_data)
        
        # 驗證結果
        assert result.is_success is True
        assert result.vendor_code == "GTK"
        assert result.mo_number == "F123456"
        assert self.processor.processed_count == 1

    @pytest.mark.asyncio
    async def test_process_email_with_parsing_failure(self):
        """測試郵件解析失敗處理"""
        email_data = EmailData(
            message_id="test-002",
            subject="Unknown format email",
            sender="<EMAIL>",
            body="Unknown content",
            received_time=datetime.now()
        )
        
        # 模擬廠商識別失敗
        from backend.email.models.email_models import VendorIdentificationResult
        
        failed_vendor_result = VendorIdentificationResult(
            vendor_code=None,
            vendor_name=None,
            confidence_score=0.0,
            matching_patterns=[],
            is_identified=False
        )
        
        # 配置模擬行為（無法識別廠商）
        self.processor.parser_factory.identify_vendor = Mock(return_value=(None, failed_vendor_result))
        
        # 執行測試
        result = await self.processor.process_email(email_data)
        
        # 驗證錯誤處理
        assert result.is_success is False
        assert "無法識別廠商或格式" in result.error_message
        assert self.processor.failed_count == 1

    @pytest.mark.asyncio
    async def test_batch_email_processing(self):
        """測試批次郵件處理"""
        # 準備批次郵件數據
        emails = [
            EmailData(message_id=f"batch-{i}", subject=f"Test {i}", 
                     sender="<EMAIL>", body="Test body", 
                     received_time=datetime.now())
            for i in range(5)
        ]
        
        # 模擬廠商識別和解析成功
        from backend.email.models.email_models import VendorIdentificationResult
        from backend.email.parsers.base_parser import BaseParser
        
        # 模擬解析器
        mock_parser = Mock(spec=BaseParser)
        mock_parser.vendor_code = "TEST"
        
        success_result = EmailParsingResult(
            vendor_code="TEST",
            is_success=True,
            extracted_data={"test": True}
        )
        mock_parser.parse_email.return_value = success_result
        
        # 模擬廠商識別結果
        vendor_result = VendorIdentificationResult(
            vendor_code="TEST",
            vendor_name="TEST",
            confidence_score=0.8,
            matching_patterns=["test"],
            is_identified=True
        )
        
        # 配置模擬行為
        self.processor.parser_factory.identify_vendor = Mock(return_value=(mock_parser, vendor_result))
        
        # 執行批次處理
        results = await self.processor.process_batch(emails)
        
        # 驗證結果
        assert len(results) == 5
        assert all(result.is_success for result in results)
        assert self.processor.processed_count == 5

    def test_outlook_version_compatibility_check(self):
        """測試 Outlook 版本相容性檢查"""
        # 測試支援的版本
        supported_versions = ["2016", "2019", "2021", "365"]
        
        for version in supported_versions:
            config = EmailProcessingConfig(outlook_version=version)
            processor = EmailProcessor(
                email_reader=self.mock_email_reader,
                task_queue=self.mock_task_queue,
                outlook_adapter=self.mock_outlook_adapter,
                config=config
            )
            assert processor.is_outlook_version_supported() is True

        # 測試不支援的版本
        unsupported_config = EmailProcessingConfig(outlook_version="2010")
        processor = EmailProcessor(
            email_reader=self.mock_email_reader,
            task_queue=self.mock_task_queue,
            outlook_adapter=self.mock_outlook_adapter,
            config=unsupported_config
        )
        assert processor.is_outlook_version_supported() is False

    @pytest.mark.asyncio
    async def test_concurrent_processing_limit(self):
        """測試並發處理限制"""
        # 準備大批郵件
        emails = [
            EmailData(message_id=f"concurrent-{i}", subject=f"Test {i}",
                     sender="<EMAIL>", body="Test body",
                     received_time=datetime.now())
            for i in range(10)
        ]
        
        # 模擬廠商識別和解析
        from backend.email.models.email_models import VendorIdentificationResult
        from backend.email.parsers.base_parser import BaseParser
        
        # 模擬解析器
        mock_parser = Mock(spec=BaseParser)
        mock_parser.vendor_code = "TEST"
        
        def slow_parse_email(parsing_context):
            # 返回直接結果，不需要 async，因為會在 EmailProcessor 中處理
            return EmailParsingResult(vendor_code="TEST", is_success=True)
        
        mock_parser.parse_email = slow_parse_email
        
        vendor_result = VendorIdentificationResult(
            vendor_code="TEST",
            vendor_name="TEST",
            confidence_score=0.8,
            matching_patterns=["test"],
            is_identified=True
        )
        
        # 配置模擬行為
        self.processor.parser_factory.identify_vendor = Mock(return_value=(mock_parser, vendor_result))
        
        # 記錄開始時間
        start_time = asyncio.get_event_loop().time()
        
        # 執行並發處理
        results = await self.processor.process_batch(emails)
        
        # 記錄結束時間
        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time
        
        # 驗證並發限制生效（應該比順序處理快）
        assert len(results) == 10
        assert processing_time < 1.0  # 應該在 1 秒內完成
        assert all(result.is_success for result in results)

    @pytest.mark.asyncio
    async def test_error_recovery_mechanism(self):
        """測試錯誤恢復機制"""
        email_data = EmailData(
            message_id="error-test",
            subject="Error test email",
            sender="<EMAIL>",
            body="Error content",
            received_time=datetime.now()
        )
        
        # 模擬前兩次失敗，第三次成功
        from backend.email.models.email_models import VendorIdentificationResult
        from backend.email.parsers.base_parser import BaseParser
        
        call_count = 0
        
        # 模擬解析器
        mock_parser = Mock(spec=BaseParser)
        mock_parser.vendor_code = "TEST"
        
        def failing_parse_email(parsing_context):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise Exception("Temporary processing error")
            return EmailParsingResult(vendor_code="TEST", is_success=True)
        
        mock_parser.parse_email = failing_parse_email
        
        vendor_result = VendorIdentificationResult(
            vendor_code="TEST",
            vendor_name="TEST",
            confidence_score=0.8,
            matching_patterns=["test"],
            is_identified=True
        )
        
        # 配置模擬行為
        self.processor.parser_factory.identify_vendor = Mock(return_value=(mock_parser, vendor_result))
        
        # 執行錯誤恢復測試
        result = await self.processor.process_email_with_retry(email_data)
        
        # 驗證重試機制
        assert result.is_success is True
        assert call_count == 3  # 重試了兩次後成功

    def test_task_queue_integration(self):
        """測試任務佇列整合"""
        email_data = EmailData(
            message_id="queue-test",
            subject="Queue test email",
            sender="<EMAIL>",
            body="Queue content",
            received_time=datetime.now()
        )
        
        # 配置模擬佇列
        self.mock_task_queue.enqueue = AsyncMock(return_value="task-id-123")
        self.mock_task_queue.get_task_info = AsyncMock(return_value=Mock(status=Mock(value="pending")))
        
        # 執行佇列操作（在非異步環境中會返回空字符串）
        task_result = self.processor.enqueue_email_processing(email_data)
        
        # 驗證佇列整合 - 在非異步環境中應該返回空字符串
        assert task_result == ""  # 沒有事件循環時返回空字符串

    @pytest.mark.asyncio
    async def test_outlook_adapter_integration(self):
        """測試 Outlook 適配器整合"""
        # 模擬 Outlook 連接
        self.mock_outlook_adapter.connect = AsyncMock(return_value=True)
        self.mock_outlook_adapter.get_inbox_emails = AsyncMock(return_value=[])
        self.mock_outlook_adapter.is_connected = Mock(return_value=True)
        
        # 測試連接
        connected = await self.processor.connect_to_outlook()
        assert connected is True
        
        # 測試獲取郵件
        emails = await self.processor.fetch_new_emails()
        assert isinstance(emails, list)
        
        self.mock_outlook_adapter.connect.assert_called_once()
        self.mock_outlook_adapter.get_inbox_emails.assert_called_once()

    def test_processing_metrics_tracking(self):
        """測試處理指標追蹤"""
        # 驗證初始指標
        metrics = self.processor.get_processing_metrics()
        
        assert metrics["processed_count"] == 0
        assert metrics["failed_count"] == 0
        assert metrics["success_rate"] == 0.0
        assert "uptime" in metrics
        assert "average_processing_time" in metrics

    @pytest.mark.asyncio
    async def test_inbox_monitoring_functionality(self):
        """測試收件夾監控功能"""
        # 模擬收件夾監控
        new_emails = [
            EmailData(message_id="monitor-1", subject="New email 1",
                     sender="<EMAIL>", body="Content 1",
                     received_time=datetime.now()),
            EmailData(message_id="monitor-2", subject="New email 2",
                     sender="<EMAIL>", body="Content 2",
                     received_time=datetime.now())
        ]
        
        self.mock_outlook_adapter.get_inbox_emails = AsyncMock(return_value=new_emails)
        
        # 執行監控
        monitored_emails = await self.processor.monitor_inbox_once()
        
        # 驗證監控結果
        assert len(monitored_emails) == 2
        assert monitored_emails[0].message_id == "monitor-1"
        assert monitored_emails[1].message_id == "monitor-2"

    @pytest.mark.asyncio
    async def test_attachment_processing_integration(self):
        """測試附件處理整合"""
        # 含附件的郵件
        email_with_attachment = EmailData(
            message_id="attachment-test",
            subject="Email with attachment",
            sender="<EMAIL>",
            body="Email body",
            received_time=datetime.now(),
            attachments=[
                EmailAttachment(
                    filename="test.csv", 
                    size_bytes=1024, 
                    content_type="text/csv",
                    file_path=Path("/tmp/test.csv")
                )
            ]
        )
        
        # 模擬附件處理
        self.mock_outlook_adapter.download_attachments = AsyncMock(
            return_value=["/tmp/test.csv"]
        )
        
        # 執行附件處理
        attachment_paths = await self.processor.process_attachments(email_with_attachment)
        
        # 驗證附件處理
        assert len(attachment_paths) == 1
        assert attachment_paths[0] == "/tmp/test.csv"

    def test_configuration_validation(self):
        """測試配置驗證"""
        # 測試有效配置
        valid_config = EmailProcessingConfig(
            max_concurrent_processing=10,
            retry_attempts=5,
            processing_timeout=600,
            outlook_version="2019"
        )
        
        processor = EmailProcessor(
            email_reader=self.mock_email_reader,
            task_queue=self.mock_task_queue,
            outlook_adapter=self.mock_outlook_adapter,
            config=valid_config
        )
        
        assert processor.validate_configuration() is True
        
        # 測試無效配置
        invalid_config = EmailProcessingConfig(
            max_concurrent_processing=0,  # 無效值
            retry_attempts=-1,            # 無效值
            processing_timeout=0,         # 無效值
            outlook_version="unknown"     # 不支援版本
        )
        
        processor_invalid = EmailProcessor(
            email_reader=self.mock_email_reader,
            task_queue=self.mock_task_queue,
            outlook_adapter=self.mock_outlook_adapter,
            config=invalid_config
        )
        
        assert processor_invalid.validate_configuration() is False

    @pytest.mark.asyncio
    async def test_graceful_shutdown(self):
        """測試優雅關閉"""
        # 模擬運行中的處理器
        self.processor._is_running = True
        self.processor._active_tasks = set()
        
        # 模擬活躍任務
        mock_task = AsyncMock()
        self.processor._active_tasks.add(mock_task)
        
        # 執行優雅關閉
        await self.processor.graceful_shutdown(timeout=1.0)
        
        # 驗證關閉狀態
        assert self.processor.is_running is False

    def test_logging_integration(self):
        """測試日誌整合"""
        # 測試日誌記錄功能 - 直接測試 logger 屬性
        original_logger = self.processor.logger
        mock_logger = Mock()
        self.processor.logger = mock_logger
        
        try:
            self.processor.log_processing_event("test_event", {"key": "value"})
            
            # 驗證日誌調用
            mock_logger.info.assert_called_once()
        finally:
            # 恢復原始 logger
            self.processor.logger = original_logger

    def test_chinese_content_support(self):
        """測試中文內容支援"""
        # 中文郵件數據
        chinese_email = EmailData(
            message_id="chinese-test",
            subject="測試中文郵件主旨",
            sender="測試@example.com",
            body="這是一封包含中文內容的測試郵件",
            received_time=datetime.now()
        )
        
        # 驗證中文內容處理
        assert self.processor.validate_email_data(chinese_email) is True
        assert chinese_email.subject == "測試中文郵件主旨"
        assert "中文內容" in chinese_email.body

    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """測試效能監控"""
        # 模擬處理時間
        start_time = datetime.now()
        
        email_data = EmailData(
            message_id="perf-test",
            subject="Performance test",
            sender="<EMAIL>",
            body="Performance content",
            received_time=datetime.now()
        )
        
        # 模擬處理
        result = EmailParsingResult(vendor_code="TEST", is_success=True)
        self.mock_email_reader.parse_email = AsyncMock(return_value=result)
        
        # 執行處理
        processed_result = await self.processor.process_email(email_data)
        
        # 驗證效能指標
        metrics = self.processor.get_processing_metrics()
        assert "average_processing_time" in metrics
        assert metrics["processed_count"] == 1