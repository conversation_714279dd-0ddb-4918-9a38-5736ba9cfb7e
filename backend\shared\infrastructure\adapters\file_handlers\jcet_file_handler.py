"""
JCET 廠商檔案處理器
對應 VBA 的 CopyFilesJCET 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class JCETFileHandler(BaseFileHandler):
    """
    JCET 廠商檔案處理器

    VBA 邏輯：
    - 預設從 \JCET\JCET\ 搜尋包含 MO 的壓縮檔
    - 🔧 新增：如果郵件內文包含「宿迁」，則從 \JCET\SUQIAN\{PD}\{LOT}\ 搜尋
    """

    def __init__(self, source_base_path: str):
        """初始化 JCET 檔案處理器"""
        super().__init__(source_base_path, "JCET")
        self.email_content = None  # 儲存郵件內容用於路徑判斷
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        JCET 的來源路徑

        🔧 新增邏輯：
        - 如果郵件內文包含「宿迁」：\\192.168.1.60\\test_log\\JCET\\SUQIAN\\{PD}\\{LOT}\\
        - 否則使用預設路徑：\\192.168.1.60\\test_log\\JCET\\JCET\\

        VBA: sourcePathJCET = sourcePath & "\JCET\JCET\"
        """
        paths = []

        # 檢查是否包含「宿迁」
        if self._contains_suqian():
            # 宿迁路徑：\JCET\SUQIAN\{PD}\{LOT}\
            if pd != "default" and lot != "default":
                suqian_path = self.source_base_path / "JCET" / "SUQIAN" / pd / lot
                paths.append(suqian_path)
                self.logger.info(f"🏭 檢測到宿迁，使用宿迁路徑: {suqian_path}")
            else:
                self.logger.warning("🏭 檢測到宿迁但缺少 PD 或 LOT 資訊，無法構建宿迁路徑")

        # 預設路徑：\JCET\JCET\（總是包含作為備用）
        default_path = self.source_base_path / "JCET" / "JCET"
        paths.append(default_path)

        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        JCET 的檔案搜尋模式
        
        VBA: file = Dir(sourcePathJCET & "*" & fileName & "*")
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")  # VBA 模式：包含 MO
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """JCET 不支援資料夾複製"""
        return False

    def set_email_content(self, subject: str = "", body: str = ""):
        """
        設置郵件內容用於路徑判斷

        Args:
            subject: 郵件主旨
            body: 郵件內文
        """
        self.email_content = {
            'subject': subject or "",
            'body': body or ""
        }
        self.logger.debug(f"已設置郵件內容用於路徑判斷")

    def _contains_suqian(self) -> bool:
        """
        檢查郵件內容是否包含「宿迁」

        Returns:
            bool: 是否包含宿迁
        """
        if not self.email_content:
            self.logger.debug("未設置郵件內容，無法檢查宿迁")
            return False

        # 檢查主旨和內文
        subject = self.email_content.get('subject', '').lower()
        body = self.email_content.get('body', '').lower()

        # 檢查各種可能的宿迁寫法
        suqian_keywords = ['宿迁', '宿遷', 'suqian', 'sq']

        for keyword in suqian_keywords:
            if keyword in subject or keyword in body:
                self.logger.info(f"🏭 在郵件內容中檢測到宿迁關鍵字: '{keyword}'")
                return True

        self.logger.debug("郵件內容中未檢測到宿迁關鍵字")
        return False