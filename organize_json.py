#!/usr/bin/env python3
"""
JSON 檔案整理工具
將 JSON 檔案分類並移動到 docs/data/ 目錄
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json

# 專案根目錄
ROOT_DIR = Path(__file__).parent
DOCS_DIR = ROOT_DIR / "docs"
DATA_DIR = DOCS_DIR / "data"

# JSON 檔案分類
JSON_CATEGORIES = {
    "config": {
        "patterns": ["project_info.json", "dramatiq_metrics.json", "package.json"],
        "description": "專案配置和統計檔案"
    },
    "reports": {
        "patterns": ["*test*", "*verification*", "*validation*", "*report*"],
        "description": "測試和驗證報告"
    },
    "archive": {
        "patterns": ["*20250*", "*old*", "*backup*"],
        "description": "歷史存檔檔案"
    }
}

def create_data_structure():
    """建立 docs/data 目錄結構"""
    print("建立 docs/data 目錄結構...")
    
    # 建立主要資料目錄
    DATA_DIR.mkdir(parents=True, exist_ok=True)
    print(f"   建立: {DATA_DIR}")
    
    # 建立子目錄
    for category in JSON_CATEGORIES.keys():
        category_dir = DATA_DIR / category
        category_dir.mkdir(parents=True, exist_ok=True)
        print(f"   建立: {category_dir}")

def categorize_json_file(file_path):
    """根據檔案名判斷 JSON 檔案分類"""
    file_name = file_path.name.lower()
    
    # 檢查每個分類
    for category, config in JSON_CATEGORIES.items():
        for pattern in config["patterns"]:
            pattern_lower = pattern.lower()
            if "*" in pattern_lower:
                # 處理萬用字元
                if pattern_lower.startswith("*") and pattern_lower.endswith("*"):
                    keyword = pattern_lower[1:-1]
                    if keyword in file_name:
                        return category
                elif pattern_lower.startswith("*"):
                    keyword = pattern_lower[1:]
                    if file_name.endswith(keyword):
                        return category
                elif pattern_lower.endswith("*"):
                    keyword = pattern_lower[:-1]
                    if file_name.startswith(keyword):
                        return category
            else:
                if pattern_lower == file_name:
                    return category
    
    # 預設分類
    return "reports"

def move_json_files():
    """移動 JSON 檔案到適當目錄"""
    json_files = list(ROOT_DIR.glob("*.json"))
    
    if not json_files:
        print("沒有找到 JSON 檔案")
        return
    
    print(f"\n找到 {len(json_files)} 個 JSON 檔案")
    
    moved_count = 0
    
    print("\n開始分類和移動 JSON 檔案...")
    
    for file_path in json_files:
        category = categorize_json_file(file_path)
        
        # 目標目錄
        target_dir = DATA_DIR / category
        target_path = target_dir / file_path.name
        
        # 檢查目標檔案是否已存在
        if target_path.exists():
            print(f"   跳過: {file_path.name} (目標已存在)")
            continue
        
        try:
            shutil.move(str(file_path), str(target_path))
            print(f"   移動: {file_path.name} -> docs/data/{category}/")
            moved_count += 1
        except Exception as e:
            print(f"   錯誤: {file_path.name} - {e}")
    
    print(f"\n移動完成: {moved_count} 個檔案")

def create_index_files():
    """為每個分類建立索引檔案"""
    print("\n建立目錄索引...")
    
    for category, config in JSON_CATEGORIES.items():
        category_dir = DATA_DIR / category
        index_file = category_dir / "README.md"
        
        # 列出該目錄的 JSON 檔案
        json_files = list(category_dir.glob("*.json"))
        
        if json_files or not index_file.exists():
            content = f"""# {category.title()} - JSON 資料檔案

{config['description']}

## 檔案列表

"""
            
            if json_files:
                for json_file in sorted(json_files):
                    # 嘗試讀取 JSON 檔案獲取描述
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            
                        # 提取描述信息
                        description = ""
                        if isinstance(data, dict):
                            if 'description' in data:
                                description = f" - {data['description']}"
                            elif 'project_name' in data:
                                description = f" - {data['project_name']}"
                            elif 'test_name' in data:
                                description = f" - {data['test_name']}"
                            elif 'report_type' in data:
                                description = f" - {data['report_type']}"
                        
                        content += f"- [{json_file.name}](./{json_file.name}){description}\n"
                        
                    except:
                        content += f"- [{json_file.name}](./{json_file.name})\n"
            else:
                content += "目前沒有檔案\n"
            
            content += f"""

## 檔案格式

此目錄包含 JSON 格式的資料檔案，用於：
- 配置設定
- 測試結果記錄
- 專案統計資料
- 驗證報告

---
*最後更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
            
            with open(index_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"   建立索引: docs/data/{category}/README.md")

def create_main_data_index():
    """建立主要的資料目錄索引"""
    main_index = DATA_DIR / "README.md"
    
    content = f"""# Data 目錄 - JSON 資料檔案

此目錄包含專案的各種 JSON 資料檔案，按類型分類存放。

## 目錄結構

"""
    
    for category, config in JSON_CATEGORIES.items():
        category_dir = DATA_DIR / category
        json_count = len(list(category_dir.glob("*.json")))
        content += f"- **[{category}/](./{category}/)** - {config['description']} ({json_count} 個檔案)\n"
    
    content += f"""

## 使用說明

### 配置檔案 (config/)
包含專案配置和統計資料，如：
- 專案基本資訊
- 效能指標
- 系統配置

### 報告檔案 (reports/)  
包含各種測試和驗證報告，如：
- 功能測試結果
- 架構驗證報告
- 效能監控資料

### 存檔檔案 (archive/)
包含歷史資料和備份檔案，如：
- 舊版本測試結果
- 歷史配置備份
- 過時的報告資料

## 檔案管理

- **新增檔案**: 根據用途放入適當的子目錄
- **命名規範**: 使用描述性檔名，包含日期或版本
- **定期清理**: 定期檢查並移除過時的檔案

---
*最後更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*目錄建立: JSON 檔案整理工具*
"""
    
    with open(main_index, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"   建立主索引: docs/data/README.md")

def main():
    """主函數"""
    print("JSON 檔案整理工具")
    print("="*50)
    
    # 建立目錄結構
    create_data_structure()
    
    # 移動檔案
    move_json_files()
    
    # 建立索引
    create_index_files()
    create_main_data_index()
    
    print("\nJSON 檔案整理完成!")
    print(f"資料目錄: {DATA_DIR}")

if __name__ == "__main__":
    main()