#!/usr/bin/env python3
"""
EQC BIN1 核心處理模組
負責主要的EQC BIN1處理邏輯和流程控制
遵循 CLAUDE.md 功能替換原則，從主處理器中獨立出來
"""

import os
import time
from typing import Tuple, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from ..utils.timestamp_extractor import TimestampExtractor
    from ..hyperlinks.hyperlink_processor import HyperlinkProcessor
    from ..monitoring.progress_monitor import ProgressMonitor
    from ..monitoring.debug_logger import EQCDebugLogger
    from .eqc_statistics_calculator import EQCStatisticsCalculator
    from .eqc_file_scanner import EQCFileScanner


class EQCBin1Processor:
    """
    EQC BIN1 核心處理器
    負責主要的處理邏輯和流程控制
    """
    
    def __init__(self, 
                 timestamp_extractor: 'TimestampExtractor',
                 hyperlink_processor: 'HyperlinkProcessor', 
                 progress_monitor: 'ProgressMonitor',
                 debug_logger: 'EQCDebugLogger',
                 statistics_calculator: 'EQCStatisticsCalculator',
                 file_scanner: 'EQCFileScanner'):
        """
        初始化EQC BIN1處理器 - 使用依賴注入
        
        Args:
            timestamp_extractor: 時間戳提取器
            hyperlink_processor: 超連結處理器
            progress_monitor: 進度監控器
            debug_logger: 調試日誌記錄器
            statistics_calculator: 統計計算器
            file_scanner: 檔案掃描器
        """
        self.timestamp_extractor = timestamp_extractor
        self.hyperlink_processor = hyperlink_processor
        self.progress_monitor = progress_monitor
        self.debug_logger = debug_logger
        self.statistics_calculator = statistics_calculator
        self.file_scanner = file_scanner
    
    def process_complete_eqc_integration(self, grouping_result, eqc_files: list, folder_path: str) -> Tuple[Optional[str], Optional[str]]:
        """
        完整的 EQC 整合處理流程 - 直接生成 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv
        
        Args:
            grouping_result: FT-EQC配對結果
            eqc_files: EQC檔案列表
            folder_path: 處理資料夾路徑
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (EQCTOTALDATA.csv路徑, EQCTOTALDATA_RAW.csv路徑)
        """
        print(f"[FIRE] 開始 EQC BIN1 完整整合處理系統（超時限制: {self.progress_monitor.timeout}秒）")
        print("=" * 60)

        # 記錄配對結果到詳細日誌
        self.debug_logger.log_section("FT-EQC 檔案配對處理")
        
        # 記錄所有 FT 檔案
        ft_files = [ft_file for ft_file, _ in grouping_result.matched_pairs]
        self.debug_logger.log_file_scan("FT", ft_files)
        
        # 記錄所有 EQC 檔案（包括配對和未配對的）
        all_eqc_files = [eqc_file for _, eqc_file in grouping_result.matched_pairs] + grouping_result.unmatched_eqc
        self.debug_logger.log_file_scan("EQC", all_eqc_files)
        
        # 記錄配對結果 - 按時間排序
        self.debug_logger.log_section("配對結果詳細資訊")

        # 為配對結果添加時間戳並排序
        pairs_with_timestamps = []
        for ft_file, eqc_file in grouping_result.matched_pairs:
            ft_timestamp = self.timestamp_extractor.extract_internal_timestamp(ft_file)
            eqc_timestamp = self.timestamp_extractor.extract_internal_timestamp(eqc_file)

            # 使用較早的時間戳作為配對的排序基準
            pair_timestamp = min(ft_timestamp or 0, eqc_timestamp or 0)
            pairs_with_timestamps.append((ft_file, eqc_file, ft_timestamp, eqc_timestamp, pair_timestamp))

        # 按時間戳排序 (早→晚)
        pairs_with_timestamps.sort(key=lambda x: x[4])

        # 記錄排序後的配對結果
        for ft_file, eqc_file, ft_timestamp, eqc_timestamp, _ in pairs_with_timestamps:
            ft_time = self.timestamp_extractor.format_timestamp_readable(ft_timestamp)
            eqc_time = self.timestamp_extractor.format_timestamp_readable(eqc_timestamp)

            self.debug_logger.log_file_timestamp(ft_file, ft_timestamp, ft_time)
            self.debug_logger.log_file_timestamp(eqc_file, eqc_timestamp, eqc_time)
            self.debug_logger.log_pairing_result(ft_file, eqc_file, "時間匹配")
        
        # 記錄未配對檔案 (包含內部時間戳)
        self.debug_logger.log_unmatched_files("EQC", grouping_result.unmatched_eqc, self.file_scanner)
        
        # 發現 EQC 檔案總數
        total_eqc_files = len(eqc_files)
        print(f"[CHART] 發現 EQC 檔案總數: {total_eqc_files}")
        
        # 步驟1: 直接基於配對結果計算統計數據
        online_eqc_fail_count, eqc_rt_pass_count = self.statistics_calculator.calculate_statistics_from_pairs(grouping_result)
        
        # 步驟2: 找到 EQC BIN=1 golden IC
        print(f"[SEARCH] 搜尋 EQC BIN=1 資料...")
        golden_content = self.file_scanner.find_online_eqc_bin1_datalog(eqc_files, self.progress_monitor)
        
        if not golden_content:
            print("[ERROR] 無法找到 EQC BIN=1 資料")
            return None, None
        
        # 步驟3: 填入統計資料
        content_lines = golden_content.strip().split('\n')
        content_lines = self.statistics_calculator.fill_eqc_bin1_statistics(
            content_lines, online_eqc_fail_count, eqc_rt_pass_count
        )
        
        # 步驟4: 生成帶超連結的 FT-EQC 失敗配對資料
        ft_eqc_fail_data = self.file_scanner.generate_ft_eqc_fail_data_with_hyperlinks(grouping_result.matched_pairs)
        
        # 步驟5: 添加 FT-EQC 失敗配對資料
        if ft_eqc_fail_data:
            print(f"[BOARD] 添加 {len(ft_eqc_fail_data)} 行 FT-EQC 配對失敗資料")
            content_lines.extend(ft_eqc_fail_data)
        
        # 步驟6: 添加帶超連結的 EQC RT 資料 (按時間排序)
        eqc_rt_data_lines = self.file_scanner.process_eqc_rt_files_sorted(grouping_result.unmatched_eqc, self.progress_monitor)
        
        if eqc_rt_data_lines:
            print(f"[BOARD] 添加 {len(eqc_rt_data_lines)} 行 EQC RT 資料 (已按時間排序)")
            content_lines.extend(eqc_rt_data_lines)
        
        # 步驟7: 生成 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv
        eqc_total_file = os.path.join(folder_path, "EQCTOTALDATA.csv")
        eqc_raw_file = os.path.join(folder_path, "EQCTOTALDATA_RAW.csv")
        
        final_content = '\n'.join(content_lines) + '\n'
        
        # 生成 EQCTOTALDATA.csv
        with open(eqc_total_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        # 複製為 EQCTOTALDATA_RAW.csv
        with open(eqc_raw_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"\n[SAVE] 整合檔案已生成:")
        print(f"   [FILE] EQCTOTALDATA.csv")
        print(f"   [FILE] EQCTOTALDATA_RAW.csv")
        print(f"[RULER] 檔案大小: {len(final_content)} 字元")
        print(f"[FILE] 總行數: {len(content_lines)} 行")
        
        print(f"\n[TARGET] 最終統計結果:")
        print(f"   [CHART] A9/B9 (Online EQC FAIL): {online_eqc_fail_count}")
        print(f"   [CHART] A10/B10 (EQC RT PASS): {eqc_rt_pass_count}")
        print(f"   [CHART] FT-EQC 配對失敗資料: {len(ft_eqc_fail_data)} 行")
        print(f"   [CHART] EQC RT 資料: {len(eqc_rt_data_lines)} 行")
        print(f"   [CHART] 配對成功: {len(grouping_result.matched_pairs)} 對")
        print(f"   [CHART] 未配對 EQC: {len(grouping_result.unmatched_eqc)} 個")
        
        # 步驟8: 顯示關鍵行預覽
        print(f"\n[EDIT] 關鍵內容預覽:")
        for i, line in enumerate(content_lines[:15], 1):
            if i == 9 or i == 10 or i >= 13:
                line_preview = line[:100] + "..." if len(line) > 100 else line
                print(f"   第{i:2d}行: {line_preview}")
        
        # 保存詳細日誌
        self.debug_logger.log_summary(online_eqc_fail_count, eqc_rt_pass_count, len(grouping_result.matched_pairs))
        self.debug_logger.save_log()
        
        # [FIRE] 計算總處理時間
        total_time = self.progress_monitor.get_elapsed_time()
        print(f"[TIME] 總處理時間: {total_time:.1f} 秒")
        
        return eqc_total_file, eqc_raw_file