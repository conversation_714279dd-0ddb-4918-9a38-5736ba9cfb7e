"""
擴展配置管理系統測試
TASK_002: 實現靈活的配置管理系統
遵循 TDD 原則 - 先寫失敗的測試
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from typing import Dict, Any

from backend.shared.infrastructure.config.config_manager import (
    ConfigManager,
    FileStorageConfig,
    OutlookConfig,
    VendorConfig,
    PerformanceConfig,
    SecurityConfig,
)


class TestAdvancedConfigManager:
    """測試擴展配置管理系統"""

    def test_config_manager_initialization(self):
        """測試配置管理器初始化"""
        config_manager = ConfigManager()
        
        assert config_manager.current_env == "development"
        assert config_manager.config is not None
        assert hasattr(config_manager.config, 'database')
        assert hasattr(config_manager.config, 'email')

    def test_environment_switching(self):
        """測試環境切換功能"""
        config_manager = ConfigManager()
        
        # 測試切換到 staging 環境
        config_manager.switch_environment("staging")
        assert config_manager.current_env == "staging"
        
        # 測試切換到 production 環境
        config_manager.switch_environment("production")
        assert config_manager.current_env == "production"
        
        # 測試無效環境
        with pytest.raises(ValueError, match="不支援的環境"):
            config_manager.switch_environment("invalid_env")

    def test_config_file_loading(self):
        """測試從檔案載入配置"""
        # 建立臨時配置檔案
        config_data = {
            "app_name": "測試應用程式",
            "debug": True,
            "database": {
                "driver": "postgresql",
                "host": "test.database.com",
                "port": 5432,
                "database_name": "test_outlook_summary"
            },
            "file_storage": {
                "temp_dir": "/tmp/test_outlook",
                "network_base_path": "//test-server/outlook-data",
                "max_file_size_mb": 100,
                "allowed_extensions": [".csv", ".xlsx", ".zip"]
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f, allow_unicode=True)
            config_file = f.name
        
        try:
            config_manager = ConfigManager()
            loaded_config = config_manager.load_from_file(config_file)
            
            assert loaded_config.app_name == "測試應用程式"
            assert loaded_config.debug is True
            assert loaded_config.database.host == "test.database.com"
            assert loaded_config.file_storage.max_file_size_mb == 100
            
        finally:
            Path(config_file).unlink()

    def test_file_storage_config(self):
        """測試檔案儲存配置"""
        storage_config = FileStorageConfig(
            temp_dir="/tmp/outlook_test",
            network_base_path="//server/data",
            max_file_size_mb=50,
            allowed_extensions=[".csv", ".xlsx"]
        )
        
        assert storage_config.temp_dir == Path("/tmp/outlook_test")
        assert storage_config.network_base_path == "//server/data"
        assert storage_config.max_file_size_mb == 50
        assert ".csv" in storage_config.allowed_extensions
        assert storage_config.cleanup_after_days == 7  # 預設值

    def test_outlook_config(self):
        """測試 Outlook 配置"""
        outlook_config = OutlookConfig(
            monitor_folder="Inbox",
            backup_folder="Processed",
            check_interval_seconds=30,
            auto_mark_read=True
        )
        
        assert outlook_config.monitor_folder == "Inbox"
        assert outlook_config.backup_folder == "Processed"
        assert outlook_config.check_interval_seconds == 30
        assert outlook_config.auto_mark_read is True
        assert outlook_config.max_email_age_days == 30  # 預設值

    def test_vendor_config_management(self):
        """測試廠商配置管理"""
        vendor_configs = {
            "GTK": VendorConfig(
                name="Greatek Technology",
                email_patterns=["@gtk.com", "@greatek.com"],
                subject_keywords=["ft hold", "ft lot"],
                parser_class="GTKParser",
                enabled=True,
                priority=1
            ),
            "ETD": VendorConfig(
                name="Elite Test Division",
                email_patterns=["@etd.com"],
                subject_keywords=["anf"],
                parser_class="ETDParser",
                enabled=True,
                priority=2
            )
        }
        
        config_manager = ConfigManager()
        config_manager.set_vendor_configs(vendor_configs)
        
        # 測試取得廠商配置
        gtk_config = config_manager.get_vendor_config("GTK")
        assert gtk_config.name == "Greatek Technology"
        assert "@gtk.com" in gtk_config.email_patterns
        
        # 測試取得所有啟用的廠商
        enabled_vendors = config_manager.get_enabled_vendors()
        assert "GTK" in enabled_vendors
        assert "ETD" in enabled_vendors

    def test_performance_config(self):
        """測試效能配置"""
        perf_config = PerformanceConfig(
            max_concurrent_emails=5,
            email_processing_timeout=300,
            file_processing_timeout=600,
            memory_limit_mb=512,
            enable_caching=True,
            cache_ttl_seconds=3600
        )
        
        assert perf_config.max_concurrent_emails == 5
        assert perf_config.email_processing_timeout == 300
        assert perf_config.enable_caching is True
        assert perf_config.cache_ttl_seconds == 3600

    def test_security_config(self):
        """測試安全配置"""
        security_config = SecurityConfig(
            encrypt_sensitive_data=True,
            log_email_content=False,
            allowed_file_types=[".csv", ".xlsx", ".zip"],
            max_attachment_size_mb=50,
            virus_scan_enabled=True
        )
        
        assert security_config.encrypt_sensitive_data is True
        assert security_config.log_email_content is False
        assert ".csv" in security_config.allowed_file_types
        assert security_config.max_attachment_size_mb == 50

    def test_config_validation(self):
        """測試配置驗證"""
        config_manager = ConfigManager()
        
        # 測試有效配置
        valid_config = config_manager.config
        validation_result = config_manager.validate_config(valid_config)
        assert validation_result.is_valid is True
        assert len(validation_result.errors) == 0
        
        # 測試無效配置
        invalid_config_data = {
            "database": {
                "port": -1,  # 無效埠號
                "driver": "unsupported"  # 不支援的驅動
            },
            "file_storage": {
                "max_file_size_mb": -5  # 無效大小
            }
        }
        
        validation_result = config_manager.validate_config_dict(invalid_config_data)
        assert validation_result.is_valid is False
        assert len(validation_result.errors) > 0

    def test_config_hot_reload(self):
        """測試配置熱重載"""
        config_manager = ConfigManager()
        original_debug = config_manager.config.debug
        
        # 模擬配置變更
        new_config_data = {
            "debug": not original_debug,
            "app_name": "熱重載測試"
        }
        
        config_manager.reload_config(new_config_data)
        
        assert config_manager.config.debug != original_debug
        assert config_manager.config.app_name == "熱重載測試"

    def test_config_merging(self):
        """測試配置合併"""
        config_manager = ConfigManager()
        
        base_config = {
            "app_name": "基礎應用",
            "debug": False,
            "database": {
                "host": "localhost",
                "port": 5432
            }
        }
        
        override_config = {
            "debug": True,
            "database": {
                "host": "remote.server.com"
            },
            "new_feature": {
                "enabled": True
            }
        }
        
        merged_config = config_manager.merge_configs(base_config, override_config)
        
        assert merged_config["app_name"] == "基礎應用"  # 保留基礎值
        assert merged_config["debug"] is True  # 覆蓋值
        assert merged_config["database"]["host"] == "remote.server.com"  # 覆蓋巢狀值
        assert merged_config["database"]["port"] == 5432  # 保留基礎巢狀值
        assert merged_config["new_feature"]["enabled"] is True  # 新增值

    def test_config_encryption(self):
        """測試敏感配置加密"""
        config_manager = ConfigManager()
        
        sensitive_data = {
            "database_password": "super_secret_password",
            "api_key": "secret_api_key_12345",
            "encryption_key": "another_secret"
        }
        
        # 加密敏感資料
        encrypted_data = config_manager.encrypt_sensitive_config(sensitive_data)
        
        # 驗證資料已加密
        assert encrypted_data["database_password"] != "super_secret_password"
        assert encrypted_data["api_key"] != "secret_api_key_12345"
        
        # 解密並驗證
        decrypted_data = config_manager.decrypt_sensitive_config(encrypted_data)
        assert decrypted_data["database_password"] == "super_secret_password"
        assert decrypted_data["api_key"] == "secret_api_key_12345"