"""
GTK 解析器單元測試
遵循 TDD 開發，先寫失敗的測試

基於 VBA 原始邏輯：
- 識別條件：主旨包含 "ft hold" 或 "ft lot"
- 提取 MO、LOT、YIELD 等關鍵字
- 處理 GTK 廠商特有的格式
"""

import pytest
from unittest.mock import Mock
from datetime import datetime

from backend.email.models.email_models import EmailData, EmailAttachment
from backend.email.parsers.gtk_parser import GTKParser
from backend.email.parsers.base_parser import ParsingError, ParsingContext


class TestGTKParser:
    """GTK 解析器測試類別"""

    def setup_method(self):
        """每個測試方法前的設置"""
        self.parser = GTKParser()
        # 創建預設的解析上下文
        self.context = ParsingContext(
            email_data=None,  # 在實際測試中會設定
            vendor_code="GTK"
        )

    def test_gtk_parser_initialization(self):
        """測試 GTK 解析器初始化"""
        assert self.parser.vendor_name == "GTK"
        assert self.parser.vendor_code == "GTK"
        assert self.parser.get_confidence_threshold() == 0.8
        assert "ft hold" in self.parser.supported_patterns
        assert "ft lot" in self.parser.supported_patterns

    def test_can_parse_ft_hold_subject(self):
        """測試識別包含 'ft hold' 的主旨"""
        # 基於 VBA 邏輯：If InStr(1, LCase(subject), "ft hold") > 0
        email = EmailData(
            subject="GTK FT HOLD MO:F123456 LOT:ABC123 YIELD:95.5%",
            sender="<EMAIL>",
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8
        assert "ft hold" in result.matching_patterns

    def test_can_parse_ft_lot_subject(self):
        """測試識別包含 'ft lot' 的主旨"""
        # 基於 VBA 邏輯：If InStr(1, LCase(subject), "ft lot") > 0
        email = EmailData(
            subject="GTK FT LOT processing MO:F123456 LOT:XYZ789",
            sender="<EMAIL>", 
            body="Test email body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True
        assert result.confidence_score >= 0.8

    def test_cannot_parse_non_gtk_subject(self):
        """測試不能識別非 GTK 格式的主旨"""
        email = EmailData(
            subject="ETD ANF processing data",
            sender="<EMAIL>",
            body="Test email body", 
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is False
        assert result.confidence_score < 0.5

    def test_extract_keyword_value_basic(self):
        """測試基本關鍵字提取功能"""
        # 基於 VBA GetKeywordValue 函數邏輯
        subject = "GTK FT HOLD MO:F123456 LOT:ABC123 YIELD:95.5%"
        
        mo_value = self.parser.extract_keyword_value("MO", subject)
        assert mo_value == "F123456"
        
        lot_value = self.parser.extract_keyword_value("LOT", subject)
        assert lot_value == "ABC123"
        
        yield_value = self.parser.extract_keyword_value("YIELD", subject)
        assert yield_value == "95.5%"

    def test_extract_keyword_value_with_separators(self):
        """測試包含分隔符的關鍵字提取"""
        # 基於 VBA 邏輯：處理 "~", " ", "_" 分隔符
        subject = "GTK FT HOLD MO:F123456~extra LOT:ABC123 more_data YIELD:95.5% other"
        
        mo_value = self.parser.extract_keyword_value("MO", subject)
        assert mo_value == "F123456"  # 應該在 "~" 前停止
        
        lot_value = self.parser.extract_keyword_value("LOT", subject)
        assert lot_value == "ABC123"  # 應該在空格前停止

    def test_extract_keyword_value_not_found(self):
        """測試找不到關鍵字時的處理"""
        # 基於 VBA 邏輯：返回 "?"
        subject = "GTK FT HOLD processing data"
        
        missing_value = self.parser.extract_keyword_value("MISSING", subject)
        assert missing_value == "?"

    def test_parse_complete_gtk_email(self):
        """測試完整的 GTK 郵件解析"""
        email = EmailData(
            subject="GTK FT HOLD MO:F123456 LOT:ABC123 YIELD:95.5%",
            sender="<EMAIL>",
            body="BIN1: 1000 units\nTotal processed: 1050 units",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="GTK")
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "GTK"
        assert result.mo_number == "F123456"
        assert result.lot_number == "ABC123"
        assert result.extracted_data["yield_value"] == "95.5%"
        assert result.is_success is True

    def test_parse_with_bin1_extraction(self):
        """測試 BIN1 資料提取"""
        # 基於 VBA FindBin1Line 函數邏輯
        email = EmailData(
            subject="GTK FT HOLD MO:F123456 LOT:ABC123",
            sender="<EMAIL>",
            body="Processing status:\nBIN1: 950 passed units\nBIN2: 50 failed units",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="GTK")
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "GTK"
        assert result.extracted_data.get("bin1_data") == "BIN1: 950 passed units"

    def test_parse_missing_keywords_partial_success(self):
        """測試部分關鍵字缺失的情況"""
        email = EmailData(
            subject="GTK FT HOLD MO:F123456",  # 缺少 LOT 和 YIELD
            sender="<EMAIL>",
            body="Test body",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="GTK")
        result = self.parser.parse_email(context)
        
        assert result.vendor_code == "GTK"
        assert result.mo_number == "F123456"
        assert result.lot_number == "?"  # 應該是預設值
        assert result.extracted_data.get("yield_value", "?") == "?"  # 應該是預設值
        assert result.is_success is True  # 部分成功也算成功

    def test_parse_invalid_email_format(self):
        """測試無效郵件格式的錯誤處理"""
        email = EmailData(
            subject="",  # 空主旨
            sender="<EMAIL>",
            body="Test body",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="GTK")
        result = self.parser.parse_email(context)
        # GTK 解析器應該優雅處理空主旨，不拋出異常
        assert result.vendor_code == "GTK"
        assert result.is_success is False

    def test_extract_in_qty_from_body(self):
        """測試從郵件內容提取入料數量"""
        # 基於 VBA GetInQty 函數邏輯（具體實作需要參考 VBA 程式碼）
        email = EmailData(
            subject="GTK FT HOLD MO:F123456",
            sender="<EMAIL>",
            body="Input quantity: 1000 units\nProcessed: 950 units",
            received_time=datetime.now()
        )
        
        in_qty = self.parser.extract_in_qty(email)
        assert in_qty is not None
        # 具體數值需要根據 VBA GetInQty 實作來確定

    def test_case_insensitive_parsing(self):
        """測試大小寫不敏感的解析"""
        # 基於 VBA LCase 邏輯
        email = EmailData(
            subject="gtk Ft Hold mo:f123456 lot:abc123",  # 小寫
            sender="<EMAIL>",
            body="Test body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(email)
        assert result.is_identified is True

    def test_gtk_confidence_scoring(self):
        """測試 GTK 解析器的信心分數計算"""
        # 完全匹配的情況
        perfect_email = EmailData(
            subject="GTK FT HOLD MO:F123456 LOT:ABC123 YIELD:95.5%",
            sender="<EMAIL>",
            body="Standard GTK processing",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(perfect_email)
        assert result.confidence_score >= 0.9

        # 部分匹配的情況
        partial_email = EmailData(
            subject="Some other text ft hold processing",
            sender="<EMAIL>",
            body="Generic body",
            received_time=datetime.now()
        )
        
        result = self.parser.identify_vendor(partial_email)
        assert 0.5 <= result.confidence_score < 0.9

    def test_error_handling_malformed_subject(self):
        """測試畸形主旨的錯誤處理"""
        email = EmailData(
            subject="GTK FT HOLD MO: LOT: YIELD:",  # 空值
            sender="<EMAIL>",
            body="Test body",
            received_time=datetime.now()
        )
        
        context = ParsingContext(email_data=email, vendor_code="GTK")
        result = self.parser.parse_email(context)
        # 應該優雅地處理，不應該拋出異常
        assert result.vendor_code == "GTK"
        assert result.is_success is True  # 即使資料不完整

    def test_performance_with_large_body(self):
        """測試大型郵件內容的效能"""
        large_body = "Line data\n" * 10000 + "BIN1: 950 units\n" + "More data\n" * 10000
        
        email = EmailData(
            subject="GTK FT HOLD MO:F123456",
            sender="<EMAIL>", 
            body=large_body,
            received_time=datetime.now()
        )
        
        import time
        start_time = time.time()
        context = ParsingContext(email_data=email, vendor_code="GTK")
        result = self.parser.parse_email(context)
        end_time = time.time()
        
        # 解析應該在 1 秒內完成
        assert end_time - start_time < 1.0
        assert result.vendor_code == "GTK"

    def test_thread_safety(self):
        """測試多執行緒安全性"""
        import threading
        import concurrent.futures
        
        def parse_email(i):
            email = EmailData(
                subject=f"GTK FT HOLD MO:F{i:06d} LOT:ABC{i:03d}",
                sender="<EMAIL>",
                body=f"Test body {i}",
                received_time=datetime.now()
            )
            context = ParsingContext(email_data=email, vendor_code="GTK")
            return self.parser.parse_email(context)
        
        # 並行解析多個郵件
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(parse_email, i) for i in range(100)]
            results = [future.result() for future in futures]
        
        # 所有解析都應該成功
        assert len(results) == 100
        assert all(result.vendor_code == "GTK" for result in results)
        assert all(result.is_success for result in results)