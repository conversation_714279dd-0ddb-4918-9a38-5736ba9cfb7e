#!/usr/bin/env python3
"""
異步檔案鎖定管理器
基於 asyncio 的檔案鎖定管理，解決同步鎖在異步上下文中的阻塞問題
"""

import os
import time
import hashlib
import asyncio
from pathlib import Path
from typing import Dict, Set, Optional, List
from datetime import datetime, timedelta
from loguru import logger

class AsyncFileLockManager:
    """異步檔案鎖定管理器 - 防止並發檔案衝突"""
    
    def __init__(self):
        self._locks: Dict[str, Dict] = {}  # path_hash -> lock_info
        self._lock = asyncio.Lock()  # 異步鎖替代同步 threading.Lock
        self._cleanup_task = None  # 清理任務
        self._is_initialized = False
        
    async def initialize(self):
        """初始化異步管理器"""
        if not self._is_initialized:
            # 啟動定期清理任務
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            self._is_initialized = True
            logger.info("🔄 異步檔案鎖定管理器已初始化")
    
    async def shutdown(self):
        """關閉異步管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("🔒 異步檔案鎖定管理器已關閉")
        
    def _get_path_hash(self, folder_path: str) -> str:
        """獲取路徑的唯一哈希值"""
        normalized_path = os.path.normpath(folder_path).lower()
        return hashlib.md5(normalized_path.encode()).hexdigest()
    
    async def _is_path_conflict_async(self, folder_path: str) -> Optional[str]:
        """異步檢查路徑是否與現有鎖定路徑衝突"""
        # 讓出控制權，避免阻塞事件循環
        await asyncio.sleep(0)
        
        normalized_path = os.path.normpath(folder_path).lower()
        
        for path_hash, lock_info in self._locks.items():
            locked_path = os.path.normpath(lock_info['path']).lower()
            
            # 檢查是否為相同路徑或父子路徑關係
            if (normalized_path == locked_path or 
                normalized_path.startswith(locked_path + os.sep) or
                locked_path.startswith(normalized_path + os.sep)):
                return lock_info['session_id']
        
        return None
    
    async def acquire_lock_async(
        self, 
        folder_path: str, 
        session_id: str, 
        user_id: str,
        timeout_hours: int = 2
    ) -> Dict[str, any]:
        """
        異步獲取路徑鎖定
        
        Args:
            folder_path: 資料夾路徑
            session_id: 會話ID
            user_id: 用戶ID
            timeout_hours: 鎖定超時時間（小時）
        
        Returns:
            Dict with 'success', 'message', 'conflict_session', 'lock_info' keys
        """
        async with self._lock:
            try:
                # 異步檢查路徑衝突
                conflict_session = await self._is_path_conflict_async(folder_path)
                if conflict_session:
                    logger.warning(f"⚠️ 路徑衝突檢測: {folder_path} (衝突會話: {conflict_session})")
                    return {
                        'success': False,
                        'message': f'路徑衝突：該路徑或相關路徑正在被其他會話處理',
                        'conflict_session': conflict_session,
                        'lock_info': None
                    }
                
                # 獲取鎖定
                path_hash = self._get_path_hash(folder_path)
                lock_info = {
                    'path': folder_path,
                    'session_id': session_id,
                    'user_id': user_id,
                    'locked_at': datetime.now(),
                    'expires_at': datetime.now() + timedelta(hours=timeout_hours),
                    'path_hash': path_hash
                }
                
                self._locks[path_hash] = lock_info
                
                logger.info(f"🔒 異步路徑鎖定成功: {folder_path} (會話: {session_id})")
                return {
                    'success': True,
                    'message': '路徑鎖定成功',
                    'conflict_session': None,
                    'lock_info': lock_info.copy()
                }
                
            except Exception as e:
                logger.error(f"❌ 異步獲取鎖定失敗: {e}")
                return {
                    'success': False,
                    'message': f'獲取鎖定失敗: {str(e)}',
                    'conflict_session': None,
                    'lock_info': None
                }
    
    async def release_lock_async(self, folder_path: str, session_id: str) -> bool:
        """異步釋放路徑鎖定"""
        async with self._lock:
            try:
                path_hash = self._get_path_hash(folder_path)
                
                if path_hash in self._locks:
                    lock_info = self._locks[path_hash]
                    if lock_info['session_id'] == session_id:
                        del self._locks[path_hash]
                        logger.info(f"🔓 異步路徑鎖定釋放: {folder_path} (會話: {session_id})")
                        return True
                    else:
                        logger.warning(f"⚠️ 無權釋放鎖定: {folder_path} (會話: {session_id})")
                        return False
                
                return True  # 鎖定不存在，視為成功
                
            except Exception as e:
                logger.error(f"❌ 異步釋放鎖定失敗: {e}")
                return False
    
    async def cleanup_expired_locks_async(self) -> int:
        """異步清理過期的鎖定"""
        async with self._lock:
            try:
                now = datetime.now()
                expired_hashes = []

                for path_hash, lock_info in self._locks.items():
                    if now > lock_info['expires_at']:
                        expired_hashes.append(path_hash)

                cleaned_count = 0
                for path_hash in expired_hashes:
                    lock_info = self._locks[path_hash]
                    del self._locks[path_hash]
                    cleaned_count += 1
                    logger.info(f"🧹 清理過期鎖定: {lock_info['path']} (會話: {lock_info['session_id']})")
                
                if cleaned_count > 0:
                    logger.info(f"🧹 異步清理完成，釋放了 {cleaned_count} 個過期鎖定")
                
                return cleaned_count
                
            except Exception as e:
                logger.error(f"❌ 異步清理過期鎖定失敗: {e}")
                return 0
    
    async def get_active_locks_async(self) -> Dict[str, Dict]:
        """異步獲取所有活躍的鎖定"""
        async with self._lock:
            try:
                # 先在同一鎖內清理過期鎖定，避免死鎖
                now = datetime.now()
                expired_hashes = []
                for path_hash, lock_info in self._locks.items():
                    if now > lock_info['expires_at']:
                        expired_hashes.append(path_hash)
                
                # 清理過期鎖定
                cleaned_count = 0
                for path_hash in expired_hashes:
                    if path_hash in self._locks:  # 雙重檢查
                        lock_info = self._locks[path_hash]
                        del self._locks[path_hash]
                        cleaned_count += 1
                        logger.info(f"🧹 清理過期鎖定: {lock_info['path']} (會話: {lock_info['session_id']})")
                
                if cleaned_count > 0:
                    logger.info(f"🧹 異步清理完成，釋放了 {cleaned_count} 個過期鎖定")
                
                # 返回活躍鎖定的深拷貝
                active_locks = {}
                for path_hash, lock_info in self._locks.items():
                    active_locks[path_hash] = lock_info.copy()
                
                return {
                    'locks': active_locks,
                    'total_locks': len(active_locks),
                    'timestamp': datetime.now().isoformat()
                }
                
            except Exception as e:
                logger.error(f"❌ 異步獲取活躍鎖定失敗: {e}")
                return {
                    'locks': {},
                    'total_locks': 0,
                    'timestamp': datetime.now().isoformat(),
                    'error': str(e)
                }
    
    async def is_path_locked_async(self, folder_path: str) -> Optional[Dict]:
        """異步檢查路徑是否被鎖定"""
        async with self._lock:
            try:
                conflict_session = await self._is_path_conflict_async(folder_path)
                if conflict_session:
                    # 找到衝突的鎖定信息
                    for lock_info in self._locks.values():
                        if lock_info['session_id'] == conflict_session:
                            return lock_info.copy()
                return None
                
            except Exception as e:
                logger.error(f"❌ 異步檢查路徑鎖定失敗: {e}")
                return None
    
    async def get_session_locks_async(self, session_id: str) -> List[Dict]:
        """異步獲取指定會話的所有鎖定"""
        async with self._lock:
            try:
                session_locks = []
                for lock_info in self._locks.values():
                    if lock_info['session_id'] == session_id:
                        session_locks.append(lock_info.copy())
                return session_locks
                
            except Exception as e:
                logger.error(f"❌ 異步獲取會話鎖定失敗: {e}")
                return []
    
    async def force_release_session_locks_async(self, session_id: str) -> int:
        """異步強制釋放指定會話的所有鎖定"""
        async with self._lock:
            try:
                to_remove = []
                for path_hash, lock_info in self._locks.items():
                    if lock_info['session_id'] == session_id:
                        to_remove.append(path_hash)
                
                released_count = 0
                for path_hash in to_remove:
                    lock_info = self._locks[path_hash]
                    del self._locks[path_hash]
                    released_count += 1
                    logger.warning(f"🔓 強制釋放鎖定: {lock_info['path']} (會話: {session_id})")
                
                if released_count > 0:
                    logger.warning(f"🔓 強制釋放完成，釋放了 {released_count} 個鎖定 (會話: {session_id})")
                
                return released_count
                
            except Exception as e:
                logger.error(f"❌ 異步強制釋放鎖定失敗: {e}")
                return 0
    
    async def get_user_locks_async(self, user_id: str) -> List[Dict]:
        """異步獲取指定用戶的所有鎖定"""
        async with self._lock:
            try:
                user_locks = []
                for lock_info in self._locks.values():
                    if lock_info['user_id'] == user_id:
                        user_locks.append(lock_info.copy())
                return user_locks
                
            except Exception as e:
                logger.error(f"❌ 異步獲取用戶鎖定失敗: {e}")
                return []
    
    async def get_lock_stats_async(self) -> Dict:
        """異步獲取鎖定統計信息"""
        async with self._lock:
            try:
                now = datetime.now()
                
                # 統計信息
                total_locks = len(self._locks)
                expired_count = 0
                users = set()
                sessions = set()
                
                for lock_info in self._locks.values():
                    if now > lock_info['expires_at']:
                        expired_count += 1
                    users.add(lock_info['user_id'])
                    sessions.add(lock_info['session_id'])
                
                return {
                    'total_locks': total_locks,
                    'active_locks': total_locks - expired_count,
                    'expired_locks': expired_count,
                    'unique_users': len(users),
                    'unique_sessions': len(sessions),
                    'timestamp': now.isoformat()
                }
                
            except Exception as e:
                logger.error(f"❌ 異步獲取鎖定統計失敗: {e}")
                return {
                    'total_locks': 0,
                    'active_locks': 0,
                    'expired_locks': 0,
                    'unique_users': 0,
                    'unique_sessions': 0,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
    
    async def _periodic_cleanup(self):
        """定期清理過期鎖定的後台任務"""
        while True:
            try:
                # 每 30 分鐘清理一次過期鎖定
                await asyncio.sleep(30 * 60)  # 30 minutes
                
                cleaned_count = await self.cleanup_expired_locks_async()
                if cleaned_count > 0:
                    logger.info(f"🔄 定期清理完成，釋放了 {cleaned_count} 個過期鎖定")
                    
            except asyncio.CancelledError:
                logger.info("🛑 定期清理任務已取消")
                break
            except Exception as e:
                logger.error(f"❌ 定期清理任務失敗: {e}")
                # 繼續運行，不要停止清理任務
                await asyncio.sleep(60)  # 出錯後等待 1 分鐘再重試

# 全局實例
_async_file_lock_manager = None

async def get_async_file_lock_manager() -> AsyncFileLockManager:
    """獲取異步檔案鎖定管理器實例"""
    global _async_file_lock_manager
    if _async_file_lock_manager is None:
        _async_file_lock_manager = AsyncFileLockManager()
        await _async_file_lock_manager.initialize()
    return _async_file_lock_manager

def get_async_file_lock_manager_sync() -> AsyncFileLockManager:
    """同步獲取異步檔案鎖定管理器實例（不初始化）"""
    global _async_file_lock_manager
    if _async_file_lock_manager is None:
        _async_file_lock_manager = AsyncFileLockManager()
    return _async_file_lock_manager

# 兼容性包裝器 - 將異步操作包裝為同步
class AsyncToSyncWrapper:
    """
    將異步檔案鎖定管理器包裝為同步接口
    用於與現有同步代碼的兼容性
    """
    
    def __init__(self, async_manager: AsyncFileLockManager):
        self.async_manager = async_manager
    
    def acquire_lock(self, folder_path: str, session_id: str, user_id: str) -> Dict[str, any]:
        """同步版本的獲取鎖定"""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.async_manager.acquire_lock_async(folder_path, session_id, user_id)
            )
        except RuntimeError:
            # 如果沒有運行中的事件循環，創建一個新的
            return asyncio.run(
                self.async_manager.acquire_lock_async(folder_path, session_id, user_id)
            )
    
    def release_lock(self, folder_path: str, session_id: str) -> bool:
        """同步版本的釋放鎖定"""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.async_manager.release_lock_async(folder_path, session_id)
            )
        except RuntimeError:
            return asyncio.run(
                self.async_manager.release_lock_async(folder_path, session_id)
            )
    
    def get_active_locks(self) -> Dict[str, Dict]:
        """同步版本的獲取活躍鎖定"""
        try:
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(
                self.async_manager.get_active_locks_async()
            )
            return result.get('locks', {})
        except RuntimeError:
            result = asyncio.run(
                self.async_manager.get_active_locks_async()
            )
            return result.get('locks', {})
    
    def cleanup_expired_locks(self):
        """同步版本的清理過期鎖定"""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.async_manager.cleanup_expired_locks_async()
            )
        except RuntimeError:
            return asyncio.run(
                self.async_manager.cleanup_expired_locks_async()
            )
    
    def is_path_locked(self, folder_path: str) -> Optional[Dict]:
        """同步版本的檢查路徑鎖定"""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.async_manager.is_path_locked_async(folder_path)
            )
        except RuntimeError:
            return asyncio.run(
                self.async_manager.is_path_locked_async(folder_path)
            )

def get_sync_wrapper() -> AsyncToSyncWrapper:
    """獲取同步包裝器實例"""
    async_manager = get_async_file_lock_manager_sync()
    return AsyncToSyncWrapper(async_manager)