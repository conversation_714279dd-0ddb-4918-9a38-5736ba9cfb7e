#!/usr/bin/env python3
"""
固定測試程式 - 完整的 CSV 到 Excel 轉換流程
輸入: CSV 檔案
經過: csv_to_excel_converter.py (包含 Site 和 BIN 分佈)
輸出: Excel 檔案
"""

import os
import sys
from datetime import datetime
import time
from csv_to_excel_converter import CsvToExcelConverter

def log_processing_time(csv_filename: str, processing_time: float, file_size: int, start_time: datetime, end_time: datetime, success: bool = True, error_msg: str = None):
    """
    記錄程式處理時間到 logs/datalog.txt
    """
    try:
        # 確保 logs 目錄存在
        logs_dir = "/mnt/d/project/python/outlook_summary/logs"
        os.makedirs(logs_dir, exist_ok=True)
        log_filename = os.path.join(logs_dir, "datalog.txt")
        
        # 準備日誌內容
        log_content = []
        log_content.append("[CHART] 固定測試程式處理時間記錄")
        log_content.append("=" * 80)
        log_content.append(f"檔案名稱: {csv_filename}")
        log_content.append(f"開始時間: {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        log_content.append(f"結束時間: {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        log_content.append(f"處理時間: {processing_time:.3f} 秒")
        
        if success:
            log_content.append(f"處理狀態: [OK] 成功")
            log_content.append(f"輸出檔案大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            log_content.append("")
            log_content.append("[TOOL] 處理步驟完成狀態:")
            log_content.append("  步驟 1: CSV 結構補全 [OK]")
            log_content.append("  步驟 2: BIN 號碼分配 [OK]")
            log_content.append("  步驟 3: BIN1 保護機制 [OK]")
            log_content.append("  步驟 4: 全設備 BIN 分類 [OK]")
            log_content.append("  步驟 5: Site 統計與分佈 [OK]")
            log_content.append("  步驟 6: Summary 工作表生成 [OK]")
            log_content.append("  步驟 7: Excel 檔案輸出 [OK]")
        else:
            log_content.append(f"處理狀態: [ERROR] 失敗")
            if error_msg:
                log_content.append(f"錯誤訊息: {error_msg}")
        
        log_content.append("")
        log_content.append("[FAST] 效能統計:")
        log_content.append(f"  總處理時間: {processing_time:.3f} 秒")
        
        if processing_time > 0:
            if file_size > 0:
                throughput_mb_s = (file_size / 1024 / 1024) / processing_time
                log_content.append(f"  處理速度: {throughput_mb_s:.2f} MB/秒")
            
            steps_per_second = 7 / processing_time  # 7個主要步驟
            log_content.append(f"  步驟處理速度: {steps_per_second:.2f} 步驟/秒")
        
        log_content.append("")
        log_content.append(f"固定測試程式記錄完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 寫入日誌檔案 (附加模式)
        with open(log_filename, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"固定測試程式處理時間記錄 - {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*80}\n")
            f.write('\n'.join(log_content))
            f.write(f"\n{'='*80}\n\n")
        
        print(f"\n[EDIT] 處理時間記錄已保存到: datalog.txt")
        
    except Exception as e:
        print(f"\n[WARNING] 無法保存處理時間記錄: {str(e)}")

def process_csv_file(csv_filename: str):
    """
    處理 CSV 檔案的完整流程
    """
    print(f"[ROCKET] 固定測試程式 - CSV 到 Excel 完整轉換")
    print(f"{'='*60}")
    
    # 檔案路徑設定
    csv_file_path = f"/mnt/d/project/python/outlook_summary/doc/{csv_filename}"
    
    # 保持輸入檔名作為輸出檔名
    base_name = csv_filename.replace('.csv', '')
    output_file_path = f"/mnt/d/project/python/outlook_summary/logs/{base_name}.xlsx"
    
    print(f"[FOLDER] 輸入檔案: {csv_file_path}")
    print(f"[FOLDER] 輸出檔案: {output_file_path}")
    
    # 檢查輸入檔案是否存在
    if not os.path.exists(csv_file_path):
        print(f"[ERROR] 錯誤: 找不到檔案 {csv_file_path}")
        return False
    
    # 記錄開始時間（高精度）
    start_time = datetime.now()
    start_perf = time.perf_counter()
    
    try:
        # 初始化轉換器
        print(f"\n[TOOL] 初始化 CsvToExcelConverter...")
        converter = CsvToExcelConverter()
        
        # 執行完整轉換流程
        print(f"[REFRESH] 開始執行完整轉換流程...")
        
        result = converter.convert_csv_to_excel(
            csv_file_path=csv_file_path,
            output_file_path=output_file_path
        )
        
        # 記錄結束時間（高精度）
        end_time = datetime.now()
        end_perf = time.perf_counter()
        processing_time = end_perf - start_perf
        
        # 檢查結果
        if os.path.exists(output_file_path):
            file_size = os.path.getsize(output_file_path)
            print(f"\n[OK] 轉換成功完成！")
            print(f"[TIME] 處理時間: {processing_time:.3f} 秒")
            print(f"[CHART] 輸出檔案大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            print(f"[FILE] 輸出位置: {output_file_path}")
            
            # 顯示處理摘要
            print(f"\n[BOARD] 處理摘要:")
            print(f"  [TOOL] 步驟 1: CSV 結構補全 [OK]")
            print(f"  [TOOL] 步驟 2: BIN 號碼分配 [OK]") 
            print(f"  [TOOL] 步驟 3: BIN1 保護機制 [OK]")
            print(f"  [TOOL] 步驟 4: 全設備 BIN 分類 [OK]")
            print(f"  [TOOL] 步驟 5: Site 統計與分佈 [OK]")
            print(f"  [TOOL] 步驟 6: Summary 工作表生成 [OK]")
            print(f"  [CHART] Excel 檔案生成 [OK]")
            
            # 記錄成功的處理時間
            log_processing_time(csv_filename, processing_time, file_size, start_time, end_time, success=True)
            
            return True
        else:
            print(f"[ERROR] 錯誤: 輸出檔案未生成")
            
            # 記錄失敗的處理時間
            log_processing_time(csv_filename, processing_time, 0, start_time, end_time, success=False, error_msg="輸出檔案未生成")
            
            return False
            
    except Exception as e:
        # 記錄結束時間（即使失敗）
        end_time = datetime.now()
        end_perf = time.perf_counter()
        processing_time = end_perf - start_perf
        
        error_msg = str(e)
        print(f"[ERROR] 處理失敗: {error_msg}")
        print(f"[TIME] 失敗前處理時間: {processing_time:.3f} 秒")
        
        import traceback
        traceback.print_exc()
        
        # 記錄失敗的處理時間
        log_processing_time(csv_filename, processing_time, 0, start_time, end_time, success=False, error_msg=error_msg)
        
        return False

def main():
    """主要執行函數"""
    
    # 檢查命令行參數
    if len(sys.argv) != 2:
        print("[OPEN_BOOK] 使用方式:")
        print(f"python {sys.argv[0]} <CSV檔名>")
        print(f"")
        print(f"範例:")
        print(f"python {sys.argv[0]} KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv")
        print(f"python {sys.argv[0]} G2735KS1U-K(BA)_GHKR03.13_F2490018A_FT1_R0_ALL_20240910203816.csv")
        
        # 顯示可用的檔案
        doc_path = "/mnt/d/project/python/outlook_summary/doc"
        if os.path.exists(doc_path):
            print(f"\n[FOLDER] 可用的 CSV 檔案:")
            csv_files = [f for f in os.listdir(doc_path) if f.endswith('.csv')]
            for csv_file in csv_files:
                print(f"  • {csv_file}")
        
        return
    
    csv_filename = sys.argv[1]
    
    # 執行處理
    success = process_csv_file(csv_filename)
    
    if success:
        print(f"\n[PARTY] 程式執行完成！檔案已輸出到 logs 資料夾")
        print(f"[EDIT] 詳細記錄請查看: /mnt/d/project/python/outlook_summary/logs/datalog.txt")
    else:
        print(f"\n[ERROR] 程式執行失敗")

if __name__ == "__main__":
    main()