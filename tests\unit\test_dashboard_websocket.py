"""
統一監控儀表板 - WebSocket 管理服務單元測試

測試 WebSocket 管理服務的核心功能：
- 連接管理
- 訂閱機制
- 廣播功能
- 異常處理

符合測試要求：90% 以上覆蓋率
"""

import asyncio
import json
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from backend.monitoring.api.dashboard_websocket import (
    DashboardWebSocketManager,
    DashboardWebSocketClient,
    DashboardWebSocketMessage,
    DashboardSubscriptionType,
    DashboardMessageType
)
from backend.monitoring.core.dashboard_websocket_manager import (
    DashboardWebSocketService,
    DashboardWebSocketStats
)
from backend.monitoring.config.dashboard_config import DashboardConfig
from backend.monitoring.models.dashboard_models import DashboardState


class TestDashboardWebSocketMessage:
    """測試 WebSocket 訊息類"""
    
    def test_message_creation(self):
        """測試訊息創建"""
        message = DashboardWebSocketMessage(
            type=DashboardMessageType.METRICS_UPDATE,
            payload={"test": "data"},
            client_id="test-client"
        )
        
        assert message.type == DashboardMessageType.METRICS_UPDATE
        assert message.payload == {"test": "data"}
        assert message.client_id == "test-client"
        assert isinstance(message.timestamp, datetime)
    
    def test_message_to_dict(self):
        """測試訊息轉換為字典"""
        message = DashboardWebSocketMessage(
            type=DashboardMessageType.ALERT,
            payload={"alert": "test"},
            client_id="client-1"
        )
        
        result = message.to_dict()
        
        assert result["type"] == "alert"
        assert result["payload"] == {"alert": "test"}
        assert result["client_id"] == "client-1"
        assert "timestamp" in result
    
    def test_message_to_json(self):
        """測試訊息轉換為 JSON"""
        message = DashboardWebSocketMessage(
            type=DashboardMessageType.PING,
            payload={"ping": True}
        )
        
        json_str = message.to_json()
        parsed = json.loads(json_str)
        
        assert parsed["type"] == "ping"
        assert parsed["payload"] == {"ping": True}


class TestDashboardWebSocketClient:
    """測試 WebSocket 客戶端類"""
    
    def test_client_creation(self):
        """測試客戶端創建"""
        mock_websocket = Mock()
        client = DashboardWebSocketClient(
            client_id="test-client",
            websocket=mock_websocket,
            user_agent="test-agent"
        )
        
        assert client.client_id == "test-client"
        assert client.websocket == mock_websocket
        assert client.user_agent == "test-agent"
        assert len(client.subscriptions) == 0
        assert client.messages_sent == 0
        assert client.messages_received == 0
    
    def test_subscription_management(self):
        """測試訂閱管理"""
        mock_websocket = Mock()
        client = DashboardWebSocketClient("test", mock_websocket)
        
        # 測試訂閱
        client.subscriptions.add(DashboardSubscriptionType.METRICS)
        assert client.is_subscribed_to(DashboardSubscriptionType.METRICS)
        assert not client.is_subscribed_to(DashboardSubscriptionType.ALERTS)
        
        # 測試全部訂閱
        client.subscriptions.add(DashboardSubscriptionType.ALL)
        assert client.is_subscribed_to(DashboardSubscriptionType.ALERTS)
    
    def test_activity_tracking(self):
        """測試活動追蹤"""
        mock_websocket = Mock()
        client = DashboardWebSocketClient("test", mock_websocket)
        
        original_time = client.last_activity
        client.update_activity()
        
        assert client.last_activity > original_time
    
    def test_is_alive(self):
        """測試連接存活檢查"""
        mock_websocket = Mock()
        client = DashboardWebSocketClient("test", mock_websocket)
        
        # 新連接應該是存活的
        assert client.is_alive(timeout_seconds=300)
        
        # 模擬超時
        client.last_activity = datetime.now() - timedelta(seconds=400)
        assert not client.is_alive(timeout_seconds=300)
    
    def test_to_dict(self):
        """測試轉換為字典"""
        mock_websocket = Mock()
        client = DashboardWebSocketClient(
            client_id="test-client",
            websocket=mock_websocket,
            user_agent="test-agent"
        )
        client.subscriptions.add(DashboardSubscriptionType.METRICS)
        
        result = client.to_dict()
        
        assert result["client_id"] == "test-client"
        assert result["user_agent"] == "test-agent"
        assert "metrics" in result["subscriptions"]
        assert "connected_at" in result
        assert "is_alive" in result


class TestDashboardWebSocketManager:
    """測試 WebSocket 管理器"""
    
    @pytest.fixture
    def mock_config(self):
        """模擬配置"""
        config = Mock()
        config.websocket_config = Mock()
        config.websocket_config.max_connections = 10
        config.websocket_config.message_queue_size = 100
        config.websocket_config.heartbeat_interval = 30
        config.websocket_config.connection_timeout = 300
        return config
    
    @pytest.fixture
    def manager(self, mock_config):
        """創建管理器實例"""
        return DashboardWebSocketManager(mock_config)
    
    def test_manager_initialization(self, manager, mock_config):
        """測試管理器初始化"""
        assert manager.config == mock_config
        assert len(manager.clients) == 0
        assert manager.total_connections == 0
        assert manager.total_messages_sent == 0
        assert manager.total_messages_received == 0
    
    @pytest.mark.asyncio
    async def test_connect_client(self, manager):
        """測試客戶端連接"""
        mock_websocket = AsyncMock()
        mock_websocket.headers = {"user-agent": "test-agent"}
        mock_websocket.client = Mock()
        mock_websocket.client.host = "127.0.0.1"
        
        client = await manager.connect_client(mock_websocket, "test-client")
        
        assert client.client_id == "test-client"
        assert "test-client" in manager.clients
        assert manager.total_connections == 1
        mock_websocket.accept.assert_called_once()
        mock_websocket.send_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_connect_client_max_connections(self, manager):
        """測試最大連接數限制"""
        # 設置最大連接數為 1
        manager.config.websocket_config.max_connections = 1
        
        # 連接第一個客戶端
        mock_websocket1 = AsyncMock()
        mock_websocket1.headers = {}
        mock_websocket1.client = None
        await manager.connect_client(mock_websocket1, "client-1")
        
        # 嘗試連接第二個客戶端（應該失敗）
        mock_websocket2 = AsyncMock()
        mock_websocket2.headers = {}
        mock_websocket2.client = None
        
        with pytest.raises(Exception, match="達到最大連接數限制"):
            await manager.connect_client(mock_websocket2, "client-2")
        
        mock_websocket2.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_disconnect_client(self, manager):
        """測試客戶端斷線"""
        # 先連接一個客戶端
        mock_websocket = AsyncMock()
        mock_websocket.headers = {}
        mock_websocket.client = None
        mock_websocket.client_state = Mock()
        mock_websocket.client_state.DISCONNECTED = False
        
        await manager.connect_client(mock_websocket, "test-client")
        assert "test-client" in manager.clients
        
        # 斷開連接
        await manager.disconnect_client("test-client", "測試斷線")
        assert "test-client" not in manager.clients
        mock_websocket.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_handle_subscribe_message(self, manager):
        """測試處理訂閱訊息"""
        # 創建客戶端
        mock_websocket = AsyncMock()
        mock_websocket.headers = {}
        mock_websocket.client = None
        client = await manager.connect_client(mock_websocket, "test-client")
        
        # 處理訂閱訊息
        message_data = json.dumps({
            "type": "subscribe",
            "payload": {"types": ["metrics", "alerts"]}
        })
        
        await manager.handle_client_message(client, message_data)
        
        assert DashboardSubscriptionType.METRICS in client.subscriptions
        assert DashboardSubscriptionType.ALERTS in client.subscriptions
        assert client.messages_received == 1
    
    @pytest.mark.asyncio
    async def test_handle_unsubscribe_message(self, manager):
        """測試處理取消訂閱訊息"""
        # 創建客戶端並添加訂閱
        mock_websocket = AsyncMock()
        mock_websocket.headers = {}
        mock_websocket.client = None
        client = await manager.connect_client(mock_websocket, "test-client")
        client.subscriptions.add(DashboardSubscriptionType.METRICS)
        client.subscriptions.add(DashboardSubscriptionType.ALERTS)
        
        # 處理取消訂閱訊息
        message_data = json.dumps({
            "type": "unsubscribe",
            "payload": {"types": ["metrics"]}
        })
        
        await manager.handle_client_message(client, message_data)
        
        assert DashboardSubscriptionType.METRICS not in client.subscriptions
        assert DashboardSubscriptionType.ALERTS in client.subscriptions
    
    @pytest.mark.asyncio
    async def test_handle_ping_message(self, manager):
        """測試處理心跳訊息"""
        # 創建客戶端
        mock_websocket = AsyncMock()
        mock_websocket.headers = {}
        mock_websocket.client = None
        client = await manager.connect_client(mock_websocket, "test-client")
        
        original_ping_time = client.last_ping
        
        # 處理心跳訊息
        message_data = json.dumps({
            "type": "ping",
            "payload": {"client_time": datetime.now().isoformat()}
        })
        
        await manager.handle_client_message(client, message_data)
        
        assert client.last_ping > original_ping_time
    
    @pytest.mark.asyncio
    async def test_broadcast_metrics_update(self, manager):
        """測試廣播指標更新"""
        # 創建訂閱客戶端
        mock_websocket = AsyncMock()
        mock_websocket.headers = {}
        mock_websocket.client = None
        client = await manager.connect_client(mock_websocket, "test-client")
        client.subscriptions.add(DashboardSubscriptionType.METRICS)
        
        # 廣播指標更新
        test_data = {"cpu": 50, "memory": 60}
        await manager.broadcast_metrics_update(test_data)
        
        # 等待廣播處理
        await asyncio.sleep(0.1)
        
        # 驗證訊息已發送（除了連接確認訊息）
        assert mock_websocket.send_text.call_count >= 2
    
    @pytest.mark.asyncio
    async def test_invalid_json_message(self, manager):
        """測試無效 JSON 訊息處理"""
        # 創建客戶端
        mock_websocket = AsyncMock()
        mock_websocket.headers = {}
        mock_websocket.client = None
        client = await manager.connect_client(mock_websocket, "test-client")
        
        # 發送無效 JSON
        await manager.handle_client_message(client, "invalid json")
        
        # 應該發送錯誤訊息
        assert mock_websocket.send_text.call_count >= 2  # 連接確認 + 錯誤訊息
    
    def test_get_connection_stats(self, manager):
        """測試獲取連接統計"""
        stats = manager.get_connection_stats()
        
        assert "current_connections" in stats
        assert "max_connections" in stats
        assert "total_connections" in stats
        assert "total_messages_sent" in stats
        assert "total_messages_received" in stats
        assert "uptime_seconds" in stats
        assert "message_queue_size" in stats
        assert "clients" in stats


class TestDashboardWebSocketService:
    """測試 WebSocket 核心服務"""
    
    @pytest.fixture
    def mock_config(self):
        """模擬配置"""
        config = Mock()
        config.websocket_config = Mock()
        config.websocket_config.max_connections = 100
        return config
    
    @pytest.fixture
    def service(self, mock_config):
        """創建服務實例"""
        return DashboardWebSocketService(mock_config)
    
    def test_service_initialization(self, service, mock_config):
        """測試服務初始化"""
        assert service.config == mock_config
        assert not service.is_running
        assert isinstance(service.stats, DashboardWebSocketStats)
    
    @pytest.mark.asyncio
    async def test_start_stop_service(self, service):
        """測試服務啟動和停止"""
        with patch('src.dashboard_monitoring.core.dashboard_websocket_manager.get_websocket_manager') as mock_get_manager:
            mock_manager = AsyncMock()
            mock_get_manager.return_value = mock_manager
            
            # 啟動服務
            await service.start_service()
            assert service.is_running
            mock_manager.start_background_tasks.assert_called_once()
            
            # 停止服務
            await service.stop_service()
            assert not service.is_running
            mock_manager.stop_background_tasks.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_broadcast_dashboard_state(self, service):
        """測試廣播儀表板狀態"""
        with patch('src.dashboard_monitoring.core.dashboard_websocket_manager.get_websocket_manager') as mock_get_manager:
            mock_manager = AsyncMock()
            mock_get_manager.return_value = mock_manager
            service._websocket_manager = mock_manager
            service.is_running = True
            
            # 創建測試狀態
            dashboard_state = DashboardState()
            
            # 廣播狀態
            await service.broadcast_dashboard_state(dashboard_state)
            
            mock_manager.broadcast_metrics_update.assert_called_once()
            assert service.stats.last_broadcast is not None
    
    @pytest.mark.asyncio
    async def test_broadcast_alert(self, service):
        """測試廣播告警"""
        with patch('src.dashboard_monitoring.core.dashboard_websocket_manager.get_websocket_manager') as mock_get_manager:
            mock_manager = AsyncMock()
            mock_get_manager.return_value = mock_manager
            service._websocket_manager = mock_manager
            service.is_running = True
            
            # 廣播告警
            alert_data = {"title": "測試告警", "level": "warning"}
            await service.broadcast_alert(alert_data)
            
            mock_manager.broadcast_alert.assert_called_once()
            assert service.stats.last_broadcast is not None
    
    def test_get_service_stats(self, service):
        """測試獲取服務統計"""
        stats = service.get_service_stats()
        
        assert isinstance(stats, DashboardWebSocketStats)
        assert stats.active_connections >= 0
        assert stats.total_connections >= 0
    
    def test_get_connection_info(self, service):
        """測試獲取連接資訊"""
        info = service.get_connection_info()
        
        assert "service_running" in info
        assert "connections" in info
        assert "max_connections" in info
        assert info["service_running"] == service.is_running
    
    def test_is_healthy(self, service):
        """測試健康檢查"""
        # 服務未運行時應該不健康
        assert not service.is_healthy()
        
        # 模擬服務運行
        service.is_running = True
        service._websocket_manager = Mock()
        
        assert service.is_healthy()
    
    @pytest.mark.asyncio
    async def test_cleanup_connections(self, service):
        """測試清理連接"""
        with patch('src.dashboard_monitoring.core.dashboard_websocket_manager.get_websocket_manager') as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_connection_stats.side_effect = [
                {"current_connections": 5},  # 清理前
                {"current_connections": 3}   # 清理後
            ]
            mock_get_manager.return_value = mock_manager
            service._websocket_manager = mock_manager
            
            cleaned = await service.cleanup_connections()
            assert cleaned == 2


class TestWebSocketStats:
    """測試 WebSocket 統計類"""
    
    def test_stats_creation(self):
        """測試統計創建"""
        stats = DashboardWebSocketStats()
        
        assert stats.active_connections == 0
        assert stats.total_connections == 0
        assert stats.messages_sent == 0
        assert stats.messages_received == 0
        assert stats.uptime_seconds == 0.0
        assert stats.last_broadcast is None
    
    def test_stats_to_dict(self):
        """測試統計轉換為字典"""
        stats = DashboardWebSocketStats(
            active_connections=5,
            total_connections=10,
            messages_sent=100,
            messages_received=50,
            uptime_seconds=3600.0,
            last_broadcast=datetime.now()
        )
        
        result = stats.to_dict()
        
        assert result["active_connections"] == 5
        assert result["total_connections"] == 10
        assert result["messages_sent"] == 100
        assert result["messages_received"] == 50
        assert result["uptime_seconds"] == 3600.0
        assert "last_broadcast" in result


if __name__ == "__main__":
    pytest.main([__file__, "-v"])