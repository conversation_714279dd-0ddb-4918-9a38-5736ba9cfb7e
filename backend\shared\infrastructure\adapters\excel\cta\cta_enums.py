#!/usr/bin/env python3
"""
CTA 核心枚舉類別
定義測試器類型和資料區段狀態
"""

from enum import Enum


class TesterType(Enum):
    """測試器類型枚舉 - 對應 VBA myTesterType"""
    UNKNOWN = 0
    CTA8280 = 1      # Data11, QAData, DataInfo 工作表
    STS8200 = 2      # DUT_DATA, QA_Data 工作表  
    EAGLE = 3        # ETS 標記
    YS = 4           # .PRG Line# 標記


class DataSectionState(Enum):
    """資料區段狀態枚舉 - 對應 VBA myFindedDataN"""
    INITIAL = 0      # 初始狀態
    DATA_FOUND = 1   # 找到 [Data] 標記
    QADATA_FOUND = 2 # 找到 [QAData] 標記