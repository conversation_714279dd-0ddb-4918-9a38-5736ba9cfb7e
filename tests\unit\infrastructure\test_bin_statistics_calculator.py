"""
BIN 統計計算器測試 - TDD 實作
測試 BIN 數量統計和降序排列功能

CLAUDE.md 規則:
- 後端程式碼必須先寫測試 (強制要求)
- 測試必須先失敗後再寫程式
"""

import pytest
import pandas as pd
from typing import Dict, List, Tuple
import sys
import os

# 加入專案路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

# 這個 import 會先失敗，因為我們還沒實作
try:
    from infrastructure.adapters.excel.bin_statistics_calculator import BinStatisticsCalculator
except ImportError:
    # TDD: 預期的失敗，我們稍後實作
    pass


class TestBinStatisticsCalculator:
    """BIN 統計計算器測試類別"""
    
    def test_calculate_bin_statistics_basic(self):
        """測試基本 BIN 統計計算功能"""
        # 準備測試資料
        test_data = {
            'A': ['Device1', 'Device2', 'Device3', 'Device4', 'Device5'],
            'B': [666, 298, 666, 102, 666],  # BIN 欄位
            'C': ['data1', 'data2', 'data3', 'data4', 'data5']
        }
        df = pd.DataFrame(test_data)
        
        # 建立計算器（這會失敗因為還沒實作）
        calculator = BinStatisticsCalculator()
        
        # 執行統計計算
        bin_stats = calculator.calculate_bin_statistics(df, bin_column=1, start_row=0)
        
        # 驗證結果
        assert isinstance(bin_stats, dict)
        assert len(bin_stats) == 3  # 應該有 3 個不同的 BIN
        
        # 驗證 BIN 666 統計
        assert '666' in bin_stats
        assert bin_stats['666']['count'] == 3
        assert bin_stats['666']['percentage'] == 60.0
        
        # 驗證 BIN 298 統計
        assert '298' in bin_stats
        assert bin_stats['298']['count'] == 1
        assert bin_stats['298']['percentage'] == 20.0
        
        # 驗證 BIN 102 統計
        assert '102' in bin_stats
        assert bin_stats['102']['count'] == 1
        assert bin_stats['102']['percentage'] == 20.0
    
    def test_calculate_bin_statistics_with_site_filtering(self):
        """測試按 Site 過濾的 BIN 統計"""
        # 準備測試資料 - 包含 Site 欄位
        test_data = {
            'A': ['Device1', 'Device2', 'Device3', 'Device4', 'Device5'],
            'B': [666, 298, 666, 102, 666],  # BIN 欄位
            'C': ['data1', 'data2', 'data3', 'data4', 'data5'],
            'D': ['Site1', 'Site1', 'Site2', 'Site2', 'Site1'],  # Site 欄位
        }
        df = pd.DataFrame(test_data)
        
        calculator = BinStatisticsCalculator()
        
        # 計算 Site1 的 BIN 統計
        site1_stats = calculator.calculate_bin_statistics_by_site(
            df, bin_column=1, site_column=3, site_value='Site1', start_row=0
        )
        
        # 驗證 Site1 結果
        assert len(site1_stats) == 2  # Site1 有 2 種 BIN: 666, 298
        assert site1_stats['666']['count'] == 2
        assert site1_stats['298']['count'] == 1
        
        # 計算 Site2 的 BIN 統計
        site2_stats = calculator.calculate_bin_statistics_by_site(
            df, bin_column=1, site_column=3, site_value='Site2', start_row=0
        )
        
        # 驗證 Site2 結果
        assert len(site2_stats) == 2  # Site2 有 2 種 BIN: 666, 102
        assert site2_stats['666']['count'] == 1
        assert site2_stats['102']['count'] == 1
    
    def test_sort_bin_statistics_descending(self):
        """測試 BIN 統計降序排列"""
        calculator = BinStatisticsCalculator()
        
        # 準備未排序的統計資料
        unsorted_stats = {
            '298': {'count': 2, 'percentage': 20.0},
            '666': {'count': 6, 'percentage': 60.0},
            '102': {'count': 1, 'percentage': 10.0},
            '1': {'count': 1, 'percentage': 10.0}
        }
        
        # 執行降序排列
        sorted_stats = calculator.sort_statistics_by_count_desc(unsorted_stats)
        
        # 驗證排序結果
        assert isinstance(sorted_stats, list)
        assert len(sorted_stats) == 4
        
        # 驗證降序排列 (依 count)
        assert sorted_stats[0][0] == '666'  # 最多的 BIN
        assert sorted_stats[0][1]['count'] == 6
        
        assert sorted_stats[1][0] == '298'  # 第二多的 BIN
        assert sorted_stats[1][1]['count'] == 2
        
        assert sorted_stats[2][0] == '102'  # 第三多的 BIN (平手時依 BIN 號排序)
        assert sorted_stats[2][1]['count'] == 1
        
        assert sorted_stats[3][0] == '1'   # 第四多的 BIN
        assert sorted_stats[3][1]['count'] == 1
    
    def test_generate_summary_table(self):
        """測試生成 Summary 表格"""
        calculator = BinStatisticsCalculator()
        
        # 準備統計資料
        bin_stats = {
            '666': {'count': 6, 'percentage': 60.0},
            '298': {'count': 2, 'percentage': 20.0},
            '102': {'count': 1, 'percentage': 10.0},
            '1': {'count': 1, 'percentage': 10.0}
        }
        
        # 生成 Summary 表格
        summary_table = calculator.generate_summary_table(bin_stats, total_devices=10)
        
        # 驗證表格結構
        assert isinstance(summary_table, list)
        assert len(summary_table) >= 4  # 至少要有標頭行 + 3 個 BIN 行
        
        # 驗證標頭
        header = summary_table[0]
        assert 'BIN' in header
        assert 'Count' in header
        assert 'Percentage' in header
        
        # 驗證資料行 (依 count 降序)
        data_rows = summary_table[1:]
        assert len(data_rows) == 4
        
        # 第一行應該是 BIN 666
        assert '666' in data_rows[0]
        assert '6' in data_rows[0]
        assert '60.0%' in data_rows[0]
    
    def test_real_csv_data_integration(self):
        """測試真實 CSV 資料整合"""
        calculator = BinStatisticsCalculator()
        
        # 模擬真實 CSV 資料結構 (基於 KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv)
        test_data = []
        # 模擬 12 行頭部資料
        for i in range(12):
            test_data.append(['header'] + ['col' + str(j) for j in range(10)])
        
        # 模擬設備資料 (第 13 行開始)
        device_data = [
            ['Device1', '666', 'data2', 'data3', 'Site1'],
            ['Device2', '666', 'data2', 'data3', 'Site1'], 
            ['Device3', '666', 'data2', 'data3', 'Site1'],
            ['Device4', '298', 'data2', 'data3', 'Site2'],
            ['Device5', '102', 'data2', 'data3', 'Site2'],
            ['Device6', '666', 'data2', 'data3', 'Site2'],
            ['Device7', '666', 'data2', 'data3', 'Site2'],
            ['Device8', '298', 'data2', 'data3', 'Site1'],
            ['Device9', '666', 'data2', 'data3', 'Site2'],
            ['Device10', '601', 'data2', 'data3', 'Site2'],
        ]
        
        test_data.extend(device_data)
        df = pd.DataFrame(test_data)
        
        # 執行統計計算 (第 13 行開始 = index 12)
        bin_stats = calculator.calculate_bin_statistics(df, bin_column=1, start_row=12)
        
        # 驗證統計結果符合真實資料
        assert '666' in bin_stats
        assert bin_stats['666']['count'] == 6  # 6 個 BIN 666
        
        assert '298' in bin_stats  
        assert bin_stats['298']['count'] == 2  # 2 個 BIN 298
        
        assert '102' in bin_stats
        assert bin_stats['102']['count'] == 1  # 1 個 BIN 102
        
        assert '601' in bin_stats
        assert bin_stats['601']['count'] == 1  # 1 個 BIN 601
        
        # 驗證百分比計算
        total_devices = 10
        assert bin_stats['666']['percentage'] == 60.0
        assert bin_stats['298']['percentage'] == 20.0
        assert bin_stats['102']['percentage'] == 10.0
        assert bin_stats['601']['percentage'] == 10.0
        
        # 驗證降序排列
        sorted_stats = calculator.sort_statistics_by_count_desc(bin_stats)
        assert sorted_stats[0][0] == '666'  # 最多的 BIN
        assert sorted_stats[1][0] == '298'  # 第二多的 BIN

    def test_empty_dataframe_handling(self):
        """測試空 DataFrame 處理"""
        calculator = BinStatisticsCalculator()
        empty_df = pd.DataFrame()
        
        result = calculator.calculate_bin_statistics(empty_df, bin_column=1, start_row=0)
        
        # 空資料應回傳空字典
        assert result == {}
    
    def test_invalid_column_handling(self):
        """測試無效欄位處理"""
        calculator = BinStatisticsCalculator()
        
        test_data = {
            'A': ['Device1', 'Device2'],
            'B': [666, 298]
        }
        df = pd.DataFrame(test_data)
        
        # 測試無效的 bin_column
        with pytest.raises(IndexError):
            calculator.calculate_bin_statistics(df, bin_column=10, start_row=0)


if __name__ == "__main__":
    # 執行測試 - 這些測試應該先失敗
    pytest.main([__file__, "-v"])