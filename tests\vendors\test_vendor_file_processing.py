#!/usr/bin/env python3
"""
測試廠商檔案處理流程
檢查為什麼沒有看到檔案處理的日誌
"""

import sys
import os
from datetime import datetime

# 加入項目路徑
sys.path.insert(0, os.path.dirname(__file__))

from backend.email.models.email_models import EmailData, EmailParsingResult
from backend.shared.application.services.unified_email_processor import UnifiedEmailProcessor
import asyncio

def create_test_gtk_email():
    """創建測試用 GTK 郵件"""
    return EmailData(
        message_id="test_gtk_vendor_files",
        subject="FW: [FT Hold Lot Report] [ Low yield ]---- Type: G2772HJ1U-A~B1 , MO: YNT8727512563 , LOT: GK8S54.1N~左 , Yield:93.64%",
        body="Original Message: From: GTK Team",
        sender="<EMAIL>",
        received_time=datetime.now(),
        attachments=[]
    )

async def test_full_workflow():
    """測試完整工作流程"""
    print("測試廠商檔案處理工作流程...")
    
    # 創建測試郵件
    test_email = create_test_gtk_email()
    print(f"   郵件主旨: {test_email.subject}")
    
    # 創建處理器
    processor = UnifiedEmailProcessor()
    
    # 執行完整處理流程
    print("\n開始 ALL IN ONE 郵件處理...")
    result = await processor.process_email_complete(
        email_data=test_email,
        email_id="test_001",
        process_attachments=False,  # 跳過附件處理
        process_vendor_files=True,   # 啟用廠商檔案處理
        send_notifications=False,    # 跳過通知
        update_database=False        # 跳過資料庫更新
    )
    
    print(f"\n處理結果:")
    print(f"   整體成功: {result.is_success}")
    print(f"   廠商代碼: {result.vendor_code}")
    print(f"   解析成功: {result.parsing_result.is_success}")
    if result.parsing_result.is_success:
        print(f"   產品代碼: {result.parsing_result.product_code}")
        print(f"   MO編號: {result.parsing_result.mo_number}")
        print(f"   LOT編號: {result.parsing_result.lot_number}")
    
    print(f"\n廠商檔案處理結果:")
    if result.vendor_files_result:
        print(f"   檔案處理成功: {result.vendor_files_result.get('success')}")
        print(f"   處理訊息: {result.vendor_files_result.get('message')}")
        if result.vendor_files_result.get('error'):
            print(f"   錯誤訊息: {result.vendor_files_result.get('error')}")
    else:
        print(f"   沒有廠商檔案處理結果")
    
    # 檢查管道模式狀態
    print(f"\n系統配置:")
    print(f"   管道模式: {processor.use_pipeline}")
    print(f"   FILE_TEMP_BASE_PATH: {os.getenv('FILE_TEMP_BASE_PATH', 'D:\\\\temp')}")
    print(f"   FILE_SOURCE_BASE_PATH: {os.getenv('FILE_SOURCE_BASE_PATH', '/mnt/share')}")
    
    return result

async def main():
    """主函數"""
    print("開始測試廠商檔案處理工作流程...")
    try:
        result = await test_full_workflow()
        
        if result.is_success and result.vendor_files_result and result.vendor_files_result.get('success'):
            print("廠商檔案處理測試成功！")
        else:
            print("廠商檔案處理測試失敗或跳過")
            
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())