"""檔案暫存服務測試"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from unittest.mock import patch, MagicMock

from backend.shared.infrastructure.adapters.file_staging_service import (
    FileStagingService,
    StagingStatus,
    StagingError,
    InsufficientSpaceError,
    FileIntegrityError,
    StagingPermissionError
)


@pytest.fixture
def temp_staging_dir():
    """建立臨時暫存目錄"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def temp_source_files():
    """建立臨時來源檔案"""
    temp_dir = tempfile.mkdtemp()
    
    # 建立測試檔案
    test_files = []
    for i in range(3):
        file_path = Path(temp_dir) / f"test_file_{i}.txt"
        file_path.write_text(f"Test content {i}" * 100)  # 建立一些內容
        test_files.append(file_path)
    
    # 建立測試目錄結構
    sub_dir = Path(temp_dir) / "subdir"
    sub_dir.mkdir()
    sub_file = sub_dir / "sub_file.txt"
    sub_file.write_text("Sub directory content" * 50)
    test_files.append(sub_file)
    
    yield test_files
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def staging_service(temp_staging_dir):
    """建立檔案暫存服務實例"""
    return FileStagingService(
        base_staging_path=temp_staging_dir,
        max_workers=2,
        verify_integrity=True
    )


class TestFileStagingService:
    """檔案暫存服務測試類"""
    
    def test_service_initialization(self, temp_staging_dir):
        """測試服務初始化"""
        service = FileStagingService(base_staging_path=temp_staging_dir)
        
        assert service.base_staging_path == Path(temp_staging_dir)
        assert service.max_workers == 4  # 預設值
        assert service.verify_integrity is True  # 預設值
        assert Path(temp_staging_dir).exists()
    
    def test_create_staging_task(self, staging_service, temp_source_files):
        """測試建立暫存任務"""
        product_name = "TEST_PRODUCT"
        
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files,
            preserve_structure=True
        )
        
        assert task_id is not None
        assert len(task_id) > 0
        
        # 檢查任務是否正確建立
        task = staging_service.get_task_status(task_id)
        assert task is not None
        assert task.product_name == product_name
        assert task.status == StagingStatus.PENDING
        assert len(task.file_infos) == len(temp_source_files)
        assert task.total_size > 0
    
    @pytest.mark.asyncio
    async def test_execute_staging_task_success(self, staging_service, temp_source_files):
        """測試成功執行暫存任務"""
        product_name = "TEST_PRODUCT"
        
        # 建立任務
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:2],  # 只使用前兩個檔案
            preserve_structure=True
        )
        
        # 執行任務
        result = await staging_service.execute_staging_task(task_id)
        
        # 驗證結果
        assert result.success is True
        assert result.task_id == task_id
        assert len(result.staged_files) == 2
        assert result.total_files == 2
        assert result.staging_duration > 0
        assert result.integrity_check_passed is True
        
        # 檢查檔案是否實際複製
        for staged_file in result.staged_files:
            assert Path(staged_file).exists()
            assert Path(staged_file).is_file()
        
        # 檢查任務狀態
        task = staging_service.get_task_status(task_id)
        assert task.status == StagingStatus.COMPLETED
        assert task.progress == 100.0
    
    @pytest.mark.asyncio
    async def test_execute_staging_task_with_directory(self, staging_service, temp_source_files):
        """測試暫存包含目錄的檔案"""
        product_name = "TEST_PRODUCT"
        
        # 包含目錄中的檔案
        source_dir = temp_source_files[0].parent
        
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=[source_dir],
            preserve_structure=True
        )
        
        result = await staging_service.execute_staging_task(task_id)
        
        assert result.success is True
        assert len(result.staged_files) > 0
        
        # 檢查目錄結構是否保持
        staging_dir = result.staging_directory
        assert staging_dir.exists()
    
    @pytest.mark.asyncio
    async def test_staging_task_nonexistent_file(self, staging_service):
        """測試暫存不存在的檔案"""
        product_name = "TEST_PRODUCT"
        nonexistent_files = ["/path/to/nonexistent/file.txt"]
        
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=nonexistent_files,
            preserve_structure=True
        )
        
        # 任務應該建立成功，但檔案列表為空
        task = staging_service.get_task_status(task_id)
        assert len(task.file_infos) == 0
        assert task.total_size == 0
    
    @pytest.mark.asyncio
    async def test_staging_task_insufficient_space(self, staging_service, temp_source_files):
        """測試磁碟空間不足的情況"""
        product_name = "TEST_PRODUCT"
        
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:1],
            preserve_structure=True
        )
        
        # 模擬磁碟空間不足
        with patch('shutil.disk_usage') as mock_disk_usage:
            mock_disk_usage.return_value = MagicMock(free=100)  # 只有100位元組可用
            
            result = await staging_service.execute_staging_task(task_id)
            
            assert result.success is False
            assert "磁碟空間不足" in result.error_message
    
    @pytest.mark.asyncio
    async def test_file_integrity_verification(self, staging_service, temp_source_files):
        """測試檔案完整性驗證"""
        product_name = "TEST_PRODUCT"
        
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:1],
            preserve_structure=True
        )
        
        # 啟用完整性驗證
        task = staging_service.get_task_status(task_id)
        task.verify_integrity = True
        
        result = await staging_service.execute_staging_task(task_id)
        
        assert result.success is True
        assert result.integrity_check_passed is True
        
        # 檢查檔案資訊中是否包含校驗和
        task = staging_service.get_task_status(task_id)
        for file_info in task.file_infos:
            if file_info.copied:
                assert file_info.checksum is not None
                assert file_info.verified is True
    
    @pytest.mark.asyncio
    async def test_cleanup_staging_directory(self, staging_service, temp_source_files):
        """測試清理暫存目錄"""
        product_name = "TEST_PRODUCT"
        
        # 建立並執行任務
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:1],
            preserve_structure=True
        )
        
        result = await staging_service.execute_staging_task(task_id)
        assert result.success is True
        
        # 確認暫存目錄存在
        staging_dir = result.staging_directory
        assert staging_dir.exists()
        
        # 清理暫存目錄
        cleanup_success = await staging_service.cleanup_staging_directory(task_id)
        assert cleanup_success is True
        assert not staging_dir.exists()
    
    def test_get_task_progress(self, staging_service, temp_source_files):
        """測試取得任務進度"""
        product_name = "TEST_PRODUCT"
        
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:1],
            preserve_structure=True
        )
        
        progress = staging_service.get_task_progress(task_id)
        
        assert progress["task_id"] == task_id
        assert progress["product_name"] == product_name
        assert progress["status"] == StagingStatus.PENDING.value
        assert progress["progress"] == 0.0
        assert progress["total_files"] == 1
        assert "staging_directory" in progress
        assert "created_at" in progress
    
    def test_list_tasks(self, staging_service, temp_source_files):
        """測試列出所有任務"""
        product_name = "TEST_PRODUCT"
        
        # 建立多個任務
        task_ids = []
        for i in range(3):
            task_id = staging_service.create_staging_task(
                product_name=f"{product_name}_{i}",
                source_files=temp_source_files[:1],
                preserve_structure=True
            )
            task_ids.append(task_id)
        
        tasks = staging_service.list_tasks()
        
        assert len(tasks) == 3
        for task in tasks:
            assert task["task_id"] in task_ids
            assert "product_name" in task
            assert "status" in task
            assert "progress" in task
    
    def test_cleanup_completed_tasks(self, staging_service, temp_source_files):
        """測試清理已完成的舊任務"""
        from datetime import timedelta
        
        product_name = "TEST_PRODUCT"
        
        # 建立任務
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files[:1],
            preserve_structure=True
        )
        
        # 手動設定任務為已完成狀態，並設定為很久以前完成
        task = staging_service.get_task_status(task_id)
        task.status = StagingStatus.COMPLETED
        task.completed_at = datetime.now() - timedelta(hours=25)  # 設定為25小時前完成
        
        # 清理舊任務（清理超過24小時的任務）
        staging_service.cleanup_completed_tasks(max_age_hours=24)
        
        # 檢查任務是否被清理
        remaining_task = staging_service.get_task_status(task_id)
        assert remaining_task is None
    
    @pytest.mark.asyncio
    async def test_preserve_structure_false(self, staging_service, temp_source_files):
        """測試不保持目錄結構的暫存"""
        product_name = "TEST_PRODUCT"
        
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=temp_source_files,
            preserve_structure=False  # 不保持結構
        )
        
        result = await staging_service.execute_staging_task(task_id)
        
        assert result.success is True
        
        # 檢查所有檔案都在根目錄下
        staging_dir = result.staging_directory
        for staged_file in result.staged_files:
            # 檔案應該直接在暫存目錄下，而不是在子目錄中
            relative_path = Path(staged_file).relative_to(staging_dir)
            assert len(relative_path.parts) == 1  # 只有檔案名，沒有子目錄
    
    def test_get_task_status_nonexistent(self, staging_service):
        """測試取得不存在任務的狀態"""
        nonexistent_task_id = "nonexistent-task-id"
        
        task = staging_service.get_task_status(nonexistent_task_id)
        assert task is None
        
        progress = staging_service.get_task_progress(nonexistent_task_id)
        assert "error" in progress
        assert progress["error"] == "任務不存在"


@pytest.mark.asyncio
async def test_staging_service_integration():
    """整合測試：完整的暫存工作流程"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # 建立測試檔案
        source_dir = Path(temp_dir) / "source"
        source_dir.mkdir()
        
        test_files = []
        for i in range(2):
            file_path = source_dir / f"integration_test_{i}.txt"
            file_path.write_text(f"Integration test content {i}" * 200)
            test_files.append(file_path)
        
        # 建立暫存服務
        staging_dir = Path(temp_dir) / "staging"
        service = FileStagingService(
            base_staging_path=str(staging_dir),
            max_workers=2,
            verify_integrity=True
        )
        
        try:
            # 建立任務
            task_id = service.create_staging_task(
                product_name="INTEGRATION_TEST",
                source_files=test_files,
                preserve_structure=True
            )
            
            # 檢查初始狀態
            progress = service.get_task_progress(task_id)
            assert progress["status"] == StagingStatus.PENDING.value
            
            # 執行任務
            result = await service.execute_staging_task(task_id)
            
            # 驗證結果
            assert result.success is True
            assert len(result.staged_files) == 2
            assert result.integrity_check_passed is True
            
            # 檢查最終狀態
            final_progress = service.get_task_progress(task_id)
            assert final_progress["status"] == StagingStatus.COMPLETED.value
            assert final_progress["progress"] == 100.0
            
            # 驗證檔案內容
            for i, staged_file in enumerate(result.staged_files):
                staged_path = Path(staged_file)
                assert staged_path.exists()
                content = staged_path.read_text()
                assert f"Integration test content {i}" in content
            
            # 清理
            cleanup_success = await service.cleanup_staging_directory(task_id)
            assert cleanup_success is True
            
        finally:
            # 確保清理資源
            if hasattr(service, 'executor'):
                service.executor.shutdown(wait=True)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])